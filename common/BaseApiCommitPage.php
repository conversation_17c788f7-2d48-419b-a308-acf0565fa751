<?php
/**
 * @category    library
 * @package     common
 * <AUTHOR>
 * @version     2014/12/1 17:11:10
 * @copyright   Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 **/

/**
 * 提交页面的基类
 */
class Zb_Common_BaseApiCommitPage {

    // ----------- NMQ CMD START -------------
    protected $dbName;
    protected $db;
    protected $autoTrans;
    protected $intCmdNo;
    protected $arrCommand;
    protected $strModName;
    protected $transId;

    public function initCommand($intCmdNo, $arrCommand, $strModName, $dbName, $autoTrans='on') {
        $this->intCmdNo   = $intCmdNo;
        $this->arrCommand = $arrCommand;
        $this->strModName = $strModName;
        $this->dbName     = $dbName;
        $this->autoTrans  = $autoTrans;
        $this->transId    = intval($_REQUEST['transid']);

        return true;
    }

    public function execute() {
        try {
            $out['errno'] = 0;
            //命令先进行去重处理
            $objMutex = new Zb_Util_TransIdMutex(array(
                'transId'    => $this->transId,
                'moduleName' => $this->strModName,
            ));
            $flag = $objMutex->flag;
            //nmqpusher logId 计入commit
            $nmqLogId = $this->arrCommand['_log_id'];
            Bd_Log::addNotice('nmq_logId', $nmqLogId);
            //新命令进入处理过程
            if ($flag === Hk_Util_TransIdMutex::STATUS_ALL_OK) {
                try {
                    $this->_start();
                    $this->process();
                    $this->_end();
                } catch (Hk_Util_Exception $e) {
                    //autoTrans默认开启，自动连接DB开启事务
                    if ($this->autoTrans === 'on') {
                        if (!empty($this->db)) {
                            $this->db->rollback();
                        }
                    }

                    $out['errno'] = $e->getErrNo();
                    $objMutex->setError();
                    Bd_Log::warning("Error:[Deal command failed], Abstract:[transid:'{$this->transId}']");
                }
            }

            //命令去重结束，根据命令状态决定结束还是重试
            $objMutex->unlock();
            Zb_Util_CacheHelper::getInstance(true)->finalExec();
        } catch (Hk_Util_Exception $e) {
            $out['errno'] = $e->getErrNo();
        }
        // 没有错误发生时，执行终极任务
        if ($out['errno'] === 0) {
            Zb_Common_FinalTask::execTask();
        }

        return $out;
    }

    protected function _start() {
        //autoTrans默认开启，自动连接DB开启事务
        if ($this->autoTrans === 'on') {
            $this->db = Hk_Service_Db::getDB($this->dbName);
            if (empty ( $this->db )) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('dbname' => $this->dbName));
            }

            $this->db->startTransaction();
        }
    }

    protected function _end() {
        //autoTrans默认开启，自动连接DB提交事务
        if ($this->autoTrans === 'on') {
            $this->db->commit();
        }
    }

    protected function _rollback() {
        //autoTrans默认开启，自动连接DB回滚事务
        if ($this->autoTrans === 'on') {
            $this->db->rollback();
        }
    }

    protected function process() {
        return true;
    }
    // ----------- NMQ CMD END -------------



    // ----------- API Base Start-------------
    /**
     * @var array data service list
     */
    private $services            = array();

    public function __construct () {
        $this->servicesAlias();
    }

    /**
     * 处理成功的返回数据结构
     *
     * @param  array|mixed    $data 具体数据包
     * @param  int      total 记录总行数, List 接口需要传该值
     * @return array
     */
    protected function success($data, $total = -1) {
        $total  = intval($total);
        $res    = array("errNo"=>Zb_Const_ServiceError::SUCCESS, "errStr"=>"", "data"=>$data);
        if ($total >= 0) {
            $res['total'] = $total;
        }
        return  $res;
    }

    /**
     * 处理失败，返回数据结构
     *
     * @param  int      $errNo      返回错误码
     * @param  string   $errStr     自定义返回消息
     * @return array
     */
    protected function error($errNo, $errStr='') {
        return Zb_Const_ServiceError::apiError($errNo, $errStr);
    }

    /**
     * 校验接口必传参数
     *
     * @param array|string  $required 必须传递的参数
     * @param array         $arrInput 接口接收到的参数
     * @return bool
     */
    protected function paramsRequired($required, array $arrInput) {
        if (empty($arrInput)) {
            return false;
        }
        if (!is_array($required)) {
            $required = array($required);
        }

        $lostParams = array();
        foreach($required as $req) {
            if (!array_key_exists($req, $arrInput)) {
                $lostParams[] = $req;
            }
        }

        if (empty($lostParams)) {
            return true;
        }

        return false;
    }

    /**
     * 用于调用 自动调用 service
     *
     * @param  string $name
     * @return mixed|null
     */
    public function __get ($name) {
        if (!class_exists($name)) {
            Zb_Const_ServiceError::warning('service does not exist', array("service" => $name));
            return null;
        }

        if (isset($this->services[$name])) {
            return $this->services[$name];
        }

        $obj = new $name;

        if (!is_null($obj)) {
            $this->services[$name] = $obj;
        } else {
            Zb_Const_ServiceError::warning('service instantiation failed', array("service" => $name));
        }

        return $obj;
    }

    // 定义lib 库中的类alias
    private function servicesAlias() {
    }

    // ----------- API Base End-------------
}





/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
