<?php
/**
 *  Qdlib_Common_Exception
 * 文件描述 缓存
 * Created on 2019/3/25  11:12
 * Create by xux<PERSON><PERSON>@zuoyebang.com
 */


class Qdlib_Common_Cache
{
    public function __construct()
    {
        $this->objRedis = Qdlib_Util_Cache::getQudaoRedis();
        $this->objCache = new Qdlib_Ds_Cache();
        $this->cacheUpdateTimeKey = '_CACHE_UPDATE_TIME';
        $this->updateStepTime = 60 * 20;
    }

    public function set($key, $val, $expire)
    {
        if (!$key) {
            return false;
        }

        if (!is_array($val)) {
            return false;
        }
        if (empty($val)) {
            return false;
        }

        if (!$expire) {
            $expire = 60 * 60 * 24;
        }

        $val = json_encode($val);

        $redisRes = $this->objRedis->set($key, $val);
        $this->objRedis->expire($key, $expire);

        $dbRes = $this->objCache->set($key, $val);
        if ($redisRes === false && $dbRes === false) {
            return false;
        }

        //记录缓存生成时间
        $cacheKey = $key . $this->cacheUpdateTimeKey;
        $ret = $this->objRedis->set($cacheKey, time(), $expire);
        Qdlib_Util_Log::addNotice("skupool_updateTime_key=" . $cacheKey, json_encode(['Key' => $cacheKey, 'time' => time(),'ret'=>$ret]));
        return true;
    }

    public function del($key){
        if (!$key) {
            return false;
        }
        $res = $this->objRedis->del($key);
        if ($res === null) {
            return false;
        }
        $dbres = $this->objCache->deleteRecode($key);
        if($dbres === false){
            return false;
        }
        return true;

    }

    public function delAndUpdateTime($key){
        if (!$key) {
            return false;
        }
        $res = $this->objRedis->del($key);
        if ($res === null) {
            return false;
        }
        $dbres = $this->objCache->deleteRecode($key);
        if($dbres === false){
            return false;
        }
        $cacheKey = $key . $this->cacheUpdateTimeKey;
        $res = $this->objRedis->del($cacheKey);
        if ($res === null) {
            return false;
        }
        return true;
    }
    public function get($key, $isWarn = 1)
    {
        if (!$key) {
            return false;
        }

        $res = $this->objRedis->get($key);

        if ($res === null || !$res) {
            Qdlib_Util_Log::warning('activity', 'Qdlib_Common_Cache', 'get', $key . "获取缓存失败", '');
            $res = $this->objCache->get($key);

            $this->objRedis->setex($key, 60 * 60 * 24, $res);
        }

        //缓存更新时间检测
        $cacheKey = $key . $this->cacheUpdateTimeKey;
        $updateTime = $this->objRedis->get($cacheKey);
        if ($updateTime && (time() - $updateTime >= $this->updateStepTime) && $isWarn) {

           if(strpos($key, '_NO_RUN_') === false){
               //发送报警邮件
               Qdlib_Util_Log::fatal('activity', 'Qdlib_Common_Cache', 'get', '缓存更新时间过长', json_encode(['key' => $cacheKey, 'cacheTime' => date('Y-m-d H:i:s', $updateTime), 'now' => date('Y-m-d H:i:s', time())]),['15210065534']);
               //ral调用 执行脚本

               //暂时关闭
               $this->notiScriptRun($key);
           }

        }


        return $res;
    }

    public function notiScriptRun($key = '')
    {
        $arrInput = [
            'cacheKey' => $key,
        ];
        $headers = array(
            'pathinfo' => "/qudao/repair/runcachescript",
        );
        $extra = [
            'bk'=>mt_rand(),
            'retry' => 0,
            'ctimeout' => 100,
            'wtimeout' => 100,
            'rtimeout' => 100,
        ];

        Qdlib_Util_Tool::sendRal($headers, $arrInput, 'post', 'toufang', $extra);
    }
}