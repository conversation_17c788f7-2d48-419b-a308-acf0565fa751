<?php

/**
 * 数组相关辅助方法类
 * Class Util_Arr
 */
class Zb_Util_Arr
{
    const PICKUP_KK_AUTOINCR = NULL;
    const PICKUP_KK_HOLD = TRUE;
    const PICKUP_VK_ENTIRE = NULL;
    const PICKUP_VD_SKIP = FALSE;
    const PICKUP_VD_OVERWRITE = TRUE;
    const PICKUP_VD_MERGE = NULL;

    /**
     * pickup
     * 从二维数组中提取信息
     * @param array $array 目标二维数组
     * @param mixed $valueKey
     *      Course_Util_Array::PICKUP_VK_ENTIRE: 使用整个子数组作为提取后的值
     *      array('field1', 'field2', ...): 使用子数组中指定的几列组成新数组作为提取后的值
     *      string: 使用指定子数组中一列值作为提取后的值
     * @param mixed $keyKey
     *      Course_Util_Array::PICKUP_KK_HOLD: 保留原来的Key
     *      Course_Util_Array::PICKUP_KK_AUTOINCR: 使用自增长Key
     *      string: 使用子数组中一列值作为提取后的key
     * @param mixed $onDup
     *      Course_Util_Array::PICKUP_VD_MERGE: 合并重复key对应的值
     *      Course_Util_Array::PICKUP_VD_OVERWRITE: 重复key的值, 后面覆盖前面
     *      Course_Util_Array::PICKUP_VD_SKIP: 重复key的值, 保留前面, 跳过后面
     * @static
     * @access public
     * @return array 提取到的信息
     */
    public static function pickup($array, $valueKey, $keyKey = self::PICKUP_KK_HOLD, $onDup = self::PICKUP_VD_SKIP) {
        $target = array();
        if (is_array($array)) {
            $index = -1;
            foreach ($array as $k => $v) {
                $key = $keyKey === self::PICKUP_KK_AUTOINCR
                    ? (++$index)
                    : ($keyKey === self::PICKUP_KK_HOLD ? $k : @$v[$keyKey]);
                if (is_string($valueKey)) {
                    $value = @$v[$valueKey];
                } else if (is_array($valueKey)) {
                    $value = array();
                    foreach ($valueKey as $vk) {
                        $value[$vk] = @$v[$vk];
                    }
                } else {
                    $value = $v;
                }
                if ($onDup === self::PICKUP_VD_MERGE) {
                    $target[$key][] = $value;
                } else if ($onDup === self::PICKUP_VD_OVERWRITE) {
                    $target[$key] = $value;
                } else if (!array_key_exists($key, $target)) {
                    $target[$key] = $value;
                }
            }
        }
        return $target;
    }


    /**
     * 按照二维数组的某个key进行排序
     * @param array $arr 要排序的二维数组
     * @param string $sortKey 指定排序的 key
     * @param string $sortType 排序类型
     * <AUTHOR>
     * @return array 排序后的数组
     */
    public static function sortMultiArr($arr, $sortKey, $sortType)
    {

        $arrSort = [];
        foreach ($arr as $rowId => $row) {
            foreach ($row as $key => $val) {
                $arrSort[$key][$rowId] = $val;
            }
        }

        array_multisort($arrSort[$sortKey], $sortType, $arr);
        return $arr;
    }

    /**
     * Flatten a multi-dimensional associative array with dots.
     *
     * @param  array $array
     * @param  string $prepend
     * @return array
     */
    public static function dot($array, $prepend = '')
    {
        $results = [];

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $results = array_merge($results, static::dot($value, $prepend . $key . '.'));
            } else {
                $results[$prepend . $key] = $value;
            }
        }

        return $results;
    }

    /**
     * Determines if an array is associative.
     *
     * An array is "associative" if it doesn't have sequential numerical keys beginning with zero.
     *
     * @param  array $array
     * @return bool
     */
    public static function isAssoc($array)
    {
        $keys = array_keys($array);

        return array_keys($keys) !== $keys;
    }

    /**
     * Get an item from an array using "dot" notation.
     *
     * @param array|\ArrayAccess $array
     * @param string $key
     * @param mixed|null $default
     * @return mixed
     */
    public static function get($array, $key, $default = null)
    {
        if (is_null($key)) {
            return $array;
        }

        if (isset($array[$key])) {
            return $array[$key];
        }

        foreach (explode('.', $key) as $segment) {
            if (is_null($array)) {
                break;
            }
            $array = isset($array[$segment]) ? $array[$segment] : null;
        }

        return is_null($array) ? $default : $array;
    }

    /**
     * Set an item to an array using "dot" notation
     * @param $array
     * @param $key
     * @param $value
     */
    public static function set(&$array, $key, $value) {
        if (is_null($key)) return;
        $keys = explode(".", $key);
        if (count($keys) == 1) {
            $array[$key] = $value;
            return;
        }
        $first = $keys[0];
        if (!isset($array[$first]) || is_array($array[$first])) {
            $array[$first] = [];
        }
        self::set($array[$first], implode('.', array_slice($keys, 1)), $value);
    }

    /**
     * Check if an item exists in an array using "dot" notation.
     *
     * @param  array $array
     * @param  string $key
     * @return bool
     */
    public static function has($array, $key)
    {
        if (empty($array) || is_null($key)) {
            return false;
        }

        if (static::isAssoc($array)) {
            if (array_key_exists($key, $array)) {
                return true;
            }
        } else {
            if (in_array($key, $array)) {
                return true;
            }
        }

        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return false;
            }
            $array = $array[$segment];
        }

        return true;
    }

    /**
     * @param array $array
     *
     * @return mixed|null
     */
    public static function last($array)
    {
        if (empty($array)) {
            return null;
        }

        return array_pop($array);
    }

    public static function snakeKey($array)
    {
        if (empty($array) || !static::isAssoc($array)) {
            return $array;
        }

        $ret = [];
        foreach ($array as $key => $value) {
            $snakeKey = Zb_Util_Str::snake($key);
            $ret[$snakeKey] = $value;
        }

        return $ret;
    }

    /**
     * 对array进行遍历查询
     * @param array|\Iterator $array
     * @param callable $where
     * @param null|string|array|callable $columns
     * @return array
     */
    public static function select($array, callable $where, $columns = null)
    {
        $result = [];
        if (empty($array) || count($array) == 0) {
            return $result;
        }

        foreach ($array as $key => $value) {
            if ($where($value)) {
                if (is_string($columns) || is_null($columns)) {
                    $result[$key] = static::get($value, $columns);
                } elseif (is_array($columns)) {
                    $temp = [];
                    foreach ($columns as $k => $v) {
                        $temp[$k] = static::get($v, $value);
                    }
                    $result[$key] = $temp;
                } elseif (is_callable($columns)) {
                    $result[$key] = $columns($value);
                }
            }
        }

        if (static::isAssoc($array)) {
            return $result;
        } else {
            return array_values($result);
        }
    }

    /**
     * 索引数组转换成关联数组
     * @param array $arr
     * @param string $key
     * @return array[]
     */
    public static function convertToMapArr($arr, $key)
    {
        $ret = [];
        if (empty($arr)) {
            return $ret;
        }

        foreach ($arr as $item) {
            $ret[$item[$key]] = $item;
        }

        return $ret;
    }

    /**
     * @param $arr
     * @param array $keys
     * @param bool $skip
     * @return array
     */
    public static function collect($arr, $keys=[], $skip=true) {
        if (empty($keys)) return [];
        $r = [];
        foreach ($keys as $key) {
            if ($skip && !isset($arr[$key])) continue;
            $r[$key] = isset($arr[$key]) ? $arr[$key] : null;
        }
        return $r;
    }

    /**
     * 判定两个数组是否相等, 两个数组必需为完全的关联数组
     * @param array $arrA
     * @param array $arrB
     * @return bool
     */
    public static function same(array $arrA, array $arrB) {
        if (count(array_keys($arrA)) !== count(array_keys($arrB))) {
            return false;
        }
        foreach ($arrA as $key => $elem) {
            if (!isset($arrB[$key])) {
                return false;
            }
            if (is_scalar($elem)) { // 非数组
                if (!is_scalar($arrB[$key]) || $elem != $arrB[$key]) {
                    return false;
                }
            }elseif (is_array($elem)) {
                if (!is_array($arrB[$key])) {
                    return false;
                }
                if (!self::same($elem, $arrB[$key])) {
                    return false;
                }
            }
        }
        return true;
    }


    /**
     * 对数组进行递归ksort操作
     * @param $arr
     */
    public static function ksort(&$arr) {
        ksort($arr);
        foreach ($arr as &$value) {
            if (is_array($value)) {
                self::ksort($value);
            }
        }
    }

    /**
     * 进行重复元素排序
     * @param $fromToMap
     * @return array|bool
     * 形如
     * [
     *      5 => 6,
     *      1 => 3,
     *      3 => 5,
     *      6 => 2,
     *      4 => 1,
     * ],
     * 返回 [4,1,3,5,6]
     */
    public static function linkSort($fromToMap) {
        $mapKeys = array_keys($fromToMap);
        $mapValues = array_unique(array_values($fromToMap));
        if (count($mapKeys) !== count($mapValues)) {
            return false;
        }
        $diff = array_diff($mapKeys, $mapValues);
        if (count($diff) !== 1) {
            return false;
        }

        $from = array_values($diff)[0];
        $list = [$from];
        $to = $fromToMap[$from];
        while (isset($fromToMap[$to])) {
            $list[] = $to;
            $to = $fromToMap[$to];
        }
        return $list;
    }
}