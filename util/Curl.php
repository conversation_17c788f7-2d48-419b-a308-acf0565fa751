<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      library/snsmis/util/Curl.php
 * @author:        <EMAIL>
 * @desc:          curl类 
 * @create:        2020-04-23 17:55:16
 * @last modified: 2020-04-23 17:55:16
 */
class Saaslib_Util_Curl {

	private static $url    = '';      // 访问的url
    private static $oriUrl = '';      // referer url
    private static $data   = array(); // 可能发出的数据 post,put
    private static $method = 'GET';   // 访问方式，默认是GET请求
    
    /**
     * 发送请求
     * @param  string  $url    请求url
     * @param  array   $data   数据体
     * @param  string  $method 请求方式
     * @return boolean
     */
    public static function send($url, $data = array(), $method = 'get') {
        if ( ! $url){
            Bd_Log::warning('url can not be null');
            return false;
        }
        self::$url    = $url;
        self::$method = strtolower($method);
        $urlArr = parse_url($url);
        self::$oriUrl = $urlArr['scheme'] .'://'. $urlArr['host'];
        self::$data = $data;
        if ( ! in_array(self::$method, array('get', 'post', 'put', 'delete')) ){
            Bd_Log::warning('error request method type!');
            return false;
        }
        $func = self::$method . 'Request';
        return self::$func();
    }
    /**
     * 基础发起curl请求函数
     * @param int $is_post 是否是post请求
     */
    private static function doRequest($is_post = 0) {
        $ch = curl_init();//初始化curl
        curl_setopt($ch, CURLOPT_URL, self::$url);//抓取指定网页
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        // 来源一定要设置成来自本站
        curl_setopt($ch, CURLOPT_REFERER, self::$oriUrl);
        
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        if($is_post == 1){
            curl_setopt($ch, CURLOPT_POST, $is_post);//post提交方式
        }
        if ( ! empty(self::$data)) {
            self::$data = self::dealPostData(self::$data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, self::$data);
        }
        
        $data = curl_exec($ch);//运行curl    
        curl_close($ch);
        return $data;
    }

    /**
     * 处理发起非get请求的传输数据
     * @param  array $postData
     * @return boolean
     */
    private static function dealPostData($postData) {
        if (!is_array($postData)){
            Bd_Log::warning('post data should be array');
            return false;
        }
        foreach ($postData as $k => $v) {
            $o .= "$k=" . ( is_array($v) ? json_encode($v) : urlencode($v)) . "&";
        }
        $postData = substr($o, 0, -1);
        return $postData;
    }

    /**
     * 发起get请求
     */
    private static function getRequest() {
        return self::doRequest(0);
    }
    /**
     * 发起post请求
     */
    private static function postRequest() {
        return self::doRequest(1);
    }
    
    /**
     * 发起put请求
     */
    private static function putRequest($param) {
        return self::doRequest(2);
    }
    
    /**
     * 发起delete请求
     */
    private static function deleteRequest($param) {
        return self::doRequest(3);
    }

}
