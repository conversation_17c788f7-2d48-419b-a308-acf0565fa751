<?php
/**************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   UcepCandyPosition.php
 * <AUTHOR>
 * @date   2018/10/19 16:18
 * @brief  ucep-糖果位
 **/
class Oplib_Util_UcepCandyPosition
{
    const TAG_ID_OFFSET        = 500000000; // ucep糖果位置tagId偏移值，糖果位置tagId=TAG_ID_OFFSET+adgId
    const TYPE_MING_SHI_WEI_KE = 10; // 名师微课相关 10-19

    public static $TYPE_ARRAY = [
        /* 暂时不启用,由ucep后台投放
        self::TYPE_MING_SHI_WEI_KE => [
            'name'      => '名师微课',
            'clickType' => 1,
            'clickUrl'  => '/fdactivity/ucep/mingshiweikerecommend', // 不包括域名的url
            'cardImg'   => 'https://img.zuoyebang.cc/zyb_500f00003b920fecfdbef7d206409972.jpg',
        ],
        */

    ];


    /** 获取所有ucep用户在糖果位置的活动信息
     * @param $uid
     * @param $posId
     * @param $fromPrefix
     * @param array $arrParams
     * @return array
     */
    public static function getUcepUserAllActivityInfoList($uid, $posId, $fromPrefix, $arrParams = [])
    {
        $arrOutput = [];

        // 使用ucep后台投放的
        $activityInfo = self::getUcepInfo($uid, $posId, $fromPrefix, $arrParams);
        if ($activityInfo) {
            $arrOutput[] = $activityInfo;
        }

        $typeList = array_keys(self::$TYPE_ARRAY);
        foreach ($typeList as $type) {
            $activityInfo = self::getUcepUserActivityInfo($type, $uid, $posId, $fromPrefix);
            if ($activityInfo) {
                $arrOutput[] = $activityInfo;
            }
        }

        return $arrOutput;
    }

    // 使用ucep后台投放的
    public static function getUcepInfo($uid, $posId, $fromPrefix, $arrParams)
    {
        $arrOutput = [];

        $cuid     = strval($arrParams['cuid']);
        $ucep     = 0;

        // 是否ucep用户
        do {
            if(0 < $uid){
                $isUseUid = 1;
                $uuid     = $uid;
            }elseif (0 < strlen($cuid)){
                $isUseUid = 0;
                $uuid     = $cuid;
            }else{
                break;
            }
            Bd_Log::addNotice("ucepUseUid{$posId}", $isUseUid);

            $objUcep     = new Oplib_Common_UCEP();
            $arrUcepInfo = $objUcep->getUcepByPos($uuid, $posId);
            $adgInfo     = $arrUcepInfo['adgInfo'];
            // 广告组
            if (empty($adgInfo)) {
                break;
            }
            // 有可能入口被下掉 check一次
            $strPos = $adgInfo['posIds'];
            $arrPos = explode(',', $strPos);
            if (!in_array($posId, $arrPos)) {
                Bd_Log::addNotice("ucepSkip{$posId}", 'posNotNeed');
                break;
            }
            // 取具体创意 暂时必须要有创意
            $arrIdea = $adgInfo['ideaList'][$posId];
            if (empty($arrIdea)) {
                Bd_Log::addNotice("ucepSkip{$posId}", 'ideaEmpty');
                break;
            }
            $adgId = $arrUcepInfo['ucepUser'][$posId][0]['adg'];

            // 是否有扩展参数数据下发
            $arrUcepArgs = [];
            // 是否有到扩展数据下发
            $arrExt = [];
            if ($arrUcepInfo['ucepUser']['ext'][$adgId]) {
                $arrExt = $arrUcepInfo['ucepUser']['ext'][$adgId];
            }
            // 按照ext来处理
            if ($arrExt) {
                if (isset($arrExt['args']) && !empty($arrExt['args'])) {
                    $arrUcepArgs = $arrExt['args'];
                    Bd_Log::addNotice("ucepArgs{$posId}", json_encode($arrUcepArgs));
                }
            }

            // 随机取创意
            $randId   = array_rand($arrIdea, 1);
            $randIdea = $arrIdea[$randId];

            // 更改输出
            $ucep      = 1;
            $arrOutput = [
                'adId'      => self::TAG_ID_OFFSET + intval($adgId),
                'name'      => strval($randIdea['ext']['name']),
                'clickType' => intval($randIdea['clickType']),
                'clickUrl'  => strval($randIdea['clickUrl']),
                'cardImg'   => strval($randIdea['pid']),
            ];

            // 如果旧版的糖果位，设置成旧版的图标
            if (isset($arrParams['isOldCandyPos']) && $arrParams['isOldCandyPos'] &&
                isset($randIdea['ext']['oldCardImg']) && !empty($randIdea['ext']['oldCardImg'])) {
                $arrOutput['cardImg'] = strval(trim($randIdea['ext']['oldCardImg']));
            }

            // from值为拼接出来的 adgId，posId，ideaId
            $from                  = $fromPrefix . sprintf('%d>%d>%d', $adgId, $posId, $randIdea['id']);
            $arrUcepArgs['from']   = $from;
            $arrOutput['clickUrl'] = Oplib_Common_UCEP::buildJumpUrl($arrOutput['clickUrl'], $adgId, $posId, $randIdea['id'], $from, $arrUcepArgs);
            $arrOutput['lastfrom'] = $from;
            break;
        } while (false);
        Bd_Log::addNotice("isUcep{$posId}", $ucep);

        return $arrOutput;
    }

    /** 获取糖果位指定类型的活动信息
     * @param        $type
     * @param        $uid
     * @param        $posId
     * @param string $fromPrefix
     * @return array|bool|mixed
     */
    public static function getUcepUserActivityInfo($type, $uid, $posId, $fromPrefix = '')
    {
        $arrOutput = [];

        $cacheKey = self::getCacheKey($type, $uid);
        if ($cacheKey === false) {
            Bd_Log::warning("Error:[getCacheKey param error],Detail:[type:{$type},uid:{$uid}]");

            return false;
        }

        $redisInstance = self::getRedisInstance();
        if ($redisInstance) {
            $strData = $redisInstance->get($cacheKey);
            if ($strData) {
                $arrParam          = json_decode($strData, true);
                $arrParam['posId'] = $posId;
                $type              = intval($type);
                $method            = "_checkParamType{$type}";
                if (method_exists(self, $method) && !self::$method($arrParam)) {
                    $strParam = json_encode($arrParam);
                    Bd_Log::warning("Error:[$method param error],Detail:[arrParam:$strParam]");

                    return false;
                }

                $lastfrom             = $fromPrefix . sprintf('%d>%d>%d', $arrParam['adgId'], $posId, $arrParam['ideaId']); // 前缀_广告组id_广告位id_创意id
                $arrParam['from']     = $lastfrom;
                $arrParam['lastfrom'] = $lastfrom;

                $arrOutput             = self::$TYPE_ARRAY[$type];
                $arrOutput['clickUrl'] = Hk_Util_Host::getHost() . $arrOutput['clickUrl'] . (strpos($arrOutput['clickUrl'], '?') === false ? '?' : '&') . http_build_query($arrParam);
                $arrOutput['lastfrom'] = $lastfrom;
                $arrOutput['adId']     = self::TAG_ID_OFFSET + intval($arrParam['adgId']);
            }
        }

        return $arrOutput;
    }

    /** 设置糖果位指定类型的活动信息
     * @param     $type            类型：必须是存在的类型
     * @param     $uid             用户id: 必须大于0
     * @param     $arrParam        保存的参数
     * @param int $expiredTime     过期时间
     * @return bool
     */
    public static function setUcepUserActivityInfo($type, $uid, $arrParam, $expiredTime = Oplib_Const_Cache::UCEP_CANDY_POSITION_TIME)
    {
        $cacheKey = self::getCacheKey($type, $uid);
        if ($cacheKey === false) {
            Bd_Log::warning("Error:[getCacheKey param error],Detail:[type:{$type},uid:{$uid}]");

            return false;
        }
        $type   = intval($type);
        $method = "_checkParamType{$type}";
        if (method_exists(self, $method) && !self::$method($arrParam)) {
            $strParam = json_encode($arrParam);
            Bd_Log::warning("Error:[$method param error],Detail:[arrParam:$strParam]");

            return false;
        }
        $redisInstance = self::getRedisInstance();
        if ($redisInstance) {
            $strParam = json_encode($arrParam);
            $ret      = $redisInstance->set($cacheKey, $strParam, $expiredTime);

            return $ret;
        }

        return false;
    }

    /** 获取redis实例
     * @param bool $isNew 是否每次都获取新的实例，true是，false否，默认false
     * @return Hk_Service_Redis|null
     */
    public static function getRedisInstance($isNew = false)
    {
        static $redisInstance = null;
        if (!$redisInstance || $isNew) {
            $redisInstance = Hk_Service_RedisClient::getInstance("zhibo");
        }
        return $redisInstance;
    }

    /** 根据type和uid生存cacheKey
     * @param $type          类型：必须是存在的类型
     * @param $uid           用户id: 必须大于0
     * @return bool|string   返回false说明参数不合法获取cacheKey失败
     */
    private static function getCacheKey($type, $uid)
    {
        $type = intval($type);
        $uid  = intval($uid);
        if (array_key_exists($type, self::$TYPE_ARRAY) && 0 < $uid) {

            return sprintf(Oplib_Const_Cache::UCEP_CANDY_POSITION_UID_KEY_PRE, $type, $uid);
        }

        return false;
    }


    /////////////////////////////////////////////////////////////////////////////////
    ///////////////////////// 验证投放数据完整性 ///////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////
    /** 验证参数是否合法
     * @param $arrParam
     * @return bool
     */
    private static function _checkParamType1($arrParam)
    {
        // 以下参数必须存在
        $keys = ['adgId', 'ideaId'];
        foreach ($keys as $key) {
            if (array_key_exists($key, $arrParam) && 0 < ($arrParam[$key] = intval($arrParam[$key]))) {
                continue;
            }

            return false;
        }

        return true;
    }
}
