<?php
/**
 * Created by PhpStorm.
 * User: s<PERSON><PERSON><PERSON>@zuoyebang.com
 * Time: 2017/11/16 20:19
 * 免测关系
 */
class Hkzb_Util_Fudao_Course_FreeExam
{
    public static $FREE_EXAM = array(
        36336 =>
            array(
                0 => 24239,
                1 => 24269,
            ),
        36337 =>
            array(
                0 => 24239,
                1 => 24269,
                2 => 24262,
                3 => 24249,
                4 => 29688,
            ),
        36338 =>
            array(
                0 => 24239,
                1 => 24269,
            ),
        36340 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
                6 => 24285,
                7 => 24346,
                8 => 29690,
            ),
        36341 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
                4 => 24087,
                5 => 24185,
                6 => 24353,
            ),
        36342 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36343 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36344 =>
            array(
                0 => 24239,
                1 => 24269,
                2 => 24262,
                3 => 24249,
                4 => 29688,
            ),
        36345 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
            ),
        36346 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
                4 => 24087,
                5 => 24185,
                6 => 24353,
            ),
        36347 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36348 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
                6 => 24285,
                7 => 24346,
                8 => 29690,
            ),
        36349 =>
            array(
                0 => 24201,
                1 => 24195,
                2 => 24191,
                3 => 24199,
            ),
        36350 =>
            array(
                0 => 24201,
                1 => 24195,
            ),
        36351 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36352 =>
            array(
                0 => 24201,
                1 => 24195,
                2 => 24191,
                3 => 24199,
            ),
        36353 =>
            array(
                0 => 24239,
                1 => 24269,
            ),
        36354 =>
            array(
                0 => 24239,
                1 => 24269,
                2 => 24262,
                3 => 24249,
                4 => 29688,
            ),
        36355 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
                6 => 24285,
                7 => 24346,
                8 => 29690,
            ),
        36356 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
                4 => 24087,
                5 => 24185,
                6 => 24353,
            ),
        36357 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
                9 => 24128,
                10 => 23987,
                11 => 24131,
                12 => 30982,
            ),
        36358 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
                9 => 24128,
                10 => 23987,
                11 => 24131,
                12 => 30982,
            ),
        36359 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
                9 => 24128,
                10 => 23987,
                11 => 24131,
                12 => 30982,
            ),
        36360 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
            ),
        36370 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        36371 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        36372 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        36373 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
            ),
        36374 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
            ),
        36375 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
                9 => 24128,
                10 => 23987,
                11 => 24131,
                12 => 30982,
            ),
        36376 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        36377 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 27431,
                6 => 23963,
                7 => 23955,
                8 => 23947,
                9 => 23942,
                10 => 24128,
                11 => 23987,
                12 => 24131,
                13 => 30982,
            ),
        36378 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
                8 => 24301,
                9 => 24308,
                10 => 30984,
            ),
        36379 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
            ),
        36380 =>
            array(
                0 => 23886,
                1 => 29678,
                2 => 23890,
                3 => 23776,
                4 => 23827,
                5 => 23807,
                6 => 30708,
            ),
        36381 =>
            array(
                0 => 23871,
                1 => 23774,
            ),
        36382 =>
            array(
                0 => 23857,
                1 => 23911,
                2 => 23949,
                3 => 23964,
                4 => 23894,
                5 => 23941,
                6 => 23927,
                7 => 23935,
                8 => 23901,
            ),
        36383 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
                6 => 24229,
                7 => 24235,
            ),
        36384 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
                6 => 24229,
                7 => 24235,
            ),
        36385 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
            ),
        36386 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 27429,
                6 => 24278,
                7 => 24294,
                8 => 24274,
                9 => 24301,
                10 => 24308,
                11 => 30984,
            ),
        36387 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
                8 => 24301,
                9 => 24308,
                10 => 30984,
            ),
        36388 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        36389 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
            ),
        36390 =>
            array(
                0 => 24137,
                1 => 24256,
            ),
        36391 =>
            array(
                0 => 23936,
            ),
        36392 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
            ),
        36393 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
                8 => 24301,
                9 => 24308,
                10 => 30984,
            ),
        36394 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 27427,
                4 => 24223,
                5 => 24220,
                6 => 24218,
                7 => 24229,
                8 => 24235,
            ),
        36395 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
            ),
        36396 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
            ),
        36397 =>
            array(
                0 => 23781,
                1 => 29680,
                2 => 23871,
                3 => 23774,
                4 => 23796,
                5 => 23772,
                6 => 23769,
            ),
        36398 =>
            array(
                0 => 23871,
                1 => 23774,
                2 => 23796,
                3 => 23772,
                4 => 23769,
            ),
        36399 =>
            array(
                0 => 23871,
                1 => 23774,
            ),
        36400 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
                3 => 23807,
                4 => 30708,
            ),
        36401 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
            ),
        36402 =>
            array(
                0 => 23700,
                1 => 29682,
                2 => 23767,
                3 => 23745,
                4 => 23720,
                5 => 23711,
                6 => 23705,
                7 => 23737,
            ),
        36403 =>
            array(
                0 => 23767,
                1 => 23745,
                2 => 23720,
                3 => 23711,
                4 => 23705,
                5 => 23737,
            ),
        36404 =>
            array(
                0 => 23871,
                1 => 23774,
                2 => 23796,
                3 => 23772,
                4 => 23769,
            ),
        36405 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
            ),
        36406 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
                4 => 23927,
                5 => 23935,
                6 => 23901,
            ),
        36407 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
            ),
        36408 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
            ),
        36409 =>
            array(
                0 => 23886,
                1 => 29678,
                2 => 23890,
                3 => 23776,
                4 => 23827,
                5 => 23807,
                6 => 30708,
            ),
        36410 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
                3 => 23807,
                4 => 30708,
            ),
        36412 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
            ),
        36413 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
            ),
        36414 =>
            array(
                0 => 24137,
                1 => 24256,
            ),
        36415 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
            ),
        36416 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
            ),
        36417 =>
            array(
                0 => 23920,
            ),
        36418 =>
            array(
                0 => 23767,
                1 => 23745,
                2 => 23720,
                3 => 23711,
                4 => 23705,
                5 => 23737,
            ),
        36419 =>
            array(
                0 => 23871,
                1 => 23774,
                2 => 23796,
                3 => 23772,
                4 => 23769,
            ),
        36420 =>
            array(
                0 => 23781,
                1 => 29680,
                2 => 23871,
                3 => 23774,
                4 => 23796,
                5 => 23772,
                6 => 23769,
            ),
        36421 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
            ),
        36422 =>
            array(
                0 => 23767,
                1 => 23745,
                2 => 23720,
                3 => 23711,
                4 => 23705,
                5 => 23737,
            ),
        36423 =>
            array(
                0 => 23767,
                1 => 23745,
            ),
        36424 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
                4 => 23927,
                5 => 23935,
                6 => 23901,
            ),
        36426 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
                3 => 24151,
                4 => 24155,
            ),
        36427 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
                3 => 24151,
                4 => 24155,
                5 => 24159,
                6 => 24166,
            ),
        36428 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
                3 => 27425,
                4 => 24151,
                5 => 24155,
                6 => 24159,
                7 => 24166,
            ),
        36429 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
                3 => 24151,
                4 => 24155,
                5 => 24159,
                6 => 24166,
            ),
        36430 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
            ),
        36431 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
            ),
        36432 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
                3 => 23807,
                4 => 30708,
            ),
        36433 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
            ),
        36434 =>
            array(
                0 => 23871,
                1 => 23774,
            ),
        36435 =>
            array(
                0 => 23857,
                1 => 23911,
                2 => 23949,
                3 => 23964,
                4 => 23894,
                5 => 23941,
                6 => 23927,
                7 => 23935,
                8 => 23901,
            ),
        36436 =>
            array(
                0 => 23700,
                1 => 29682,
                2 => 23767,
                3 => 23745,
                4 => 23720,
                5 => 23711,
                6 => 23705,
                7 => 23737,
            ),
        36437 =>
            array(
                0 => 23767,
                1 => 23745,
                2 => 23720,
                3 => 23711,
                4 => 23705,
                5 => 23737,
            ),
        36438 =>
            array(
                0 => 23767,
                1 => 23745,
            ),
        36439 =>
            array(
                0 => 23949,
                1 => 23964,
                2 => 23894,
                3 => 23941,
                4 => 23927,
                5 => 23935,
                6 => 23901,
            ),
        36440 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
                3 => 24151,
                4 => 24155,
            ),
        36441 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
            ),
        36442 =>
            array(
                0 => 23920,
                1 => 24241,
                2 => 24215,
            ),
        36443 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
            ),
        36444 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
            ),
        36445 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
            ),
        36446 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36447 =>
            array(
                0 => 24303,
                1 => 24322,
                2 => 24315,
                3 => 24325,
                4 => 24279,
                5 => 24295,
            ),
        36448 =>
            array(
                0 => 24093,
                1 => 24095,
                2 => 24114,
                3 => 24091,
            ),
        36449 =>
            array(
                0 => 24201,
                1 => 24195,
            ),
        36450 =>
            array(
                0 => 23899,
                1 => 24205,
            ),
        36451 =>
            array(
                0 => 23899,
                1 => 24205,
            ),
        36452 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
            ),
        36453 =>
            array(
                0 => 23936,
            ),
        36454 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
            ),
        36455 =>
            array(
                0 => 23890,
                1 => 23776,
                2 => 23827,
            ),
        36457 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36458 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
                3 => 36337,
                4 => 36344,
                5 => 36354,
            ),
        36459 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36461 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
                6 => 36340,
                7 => 36348,
                8 => 36355,
            ),
        36462 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36463 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36464 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
                4 => 36341,
                5 => 36346,
                6 => 36356,
            ),
        36465 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
                3 => 36337,
                4 => 36344,
                5 => 36354,
            ),
        36466 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36467 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
                4 => 36341,
                5 => 36346,
                6 => 36356,
            ),
        36468 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
                6 => 36340,
                7 => 36348,
                8 => 36355,
            ),
        36469 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36470 =>
            array(
                0 => 36350,
                1 => 36449,
                2 => 36349,
                3 => 36352,
            ),
        36471 =>
            array(
                0 => 36350,
                1 => 36449,
            ),
        36472 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36473 =>
            array(
                0 => 36350,
                1 => 36449,
                2 => 36349,
                3 => 36352,
            ),
        36474 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
                3 => 36337,
                4 => 36344,
                5 => 36354,
            ),
        36475 =>
            array(
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36476 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
                6 => 36340,
                7 => 36348,
                8 => 36355,
            ),
        36477 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
                4 => 36341,
                5 => 36346,
                6 => 36356,
            ),
        36478 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 36357,
                12 => 36358,
                13 => 36359,
                14 => 36375,
                15 => 40833,
                16 => 40834,
                17 => 45947,
                18 => 45937,
            ),
        36479 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 36357,
                12 => 36358,
                13 => 36359,
                14 => 36375,
                15 => 40833,
                16 => 40834,
                17 => 45947,
                18 => 45937,
            ),
        36480 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 36357,
                12 => 36358,
                13 => 36359,
                14 => 36375,
                15 => 40833,
                16 => 40834,
                17 => 45947,
                18 => 45937,
            ),
        36481 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 39193
            ),
        36491 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        36492 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
            ),
        36493 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        36494 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        36495 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
            ),
        36496 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 36378,
                10 => 36387,
                11 => 36393,
                12 => 42093,
            ),
        36497 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        36498 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 36357,
                12 => 36358,
                13 => 36359,
                14 => 36375,
                15 => 40833,
                16 => 40834,
                17 => 45947,
                18 => 45937,
            ),
        36499 =>
            array(
                0 => 36377,
                1 => 36450,
                2 => 36451,
                3 => 36373,
                4 => 36374,
                5 => 36415,
                6 => 36452,
                7 => 36370,
                8 => 36371,
                9 => 36372,
                10 => 36376,
                11 => 36388,
                12 => 36357,
                13 => 36358,
                14 => 36359,
                15 => 36375,
                16 => 40834,
                17 => 40833,
                18 => 42056,
            ),
        36500 =>
            array(
                0 => 36380,
                1 => 36409,
                2 => 36379,
                3 => 36408,
                4 => 36433,
                5 => 36455,
                6 => 36400,
                7 => 36410,
                8 => 36432,
            ),
        36501 =>
            array(
                0 => 36382,
                1 => 36435,
                2 => 36401,
                3 => 36405,
                4 => 36407,
                5 => 36421,
                6 => 36406,
                7 => 36424,
                8 => 36439,
            ),
        36502 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36503 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36504 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 36383,
                9 => 36384,
                10 => 42090,
            ),
        36505 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 36383,
                9 => 36384,
                10 => 42090,
            ),
        36506 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 39193,
            ),
        36507 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36386,
                7 => 36360,
                8 => 36385,
                9 => 36431,
                10 => 36378,
                11 => 36387,
                12 => 36393,
            ),
        36508 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 36378,
                10 => 36387,
                11 => 36393,
                12 => 42093,
            ),
        36509 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
            ),
        36510 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        36511 =>
            array(
                0 => 36414,
                1 => 36390,
            ),
        36512 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
            ),
        36513 =>
            array(
                0 => 36391,
                1 => 36453,
            ),
        36514 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 36378,
                10 => 36387,
                11 => 36393,
                12 => 42093,
            ),
        36515 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36394,
                6 => 36395,
                7 => 36396,
                8 => 36412,
                9 => 36383,
                10 => 36384,
            ),
        36516 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 39195,
            ),
        36517 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 39195,
            ),
        36518 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
                4 => 36400,
                5 => 36410,
                6 => 36432,
            ),
        36519 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36520 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
                3 => 36398,
                4 => 36404,
                5 => 36419,
            ),
        36521 =>
            array(
                0 => 36420,
                1 => 36381,
                2 => 36399,
                3 => 36434,
                4 => 36398,
                5 => 36404,
                6 => 36419,
            ),
        36522 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36523 =>
            array(
                0 => 36402,
                1 => 36436,
                2 => 36423,
                3 => 36438,
                4 => 36403,
                5 => 36418,
                6 => 36422,
                7 => 36437,
            ),
        36524 =>
            array(
                0 => 36423,
                1 => 36438,
                2 => 36403,
                3 => 36418,
                4 => 36422,
                5 => 36437,
            ),
        36525 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
                3 => 36398,
                4 => 36404,
                5 => 36419,
            ),
        36526 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36527 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36528 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
                4 => 36406,
                5 => 36424,
                6 => 36439,
            ),
        36529 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36530 =>
            array(
                0 => 36380,
                1 => 36409,
                2 => 36379,
                3 => 36408,
                4 => 36433,
                5 => 36455,
                6 => 36400,
                7 => 36410,
                8 => 36432,
            ),
        36531 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
                4 => 36400,
                5 => 36410,
                6 => 36432,
            ),
        36533 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 39195,
            ),
        36534 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
            ),
        36535 =>
            array(
                0 => 36414,
                1 => 36390,
            ),
        36536 =>
            array(
                0 => 36417,
            ),
        36537 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
            ),
        36538 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
            ),
        36539 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36540 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
                3 => 36398,
                4 => 36404,
                5 => 36419,
            ),
        36541 =>
            array(
                0 => 36420,
                1 => 36381,
                2 => 36399,
                3 => 36434,
                4 => 36398,
                5 => 36404,
                6 => 36419,
            ),
        36542 =>
            array(
                0 => 36423,
                1 => 36438,
                2 => 36403,
                3 => 36418,
                4 => 36422,
                5 => 36437,
            ),
        36543 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
                4 => 36406,
                5 => 36424,
                6 => 36439,
            ),
        36544 =>
            array(
                0 => 36423,
                1 => 36438,
            ),
        36545 =>
            array(
                0 => 36423,
                1 => 36438,
                2 => 36403,
                3 => 36418,
                4 => 36422,
                5 => 36437,
            ),
        36547 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 36428,
                4 => 36426,
                5 => 36440,
                6 => 36427,
                7 => 36429,
            ),
        36548 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 36426,
                4 => 36440,
                5 => 36427,
                6 => 36429,
                7 => 40991,
                8 => 40975,
            ),
        36549 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 36426,
                4 => 36440,
                5 => 40991,
            ),
        36550 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 36426,
                4 => 36440,
                5 => 36427,
                6 => 36429,
                7 => 40991,
                8 => 40975,
            ),
        36551 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
            ),
        36552 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 39193,
            ),
        36553 =>
            array(
                0 => 36382,
                1 => 36435,
                2 => 36401,
                3 => 36405,
                4 => 36407,
                5 => 36421,
                6 => 36406,
                7 => 36424,
                8 => 36439,
            ),
        36554 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36555 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
                4 => 36400,
                5 => 36410,
                6 => 36432,
            ),
        36556 =>
            array(
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36557 =>
            array(
                0 => 36423,
                1 => 36438,
            ),
        36558 =>
            array(
                0 => 36423,
                1 => 36438,
                2 => 36403,
                3 => 36418,
                4 => 36422,
                5 => 36437,
            ),
        36559 =>
            array(
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
                4 => 36406,
                5 => 36424,
                6 => 36439,
            ),
        36560 =>
            array(
                0 => 36402,
                1 => 36436,
                2 => 36423,
                3 => 36438,
                4 => 36403,
                5 => 36418,
                6 => 36422,
                7 => 36437,
            ),
        36561 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 36426,
                4 => 36440,
                5 => 40991,
            ),
        36562 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 42013,
            ),
        36563 =>
            array(
                0 => 36417,
                1 => 36441,
                2 => 36442,
                3 => 42013,
            ),
        36564 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
            ),
        36565 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36566 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36567 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36568 =>
            array(
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36569 =>
            array(
                0 => 36350,
                1 => 36449,
            ),
        36570 =>
            array(
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36571 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        36572 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
            ),
        36573 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        36574 =>
            array(
                0 => 36391,
                1 => 36453,
            ),
        36575 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
            ),
        36577 =>
            array(
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36578 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36579 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 36458,
                4 => 36465,
                5 => 36474,
                6 => 47796,
            ),
        36580 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36582 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 36461,
                7 => 36468,
                8 => 36476,
            ),
        36583 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36584 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36585 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
                4 => 36464,
                5 => 36467,
                6 => 36477,
            ),
        36586 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 36458,
                4 => 36465,
                5 => 36474,
                6 => 47796,
            ),
        36587 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36588 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
                4 => 36464,
                5 => 36467,
                6 => 36477,
            ),
        36589 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 36461,
                7 => 36468,
                8 => 36476,
            ),
        36590 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36591 =>
            array(
                0 => 36471,
                1 => 36569,
                2 => 36470,
                3 => 36473,
                4 => 47050,
            ),
        36592 =>
            array(
                0 => 36471,
                1 => 36569,
            ),
        36593 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36594 =>
            array(
                0 => 36471,
                1 => 36569,
                2 => 36470,
                3 => 36473,
            ),
        36595 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 36458,
                4 => 36465,
                5 => 36474,
                6 => 47796,
                7 => 47050,
            ),
        36596 =>
            array(
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36597 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 36461,
                7 => 36468,
                8 => 36476,
            ),
        36598 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
                4 => 36464,
                5 => 36467,
                6 => 36477,
            ),
        36599 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 36478,
                12 => 36479,
                13 => 36480,
                14 => 36498,
                15 => 40835,
                16 => 40837,
            ),
        36600 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 36478,
                12 => 36479,
                13 => 36480,
                14 => 36498,
                15 => 40835,
                16 => 40837,
            ),
        36601 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 36478,
                12 => 36479,
                13 => 36480,
                14 => 36498,
                15 => 40835,
                16 => 40837,
            ),
        36602 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 39196,
                10 => 46562,
            ),
        36612 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),
        36613 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
            ),
        36614 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),
        36615 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),
        36616 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
            ),
        36617 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 36496,
                10 => 36508,
                11 => 36514,
                12 => 42094,
                13 => 46560,
                14 => 46562,
            ),
        36618 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),
        36619 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 36478,
                12 => 36479,
                13 => 36480,
                14 => 36498,
                15 => 40835,
                16 => 40837,
            ),
        36620 =>
            array(
                0 => 36499,
                1 => 36571,
                2 => 36573,
                3 => 36492,
                4 => 36495,
                5 => 36537,
                6 => 36572,
                7 => 36491,
                8 => 36493,
                9 => 36494,
                10 => 36497,
                11 => 36510,
                12 => 36478,
                13 => 36479,
                14 => 36480,
                15 => 42061,
            ),
        36621 =>
            array(
                0 => 36500,
                1 => 36530,
                2 => 36502,
                3 => 36529,
                4 => 36554,
                5 => 36577,
                6 => 36518,
                7 => 36531,
                8 => 36555,
            ),
        36622 =>
            array(
                0 => 36501,
                1 => 36553,
                2 => 36522,
                3 => 36526,
                4 => 36527,
                5 => 36539,
                6 => 36528,
                7 => 36543,
                8 => 36559,
            ),
        36623 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        36624 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36625 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 36504,
                9 => 36505,
                10 => 42091,
            ),
        36626 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 36504,
                9 => 36505,
                10 => 42091,
            ),
        36627 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 39196,
                10 => 46562,
            ),
        36628 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36507,
                7 => 36481,
                8 => 36506,
                9 => 36552,
                10 => 36496,
                11 => 36508,
                12 => 36514,
            ),
        36629 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 36496,
                10 => 36508,
                11 => 36514,
                12 => 42094,
                13 => 46560,
                14 => 46562,
            ),
        36630 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
            ),
        36631 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),
        36632 =>
            array(
                0 => 36511,
                1 => 36535,
            ),
        36633 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
            ),
        36634 =>
            array(
                0 => 36513,
                1 => 36574,
            ),
        36635 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 36496,
                10 => 36508,
                11 => 36514,
                12 => 42094,
                13 => 46560,
                14 => 46562,
            ),
        36636 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36515,
                6 => 36516,
                7 => 36517,
                8 => 36533,
                9 => 36504,
                10 => 36505,
            ),
        36637 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 39202,
            ),
        36638 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 39202,
            ),
        36640 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36641 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
                3 => 36520,
                4 => 36525,
                5 => 36540,
            ),
        36642 =>
            array(
                0 => 36521,
                1 => 36541,
                2 => 36503,
                3 => 36519,
                4 => 36556,
                5 => 36520,
                6 => 36525,
                7 => 36540,
            ),
        36643 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36644 =>
            array(
                0 => 36523,
                1 => 36560,
                2 => 36544,
                3 => 36557,
                4 => 36524,
                5 => 36542,
                6 => 36545,
                7 => 36558,
            ),
        36645 =>
            array(
                0 => 36544,
                1 => 36557,
                2 => 36524,
                3 => 36542,
                4 => 36545,
                5 => 36558,
            ),
        36646 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
                3 => 36520,
                4 => 36525,
                5 => 36540,
            ),
        36647 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36648 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36649 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
                4 => 36528,
                5 => 36543,
                6 => 36559,
            ),
        36650 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        36651 =>
            array(
                0 => 36500,
                1 => 36530,
                2 => 36502,
                3 => 36529,
                4 => 36554,
                5 => 36577,
                6 => 36518,
                7 => 36531,
                8 => 36555,
            ),
        36652 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
                4 => 36518,
                5 => 36531,
                6 => 36555,
            ),
        36654 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 39202,
            ),
        36655 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
            ),
        36656 =>
            array(
                0 => 36511,
                1 => 36535,
            ),
        36657 =>
            array(
                0 => 36536,
            ),
        36658 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
            ),
        36659 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
            ),
        36660 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36661 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
                3 => 36520,
                4 => 36525,
                5 => 36540,
            ),
        36662 =>
            array(
                0 => 36521,
                1 => 36541,
                2 => 36503,
                3 => 36519,
                4 => 36556,
                5 => 36520,
                6 => 36525,
                7 => 36540,
            ),
        36663 =>
            array(
                0 => 36544,
                1 => 36557,
                2 => 36524,
                3 => 36542,
                4 => 36545,
                5 => 36558,
            ),
        36664 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
                4 => 36528,
                5 => 36543,
                6 => 36559,
            ),
        36665 =>
            array(
                0 => 36544,
                1 => 36557,
            ),
        36666 =>
            array(
                0 => 36544,
                1 => 36557,
                2 => 36524,
                3 => 36542,
                4 => 36545,
                5 => 36558,
            ),
        36668 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 36547,
                4 => 36549,
                5 => 36561,
                6 => 36548,
                7 => 36550,
            ),
        36669 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 36549,
                4 => 36561,
                5 => 36548,
                6 => 36550,
                7 => 40995,
                8 => 41000,
            ),
        36670 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 36549,
                4 => 36561,
                5 => 41001,
            ),
        36671 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 36549,
                4 => 36561,
                5 => 36548,
                6 => 36550,
                7 => 40995,
                8 => 41000,
            ),
        36672 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
            ),
        36673 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 39196,
                10 => 46562,
            ),
        36674 =>
            array(
                0 => 36501,
                1 => 36553,
                2 => 36522,
                3 => 36526,
                4 => 36527,
                5 => 36539,
                6 => 36528,
                7 => 36543,
                8 => 36559,
            ),
        36675 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        36676 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
                4 => 36518,
                5 => 36531,
                6 => 36555,
            ),
        36677 =>
            array(
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36678 =>
            array(
                0 => 36544,
                1 => 36557,
            ),
        36679 =>
            array(
                0 => 36544,
                1 => 36557,
                2 => 36524,
                3 => 36542,
                4 => 36545,
                5 => 36558,
            ),
        36680 =>
            array(
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
                4 => 36528,
                5 => 36543,
                6 => 36559,
            ),
        36681 =>
            array(
                0 => 36523,
                1 => 36560,
                2 => 36544,
                3 => 36557,
                4 => 36524,
                5 => 36542,
                6 => 36545,
                7 => 36558,
            ),
        36682 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 36549,
                4 => 36561,
                5 => 41001,
            ),
        36683 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 42014,
            ),
        36684 =>
            array(
                0 => 36536,
                1 => 36562,
                2 => 36563,
                3 => 42014,
            ),
        36685 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
            ),
        36686 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36687 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36688 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36689 =>
            array(
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36690 =>
            array(
                0 => 36471,
                1 => 36569,
            ),
        36691 =>
            array(
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36692 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36693 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
            ),
        36694 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36695 =>
            array(
                0 => 36513,
                1 => 36574,
            ),
        36696 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
            ),
        36698 =>
            array(
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        38496 =>
            array(
                0 => 23899,
                1 => 24205,
            ),
        38504 =>
            array(
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        38510 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        39195 =>
            array(
                0 => 23936,
                1 => 24251,
                2 => 24133,
                3 => 24223,
                4 => 24220,
                5 => 24218,
            ),
        39193 =>
            array(
                0 => 24137,
                1 => 24256,
                2 => 24140,
                3 => 24146,
                4 => 24610,
                5 => 24278,
                6 => 24294,
                7 => 24274,
            ),
        39202 =>
            array(
                0 => 36391,
                1 => 36453,
                2 => 36392,
                3 => 36443,
                4 => 36454,
                5 => 36395,
                6 => 36396,
                7 => 36412,
                8 => 39195,
            ),
        39196 =>
            array(
                0 => 36414,
                1 => 36390,
                2 => 36389,
                3 => 36413,
                4 => 36416,
                5 => 36430,
                6 => 36360,
                7 => 36385,
                8 => 36431,
                9 => 39193,
            ),
        39203 =>
            array(
                0 => 36513,
                1 => 36574,
                2 => 36512,
                3 => 36564,
                4 => 36575,
                5 => 36516,
                6 => 36517,
                7 => 36533,
                8 => 39202,
            ),
        39200 =>
            array(
                0 => 36511,
                1 => 36535,
                2 => 36509,
                3 => 36534,
                4 => 36538,
                5 => 36551,
                6 => 36481,
                7 => 36506,
                8 => 36552,
                9 => 39196,
                10 => 46562,
            ),
        40834 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23963,
                6 => 23955,
                7 => 23947,
                8 => 23942,
            ),
        40833 =>
            array(
                0 => 23899,
                1 => 24205,
                2 => 23916,
                3 => 24194,
                4 => 24175,
                5 => 23955,
                6 => 23947,
                7 => 23942,
                8 => 24128,
                9 => 23987,
                10 => 24131,
                11 => 30982,
            ),

        40835=>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 36357,
                12 => 36358,
                13 => 36359,
                14 => 36375,
                15 => 40833,
                16 => 45947,
                17 => 45937,
            ),
        40837=>
            array(
                0 => 36450,
                1 => 36451,
                2 => 36373,
                3 => 36374,
                4 => 36415,
                5 => 36452,
                6 => 36370,
                7 => 36371,
                8 => 36372,
                9 => 36376,
                10 => 36388,
                11 => 40834,
                12 => 45947,
            ),
        40838 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 40837,
            ),

        40836 =>
            array(
                0 => 36571,
                1 => 36573,
                2 => 36492,
                3 => 36495,
                4 => 36537,
                5 => 36572,
                6 => 36491,
                7 => 36493,
                8 => 36494,
                9 => 36497,
                10 => 36510,
                11 => 36478,
                12 => 36479,
                13 => 36480,
                14 => 36498,
                15 => 40835,
                16 => 40837,
            ),
        40991 =>
            array(
                23920, 24241, 24215, 24151, 24155,
            ),
        40975 =>
            array(
                23920, 24241, 24215, 24151, 24155, 24159, 24166,
            ),
        40995 =>
            array(
                36417, 36441, 36442, 36426, 36440, 36427, 36429, 40975, 40991,
            ),
        41000 =>
            array(
                36417,36441,36442,36426,36440,40991,
            ),
        40998 =>
            array(
                36536,36562,36563,36549,36561,36548,36550,40995,41000,
            ),
        41001 =>
            array(
                36536,36562,36563,36549,36561,41000,
            ),
        42093 =>
            array(
                24137,24256,24140,24146,24610,24278,24294,24274,24301,24308,30984
            ),
        42090 =>
            array(
                23936,24251,24133,24223,24220,24218,24229,24235
            ),
        42056 =>
            array(
                23936,24251,24133,27427,24223,24220,24218,24229,24235
            ),
        42013 =>
            array(
                23920,24241,24215
            ),
        42094 =>
            array(
                36414,36390,36389,36413,36416,36430,36360,36385,36431,36378,36387,36393,42093
            ),
        42091 =>
            array(
                36391,36453,36392,36443,36454,36395,36396,36412,36383,36384,42090
            ),
        42061 =>
            array(
                36377,36450,36451,36373,36374,36415,36452,36370,36371,36372,36376,36388,36357,36358,36359,36375,42056
            ),
        42014 =>
            array(
                36417,36441,36442,42013
            ),
        37736 =>
            array(
                42056
            ),
        37692 =>
            array(
                42056
            ),
        42095 =>
            array(
                36511,36535,36509,36534,36538,36551,36481,36506,36552,36496,36508,36514,42094,46560, 46562,
            ),

        42092 =>
            array(
                36536,36562,36563,36549,36561,41000
            ),
        42064 =>
            array(
                36499,36571,36573,36492,36495,36537,36572,36491,36493,36494,36497,36510,36478,36479,36480
            ),
        42034 =>
            array(
                36536,36562,36563
            ),
        45947 =>
            array(
                23899,24205,23916,24194,24175,23963,23955,23947,23942,
            ),
        45937 =>
            array(
                23899,24205,23916,24194,24175,23963,23955,23947,23942,24128,23987,24131,30982
            ),

        45949 =>
            array(
                36450,36451,36373,36374,36415,36452,36370,36371,36372,36376,36388,45947,40834
            ),

        45941 =>
            array(
                36450,36451,36373,36374,36415,36452,36370,36371,36372,36376,36388,36357,36358,36359,36375,45947,45937,40833
            ),
        46451 =>
            array(
                36342,36343,36347,36351,36446,36447
            ),
        46452 =>
            array(
                36462,36463,36469,36472,36567,36568,46451
            ),
        46560 =>
            array(
                36414,36390,36389,36413,36416,36430,36360,36385,36431,36378,36387,36393,42093
            ),
        46562 =>
            array(
                36414,36390,36389,36413,36416,36430,36360,36385,36431,39193
            ),
        46561 =>
            array(
                36511,36535,36509,36534,36538,36551,36481,36506,36552,36496,36508,36514,46560,46562,42094,39196
            ),
        46563 =>
            array(
                36511,36535,36509,36534,36538,36551,36481,36506,36552,46562,39196
            ),
        47796 =>
            array(
                36336,36338,36353
            ),
        47050 =>
            array(
                36350,36449,36349,36352
            ),
        47799 =>
            array(
                36457,36459,36475,47796
            ),
        47051 =>
            array(
                36471,36569,36470,36473,47050
            ),

        // 初中
        35785 =>
            array(
                0 => 35784,
            ),
        35790 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35773 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35782 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35778 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35776 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35780 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35768 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35770 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35769,
                9 => 35787,
                10 => 35772,
                11 => 43314
            ),
        35783 =>
            array(
                0 => 35760,
                1 => 35763,
            ),
        35786 =>
            array(
                0 => 35763,
            ),
        35736 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35740 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35738 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35741 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35737 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35739 => array(
            35728,35730,35732,35729,35734,35735,43368,43369,43370
        ),
        35742 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35743 =>
            array(
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 43368,
                7 => 43369,
                8 => 43370
            ),
        35922 =>
            array(
                0 => 35917,
                1 => 35918,
                2 => 35919,
                3 => 43277
            ),
        35923 =>
            array(
                0 => 35917,
                1 => 35918,
                2 => 35919,
                3 => 43277
            ),
        35924 =>
            array(
                0 => 35917,
                1 => 35918,
                2 => 35919,
            ),
        35925 =>
            array(
                0 => 35917,
                1 => 35918,
                2 => 35919,
                3 => 35920,
                4 => 35921,
                5 => 43277,
                48517
            ),
        35926 =>
            array(
                0 => 35917,
                1 => 35918,
                2 => 35919,
                3 => 35920,
                4 => 35921,
                5 => 43277,
                48517
            ),
        35774 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
            ),
        35788 =>
            array(
                0 => 35784,
                1 => 35789,
                2 => 35771,
                3 => 35781,
                4 => 35777,
                5 => 35775,
                6 => 35779,
                7 => 35767,
                8 => 35787,
                9 => 35772,
                10 => 43296
            ),
        35838 =>
            array(
                0 => 35816,
                1 => 35817,
                2 => 35818,
                3 => 35819,
                4 => 35820,
                5 => 35821,
                6 => 35822,
                7 => 35823,
            ),
        35839 =>
            array(
                0 => 35816,
                1 => 35817,
                2 => 35818,
                3 => 35819,
                4 => 35820,
                5 => 35821,
                6 => 35822,
                7 => 35823,
            ),
        35840 =>
            array(
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
                4 => 35822,
                5 => 35823,
            ),
        35841 =>
            array(
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
                4 => 35822,
                5 => 35823,
            ),
        35842 =>
            array(
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
                4 => 35822,
                5 => 35823,
            ),
        35843 =>
            array(
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
                4 => 35822,
                5 => 35823,
            ),
        35844 =>
            array(
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
                4 => 35822,
                5 => 35823,
            ),
        35845 =>
            array(
                0 => 35823,
            ),
        35846 =>
            array(
                0 => 35824,
                1 => 35825,
                2 => 35826,
                3 => 35827,
                4 => 35828,
                5 => 35829,
                6 => 35830,
                7 => 35831,
                8 => 35832,
                48225
            ),
        35847 =>
            array(
                0 => 35824,
                1 => 35825,
                2 => 35826,
                3 => 35827,
                4 => 35828,
                5 => 35829,
                6 => 35830,
                7 => 35831,
                8 => 35832,
                48225
            ),
        35848 =>
            array(
                0 => 35824,
                1 => 35825,
                2 => 35826,
                3 => 35827,
                4 => 35828,
                5 => 35829,
                6 => 35830,
                7 => 35831,
                8 => 35832,
                48225
            ),
        35849 =>
            array(
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
                4 => 35831,
                5 => 35832,
            ),
        35850 =>
            array(
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
                4 => 35831,
                5 => 35832,
            ),
        35851 =>
            array(
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
                4 => 35831,
                5 => 35832,
            ),
        35852 =>
            array(
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
                4 => 35831,
                5 => 35832,
            ),
        35853 =>
            array(
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
                4 => 35831,
                5 => 35832,
            ),
        35854 =>
            array(
                0 => 35832,
            ),
        35855 =>
            array(
                0 => 35833,
                1 => 35834,
                2 => 35835,
                3 => 43259
            ),
        35856 =>
            array(
                0 => 35833,
                1 => 35834,
                2 => 35835,
                3 => 43259
            ),
        35857 =>
            array(
                0 => 35833,
                1 => 35834,
                2 => 35835,
                3 => 43259
            ),
        35900 =>
            array(
                0 => 35883,
            ),
        35901 =>
            array(
                0 => 35883,
                1 => 35884,
                2 => 35887,
                3 => 38121
            ),
        35904 =>
            array(
                0 => 35883,
                1 => 35884,
                2 => 35887,
                3 => 38121,
            ),
        35902 =>
            array(
                0 => 35885,
                1 => 35886,
                2 => 38121,
                3 => 35887,
                4 => 35884,
                5 => 35883,
                6 => 47069
            ),
        35903 =>
            array(
                0 => 35885,
                1 => 35886,
                2 => 38121,
                3 => 35887,
                4 => 35884,
                5 => 35883,
                6 => 47069
            ),
        35899 =>
            array(
                0 => 35882,
            ),
        35897 =>
            array(
                0 => 35880,
                1 => 35877,
                2 => 35881,
                3 => 35879,
                4 => 35882,
            ),
        35894 =>
            array(
                0 => 35880,
                1 => 35877,
                2 => 35881,
                3 => 35879,
                4 => 35882,
            ),
        35898 =>
            array(
                0 => 35880,
                1 => 35877,
                2 => 35881,
                3 => 35879,
                4 => 35882,
            ),
        35896 =>
            array(
                0 => 35880,
                1 => 35877,
                2 => 35881,
                3 => 35879,
                4 => 35882,
            ),
        35895 =>
            array(
                0 => 35878,
                1 => 35882,
                2 => 35881,
                3 => 35880,
                4 => 35879,
                5 => 35877,
            ),
        35906 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074
            ),
        35909 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074
            ),
        35908 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074
            ),
        35905 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074,
            ),
        35907 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074
            ),
        39025 =>
            array(
                0 => 35892,
                1 => 35891,
                2 => 35890,
                3 => 35893,
                4 => 35889,
                5 => 35888,
                6 => 39030,
                47074
            ),
        35784 =>
            array(
                0 => 35747,
            ),
        35789 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35771 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35781 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35777 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35775 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=>43185
            ),
        35779 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35767 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185
            ),
        35769 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35751,
                9 => 35745,
                10 => 35766,
                11 => 43185
            ),
        35760 =>
            array(
                0 => 35755,
                1 => 38171,
                2 => 35748,
            ),
        35763 =>
            array(
                0 => 35748,
                1 => 38171,
            ),
        35733 =>
            array(
                0 => 35754,
                1 => 35750,
                2 => 35757,
                3 => 35749,
                4 => 35765,
                5 => 35758,
                6 => 35759,
                7 => 38167,
                8 => 43208,
                9 => 43210,
                10 => 43208
            ),
        35728 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
                7 => 43208,
                8 => 43210,
                9 => 43208
            ),
        35730 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
                7 => 43208,
                8 => 43210,
                9 => 43208
            ),
        35732 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 43208,
                7 => 43210,
                8 => 43208
            ),
        35729 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
                7 => 43208,
                8 => 43210,
                9 => 43208
            ),
        35734 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
                7 => 43208,
                8 => 43210,
                9 => 43208
            ),
        35735 =>
            array(
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
                7 => 43208,
                8 => 43210,
                9 => 43208
            ),
        35787 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10=> 43185

            ),
        35772 =>
            array(
                0 => 35747,
                1 => 35746,
                2 => 35744,
                3 => 35752,
                4 => 35761,
                5 => 35756,
                6 => 35762,
                7 => 35764,
                8 => 35745,
                9 => 35766,
                10 => 43185,
            ),
        35816 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35793,
                3 => 35794,
                4 => 35795,
                5 => 35796,
                6 => 35797,
                7 => 35798,
                8 => 38170,
                9 => 42219,
            ),
        35817 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35793,
                3 => 35794,
                4 => 35795,
                5 => 35796,
                6 => 35797,
                7 => 35798,
                8 => 38170,
                9 => 42219,
            ),
        35818 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35794,
                3 => 35795,
                4 => 35797,
                5 => 35798,
                6 => 38170,
            ),
        35819 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35794,
                3 => 35795,
                4 => 35797,
                5 => 35798,
                6 => 38170,
            ),
        35820 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35794,
                3 => 35795,
                4 => 35797,
                5 => 35798,
                6 => 38170,
            ),
        35821 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35794,
                3 => 35795,
                4 => 35797,
                5 => 35798,
                6 => 38170,
            ),
        35822 =>
            array(
                0 => 35791,
                1 => 35792,
                2 => 35794,
                3 => 35795,
                4 => 35797,
                5 => 35798,
                6 => 38170,
            ),
        35823 =>
            array(
                0 => 35792,
                1 => 35795,
            ),
        35824 =>
            array(
                0 => 35799,
                1 => 35800,
                2 => 35801,
                3 => 35802,
                4 => 35803,
                5 => 35804,
                6 => 35805,
                7 => 35806,
            ),
        35825 =>
            array(
                0 => 35799,
                1 => 35800,
                2 => 35801,
                3 => 35802,
                4 => 35803,
                5 => 35804,
                6 => 35805,
                7 => 35806,
            ),
        35826 =>
            array(
                0 => 35799,
                1 => 35800,
                2 => 35801,
                3 => 35802,
                4 => 35803,
                5 => 35804,
                6 => 35805,
                7 => 35806,
            ),
        35827 =>
            array(
                0 => 35800,
                1 => 35802,
                2 => 35803,
                3 => 35805,
                4 => 35806,
            ),
        35828 =>
            array(
                0 => 35800,
                1 => 35802,
                2 => 35803,
                3 => 35805,
                4 => 35806,
            ),
        35829 =>
            array(
                0 => 35800,
                1 => 35802,
                2 => 35803,
                3 => 35805,
                4 => 35806,
            ),
        35830 =>
            array(
                0 => 35800,
                1 => 35802,
                2 => 35803,
                3 => 35805,
                4 => 35806,
            ),
        35831 =>
            array(
                0 => 35800,
                1 => 35802,
                2 => 35803,
                3 => 35805,
                4 => 35806,
            ),
        35832 =>
            array(
                0 => 35803,
            ),
        35833 =>
            array(
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
                7 => 43182
            ),
        35834 =>
            array(
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
                7 => 43182
            ),
        35835 =>
            array(
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
                7 => 43182
            ),
        35836 =>
            array(
                0 => 35807,
                1 => 35808,
                2 => 35809,
                3 => 35810,
                4 => 35811,
                5 => 35812,
                6 => 35813,
                7 => 35814,
                8 => 35815,
                9 => 39482,
                10 => 43181,
                11 => 43182
            ),
        35837 =>
            array(
                0 => 35807,
                1 => 35808,
                2 => 35809,
                3 => 35810,
                4 => 35811,
                5 => 35812,
                6 => 35813,
                7 => 35814,
                8 => 35815,
                9 => 39482,
                10 => 43181,
                11 => 43182
            ),
        35877 =>
            array(
                0 => 35858,
                1 => 35860,
                2 => 35864,
                3 => 35861,
                4 => 35862,
                5 => 35863,
            ),
        35878 =>
            array(
                0 => 35859,
                1 => 35863,
                2 => 35862,
                3 => 35860,
                4 => 35864,
                5 => 35861,
                6 => 35858,
            ),
        35879 =>
            array(
                0 => 35858,
                1 => 35860,
                2 => 35864,
                3 => 35861,
                4 => 35862,
                5 => 35863,
            ),
        35880 =>
            array(
                0 => 35858,
                1 => 35860,
                2 => 35864,
                3 => 35861,
                4 => 35862,
                5 => 35863,
            ),
        35881 =>
            array(
                0 => 35858,
                1 => 35860,
                2 => 35864,
                3 => 35861,
                4 => 35862,
                5 => 35863,
            ),
        35882 =>
            array(
                0 => 35862,
            ),
        35883 =>
            array(
                0 => 35867,
            ),
        35884 =>
            array(
                0 => 35866,
                1 => 35868,
                2 => 35867,
                3 => 38118,
            ),
        35885 =>
            array(
                0 => 35865,
                1 => 35869,
                2 => 38118,
                3 => 35867,
                4 => 35868,
                5 => 35866,
                6 => 47068
            ),
        35886 =>
            array(
                0 => 35865,
                1 => 35869,
                2 => 38118,
                3 => 35867,
                4 => 35868,
                5 => 35866,
                6 => 47068
            ),
        35887 =>
            array(
                0 => 35866,
                1 => 35868,
                2 => 35867,
                3 => 38118,
            ),
        35888 =>
            array(
                0 => 35872,
                1 => 35873,
                2 => 35874,
                3 => 35870,
                4 => 35875,
                5 => 35876,
            ),
        35889 =>
            array(
                0 => 35872,
                1 => 35873,
                2 => 35874,
                3 => 35870,
                4 => 35875,
                5 => 35876,
            ),
        35890 =>
            array(
                0 => 35872,
                1 => 35873,
                2 => 35874,
                3 => 35870,
                4 => 35875,
                5 => 35876,
            ),
        35891 =>
            array(
                0 => 35871,
                1 => 35876,
                2 => 39030,
                3 => 35872,
                4 => 47073
            ),
        35892 =>
            array(
                0 => 35872,
            ),
        35893 =>
            array(
                0 => 35872,
                1 => 35873,
                2 => 35874,
                3 => 35870,
                4 => 35875,
                5 => 35876,
            ),
        35917 =>
            array(
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
                4 => 43183
            ),
        35918 =>
            array(
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
                4 => 43183
            ),
        35919 =>
            array(
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
                4 => 43183
            ),
        35920 =>
            array(
                0 => 35910,
                1 => 35915,
                2 => 35916,
                3 => 35911,
                4 => 35912,
                5 => 35913,
                6 => 35914,
                7 => 43183
            ),
        35921 =>
            array(
                0 => 35910,
                1 => 35915,
                2 => 35916,
                3 => 35911,
                4 => 35912,
                5 => 35913,
                6 => 35914,
                7 => 43183
            ),
        35744 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35746 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35751 =>
            array(
                0 => 23676,
                1 => 23686,
                2 => 23695,
                3 => 23629,
                4 => 23667,
                5 => 23642,
                6 => 23660,
                7 => 27404,
            ),
        35752 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35756 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35761 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35762 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35764 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35749 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35759 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35750 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35765 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35754 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
                5 => 23845,
                6 => 23853,
            ),
        35757 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35758 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        35748 =>
            array(
                0 => 24319,
            ),
        35755 =>
            array(
                0 => 24319,
                1 => 24316,
            ),
        35745 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35766 =>
            array(
                0 => 23629,
                1 => 23667,
                2 => 23642,
                3 => 23660,
                4 => 27404,
            ),
        35791 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
            ),
        35792 =>
            array(
                0 => 23529,
            ),
        35793 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
                4 => 23665,
                5 => 23670,
            ),
        35794 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
            ),
        35795 =>
            array(
                0 => 23529,
            ),
        35796 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
                4 => 23665,
                5 => 23670,
            ),
        35797 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
            ),
        35798 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
            ),
        35799 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
                5 => 23748,
                6 => 23756,
            ),
        35800 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
            ),
        35801 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
                5 => 23748,
                6 => 23756,
            ),
        35802 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
            ),
        35803 =>
            array(
                0 => 23691,
            ),
        35804 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
                5 => 23748,
                6 => 23756,
            ),
        35805 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
            ),
        35806 =>
            array(
                0 => 23691,
                1 => 23696,
                2 => 23704,
                3 => 23710,
                4 => 23740,
            ),
        35807 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
                5 => 24005,
                6 => 24013,
            ),
        35808 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35809 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
                5 => 24005,
                6 => 24013,
            ),
        35810 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35811 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35812 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35813 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35814 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35815 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
            ),
        35862 =>
            array(
                0 => 24080,
            ),
        35858 =>
            array(
                0 => 24103,
                1 => 24115,
                2 => 24125,
                3 => 29316,
                4 => 24080,
            ),
        35859 =>
            array(
                0 => 24103,
                1 => 24115,
                2 => 24125,
                3 => 29316,
                4 => 24080,
                5 => 24238,
                6 => 24247,
            ),
        35860 =>
            array(
                0 => 24103,
                1 => 24115,
                2 => 24125,
                3 => 29316,
                4 => 24080,
            ),
        35864 =>
            array(
                0 => 24103,
                1 => 24115,
                2 => 24125,
                3 => 29316,
                4 => 24080,
            ),
        35861 =>
            array(
                0 => 24103,
                1 => 24115,
                2 => 24125,
                3 => 29316,
                4 => 24080,
            ),
        35863 =>
            array(
                0 => 24238,
                1 => 24247,
                2 => 24103,
                3 => 24115,
                4 => 24125,
                5 => 29316,
                6 => 24080,
            ),
        35867 =>
            array(
                0 => 24047,
            ),
        35866 =>
            array(
                0 => 24054,
                1 => 24063,
                2 => 24072,
                3 => 29313,
                4 => 24047,
            ),
        35868 =>
            array(
                0 => 24054,
                1 => 24063,
                2 => 24072,
                3 => 29313,
                4 => 24047,
            ),
        35865 =>
            array(
                0 => 24085,
                1 => 24094,
                2 => 24047,
                3 => 24054,
                4 => 24063,
                5 => 24072,
                6 => 29313,
            ),
        35869 =>
            array(
                0 => 24085,
                1 => 24094,
                2 => 24047,
                3 => 24054,
                4 => 24063,
                5 => 24072,
                6 => 29313,
            ),
        35872 =>
            array(
                0 => 24068,
            ),
        35871 =>
            array(
                0 => 24068,
                1 => 24100,
                2 => 24123,
                3 => 24110,
                4 => 24211,
                5 => 24233,
            ),
        35873 =>
            array(
                0 => 24100,
                1 => 24123,
                2 => 24110,
                3 => 24211,
                4 => 24068,
            ),
        35874 =>
            array(
                0 => 24100,
                1 => 24123,
                2 => 24110,
                3 => 24211,
                4 => 24068,
            ),
        35870 =>
            array(
                0 => 24100,
                1 => 24123,
                2 => 24110,
                3 => 24211,
                4 => 24233,
            ),
        35875 =>
            array(
                0 => 24100,
                1 => 24123,
                2 => 24110,
                3 => 24211,
                4 => 24068,
            ),
        35876 =>
            array(
                0 => 24100,
                1 => 24123,
                2 => 24110,
                3 => 24211,
                4 => 24068,
            ),
        35910 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
                4 => 23566,
                5 => 23579,
            ),
        35915 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
                4 => 23566,
                5 => 23579,
            ),
        35916 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
                4 => 23566,
                5 => 23579,
            ),
        35911 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
            ),
        35912 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
            ),
        35913 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
            ),
        35914 =>
            array(
                0 => 25570,
                1 => 30902,
                2 => 23543,
                3 => 23553,
            ),
        39019 =>
            array(
                0 => 24233,
                1 => 35871,
                2 => 35876,
                3 => 39030,
                4 => 24068,
                5 => 24100,
                6 => 24110,
                7 => 24123,
                8 => 24211,
            ),
        39482 =>
            array(
                0 => 23766,
                1 => 23780,
                2 => 23971,
                3 => 23999,
                4 => 28914,
                5 => 24005,
                6 => 24013,
            ),
        39485 =>
            array(
                0 => 35807,
                1 => 35808,
                2 => 35809,
                3 => 35810,
                4 => 35811,
                5 => 35812,
                6 => 35813,
                7 => 35814,
                8 => 35815,
                9 => 39482,
            ),
        39708 =>
            array(
                0 => 35833,
                1 => 35834,
                2 => 35835,
                3 => 35833,
                4 => 35834,
                5 => 35835,
                6 => 43259
            ),
        37351 =>
            array(
                0 => 38170,
            ),
        37352 =>
            array(
                0 => 38170,
            ),
        38118 =>
            array(
                0 => 24047,
            ),
        38167 =>
            array(
                0 => 23722,
                1 => 23802,
                2 => 23733,
                3 => 23825,
                4 => 23833,
            ),
        38170 =>
            array(
                0 => 23529,
                1 => 23541,
                2 => 23552,
                3 => 23588,
            ),

        // 高中
        35110 =>
            array(
                0 => 24083,
            ),
        35112 =>
            array(
                0 => 24083,
            ),
        35113 =>
            array(
                0 => 24067,
                1 => 24076,
            ),
        35114 =>
            array(
                0 => 25920,
            ),
        35115 =>
            array(
                0 => 24067,
                1 => 24076,
            ),
        35117 =>
            array(
                0 => 25920,
            ),
        35118 =>
            array(
                0 => 24067,
                1 => 24076,
            ),
        35119 =>
            array(
                0 => 24067,
                1 => 24076,
            ),
        35120 =>
            array(
                0 => 24124,
                1 => 24129,
            ),
        35122 =>
            array(
                0 => 24124,
                1 => 24129,
            ),
        35124 =>
            array(
                0 => 24124,
                1 => 24129,
            ),
        35125 =>
            array(
                0 => 24124,
                1 => 24129,
            ),
        35126 =>
            array(
                0 => 26223,
                1 => 26226,
            ),
        35127 =>
            array(
                0 => 24098,
                1 => 24791,
            ),
        35128 =>
            array(
                0 => 24098,
                1 => 24791,
            ),
        35129 =>
            array(
                0 => 26223,
                1 => 26226,
            ),
        35130 =>
            array(
                0 => 26223,
                1 => 26226,
            ),
        35131 =>
            array(
                0 => 24098,
                1 => 24791,
            ),
        35236 =>
            array(
                0 => 24261,
                1 => 24272,
                2 => 24304,
            ),
        35237 =>
            array(
                0 => 24272,
                1 => 24290,
                2 => 24304,
            ),
        35238 =>
            array(
                0 => 24261,
                1 => 24272,
                2 => 24304,
            ),
        35239 =>
            array(
                0 => 24304,
            ),
        35240 =>
            array(
                0 => 24323,
                1 => 24345,
                2 => 24354,
                3 => 24361,
            ),
        35261 =>
            array(
                0 => 24323,
                1 => 24354,
            ),
        35243 =>
            array(
                0 => 24379,
                1 => 28203,
            ),
        35244 =>
            array(
                0 => 24379,
                1 => 28203,
            ),
        35245 =>
            array(
                0 => 24370,
            ),
        35284 =>
            array(
                0 => 26266,
                1 => 26278,
            ),
        35299 =>
            array(
                0 => 24298,
            ),
        35281 =>
            array(
                0 => 24383,
                1 => 26278,
            ),
        35287 =>
            array(
                0 => 26266,
                1 => 26278,
            ),
        35271 =>
            array(
                0 => 24593,
                1 => 26255,
            ),
        35277 =>
            array(
                0 => 24398,
                1 => 28001,
            ),
        35293 =>
            array(
                0 => 24282,
                1 => 24317,
            ),
        35302 =>
            array(
                0 => 24281,
                1 => 26931,
            ),
        35290 =>
            array(
                0 => 24390,
            ),
        35272 =>
            array(
                0 => 24593,
                1 => 26255,
            ),
        35278 =>
            array(
                0 => 24398,
                1 => 28001,
            ),
        35296 =>
            array(
                0 => 24289,
            ),
        35305 =>
            array(
                0 => 24281,
                1 => 26931,
            ),
        35306 =>
            array(
                0 => 24209,
            ),
        35326 =>
            array(
                0 => 24232,
                1 => 24441,
            ),
        35308 =>
            array(
                0 => 24147,
                1 => 24162,
                2 => 25889,
                3 => 26246,
            ),
        35310 =>
            array(
                0 => 24174,
                1 => 24273,
            ),
        35312 =>
            array(
                0 => 24232,
            ),
        35313 =>
            array(
                0 => 24246,
                1 => 24441,
            ),
        35316 =>
            array(
                0 => 28094,
            ),
        35320 =>
            array(
                0 => 24170,
                1 => 24273,
            ),
        35322 =>
            array(
                0 => 24174,
                1 => 24273,
            ),
        35324 =>
            array(
                0 => 24216,
                1 => 24263,
            ),
        35369 =>
            array(
                0 => 24216,
                1 => 24263,
            ),
        35373 =>
            array(
                0 => 24216,
                1 => 24441,
            ),
        35319 =>
            array(
                0 => 24167,
                1 => 25899,
                2 => 24162,
            ),
        35330 =>
            array(
                0 => 24157,
                1 => 25467,
                2 => 25902,
                3 => 26246,
                4 => 24162,
            ),
        35331 =>
            array(
                0 => 24147,
                1 => 24152,
                2 => 25467,
                3 => 26246,
            ),
        35314 =>
            array(
                0 => 24147,
                1 => 24152,
                2 => 25889,
            ),
        35317 =>
            array(
                0 => 24273,
                1 => 26264,
                2 => 25467,
            ),
        35318 =>
            array(
                0 => 24147,
                1 => 25467,
                2 => 26246,
            ),
        35146 =>
            array(
                0 => 35110,
                1 => 35112,
            ),
        35147 =>
            array(
                0 => 35113,
                1 => 35115,
                2 => 35118,
                3 => 35119,
            ),
        35148 =>
            array(
                0 => 35114,
                1 => 35117,
            ),
        35149 =>
            array(
                0 => 35113,
                1 => 35115,
                2 => 35118,
                3 => 35119,
            ),
        35150 =>
            array(
                0 => 35111,
                1 => 35116,
            ),
        35152 =>
            array(
                0 => 35120,
                1 => 35122,
                2 => 35124,
                3 => 35125,
            ),
        35153 =>
            array(
                0 => 35121,
                1 => 35123,
            ),
        35154 =>
            array(
                0 => 35120,
                1 => 35122,
                2 => 35124,
                3 => 35125,
            ),
        35156 =>
            array(
                0 => 35127,
                1 => 35128,
                2 => 35131,
            ),
        35157 =>
            array(
                0 => 35127,
                1 => 35128,
                2 => 35131,
            ),
        35158 =>
            array(
                0 => 35126,
                1 => 35129,
                2 => 35130,
            ),
        35159 =>
            array(
                0 => 35126,
                1 => 35129,
                2 => 35130,
            ),
        35192 =>
            array(
                0 => 35237,
            ),
        35194 =>
            array(
                0 => 35236,
                1 => 35238,
                2 => 43921,
            ),
        35196 =>
            array(
                0 => 35239,
                1 => 43921
            ),
        35198 =>
            array(
                0 => 35236,
                1 => 35238,
                2 => 43921
            ),
        35202 =>
            array(
                0 => 35240,
            ),
        35262 =>
            array(
                0 => 35261,
                1 => 43473
            ),
        35206 =>
            array(
                0 => 35243,
                1 => 35244,
            ),
        35208 =>
            array(
                0 => 35243,
                1 => 35244,
            ),
        35210 =>
            array(
                0 => 35245,
                1 => 43473
            ),
        35291 =>
            array(
                0 => 35293,
                1 => 35296,
            ),
        35294 =>
            array(
                0 => 35296,
            ),
        35297 =>
            array(
                0 => 35299,
            ),
        35300 =>
            array(
                0 => 35302,
                1 => 35305,
            ),
        35303 =>
            array(
                0 => 35302,
                1 => 35305,
            ),
        35269 =>
            array(
                0 => 35271,
                1 => 35272,
            ),
        35270 =>
            array(
                0 => 35271,
                1 => 35272,
            ),
        35275 =>
            array(
                0 => 35277,
                1 => 35278,
            ),
        35276 =>
            array(
                0 => 35277,
                1 => 35278,
            ),
        35279 =>
            array(
                0 => 35281,
                1 => 40596,
            ),
        35282 =>
            array(
                0 => 35284,
                1 => 35287,
                2 => 44818
            ),
        35285 =>
            array(
                0 => 35284,
                1 => 35287,
                2 => 44818
            ),
        35288 =>
            array(
                0 => 35290,
            ),
        35335 =>
            array(
                0 => 35308,
            ),
        35336 =>
            array(
                0 => 35331,
            ),
        35341 =>
            array(
                0 => 35330,
                1 => 35315,
            ),
        35342 =>
            array(
                0 => 35328,
                1 => 35319,
            ),
        35343 =>
            array(
                0 => 35327,
                1 => 35316,
            ),
        35356 =>
            array(
                0 => 35322,
                1 => 35310,
            ),
        35357 =>
            array(
                0 => 35320,
                1 => 35321,
                2 => 35309,
            ),
        35358 =>
            array(
                0 => 35306,
            ),
        35359 =>
            array(
                0 => 35311,
            ),
        35360 =>
            array(
                0 => 35310,
                1 => 35322,
            ),
        35368 =>
            array(
                0 => 35324,
                1 => 35369,
            ),
        35323 =>
            array(
                0 => 35324,
                1 => 35369,
            ),
        35372 =>
            array(
                0 => 35312,
                1 => 35326,
            ),
        35375 =>
            array(
                0 => 35312,
                1 => 35326,
            ),
        35379 =>
            array(
                0 => 35313,
                1 => 35370,
            ),
        35381 =>
            array(
                0 => 35373,
            ),
        35332 =>
            array(
                0 => 35314,
            ),
        35333 =>
            array(
                0 => 35317,
            ),
        35334 =>
            array(
                0 => 35318,
            ),
        35160 =>
            array(
                0 => 35146,
            ),
        35161 =>
            array(
                0 => 35147,
            ),
        35162 =>
            array(
                0 => 35148,
            ),
        35163 =>
            array(
                0 => 35149,
            ),
        35164 =>
            array(
                0 => 35150,
                1 => 35131,
            ),
        35166 =>
            array(
                0 => 35152,
            ),
        35167 =>
            array(
                0 => 35153,
                1 => 35155,
            ),
        35168 =>
            array(
                0 => 35154,
            ),
        35170 =>
            array(
                0 => 35156,
            ),
        35171 =>
            array(
                0 => 35157,
            ),
        35172 =>
            array(
                0 => 35158,
            ),
        35173 =>
            array(
                0 => 35159,
            ),
        35193 =>
            array(
                0 => 35192,
            ),
        35195 =>
            array(
                0 => 35194,
            ),
        35197 =>
            array(
                0 => 35196,
            ),
        35199 =>
            array(
                0 => 35198,
            ),
        35203 =>
            array(
                0 => 35202,
            ),
        35263 =>
            array(
                0 => 35262,
            ),
        35207 =>
            array(
                0 => 35206,
            ),
        35209 =>
            array(
                0 => 35208,
            ),
        35211 =>
            array(
                0 => 35210,
            ),
        35292 =>
            array(
                0 => 35291,
            ),
        35295 =>
            array(
                0 => 35294,
            ),
        35298 =>
            array(
                0 => 35297,
            ),
        35301 =>
            array(
                0 => 35300,
            ),
        35304 =>
            array(
                0 => 35303,
            ),
        35267 =>
            array(
                0 => 35269,
            ),
        35268 =>
            array(
                0 => 35270,
            ),
        35273 =>
            array(
                0 => 35275,
            ),
        35274 =>
            array(
                0 => 35276,
            ),
        35280 =>
            array(
                0 => 35279,
            ),
        35283 =>
            array(
                0 => 35282,
            ),
        35286 =>
            array(
                0 => 35285,
            ),
        35289 =>
            array(
                0 => 35288,
            ),
        35344 =>
            array(
                0 => 35335,
            ),
        35345 =>
            array(
                0 => 35336,
            ),
        35350 =>
            array(
                0 => 35341,
                1 => 35342,
            ),
        35355 =>
            array(
                0 => 35342,
                1 => 35343,
            ),
        35362 =>
            array(
                0 => 35356,
                1 => 35360,
            ),
        35363 =>
            array(
                0 => 35357,
            ),
        35364 =>
            array(
                0 => 35358,
            ),
        35365 =>
            array(
                0 => 35359,
            ),
        35366 =>
            array(
                0 => 35360,
            ),
        35325 =>
            array(
                0 => 35368,
            ),
        35307 =>
            array(
                0 => 35323,
            ),
        35371 =>
            array(
                0 => 35372,
            ),
        35374 =>
            array(
                0 => 35375,
            ),
        35378 =>
            array(
                0 => 35379,
            ),
        35380 =>
            array(
                0 => 35381,
            ),
        35346 =>
            array(
                0 => 35335,
                1 => 35336,
            ),
        35347 =>
            array(
                0 => 35333,
            ),
        35348 =>
            array(
                0 => 35332,
                1 => 35334,
            ),
        37738 =>
            array(
                0 => 37735,
            ),
        37741 =>
            array(
                0 => 37738,
            ),
        38121 =>
            array(
                0 => 35866,
                1 => 35868,
                2 => 38118,
                3 => 35867,
                4 => 35865,
                5 => 35869,
            ),
        38188 =>
            array(
                0 => 24054,
                1 => 24063,
                2 => 24072,
                3 => 29313,
                4 => 24085,
                5 => 24094,
            ),
        38125 =>
            array(
                0 => 35883,
                1 => 35884,
                2 => 35887,
                3 => 38121,
            ),
        39610 =>
            array(
                0 => 39424,
                1 => 46618
            ),
        39626 =>
            array(
                0 => 39425,
            ),
        39613 =>
            array(
                0 => 39610,
            ),
        39628 =>
            array(
                0 => 39626,
            ),
        39709 =>
            array(
                47074,35892,35890,35893,35889,35888,39030,35891

            ),
        40596 =>
            array(
                0 => 24383,
                1 => 26278,
            ),
        40597 =>
            array(
                0 => 40596,
                1 => 35281,
            ),
        42219 => array(
            23529,23541,23552,23588,23665,23670
        ),
        43449 => array(
            35882,35881,35880,35879,35877,35878
        ),
        43437 => array(
            38121,35887,35884,35883,35885,35886,47069
        ),
        43472 => array(
            38121,35887,35884,35883
        ),
        43462 => array(
            38118,35867,35868,35866
        ),
        43386 => array(
            38118,35867,35868,35866,35869,35865,47068
        ),
        43399 => array(
            35862,35860,35864,35861,35858,35863,35859
        ),
        43223 => array(
            24047,24054,24063,24072,29313
        ),
        43183 => array(
            25570,30902,23543,23553
        ),
        43206 => array(
            23676,23686,23695,23629,23667,23642,23660,27404
        ),
        43205 => array(
            23676,23686,23695,23629,23667,23642,23660,27404
        ),
        43185 => array(
            23629,23667,23642,23660,27404
        ),
        43213 => array(
            23722,23802,23733,23825,23833
        ),
        43210 => array(
            23722,23802,23733,23825,23833
        ),
        43208 => array(
            23722,23802,23733,23825,23833
        ),
        43182 => array(
            23766,23780,23971,23999,28914
        ),
        43181 => array(
            23766,23780,23971,23999,28914,24005,24013
        ),
        43277 => array(
            35911,35912,35913,35914,43183
        ),
        43296 => array(
            35747,35746,35744,35752,35761,35756,35762,35764,35745,35766,43185
        ),
        43314 => array(
            35747,35746,35744,35752,35761,35756,35762,35764,35751,35745,35766,43185
        ),
        43286 => array(
            35747,35746,35744,35752,35761,35756,35762,35764,35751,35745,35766,43185
        ),
        43360 => array(
            35784,35789,35771,35781,35777,35775,35779,35767,35787,35772,43296
        ),
        43370 => array(
            35750,35757,35749,35765,35758,35759,38167,43208,43210,43208
        ),
        43369 => array(
            35750,35757,35749,35765,35758,35759,38167,43208,43210,43208
        ),
        43368 => array(
            35750,35757,35749,35765,35758,35759,38167,43208,43210,43208
        ),
        43260 => array(
            35807,35808,35809,35810,35811,35812,35813,35814,35815,43181,43182
        ),
        43259 => array(
            35808,35810,35811,35812,35813,35814,35815,43182
        ),
        43278 => array(
            35917,35918,35919,43277
        ),
        43336 => array(
            35784,35789,35771,35781,35777,35775,35779,35767,35769,35787,35772,43314
        ),
        43366 => array(
            35784,35789,35771,35781,35777,35775,35779,35767,35769,35787,35772,43314
        ),
        43384 => array(
            35728,35730,35732,35729,35734,35735,43368,43369,43370
        ),
        43372 => array(
            35728,35730,35732,35729,35734,35735,43368,43369,43370
        ),
        43371 => array(
            35728,35730,35732,35729,35734,35735,43368,43369,43370
        ),
        43921 => array(24261, 24272),
        43473 => array(24345, 24361),
        44818 => array(26278, 26266),
        47068 => array(
            24047,24054,24063,24072,29313
        ),
        47073 => array(
            24068,24100,24110,24123,24211
        ),
        47079 => array(
            38118,35867,35868,35866,35869,35865,47068
        ),
        47069 => array(
            38118,35867,35868,35866
        ),
        47074 => array(
            35873,35872,35875,35874,35876,35870
        ),
        39030 => array(
            35873,35872,35875,35874,35876,35870,39019,35871,47073
        ),
        47080 => array(
            38121,35887,35884,35883,35885,35886,47069
        ),
        47071 => array(
            38121,35887,35884,35883
        ),
        47075 => array(
            47074,35892,35890,35893,35889,35888,39030,35891
        ),
        48225 => array(
            35799,35800,35801,35802,35803,35804,35805,35806
        ),
        48226 => array(
            35824,35825,35826,35827,35828,35829,35830,35831,35832,48225
        ),
        48517 => array(
            35910,35915,35916,35911,35912,35913,35914,43183
        ),
        48519 => array(
            35917,35918,35919,35920,35921,43277,48517
        )

    );
}