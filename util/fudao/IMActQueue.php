<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: IMActQueue.php
 * @author: huanghe <<EMAIL>>
 * @date: 2017/12/11 下午1:52
 * @brief: IM动作指令队列
 */
class Hkzb_Util_Fudao_IMActQueue
{
    const QUEUE_KEY_PREFIX = 'IM_U_A_';

    //指令编号：用户基本信息变更
    const ACT_NO_USER_BASIC = 1000;
    //指令编号：用户特权变更
    const ACT_NO_USER_PRIVILEGE = 1002;

    /**
     * 向用户指令队列中增加指令
     *
     * @param integer $uid 用户id
     * @param integer $actNo 动作指令
     * @return boolean
     */
    public static function add($uid, $actNo)
    {
        if ($actNo <= 0) {
            return false;
        }
        $redisConf = Bd_Conf::getConf("/hk/redis/zhiboke");
        $objRedis = new Hk_Service_Redis($redisConf['service']);
        $listKey = self::QUEUE_KEY_PREFIX . intval($uid);
        $ret = $objRedis->lpush($listKey, intval($actNo));
        $objRedis->expire($listKey, 86400); //1天有效期
        return $ret;
    }

    /**
     * 获取指定用户的动作指令列表
     *
     * @param integer $uid 指定用户
     * @param integer $max 最大的动作数量
     * @return array
     */
    public static function getActNoList($uid, $max = 2)
    {
        if ($uid <= 0) {
            return array();
        }
        $redisConf = Bd_Conf::getConf("/hk/redis/zhiboke");
        $objRedis = new Hk_Service_Redis($redisConf['service']);
        $list = array();
        for ($i = 0; $i < $max; $i++) {
            $val = $objRedis->rpop(self::QUEUE_KEY_PREFIX . intval($uid));
            if (empty($val)) {
                break;
            }
            $list[] = intval($val);
        }
        return $list;
    }

}