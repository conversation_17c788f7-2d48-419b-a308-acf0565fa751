<?php
class Hkzb_Util_Fudao_Correction{

    public static function getHomeworkType($gradeId,$subjectId,$year,$learnSeason,$courseStartTime,$courseId,$newCourseType){
        $season = substr(trim($learnSeason),0,1);//学季
        $stage = self::getStageByGrade($gradeId);//学部
        //2023年巩固练习订正补丁-从此以后高中不再使用相似题模式
        if($stage == 30){
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        //2022年巩固练习订正补丁-2022年寒季/秋季班课高中全科改为普通模式
        if($year == 2022 && $newCourseType == 2 && $stage == 30 && ($season == 4 || $season == 3)){
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        //标准配置
        if ($year >= 2021 && $season >= 1){
            if ($newCourseType == 12){//短训班
                $homeworkType = Hkzb_Const_Correction::$ShortHomeworkTypeGradeSubjectMap[$stage][$gradeId][$subjectId];
            }else{//非短训班
                $homeworkType = Hkzb_Const_Correction::$HomeworkTypeGradeSubjectMap[$stage][$gradeId][$subjectId];
            }
            return $homeworkType ?? 1;
        }
        //历史逻辑2021年后的不考虑
        if(in_array($courseId,Hkzb_Const_Correction::$BlackList)){//2020曙二期物理，英语
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        if(in_array($courseId,Hkzb_Const_Correction::$JyzCourseIdArr)){//加油站课程
            return Hkzb_Const_Correction::HOMEWORK_TYPE_NEW_CORRECTION;
        }
        if($year < 2020 || ($year == 2020  && $season == 4)){//2020寒以前
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        if($courseStartTime <= Hkzb_Const_Correction::CORRECTION_START_TIME){//2020订正第一次上线之前的课
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        if(!in_array($gradeId,[2,3,4,5,6,7,11,12,13,14,15,16,61,62,63,64])){//非法的gradeId
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }

        if($gradeId == 6 && $year == 2020 && in_array($subjectId ,[3,4]) && ($season == 1 || in_array($learnSeason,[21,22] ))){
            //高二英语物理//包括短训班和班课
            //2020春及暑一二订正
            if($newCourseType == 12 && in_array($learnSeason,[21,22])){
                return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
            }
            return Hkzb_Const_Correction::HOMEWORK_TYPE_NEW_CORRECTION;
        }
        if($newCourseType == 12 && $gradeId == 7){
            if($year = 2020 && $subjectId == 1 && $season == 1  ){
                return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
            }else{//短训班，高三语文2020暑期以后订正
                if($subjectId == 1){
                    return Hkzb_Const_Correction::HOMEWORK_TYPE_NEW_CORRECTION;
                }else{
                    return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
                }
            }
        }
        if($newCourseType == 12 && $gradeId == 5){//短训班高一
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        if($year == 2020 && $stage == 30 && ($season == 1 || in_array($learnSeason,[21,22]))){
            //除上面三个if之外，2020高中春和暑一二均为老巩固联系
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }

        // 订正二期扩科(初中语文 和 初二物理)，春季&寒季课程不订正
        if ($year == 2020 && in_array($season, [1,4])) {
            if (in_array($gradeId, [2,3,4]) && $subjectId == 1) {
                return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
            }
            if ($gradeId == 3 && $subjectId == 4) {
                return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
            }
        }
        if($year == 2020 && in_array($season,[1,2]) && $gradeId = 6){
            //20200904相似题扩科，2020年高二春暑走老巩固练习
            return Hkzb_Const_Correction::HOMEWORK_TYPE_OLD;
        }
        $homeworkType = Hkzb_Const_Correction::$HomeworkTypeGradeSubjectMap[$stage][$gradeId][$subjectId];
        return $homeworkType;

    }
    /**
     * @param int $gradeId
     * @param int $subjectId
     * @param int $year
     * @param int $learnSeason
     * @param int $courseStartTime
     * @return bool
     * @brief 检查是否需要订正服务
     */
    public static function needCorrection($gradeId,$subjectId,$year,$learnSeason,$courseStartTime,$courseId,$newCourseType = 0){
        $season = substr(trim($learnSeason),0,1);//学季
        $stage = self::getStageByGrade($gradeId);//学部
        //2023年巩固练习订正补丁-从此以后高中不再使用相似题模式
        if($stage == 30){
            return false;
        }
        //2022年巩固练习订正补丁-2022年寒季/秋季班课高中全科改为普通模式
        if($year == 2022 && $newCourseType == 2 && $stage == 30 && ($season == 4 || $season == 3)){
            return false;
        }
        if ($year >= 2021 && $season >= 1){
            if ($newCourseType == 12){//短训班
                $type = Hkzb_Const_Correction::$ShortHomeworkTypeGradeSubjectMap[$stage][$gradeId][$subjectId];
            }else{//非短训班
                $type = Hkzb_Const_Correction::$HomeworkTypeGradeSubjectMap[$stage][$gradeId][$subjectId];
            }
            //如果是老逻辑或者没有匹配到的（学科不支持）都按照不订正处理
            if ($type == 1 || !$type){
                return false;
            }else{
                return true;
            }
        }
        //历史逻辑2021年后的不考虑
        if(in_array($courseId,Hkzb_Const_Correction::$BlackList)){
            return false;
        }
        if(in_array($courseId,Hkzb_Const_Correction::$JyzCourseIdArr)){
            return true;
        }
        $season = substr(trim($learnSeason),0,1);
        $stage  =self::getStageByGrade($gradeId);
        if($year < 2020 || ($year == 2020  && $season == 4)){
            return false;
        }
        if($courseStartTime <= Hkzb_Const_Correction::CORRECTION_START_TIME){
            return false;
        }
        if(!in_array($gradeId,[2,3,4,5,6,7,11,12,13,14,15,16,61,62,63,64])){
            return false;
        }

        if($gradeId == 6 && $year == 2020 && in_array($subjectId ,[3,4]) && ($season == 1 || in_array($learnSeason,[21,22] ))){
            //高二英语物理//包括短训班和班课
            //2020春及暑一二订正
            if($newCourseType == 12 && in_array($learnSeason,[21,22])){
                return false;
            }
            return true;
        }
        if($newCourseType == 12 && $gradeId == 7){
            if($year = 2020 && $subjectId == 1 && $season == 1  ){
                return false;
            }else{//短训班，高三语文2020暑期以后订正
                if($subjectId == 1){
                    return true;
                }else{
                   return false;
                }
            }
        }
        if($newCourseType == 12 && $gradeId == 5){//短训班高一
            return false;
        }
        if($year == 2020 && $stage == 30 && ($season == 1 || in_array($learnSeason,[21,22]))){
            //除上面三个if之外，2020高中春和暑一二均为老巩固联系
            return false;
        }
        // 订正二期扩科(初中语文 和 初二物理)，春季&寒季课程不订正
        if ($year == 2020 && in_array($season, [1,4])) {
            if (in_array($gradeId, [2,3,4]) && $subjectId == 1) {
                return false;
            }
            if ($gradeId == 3 && $subjectId == 4) {
                return false;
            }
        }
        if($newCourseType == 12){
            if($gradeId == 7){
                if($year = 2020 && $subjectId == 1 && $season == 1  ){
                    return false;
                }else{
                    return true;
                }
            }
        }

        if($stage){
            $subjectArr = Hkzb_Const_Correction::$CorrectionGradeSubjectMap[$stage][$gradeId];
            if(in_array($subjectId,$subjectArr)){
                return true;
            }
        }
        return false;
    }
    /**
     * @param int $grade
     * @return bool|int
     * @brief 根据年级计算学部
     */
    public static function getStageByGrade($grade){
        if(in_array($grade,[1,20,30,60])){return $grade;}
        if(in_array($grade,[61,62,63,64])){return 60;}
        if(in_array($grade,[11,12,13,14,15,16])){return 1;}
        if(in_array($grade,[2,3,4])){return 20;}
        if(in_array($grade,[5,6,7])){return 30;}
        return false;
    }

    /**
     * 是否延长巩固练习的作答时间
     * @deprecated 巩固练习的时间逻辑越来越复杂，抽象到 Hkzb_Const_CorrectionHomework，此方法逐渐废弃
     * @param $gradeId
     * @param $subjectId
     * @param $year
     * @param $learnSeason
     * @param $courseStartTime
     * @param $courseId
     * @return bool
     */
    public static function isDelayHomework($gradeId, $subjectId, $year, $learnSeason, $courseStartTime, $courseId) {
        $isDelayHomework = false;
        do {
            if (!self::needCorrection($gradeId, $subjectId, $year, $learnSeason, $courseStartTime, $courseId)) {
                break;
            }

            $department = Hkzb_Const_FudaoGradeMap::$gradeXueBuMap[$gradeId];
            if (20 == $department) { // 初中订正范围内学科
                $isDelayHomework = true;
                break;
            }

            if (1 == $department && 2 == $subjectId) { // 小学仅数学
                $isDelayHomework = true;
                break;
            }
        } while (false);

        return $isDelayHomework;
    }
}
