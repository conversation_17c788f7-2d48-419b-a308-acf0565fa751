<?php

/**
 * 蜂鸟抽奖：平台优惠券
 * <AUTHOR> <<EMAIL>>
 * @version    2019/10/27 下午11:34
 * @copyright  zybang.com
 */
class Oplib_Util_Lottery_CouponPingTai
{
    public static function getInfoById($couponId)
    {
        $url       = '/support/coupon/detail'; //优惠券信息
        $arrParams = [
                'couponId' => $couponId,
        ];
        $aResult   = Oplib_Util_RalClient::ralPost('support', $arrParams, $url);
        if ($aResult['errNo'] != 0 || empty($aResult['data'])) {
            return [];
        }
        return $aResult['data'];
    }

    //领取
    public static function handle($uid, $couponId, $lotteryId)
    {
        $url       = '/support/coupon/codereceive'; //领取优惠券
        $arrParams = [
                'uid'      => $uid,
                'channel'  => 'fengniao_lottery',
                'opCode'   => $lotteryId,
                'couponId' => $couponId,
        ];
        return Oplib_Util_RalClient::ralPost('support', $arrParams, $url);
//        if ($aResult['errNo'] != 0 || empty($aResult['data']['code'])) {
//            Oplib_Util_Log::warning('fengniao_lottery', 'FengNiao_Lottery_CouponPingTai', 'handle', '领取优惠券失败！', json_encode( ['request' => $arrParams,'response' => $aResult]));
//            return false;
//        }
//        return $aResult['data']['code']; //返回优惠券id
    }
}
