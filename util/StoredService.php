<?php

/**
 * stored
 * 注意：一课教学部专用，随时可能迁移集群，其它方向禁用。
 * 此类 和Liveservice_Util_StoredService功能一致 不要随便改动
 */
class Hkzb_Util_StoredService {
    private static $_keyPrefix = 'WARM_UP_';

    /**
     * 返回直播redis实例
     *
     * @return object
     */
    public static function getZhiboInstance() {
        return Hk_Service_RedisClient::getInstance("spdata");
    }

    /**
     * string类型，set操作
     *
     * @param $key
     * @param $value
     * @param $expireSeconds
     * @return mixed
     */
    public static function set($key, $value, $expireSeconds) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);
        if (!empty($value) || is_array($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }

        $ret = $redis->set($key, $value, $expireSeconds);

        return $ret;
    }

    /**
     * string 类型，get操作
     *
     * @param $key
     * @return mixed
     */
    public static function get($key) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);

        $value = $redis->get($key);

        if (!empty($value)) {
            $value = json_decode($value, true);
        }

        return $value;
    }

    /**
     * hash 类型，hget操作
     *
     * @param $key string
     * @param $field mixed
     * @return mixed
     */
    public static function hGet($key, $field) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);

        $value = $redis->hget($key, $field);

        if (!empty($value)) {
            $jsonValue = json_decode($value, true);
            if (json_last_error() == JSON_ERROR_NONE) {
                $value = $jsonValue;
            }
        }

        return $value;
    }


    /**
     * hash 类型，hset操作
     *
     * @param $key string
     * @param $field mixed
     * @param $value mixed
     * @return mixed
     */
    public static function hSet($key, $field, $value) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);

        if (!empty($value) || is_array($value)) {
            $value = json_encode($value);
        }
        $ret = $redis->hset($key, $field, $value);

        return $ret;
    }

    /**
     * 设置生命周期
     *
     * @param $key
     * @param $expire
     * @return mixed
     */
    public static function expire($key, $expire = 0) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);


        $ret = $redis->expire($key, $expire);

        return $ret;
    }

    /**
     * 获取有效时间
     *
     * @param $key
     * @return mixed
     */
    public static function ttl($key) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);


        $ret = $redis->ttl($key);

        return $ret;
    }

    private static function _formatKey($key) {
        return self::$_keyPrefix . $key;
    }

    /**
     * 批量设置多个key
     *
     * @param $keyPrefix
     * @param $data
     * @param $expire
     * @param $sliceCount
     * @return bool
     */
    public static function multipleSet($keyPrefix, $data, $expire, $sliceCount = LiveService_Const_LiveRedisConfig::CODIS_HOT_KEY_CLONE_NUM) {
        if (!is_int($sliceCount) || $sliceCount <= 1) {
            return false;
        }

        $keyPrefix = strval($keyPrefix);
        $arrKey    = array();
        for ($i = 0; $i < $sliceCount; $i++) {
            $arrKey[] = sprintf("%s_CLONE_%s", $keyPrefix, $i);
        }
        $redis = self::getZhiboInstance();
        $flag  = true;
        $data  = json_encode($data, JSON_UNESCAPED_UNICODE);
        foreach ($arrKey as $key) {
            $status = $redis->set($key, $data, $expire);
            if (!$status) {
                $flag = false;
            }
        }

        return $flag;
    }

    /**
     * 批量删除多个key
     * $sliceCount应与批量设置时相同，否则
     * @param $keyPrefix
     * @param $sliceCount
     * @return bool
     */
    public static function multipleDel($keyPrefix, $sliceCount = LiveService_Const_LiveRedisConfig::CODIS_HOT_KEY_CLONE_NUM) {
        if (!is_int($sliceCount) || $sliceCount <= 1) {
            return false;
        }

        $keyPrefix = strval($keyPrefix);
        $arrKey    = array();
        for ($i = 0; $i < $sliceCount; $i++) {
            $arrKey[] = sprintf("%s_CLONE_%s", $keyPrefix, $i);
        }
        $redis = self::getZhiboInstance();
        return $redis->del($arrKey);
    }

    /**
     * 随机从一个分片中读取缓存值
     *
     * @param $key
     * @param $hash
     * @param int $sliceCount
     * @return mixed
     */
    public static function multipleGet($key, $hash = 0, $sliceCount = LiveService_Const_LiveRedisConfig::CODIS_HOT_KEY_CLONE_NUM) {
        if (empty($hash)) {
            $hash = mt_rand(0, LiveService_Const_LiveRedisConfig::CODIS_HOT_KEY_CLONE_NUM);
        }
        $cloneIndex = intval($hash) % $sliceCount;

        $sliceCacheKey = sprintf('%s_CLONE_%s', $key, $cloneIndex);

        $redis = self::getZhiboInstance();
        $data  = $redis->get($sliceCacheKey);
        if ($data) {
            $data = json_decode($data, true);
        }

        return $data;
    }


    /**
     * 判断key是否存在
     *
     * @param $key string
     * @return int 0|1
     */
    public static function exists($key) {
        $redis = self::getZhiboInstance();
        $key   = self::_formatKey($key);
        return $redis->exists($key);
    }

}