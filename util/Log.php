<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: <EMAIL>
 * @file: phplib/util/Log.php
 * @date: 2019/02/19
 * @file: 下午5:24
 * @desc: 当前Bd_Log 在处理addNotice上存在一些问题，这里继承Bd_Log进行一些trick 修正
 */

class Zb_Util_Log extends Hk_Util_Log {
    public static function debug($str, $errno = 0, $arrArgs = null, $depth = 0) {
        self::_innerCall(__FUNCTION__, $str, $errno, $arrArgs, $depth);
    }

    public static function trace($str, $errno = 0, $arrArgs = null, $depth = 0) {
        self::_innerCall(__FUNCTION__, $str, $errno, $arrArgs, $depth);
    }

    public static function notice($str, $errno = 0, $arrArgs = null, $depth = 0) {
        self::_innerCall(__FUNCTION__, $str, $errno, $arrArgs, $depth);
    }

    public static function warning($str, $errno = 0, $arrArgs = null, $depth = 0) {
        self::_innerCall(__FUNCTION__, $str, $errno, $arrArgs, $depth);
    }

    public static function fatal($str, $errno = 0, $arrArgs = null, $depth = 0) {
        self::_innerCall(__FUNCTION__, $str, $errno, $arrArgs, $depth);
    }

    public static function debugEasy($key, $value="", $depth = 0) {
        if (is_array($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }
        self::debug("$key". (empty($value) ? "" : ":$value"), 0, null, $depth+1);
    }

    private static function _innerCall($method, $str, $errno = 0, $arrArgs = null, $depth = 0){
        if(!in_array(strtolower($method), ['debug', 'warning', 'trace', 'fatal', 'notice'])) {
            return;
        }
        $instance = self::getInstance();
        $temp = $instance->addNotice;
        $instance->addNotice = [];
        parent::$method($str, $errno, $arrArgs, $depth + 2);
        $instance->addNotice = $temp;
    }
}