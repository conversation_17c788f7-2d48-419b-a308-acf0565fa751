<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      util/sns/Article.php
 * @author:        <EMAIL>
 * @desc:          帖子工具 
 * @create:        2020-08-13 16:26:35
 * @last modified: 2020-08-13 16:26:35
 */
class Saaslib_Util_Sns_Article {

    /**
     * 获取用户完整邮箱路径
     * @param   string  $uname   邮箱前缀
     * @return  string
     */
    public static function getUserPath($uname){
        // 通过邮箱前缀 判断 用户来源，拼接邮箱后缀
        // _v 外包；. 喵宝；作业帮
        if(strpos($uname, "_v")){
            $uname .= "@zuoyebang.com"; 
        }elseif(strpos($uname, "@paperang.com")){
        }else{
            $uname .= "@zuoyebang.com"; 
        }     
        return $uname;
    }
}
