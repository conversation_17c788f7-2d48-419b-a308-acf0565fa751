<?php
/**
 * 蜂鸟抽奖
 * Class Oplib_Util_FengNiaoLottery
 * <AUTHOR> <<EMAIL>>
 * @version    2020/3/8 上午9:45
 * @copyright  zybang.com
 */

class Oplib_Util_FengNiaoLottery
{
    //兜底奖品名称
    const DEFAULT_PRIZE = [
            'name'   => "谢谢惠顾",
            "grade"  => 100,
    ];
    const DELETED_NO                = 0;  // 未删除
    const DELETED_YES               = 1;  // 删除
    const SEND_TYPE_0               = 0; //实时发放
    const SEND_TYPE_1               = 1; //手动发放
    //奖品发放状态
    const DELIVER_TYPE_0            = 0; //待发放
    const DELIVER_TYPE_1            = 1; //待发，需要完善地址
    const DELIVER_TYPE_2            = 2; //成功
    const DELIVER_TYPE_3            = 3; //失败
    const DELIVER_TYPE_4            = 4; //nmq异步发放中
    const DELIVER_TYPE_5            = 5; //脚本发奖中
    const DELIVER_TYPE = [
            0 => '待发放',
            1 => '待完善地址',
            2 => '发放成功',
            3 => '发放失败',
            4 => '发放中',
            5 => '发放中',
    ];

    //分类
    const PRIZE_TYPE_SKU    = 1; //实物/课程
    const PRIZE_TYPE_COUPON = 2; //优惠券
    const PRIZE_TYPE_CREDITS= 3; //学分

    //奖品类型
    const PRIZE_TYPE_BUSINESS_1     = 1; //一课优惠券
    const PRIZE_TYPE_BUSINESS_2     = 2; //平台优惠券
    const PRIZE_TYPE_BUSINESS_3     = 3; //商城优惠券
    const PRIZE_TYPE_BUSINESS_4     = 4; //虚拟课程
    const PRIZE_TYPE_BUSINESS_5     = 5; //实物
    const PRIZE_TYPE_BUSINESS_6     = 6; //学分
    const PRIZE_TYPE_BUSINESS_8     = 8; //现金
    const PRIZE_TYPE_BUSINESS_9     = 9; //鸭鸭币
    const PRIZE_TYPE_BUSINESS_10     = 10; //礼品卡

    //缓存key
    const FN_LOTTERY_INFO_SETNX     = 'FN_LOTTERY_INFO_SETNX'; //首次进入页面【排他锁】
    const FN_LOTTERY_INFO           = 'FN_LOTTERY_INFO_%d'; //抽奖信息缓存
    const FN_LOTTERY_LIST           = 'FN_LOTTERY_LIST_%d'; //中奖列表（1分钟）
    const FN_LOTTERY_ALL_NUMS       = 'FN_LOTTERY_ALL_NUMS_%d'; //抽奖总人数缓存
    const FN_LOTTERY_ALL_NUMS_TTL   =  2*24*60*60;
    const FN_LOTTERY_STORE_ALL_INFO = 'FN_LOTTERY_STORE_ALL_INFO_%d_%d'; // 库存总量  抽奖id_奖品id
    const FN_LOTTERY_STORE_DAY_INFO = 'FN_LOTTERY_STORE_DAY_INFO_%d_%d'; // 每天总量 抽奖id_奖品id
    //用户缓存
    const FN_LOTTERY_USER_NUM_INFO  = 'FN_LOTTERY_%d_USER_INFO_%d';         //抽奖次数 抽奖id_uid
    const FN_LOTTERY_USER_FLOW_NUM  = 'FN_LOTTERY_%d_USER_FLOW_NUM_%d';     //用户第几次抽奖  抽奖id_uid

    const FN_LOTTERY_USER_SOURCE    = 'FN_LOTTERY_%d_USER_SOURCE_%d_%d';    //来源信新  抽奖id_类型_uid


    /**
     * 抽奖缓存时间
     */
    public static function getDayTTL()
    {
        return strtotime(date('Y-m-d 23:59:59'),time())- time();
    }

    /**
     * Notes: 发奖
     * <AUTHOR> <<EMAIL>>
     * @param $lotteryId
     * @param $uid
     * @param $flowInfo
     * @return array|mixed
     */
    public static function sendPrize($lotteryId,$uid,$flowInfo)
    {
        Bd_Log::addNotice('Oplib_Util_FengNiaoLottery',json_encode($flowInfo));

        $flowId     = $flowInfo['id'];
        $prizeType  = $flowInfo['prizeType'];
        $businessId = $flowInfo['businessId'];
        $prizeName  = $flowInfo['prizeName'];
        $lastfrom   = $flowInfo['lastfrom'];
        $address    = $flowInfo['address'];
        $flowPond  = '';
        $ext = [];
        if(!empty($flowInfo['ext'])){
            if(is_array($flowInfo['ext'])){
                $ext = $flowInfo['ext'];
            } else {
                $ext = json_decode($flowInfo['ext'], true);
            }
            $flowPond   = $ext['flowPond'] ?? '';
        }

        //一颗优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_1){
            return  Oplib_Util_Lottery_CouponYike::handle($uid,$businessId,$lotteryId,$prizeName);
        }

        //平台优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_2){
            return  Oplib_Util_Lottery_CouponPingTai::handle($uid,$businessId,$lotteryId);
        }

        //商城优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_3 ){
            return Oplib_Util_Lottery_CouponMall::handle($uid,$businessId,$lotteryId);
        }

        //虚拟课程
        if($prizeType == self::PRIZE_TYPE_BUSINESS_4){
            return Oplib_Util_Lottery_SkuPurchase::handle($flowId,$uid,$businessId,$prizeType,'',$lastfrom,$flowPond,$lotteryId);
        }
        //实物
        if($prizeType == self::PRIZE_TYPE_BUSINESS_5){
            if(!empty($address)){ //实物需要完善地址
                return Oplib_Util_Lottery_SkuPurchase::handle($flowId,$uid,$businessId,$prizeType,$address,$lastfrom,$flowPond,$lotteryId);
            }
        }

        //礼品卡兑换
        if($prizeType == self::PRIZE_TYPE_BUSINESS_10){
            $count = $ext["count"]??1;  //礼品卡数量.默认是1、
            $unqId = $lotteryId.'_'.$flowInfo["prizeId"]."_".$flowId; //幂等id
            $extData = ["lastfrom" => $lastfrom,"flowpond" => $flowPond,"lotteryId" => $lotteryId,"desc" => "蜂鸟抽奖活动"];
            return Oplib_Util_Lottery_GiftCard::handle($lotteryId,$uid,$count,$unqId,$businessId,$extData);
        }

        //现金
        if($prizeType == self::PRIZE_TYPE_BUSINESS_8){
            $unqId = $lotteryId.$flowInfo["prizeId"];
            return Oplib_Util_Lottery_Cash::handle($unqId,$flowId,$uid,$businessId,$ext);
        }

        $budgetId = $ext["budgetId"]??"";  //预算
        //学分老接口
        if($prizeType == self::PRIZE_TYPE_BUSINESS_6 && empty($budgetId)){
            return Oplib_Util_Lottery_Score::handle($flowId,$uid,$businessId);
        }
        //学分新接口
        if($prizeType == self::PRIZE_TYPE_BUSINESS_6 && !empty($budgetId)){
            return Oplib_Util_Lottery_Currency::handle($uid,$businessId,$budgetId);
        }
        //鸭鸭币
        if($prizeType == self::PRIZE_TYPE_BUSINESS_9 && !empty($budgetId)){
            return Oplib_Util_Lottery_Currency::handle($uid,$businessId,$budgetId,10003);
        }

        return ['errNo' => 5555];
    }

    public static function resolveRes($prizeType,$aResult)
    {
        $data = [
                'businessCode' => '',
                'businessStr'  => '',
        ];
        if(empty($aResult)){
            return $data;
        }
        //一颗优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_1){
            if($aResult['errNo'] == 0){
                $data['businessCode'] = $aResult['data']['couponId']??0;
            }else{
                $data['businessStr'] = $aResult['errStr'];
            }
        }

        //平台优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_2){
            if($aResult['errNo'] == 0){
                $data['businessCode'] = $aResult['data']['code']??0;
            }else{
                $data['businessStr'] = $aResult['errstr']; //平台是小写
            }
        }

        //商城优惠券
        if ($prizeType == self::PRIZE_TYPE_BUSINESS_3 ){
            if($aResult['errNo'] == 0){
                $data['businessCode'] = $aResult['data']['couponCode']??0;
            }else{
                $data['businessStr'] = $aResult['errstr'];
            }
        }

        //虚拟课程 && 实物
        if($prizeType == self::PRIZE_TYPE_BUSINESS_4 || $prizeType == self::PRIZE_TYPE_BUSINESS_5){
            if($aResult['errNo'] == 0){
                $data['businessCode'] = $aResult['data']['orderId']??0;
            }else{
                $data['businessStr'] = $aResult['errStr'];
            }
        }

        //虚拟课程 && 实物
        if($prizeType == self::PRIZE_TYPE_BUSINESS_6 || $prizeType == self::PRIZE_TYPE_BUSINESS_9){
            if($aResult['errNo'] == 0){
                $data['businessCode'] = '';
            }else{
                $data['businessStr'] = $aResult['errStr'];
            }
        }

        return $data;
    }
}
