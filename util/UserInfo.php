<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   UserInfo.php
 * <AUTHOR>
 * @date   2019/1/23 14:06
 * @brief  获取用户信息
 **/
class Oplib_Util_UserInfo
{
    /** 判断用户是否是班课用户
     * @param $intStudentUid
     * @return int 是：1，否：0
     */
    public static function isBanKeUser($intStudentUid)
    {
        $flag          = 0; // 默认不是班课用户
        $intStudentUid = intval($intStudentUid);
        if ($intStudentUid > 0) {
            $userPersonas = [Zb_Service_UserPersonas_Personas::UP_PRIVATE_LONG];
            $res          = Zb_Service_UserPersonas_Personas::getUserPersonas($intStudentUid, $userPersonas);
            $studentInfo  = isset($res['data'][$intStudentUid]) && is_array($res['data'][$intStudentUid]) ? $res['data'][$intStudentUid] : [];
            Bd_Log::addNotice('getStudentInfo_userrole', json_encode($studentInfo));
            if ($studentInfo[Zb_Service_UserPersonas_Personas::UP_PRIVATE_LONG]) {
                $flag = 1;
            }
        }
        Bd_Log::addNotice("{$intStudentUid}_isBanKeUser", $flag);

        return $flag;
    }


    /** 批量判断用户是否是班课用户
     * @param $arrUids 用户uids
     * @return array [uid => 0或1]
     */
    public static function isBanKeUserBatch($arrUids)
    {
        $arrOutput = [];

        if (is_array($arrUids)) {
            $arrUids = array_unique($arrUids);
            $role    = Zb_Service_UserPersonas_Personas::UP_PRIVATE_LONG;
            $res     = Zb_Service_UserPersonas_Personas::getBatch($arrUids, [$role]);
            Bd_Log::addNotice(crc32(json_encode($arrUids)) . '_batchGetUserRoleInfo', json_encode($res));
            foreach ($arrUids as $uid) {
                if (isset($res['data'][$uid][$role]) && 1 == $res['data'][$uid][$role]) {
                    $arrOutput[$uid] = 1;
                } else {
                    $arrOutput[$uid] = 0;
                }
            }
        }

        return $arrOutput;
    }
}