<?php

/**
 * Class Qdmis_Const_AdsEnum_EnumKuaishou
 * 快手
 */
class QdLib_Const_AdsEnum_Lv2_Kuaishou
{

    // 二级字段
    const FIELD_ZYB_DELIVERY_RANGED = "zybDeliveryRange";
    const FIELD_ZYB_DELIVERY_TYPE   = "zybDeliveryType";
    const FIELD_ZYB_CONVERT_TRACE   = "zybConvertTrace"; //转化追踪
    const FIELD_USE_APP_MARKET      = "useAppMarket";
    const FIELD_APP_STORE           = "appStore";
    const FIELD_VIDEO_LANDING_PAGE  = "videoLandingPage";
    const FIELD_ZYB_TARGET_TYPE     = "zybTargetType";
    const FIELD_SPEED               = "speed";
    const FIELD_ZYB_PLAN            = "zybPlan";
    const FIELD_ZYB_SCHEDULE_TIME   = "zybScheduleTime";
    const FIELD_SHOW_MODE           = "showMode";
    //todo 优化目标
    const FIELD_YOUHUA_MUBIAO       = "zybYouhuaMubiao";
    const FIELD_OCPX_ACTION_TYPE    = "ocpxActionType";
    const FIELD_BID_TYPE            = "bidType";
    const FIELD_ZYB_DAY_BUDGET_MODE = "zybDayBudgetMode";
    //ocpx_action_type为 180 53 时 传入cpa_bid；
    const FIELD_CPA_BID             = "cpaBid";
    //smart_bid ，优先低成本时传入
    const FIELD_SMART_BID           = "smartBid";
    const FIELD_UNIT_TYPE           = "unitType";
    const FIELD_SCENE_ID            = "sceneId";

    public static $fieldDesc = [
        self::FIELD_ZYB_DELIVERY_RANGED     => "投放范围",
        self::FIELD_ZYB_DELIVERY_TYPE       => "伏羲投放形式",
        self::FIELD_ZYB_CONVERT_TRACE       => "转化追踪",
        self::FIELD_USE_APP_MARKET          => "有限从应用商店下载",
        self::FIELD_APP_STORE               => "应用商店",
        self::FIELD_VIDEO_LANDING_PAGE      => "落地页前置",
        self::FIELD_ZYB_TARGET_TYPE         => "定向方式",
        self::FIELD_SPEED                   => "投放方式",
        self::FIELD_ZYB_PLAN                => "排期",
        self::FIELD_ZYB_SCHEDULE_TIME       => "投放时段",
        self::FIELD_SHOW_MODE               => "创意展现方式",
        self::FIELD_YOUHUA_MUBIAO           => "优化目标",
        self::FIELD_OCPX_ACTION_TYPE        => "转化目标",
        self::FIELD_BID_TYPE                => "计费方式",
        self::FIELD_CPA_BID                 => "转化目标出价",
        self::FIELD_ZYB_DAY_BUDGET_MODE     => "单日预算",
        self::FIELD_SMART_BID               => "转化目标出价",
        self::FIELD_UNIT_TYPE               => "创意制作方式",
        self::FIELD_SCENE_ID                => "广告投放位置",
    ];

    const ZYB_DELIVERY_RANGE_DEFAULT = "DEFAULT";
    const ZYB_DELIVERY_RANGE = [
        self::ZYB_DELIVERY_RANGE_DEFAULT    => "默认"
    ];

    const ZYB_DELIVERY_TYPE_SALE        = "way_sell";
    const ZYB_DELIVERY_TYPE_FORM_BOOK   = "way_form_order";
    const ZYB_DELIVERY_TYPE_APP         = "way_app";

    const ZYB_DELIVERY_TYPE = [
        self::ZYB_DELIVERY_TYPE_SALE        => "直卖",
        self::ZYB_DELIVERY_TYPE_FORM_BOOK   => "表单预约",
        self::ZYB_DELIVERY_TYPE_APP         => "APP推广",
    ];
    const ZYB_DELIVERY_TYPE_CREATE_ENUMS = [
        self::ZYB_DELIVERY_TYPE_SALE        ,
        self::ZYB_DELIVERY_TYPE_FORM_BOOK   ,
        self::ZYB_DELIVERY_TYPE_APP         ,
    ];

    const ZYB_CONVERT_TRACE_NO              = "NO";
    const ZYB_CONVERT_TRACE_API_CALLBACK    = "API_CALLBACK";

    const ZYB_CONVER_TRACE = [
        self::ZYB_CONVERT_TRACE_NO          => "不使用",
        self::ZYB_CONVERT_TRACE_API_CALLBACK=> "API回传",
    ];


    const USE_APP_MARKET_NO     = 0;
    const USE_APP_MARKET_YES    = 1;

    const USE_APP_MARKET = [
        self::USE_APP_MARKET_NO     => "不勾选",
        self::USE_APP_MARKET_YES    => "勾选",
    ];
    const USE_APP_MARKET_CREATE_ENUMS = [
        self::USE_APP_MARKET_NO     ,
        self::USE_APP_MARKET_YES    ,
    ];


    const APP_STORE_HUAWEI      = "huawei";
    const APP_STORE_OPPO        = "oppo";
    const APP_STORE_VIVO        = "vivo";
    const APP_STORE_XIAOMI      = "xiaomi";
    const APP_STORE_MEIZU       = "meizu";
    const APP_SRORE_SMARTISAN   = "smartisan";

    const APP_STORE = [
        self::APP_STORE_HUAWEI      => "华为",
        self::APP_STORE_OPPO        => "OPPO",
        self::APP_STORE_VIVO        => "VIVO",
        self::APP_STORE_XIAOMI      => "小米",
        self::APP_STORE_MEIZU       => "魅族",
        self::APP_SRORE_SMARTISAN   => "锤子",
    ];
    const APP_STORE_CREATE_ENUM = [
        self::APP_STORE_HUAWEI      ,
        self::APP_STORE_OPPO        ,
        self::APP_STORE_VIVO        ,
        self::APP_STORE_XIAOMI      ,
        self::APP_STORE_MEIZU       ,
        self::APP_SRORE_SMARTISAN   ,
    ];

    const VIDEO_LANDING_PAGE_NO = false;
    const VIDEO_LANDING_PAGE_YES = true;

    const VIDEO_LANDING_PAGE = [
        self::VIDEO_LANDING_PAGE_NO     => "不勾选",
        self::VIDEO_LANDING_PAGE_YES    => "勾选",
    ];
    const VIDEO_LANDING_PAGE_CREATE_ENUMS = [
        self::VIDEO_LANDING_PAGE_NO     ,
        self::VIDEO_LANDING_PAGE_YES    ,
    ];

    const ZYB_TARGET_TYPE_SELF_DEFINE = "SELF_DEFINE";
    const ZYB_TARGET_TYPE_AUTO_TARGET = "AUTO_TARGET";

    const ZYB_TARGET_TYPE = [
        self::ZYB_TARGET_TYPE_SELF_DEFINE => "自定义",
        self::ZYB_TARGET_TYPE_AUTO_TARGET => "智能定向"
    ];
    const ZYB_TARGET_TYPE_CREATE_ENUMS = [
        self::ZYB_TARGET_TYPE_SELF_DEFINE,
        self::ZYB_TARGET_TYPE_AUTO_TARGET,
    ];

    const ZYB_PLAN_FROM_NOW_ON  = "FROM_NOW_NO";
    const ZYB_PLAN_START_END    = "START_END";
    const ZYB_PLAN = [
        self::ZYB_PLAN_FROM_NOW_ON => "从今日开始投放",
        self::ZYB_PLAN_START_END   => "设置开始和结束日期",
    ];

    const ZYB_SCHEDULE_TIME_WHOLE_DAY       = "WHOLE_DAY";
    const ZYB_SCHEDULE_TIME_SPECIFY_TIME    = "SPECIFY_TIME";
    const ZYB_SCHEDULE_TIME = [
        self::ZYB_SCHEDULE_TIME_WHOLE_DAY       => "全天",
        self::ZYB_SCHEDULE_TIME_SPECIFY_TIME    => "特定时间"
    ];

    const UNIT_TYPE_SELF_DEFINE         = 4;
    const UNIT_TYPE_PROGRAM_CREATIVE_2  = 7;
    const UNIT_TYPE = [
        self::UNIT_TYPE_SELF_DEFINE         => "自定义",
        self::UNIT_TYPE_PROGRAM_CREATIVE_2  => "程序化创意2.0"
    ];

    const UNIT_TYPE_CREATE_ENUMS = [
        self::UNIT_TYPE_SELF_DEFINE         ,
        self::UNIT_TYPE_PROGRAM_CREATIVE_2
    ];

    const SCENE_ID_YOUXUAN          = 1;
    const SCENE_ID_SHIPIN_BOFANG    = 3;
    const SCENE_ID_SHANGXIAN_HUA    = 6;
    const SCENE_ID_XINXILIU         = 7;
    const SCENE_ID = [
        self::SCENE_ID_YOUXUAN          => "优选广告位",
        self::SCENE_ID_SHANGXIAN_HUA    => "上下滑大屏广告",
        //self::SCENE_ID_XINXILIU         => "信息流广告",
        self::SCENE_ID_SHIPIN_BOFANG    => "视频播放页广告-便利贴广告",
    ];
    const SCENE_ID_CREATE_ENUMS = [
        self::SCENE_ID_YOUXUAN          ,
        self::SCENE_ID_SHANGXIAN_HUA    ,
        self::SCENE_ID_XINXILIU         ,
        self::SCENE_ID_SHIPIN_BOFANG    ,
    ];

    const SCENE_ID_TITLE = [
        self::SCENE_ID_SHIPIN_BOFANG => [
            "mainTitle"     => "视频播放页广告",
            "subTitle"      => "在视频播放时展现广告，获取更多流量"
        ],
        self::SCENE_ID_SHANGXIAN_HUA => [
            "mainTitle"     => "上下滑大屏广告",
            "subTitle"      => "全屏沉浸式广告体验，广告展示效率高，转化高"
        ],
//        self::SCENE_ID_XINXILIU => [
//            "mainTitle"     => "信息流广告",
//            "subTitle"      => "通过热门位置展现广告，精准定向获取潜在用户"
//        ],
    ];

    /**
     * 投放状态 （操作结果）
     */
    const PUT_STATUS_ENABLE = 1;
    const PUT_STATUS_DISABLE = 2;
    const PUT_STATUS_DELETE = 3;
    const PUT_STATUS = [
        self::PUT_STATUS_ENABLE => "投放中",
        self::PUT_STATUS_DISABLE => "暂停",
        self::PUT_STATUS_DELETE => "删除",
    ];

    /**
     * 投放方式
     */
    const SPEED_UNKNOWN     = 0;
    const SPEED_NORMAL      = 1;
    const SPEED_SMOOTH      = 2;
    const SPEED_LOWCOST     = 3;
    const SPEED = [
        self::SPEED_UNKNOWN     => "-",
        self::SPEED_NORMAL      => "加速投放",
        self::SPEED_SMOOTH      => "平滑投放",
        self::SPEED_LOWCOST     => "优先低成本",

    ];
    const SPEED_CREATE_ENUMS = [
        self::SPEED_NORMAL      ,
        self::SPEED_SMOOTH      ,
        self::SPEED_LOWCOST     ,

    ];

    const ZYB_DAY_BUDGET_MODE_INFINITE  = "BUDGET_MODE_INFINITE";
    const ZYB_DAY_BUDGET_MODE_TOTAL     = "BUDGET_MODE_TOTAL";
    const ZYB_DAY_BUDGET_MODE_DAY       = "BUDGET_MODE_DAY";
    const ZYB_DAY_BUDGET_MODE = [
        self::ZYB_DAY_BUDGET_MODE_INFINITE  => "不限",
        self::ZYB_DAY_BUDGET_MODE_TOTAL     => "统一预算",
        self::ZYB_DAY_BUDGET_MODE_DAY       => "分日预算"
    ];

    /**
     * 出价类型,创建只支持oCPM
     */
    const BID_TYPE_CPM  = 1;
    const BID_TYPE_CPC  = 2;
    const BID_TYPE_OCPC = 6;
    const BID_TYPE_OCPM = 10;
    const BID_TYPE_eCPC = 20;
    const BID_TYPE = [
        self::BID_TYPE_CPM  => "CPM",
        self::BID_TYPE_CPC  => "CPC",
        self::BID_TYPE_OCPC => "OCPC",
        self::BID_TYPE_OCPM => "按展示计费(oCPM)",
        self::BID_TYPE_eCPC => "eCPC",
    ];
    const BID_TYPE_CREATE_ENUMS = [
        self::BID_TYPE_OCPM
    ];

    /**
     * 创意展现方式
     */
    const SHOW_MODE_UNKNOWN     = 0;
    const SHOW_MODE_CAROUSEL    = 1;
    const SHOW_MODE_PREFERRED   = 2;
    const SHOW_MODE = [
        self::SHOW_MODE_UNKNOWN     => "未知",
        self::SHOW_MODE_CAROUSEL    => "随机轮播",
        self::SHOW_MODE_PREFERRED   => "智能优选",
    ];

    const SHOW_MODE_CREATE_ENUMS = [
        self::SHOW_MODE_CAROUSEL    ,
        self::SHOW_MODE_PREFERRED   ,
    ];

    const SMART_BID_MANUAL  = 0;
    const SMART_BID_AUTO    = 1;
    const SMART_BID = [
        self::SMART_BID_MANUAL => "手动出价",
        self::SMART_BID_AUTO   => "自动出价"
    ];
    const SMART_BID_CREATE_ENUMS = [
        self::SMART_BID_MANUAL ,
        self::SMART_BID_AUTO
    ];

    const ZYB_YOUHUA_MUBIAO_XINGWEISHU = "zhuanhuashu";
    const ZYB_YOUHUA_MUBIAO = [
        self::ZYB_YOUHUA_MUBIAO_XINGWEISHU => "转化数"
    ];

    /**
     * 优化目标，创建只支持 53 180 190
     */
    const OCPX_ACTION_TYPE_UNKNOWN = 0;
    const OCPX_ACTION_TYPE_LINK = 2;
    const OCPX_ACTION_TYPE_EXPOSURE = 10;
    const OCPX_ACTION_TYPE_CLICK = 11;
    const OCPX_ACTION_TYPE_DOWNLOAD = 31;
    const OCPX_ACTION_TYPE_CLUE = 53;
    const OCPX_ACTION_TYPE_ACTIVATION = 180;
    const OCPX_ACTION_TYPE_PAID = 190;
    const OCPX_ACTION_TYPE_ROI = 191;
    const OCPX_ACTION_TYPE_EFFECTIVE_CLUE = 348;
    const OCPX_ACTION_TYPE_CREDIT = 383;
    const OCPX_ACTION_TYPE_FINISH = 384;
    const OCPX_ACTION_TYPE_WECHAT_COPY = 715;
    const OCPX_ACTION_TYPE = [
        self::OCPX_ACTION_TYPE_UNKNOWN => "未知",
        self::OCPX_ACTION_TYPE_LINK => "点击转化链接",
        self::OCPX_ACTION_TYPE_EXPOSURE => "曝光",
        self::OCPX_ACTION_TYPE_CLICK => "点击",
        self::OCPX_ACTION_TYPE_DOWNLOAD => "下载完成",
        self::OCPX_ACTION_TYPE_CLUE => "表单转化",
        self::OCPX_ACTION_TYPE_ACTIVATION => "激活",
        self::OCPX_ACTION_TYPE_PAID => "付费",
        self::OCPX_ACTION_TYPE_ROI => "首日ROI",
        self::OCPX_ACTION_TYPE_EFFECTIVE_CLUE => "有效线索",
        self::OCPX_ACTION_TYPE_CREDIT => "授信",
        self::OCPX_ACTION_TYPE_FINISH => "完件",
        self::OCPX_ACTION_TYPE_WECHAT_COPY => "微信复制",
    ];

    const OCPX_ACTION_TYPE_CREATE_ENUMS = [
        self::OCPX_ACTION_TYPE_CLUE         ,
        self::OCPX_ACTION_TYPE_ACTIVATION   ,
        self::OCPX_ACTION_TYPE_PAID         ,
    ];

}