<?php

class Qdlib_Const_ChannelMap
{
    const GDT     = 'gdt';
    const HSYM    = 'hs';
    const QDKJ    = 'qdkj';

    const QIMAI    = 'qimai';
    const DINGDAN    = 'dingdan';
    const XINGYE    = 'xingye';
    const ASZS    = 'aszs';

    const GDTGJ   = 'gdtgj';
    const GDTUNIT = 'gdtunit';
    const PYQ     = 'pyq';
    const JRTT    = 'jrtt';
    const XTWF    = 'xtwf';
    const DYQYH   = 'dyqyh';
    const JRTTGJ  = 'jrttgj';
    const QIHUGJ  = '360gj';
    const BDGJ    = 'bdgj';
    const BDXXLGJ = 'bdxxlgj';
    const HONOR = 'honor';

    const QIHU     = '360';
    const BD       = 'bd';
    const BDXXL    = 'bdxxl';
    const BDXXLZC  = 'bdxxlzc'; // 百度观星盘数据资产，只用于dmp推送
    const MEITUAN  = 'mtdp';
    const KUAISHOU = 'kuaishou';
    const KUAISHOUGJ = 'kuaishougj';
    const CILIJINNIU = 'cljn';
    const HUAWEISD = 'huaweisd';
    const HUAWEIPUSH = 'huaweipush';
    const WYYD     = 'wyyd';
    const WANGYIYD     = 'wangyiyd';

    const UC         = 'uc';
    const WANGYI     = 'wy';
    const ZHIHU      = 'zh';
    const LH         = 'lh';
    const PDD        = 'pdd';
    const DVD        = 'dvd';
    const DLJ        = 'dlj';
    const MMXX       = 'mmxx';
    const HQBS       = 'hqbs';
    const YJ         = 'yj';
    const JSYD       = 'jsyd';
    const HZLS       = 'hzls';
    const YZ         = 'yz';
    const SHPW       = 'shpw';
    const TM         = 'tm';
    const TXY        = 'txy';
    const JD         = 'jd';
    const XMLA       = 'xmla';
    const SHMS       = 'shms';
    const XTC        = 'xtc';
    const VIVO       = 'vivo';
    const OPPO       = 'oppo';
    const SG         = 'sg';
    const SM         = 'sm';
    const HDGG       = 'hdgg';
    const ONLINE     = 'online';
    const OFFLINE    = 'offline';
    const XHS        = 'xhs';
    const XHSAPP        = 'xhsapp';
    const XHSAPPGJ      = 'xhsappgj';
    const XHSSM        = 'xhssm';
    const CLJXCPA        = 'cljxcpa'; //快手磁力聚星
    const CLJXSM        = 'cljxsm'; //快手磁力聚星
    const XHSSMGJ      = 'xhssmgj';
    const QMM        = 'qmm';
    const SK         = 'sk';
    const ZFB        = 'zfb';
    const WXLB       = 'wxlb';
    const AQY        = 'aqy';
    const AQYGJ      = 'aqygj';
    const WPS        = 'wps';
    const HHSQ       = 'hhsq';
    const RWTF       = 'rwtf';
    const BJXZQ      = 'bjxzq';
    const XIAOMI     = 'xiaomi';
    const SP         = 'sp';
    const DX         = 'dx';
    const BMCG       = 'bmcg';
    const ZFCG       = 'zfcg';
    const XCXZFCG    = 'xcxzfcg';
    const WXPAY      = 'wxpay';
    const SYTC       = 'sytc';
    const BBS        = 'bbs';
    const SQ         = 'sq';
    const APP        = 'app';
    const PFXYK      = 'pfxyk';
    const HUAWEI     = 'huawei';
    const APPSTORE   = 'appstore';
    const MEIZU      = 'meizu';
    const YYB        = 'yyb';
    const BDZLHX     = 'bdzlhx';
    const BDQSX      = 'bdqsx';
    const SUNING     = 'suning';
    const GZH        = 'gzh';
    const YUANDING   = 'yuanding';
    const HSDZJJ     = 'hsdzjj';
    const HBKY       = 'hbky';
    const XDZJ       = 'xdzj';
    const XHLY       = 'xhly';
    const BILIBILI   = 'bilibili';
    const WXW        = 'wxw';
    const HMXX       = 'hmxx';
    const GMTS       = 'gmts';
    const YXC        = 'yxc';
    const XRKMM      = 'xrkmm';
    const AIYUE      = 'aiyue';
    const SHXT       = 'shxt';
    const XK101      = 'xk101';
    const DDXT       = 'ddxt';
    const XHJX       = 'xhjx';
    const YKK        = 'ykk';
    const YIZHONG    = 'yizhong';
    const QZZM       = 'qzzm';
    const WKQX       = 'wkqx';
    const KBK        = 'kbk';
    const XZHK       = 'xzhk';
    const XGX        = 'xgx';
    const SHZS       = 'shzs';
    const KXYB       = 'kxyb';
    const SDBK       = 'sdbk';
    const XZWH       = 'xzwh';
    const ZXQZ       = 'zxqz';
    const FANLIAPP   = 'fanliapp';
    const WXXS       = 'wxxs';
    const DBYX       = 'dbyx';
    const XMXQ       = 'xmxq';
    const LMSH       = 'lmsh';
    const TANSUOJIA  = 'tansuojia';
    const ZQBM       = 'zqbm';
    const YHCQZT     = 'yhcqzt';
    const BXYJY      = 'bxyjy';
    const KAODAOWANG = 'kaodaowang';
    const MMXP       = 'mmxp';
    const WLSZ       = 'wlsz';
    const XLKP       = 'xlkp';
    const SMZDM      = 'smzdm';
    const INMOBI     = 'inmobi';
    const YOUJU      = 'youju';
    const QMSJ       = 'qmsj';
    const HESHENG    = 'hesheng';
    const YOUMI      = 'youmi';
    const FANZHUO    = 'fanzhuo';
    const BMYX       = 'bmyx';
    const FJYL       = 'fjyl';
    const YSJZQ      = 'ysjzq';
    const DXXK       = 'dxxk';
    const KTT        = 'ktt';
    const CCKT       = 'cckt';
    const YMB        = 'ymb';
    const ZYHK       = 'zyhk';
    const BCSX       = 'bcsx';
    const FQABC      = 'fqabc';
    const YDMM       = 'ydmm';
    const GQTFT      = 'gqtft';
    const XMKJ       = 'xmkj';
    const SDKY       = 'sdky';
    const SCQX       = 'scqx';
    const QSZJ       = 'qszj';
    const CSKX       = 'cskx';
    const XALFS      = 'xalfs';
    const NJBS       = 'njbs';
    const ZCZX       = 'zczx';
    const WOCHENG    = 'wocheng';
    const YIDU       = 'yidu';
    const XDJY       = 'xdjy';
    const DYXD       = 'dyxd';

    const CSZG     = 'cszg';
    const MDYX     = 'mdyx';
    const QZSTEAM  = 'qzsteam';
    const YXJYZSC  = 'yxjyzsc';
    const ZKSC     = 'zksc';
    const ZWX      = 'zwx';
    const QZZXSE   = 'qzzxse';
    const BMHKYX   = 'bmhkyx';
    const TXCCM    = 'txccm';
    const YKZAN    = 'ykzan';
    const HSRJ     = 'hsrj';
    const MMPD     = 'mmpd';
    const MLMMZX   = 'mlmmzx';
    const CYTS     = 'cyts';
    const BQZX     = 'bqzx';
    const JXKANG   = 'jxkang';
    const XQZX     = 'xqzx';
    const BJZYUE   = 'bjzyue';
    const WHBOYA   = 'whboya';
    const AIXUEBA  = 'aixueba';
    const XNHWU    = 'xnhwu';
    const CAIBEIKE = 'caibeike';
    const CTJZXY   = 'ctjzxy';
    const CMZX     = 'cmzx';
    const PINGJIAN = 'pingjian';
    const XMGH     = 'xmgh';
    const HXHR     = 'hxhr';
    const ZHENYU   = 'zhenyu';
    const XZBAO    = 'xzbao';
    const TLYX     = 'tlyx';
    const MRFX     = 'mrfx';
    const XCXMR    = 'xcxmr';
    const ZYHYZX   = 'zyhyzx';
    const SRSM     = 'srsm';
    const LYZX     = 'lyzx';
    const LMHD     = 'lmhd';
    const NMYZ     = 'nmyz';
    const SDJY     = 'sdjy';
    const XBYJY    = 'xbyjy';
    const CXSHE    = 'cxshe';
    const BMSHUO   = 'bmshuo';
    const XXHK     = 'xxhk';
    const TXWS     = 'txws';
    const FJAFD    = 'fjafd';
    const SSKE     = 'sske';
    const TX365    = 'tx365';
    const ZHWH     = 'zhwh';
    const NMGHC    = 'nmghc';
    const SCJY     = 'scjy';
    const WQJY     = 'wqjy';
    const SSYK     = 'ssyk';
    const QLXF     = 'qlxf';
    const CTXX     = 'ctxx';
    const HKY      = 'hky';
    const HJHY     = 'hjhy';
    const NIWOXIAO = 'niwoxiao';
    const ZHANGXIN = 'zhangxin';
    const JFWH     = 'jfwh';
    const YRZX     = 'yrzx';
    const MUYUZ    = 'muyuz';
    const MKMK     = 'mkmk';
    const WENJUANW = 'wenjuanw';
    const CYKJ     = 'cykj';
    const MYXK     = 'myxk';
    const MZJY     = 'mzjy';
    const XUEHAOW  = 'xuehaow';
    const YUNYI    = 'yunyi';
    const TXYINYUE = 'txyinyue';
    const XNHK     = 'xnhk';
    const YOUZAN   = 'youzan';
    const MMJ      = 'mmj';
    const XRKQZ    = 'xrkqz';
    const XZY      = 'xzy';
    const YOUQF    = 'youqf';
    const HTPF     = 'htpf';
    const RYSH     = 'rysh';
    const CWJY     = 'cwjy';
    const PSZX     = 'pszx';
    const JUGAO    = 'jugao';
    const XDD      = 'xdd';
    const FCWL     = 'fcwl';
    const TUIA     = 'tuia';
    const SHLZ     = 'shlz';
    const NLKJ     = 'nlkj';

    const DMZZ     = 'dmzz';
    const WXWL     = 'wxwl';
    const DTWL     = 'dtwl';
    const PYYD     = 'pyyd';
    const HGWH     = 'hgwh';
    const SXUEE    = 'sxuee';
    const JXHK     = 'jxhk';
    const HAOXUEZX = 'haoxuezx';
    const PEIYOUKT = 'peiyoukt';
    const TXHK     = 'txhk';
    const WEILAIZX = 'weilaizx';
    const QSXK     = 'qsxk';
    const YOUHAOK  = 'youhaok';
    const JCWEILAI = 'jcweilai';
    const XKZX     = 'xkzx';
    const QJZX     = 'qjzx';
    const YYZX     = 'yyzx';
    const HJIALAN  = 'hjialan';
    const ZHIJIAGE = 'zhijiage';
    const OUHEJY   = 'ouhejy';
    const ZXTS     = 'zxts';
    const ZHIYUAN  = 'zhiyuan';
    const HHMT     = 'hhmt';
    const JWZX     = 'jwzx';
    const MOLIL    = 'molil';
    const FABEN    = 'faben';
    const SDONG    = 'sdong';
    const XHSD     = 'xhsd';
    const ZHAOSUW  = 'zhaosuw';
    const SHYK     = 'shyk';
    const JJKJ     = 'jjkj';
    const JZYX     = 'jzyx';
    const SHOUPIN  = 'shoupin';
    const GZGW     = 'gzgw';
    const DOUYIN   = 'douyin';
    const WEIXIN   = 'weixin';
    const KRW      = 'krw';
    const BXM      = 'bxm';
    const CAT      = 'cat';
    const ASA      = 'asa';
    const ASAGJ    = 'asagj';
    const FYXD     = 'fyxd';
    const DYXCX    = 'dyxcx';
    const JLQCDP   = 'jlqcdp';    //千川店铺
    const JLQCGJ   = 'jlqcgj';    //千川管家
    const JLQCUSER = 'jlqcuser';    //千川登录用户，暂时记录，未使用
    const JLQCZH   = 'jlqczh';    //千川账户
    const WEIBO    = 'xlwb';      //新浪微博
    const WEIBOGJ  = 'xlwbgj';    //新浪微博管家
    const TFQATEST = 'tfqatest';
    const BDXD     = 'bdxd'; // 百度小店
    const BDT      = 'bdt'; //本地推
    const WEIBOAPP    = 'xlwbapp';      //新浪微博app
    const WEIBOAPPGJ  = 'xlwbappgj';    //新浪微博app管家 (即微博的：开发者申请的应用APP_ID)

    // 广点通应用配置
    static $gdtDeveloper = [
        'client_id' => 1107765440,
        'client_secret' => 'LrsoBGnj9yBEn0fp',
//        'redirect_uri' => 'https://www.zuoyebang.com/qdapi/oauth/gdtcallback',
        'redirect_uri' => self::Redirect_URL.'gdtcallback',
    ];

    //朋友圈
    static public $pyqDeveloper = array(
        'appId' => '',
        'appSecret' => '',

    );
    const Redirect_URL = "https://tf.zuoyebang.com/qdapi/oauth/";
    //今日头条
    static public $jrttDeveloper = array(
        'uri' => 'http://ad.oceanengine.com/openapi/audit/oauth.html?app_id=%s&agent=%s&state=%s&scope=[1, 2, 3, 4, 5,501]&material_auth=1&redirect_uri=%s',
        'appId' => ****************,
        'appSecret' => '1e17336ec26c4c7ad5784cca8d3d97e1f8e0abd8',
        'redirectUri' => self::Redirect_URL.'jrttcallback',
    );

    static public $jrttTest = array(
        'uri' => 'http://%s/qdmis/test/jrttoauth?app_id=%s&agent=%s&state=%s&scope=[1, 2, 3, 4, 5]&redirect_uri=%s',
        'appId' => ****************,
        'appSecret' => '1e17336ec26c4c7ad5784cca8d3d97e1f8e0abd8',
        'redirectUri' => 'https://%s/qdapi/oauth/jrttcallback'
    );

    static public $xhsAppDeveloper = array(
        'uri' => 'https://ad-market.xiaohongshu.com/auth?appId=%s&state=%s&scope=["report_service","ad_query","ad_manage","account_manage"]&redirectUri=%s',
        'redirectUri' => self::Redirect_URL.'xhsappcallback',
    );

    static public $xhsAppDeveloperTest = array(
        'uri' => 'https://ad-market.xiaohongshu.com/auth?appId=%s&state=%s&scope=["report_service","ad_query","ad_manage","account_manage"]&redirectUri=%s',
        'redirectUri' => 'https://c4-tf-wxf-e.suanshubang.com/qdapi/oauth/xhsappcallback',
    );

    static public $xhsSmDeveloper = array(
        'uri' => 'https://ad-market.xiaohongshu.com/auth?appId=%s&state=%s&scope=["report_service","ad_query","ad_manage","account_manage"]&redirectUri=%s',
        'redirectUri' => self::Redirect_URL.'xhssmcallback',
    );

    static public $xhsSmDeveloperTest = array(
        'uri' => 'https://ad-market.xiaohongshu.com/auth?appId=%s&state=%s&scope=["report_service","ad_query","ad_manage","account_manage"]&redirectUri=%s',
        'redirectUri' => 'https://c4-tf-bilibili-e.suanshubang.com/qdapi/oauth/xhssmcallback',
    );

    static public $huaweiDeveloper = array(
        'uri' => 'https://oauth-login.cloud.huawei.com/oauth2/v2/authorize?response_type=code&scope=https://www.huawei.com/auth/account/base.profile https://ads.cloud.huawei.com/report https://ads.cloud.huawei.com/promotion https://ads.cloud.huawei.com/tools https://ads.cloud.huawei.com/account&access_type=offline&client_id=%s&redirect_uri=%s&state=%s',
        'redirectUri' => self::Redirect_URL.'huaweicallback',
    );

    static public $huaweiDeveloperTest = array(
        'uri' => 'https://oauth-login.cloud.huawei.com/oauth2/v2/authorize?response_type=code&scope=https://www.huawei.com/auth/account/base.profile https://ads.cloud.huawei.com/report https://ads.cloud.huawei.com/promotion https://ads.cloud.huawei.com/tools https://ads.cloud.huawei.com/account&access_type=offline&client_id=%s&redirect_uri=%s&state=%s',
        'redirectUri' => 'https://www-bilibili-e.suanshubang.cc/qdapi/oauth/huaweicallback',
    );

    static public $cljxcpaDeveloper = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&scope=["select_service"]&redirect_uri=%s&state=%s&oauth_type=advertiser',
        'redirectUri' => self::Redirect_URL.'cljxcpacallback',
    );

    static public $cljxcpaDeveloperTest = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&scope=["select_service"]&redirect_uri=%s&state=%s&oauth_type=advertiser',
        'redirectUri' => 'https://www-bilibili-e.suanshubang.cc/qdapi/oauth/cljxcpacallback',
    );

    static public $cljxsmDeveloper = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&scope=["select_service"]&redirect_uri=%s&state=%s&oauth_type=%s',
        'redirectUri' => self::Redirect_URL.'cljxsmcallback',
    );

    static public $cljxsmDeveloperTest = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&scope=["select_service"]&redirect_uri=%s&state=%s&oauth_type=%s',
        'redirectUri' => 'https://www-bilibili-e.suanshubang.cc/qdapi/oauth/cljxsmcallback',
    );



    //星图
    static public $xtwfDeveloper = array(
        'uri' => 'http://ad.oceanengine.com/openapi/audit/oauth.html?app_id=%s&agent=%s&state=%s&scope=[1, 2, 3, 4, 5,501,10000000,11000000,8,9]&material_auth=1&redirect_uri=%s',
        'appId' => ****************,
        'appSecret' => '1e17336ec26c4c7ad5784cca8d3d97e1f8e0abd8',
        'redirectUri' => self::Redirect_URL.'jrttcallback',
    );
    static public $xtwfTest = array(
        'uri' => 'http://%s/qdmis/test/jrttoauth?app_id=%s&agent=%s&state=%s&scope=[1, 2, 3, 4, 5,501,10000000,11000000,8,9]&redirect_uri=%s',
        'appId' => ****************,
        'appSecret' => '1e17336ec26c4c7ad5784cca8d3d97e1f8e0abd8',
        'redirectUri' => 'https://%s/qdapi/oauth/jrttcallback'
    );

    // 巨量千川店铺/巨量千川管家（https://open.oceanengine.com/labels/7/docs/1803016515293203?origin=left_nav）
    static public $jlqcdpDeveloper = array(
        'uri' => 'https://qianchuan.jinritemai.com/openapi/qc/audit/oauth.html?app_id=%s&agent=%s&state=%s&scope=[24000000,21000000,20100000,23000000,25000000,22100300,22101100,22100400,22100200,20120000,22101000,22100100,22101200,200000011]&material_auth=1&redirect_uri=%s&rid=3dpy61yuxpu',
        'appId' => 1764436033743924,
        'appSecret' => '27199b3a43ccc397f6efae565033da726a57e88d',
        'redirectUri' => self::Redirect_URL.'jlqccallback',
    );
    static public $jlqcdpTest = array(
        'uri' => 'https://qianchuan.jinritemai.com/openapi/qc/audit/oauth.html?app_id=%s&agent=%s&state=%s&scope=[24000000,21000000,20100000,23000000,25000000,22100300,22101100,22100400,22100200,20120000,22101000,22100100,22101200,200000011]&material_auth=1&redirect_uri=%s&rid=3dpy61yuxpu',
        'appId' => 1764436033743924,
        'appSecret' => '27199b3a43ccc397f6efae565033da726a57e88d',
        'redirectUri' => 'https://tf-work996-e.suanshubang.com/qdapi/oauth/jlqccallback',
    );

    //百度信息流
    static public $bdDeveloper = array(
        'uri' => 'https://u.baidu.com/oauth/page/index?platformId=4960345965958561794&appId=%s&agent=%s&scope=65,66,67,68,69,70,73,71,72,74,75,1001788,1001789,1001455,1001790,1001791,1002161,1004606,1002829&state=%s&callback=%s',
        'appId' => '4d7bcb68a378e3e2bc56114b00155043',
        'secretKey' => '4008b6e5e6a44b561eeb80302ff4e307',
        'redirectUri' => self::Redirect_URL.'bdcallback',
    );

    static public $bdTest = array(
        'uri' => 'https://u.baidu.com/oauth/page/index?platformId=4960345965958561794&appId=%s&agent=%s&scope=65,66,67,68,69,70,73,71,72,74,75,1001788,1001789,1001455,1001790,1001791,1002161,1004606,1002829&state=%s&callback=%s',
        'appId' => 'e521bc36521df1d4e8fbcbce56d763ea',
        'secretKey' => '2825ab285b68009bef82b62c50f42f8e',
        'redirectUri' =>'http://c3-tf-ljl-e.suanshubang.com/qdapi/oauth/bdcallback',
    );

    // 百度搜索
    const BdAppIDDeveloper = 'f9c2cd43617c06b0f4bc139ec292ba7f';
    static public $bdssDeveloper = array(
        'uri' => 'https://u.baidu.com/oauth/page/index?platformId=4960345965958561794&appId=%s&agent=%s&scope=65,66,67,68,69,70,73,71,72,74,75,1001788,1001789,1001455,1001790,1001791,1002161,1004606,1002829&state=%s&callback=%s',
        'appId' => self::BdAppIDDeveloper,
        'secretKey' => '701ab1a22df001355c8c8bcd343fa0c8',
        'redirectUri' => self::Redirect_URL.'bdcallback',
    );
    const BdAppIDTest = '55eabc7a03a5a313a95ef4e82c12eb11';
    static public $bdssTest = array(
        'uri' => 'https://u.baidu.com/oauth/page/index?platformId=4960345965958561794&appId=%s&agent=%s&scope=65,66,67,68,69,70,73,71,72,74,75,1001788,1001789,1001455,1001790,1001791,1002161,1004606,1002829&state=%s&callback=%s',
        'appId' => self::BdAppIDTest,
        'secretKey' => 'dfb867e0439c8b237e2f7c713a75a4a8',
        'redirectUri' =>'https://c3-tf-talentgz-e.suanshubang.com/qdapi/oauth/bdcallback',
    );


    static public $gdtTest = array(
        'uri' => 'http://%s/qdmis/test/pyqgdtoauth?app_id=%s&agent=%s&state=%s&scope=[1, 2, 3, 4, 5]&redirect_uri=%s',
        'appId' => ****************,
        'appSecret' => '1e17336ec26c4c7ad5784cca8d3d97e1f8e0abd8',
        'redirectUri' => 'https://%s/qdapi/oauth/gdtcallback'
    );

    static public $meituanDeveloper = array(
        'uri' => 'https://e.dianping.com/dz-open/merchant/auth?app_key=%s&agent=%s&state=%s&redirect_url=%s',
        'app_key' => '3c6400f8ea20dae7',
        'app_secret' => '2358a7cc1604e03aad1cc7c92fb553fba9288e4f',
//        'redirectUri' => 'https://www.zuoyebang.com/qdapi/oauth/meituancallback',
        'redirectUri' => self::Redirect_URL.'meituancallback',
    );
    static public $tmDeveloper = array(
        'uri' => 'https://oauth.taobao.com/authorize?response_type=code&client_id=%s&state=%s&redirect_uri=%s&view=%s',
        'app_key' => '********',
        'client_id' => '********',
        'app_secret' => 'c5b1ab9bee79f4316fde51d98366317b',
//        'redirectUri' => 'https://www.zuoyebang.com/qdapi/oauth/tmcallback',
        'redirectUri' => self::Redirect_URL.'tmcallback',
    );
    static public $weiboDev = array(
        'uri' => 'https://api.biz.weibo.com/oauth/authorize?client_id=%s&state=%s&scope=ads_management&response_type=code&redirect_uri=%s',
        'app_id' => '202305173389861100',
        'client_id' => '202305173389861100',
        'app_secret' => 'ef9759c43a0e34498e8c05ab98e67ae',
//        'redirectUri' => 'https://www.zuoyebang.com/qdapi/oauth/weibocallback',
        'redirectUri' => self::Redirect_URL.'weibocallback',
    );
    static public $weiboTest = array(
        'uri' => 'https://api.biz.weibo.com/oauth/authorize?client_id=%s&state=%s&scope=ads_management&response_type=code&redirect_uri=%s',
        'app_id' => '202305173389861100',
        'client_id' => '202305173389861100',
        'app_secret' => 'ef9759c43a0e34498e8c05ab98e67ae',
        'redirectUri' => 'https://c4-tf-xlwb-e.suanshubang.com/qdapi/oauth/weibocallback',
    );
    static public $weiboAppDev = array(
        'uri' => 'https://api.biz.weibo.com/oauth/authorize?client_id=%s&state=%s&scope=ads_management&response_type=code&redirect_uri=%s',
        'app_id' => '', // 从配置里取
        'client_id' => '',
        'app_secret' => '',
        'redirectUri' => self::Redirect_URL.'weiboappcallback',
    );
    static public $weiboAppTest = array(
        'uri' => 'https://api.biz.weibo.com/oauth/authorize?client_id=%s&state=%s&scope=ads_management&response_type=code&redirect_uri=%s',
        'app_id' => '', // 从配置里取
        'client_id' => '',
        'app_secret' => '',
        'redirectUri' => 'https://c4-tf-wxf-e.suanshubang.com/qdapi/oauth/weiboappcallback',
    );
    static public $aqyDev = array(
        'uri' => 'https://tuiguang.iqiyi.com/platform/authApp?app_id=%s&state=%s&scope=account,order,report,tool&redirect_uri=%s',
        'app_id' => '', // 从配置里取
        'client_id' => '',
        'app_secret' => '',
        'redirectUri' => self::Redirect_URL.'aqycallback',
    );
    static public $aqyDevTest = array(
        'uri' => 'https://tuiguang.iqiyi.com/platform/authApp?app_id=%s&state=%s&scope=account,order,report,tool&redirect_uri=%s',
        'app_id' => '', // 从配置里取
        'client_id' => '',
        'app_secret' => '',
        'redirectUri' => 'https://c4-tf-wxf-e.suanshubang.com/qdapi/oauth/aqycallback',
    );
    static public $tmTest = array(
        'uri' => 'http://oauth.daily.taobao.net/authorize?response_type=code&client_id=%s&state=%s&redirect_uri=%s&view=%s',
        'app_key' => '********',
        'client_id' => '********',
        'app_secret' => 'c5b1ab9bee79f4316fde51d98366317b',
//        'redirectUri' => 'http://zhqtf-docker.suanshubang.com/qdapi/oauth/tmcallback',
        'redirectUri' => self::Redirect_URL.'tmcallback',
    );

    static public $kuaishouDev = array(
        'uri' => 'https://ad.e.kuaishou.com/openapi/oauth?app_id=%s&agent=%s&state=%s&scope=%s&redirect_uri=%s',
        'app_id' => '517',
        'client_id' => '517',
        'app_secret' => '8pfc9PFr4Cp2Nw97',
//        'redirectUri' => 'https://www.zuoyebang.com/qdapi/oauth/kuaishoucallback',
        'redirectUri' => self::Redirect_URL.'kuaishoucallback',
    );

    static public $kuaishougjDev = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&agent=%s&state=%s&scope=%s&redirect_uri=%s&oauth_type=advertiser',
        'app_id' => '517',
        'client_id' => '517',
        'app_secret' => '8pfc9PFr4Cp2Nw97',
        'redirectUri' => self::Redirect_URL.'kuaishoucallback',
    );

    static public $cilijinniuTest = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&agent=%s&state=%s&scope=%s&redirect_uri=%s&oauth_type=advertiser',
        'app_id' => '165904926',
        'client_id' => '165904926',
        'app_secret' => 'E%870%yo-*2B2x42',
        'redirectUri' => 'http://c3-tf-nopanic-e.suanshubang.cc/qdapi/oauth/cilijinniucallback',
    );

    static public $cilijinniuDev = array(
        'uri' => 'https://developers.e.kuaishou.com/tools/authorize?app_id=%s&agent=%s&state=%s&scope=%s&redirect_uri=%s&oauth_type=advertiser',
        'app_id' => '165904926',
        'client_id' => '165904926',
        'app_secret' => 'E%870%yo-*2B2x42',
        'redirectUri' => self::Redirect_URL.'cilijinniucallback',
    );
    static public $vivoDev = array(
        'uri' => 'https://open-ad.vivo.com.cn/OAuth?clientId=%s&state=%s&redirectUri=%s',
        'clientId' => '20210707004',
        'clientSecret' => '96700947939D8D7B1E9F2837029264C92B51420104951CCA3EF70C6DA92348D4',
        'grantType' => 'code',
//        'redirectUri' => 'https://www.zuoyebang.com/qdapi/oauth/vivocallback',
        'redirectUri' => self::Redirect_URL.'vivocallback',
    );
    static public $vivoDevTest = array(
        'uri' => 'https://open-ad.vivo.com.cn/OAuth?clientId=%s&state=%s&redirectUri=%s',
        'clientId' => '20210531004',
        'clientSecret' => '5B3E56677AD3DDCFBAFC1014A81FF91C9CEAF57C2D731F800B76BD52B1AC9E50',
        'grantType' => 'code',
        'redirectUri' => 'https://test436.suanshubang.com/qdapi/oauth/vivocallback'
    );

    static public $channelMap = array(
        self::GDT => "广点通",
        self::HONOR => "荣耀",
        self::XTWF => "星图网服",
        self::DYQYH => "抖音企业号",
        self::GDTGJ => '广点通-管家',
        self::GDTUNIT => '广点通-业务单元',
        self::PYQ => "朋友圈",
        self::JRTT => "今日头条",
        self::QIHU => "360",
        self::BD => "百度搜索",
        self::BDXXL => "百度",
        self::WYYD => "网易有道",
        self::WANGYIYD => "网易有道",
        self::MEITUAN => '美团点评',
        self::UC => 'UC',
        self::ZHIHU => '知乎',
        self::WEIBO => '微博',
        self::WEIBOAPP => '微博APP',
        self::WEIBOAPPGJ => '微博APP-管家(APP_ID)',
        self::WANGYI => '网易',
        self::HUAWEISD => '华为商店',
        self::LH => '猎河',
        self::PDD => '拼多多',
        self::DVD => '大V店',
        self::DLJ => '达令家',
        self::MMXX => '妈妈心选',
        self::HQBS => '环球捕手',
        self::YJ => '云集',
        self::JSYD => '江苏移动',
        self::HZLS => '杭州罗实',
        self::YZ => '一招',
        self::SHPW => '上海普瓦',
        self::TM => '天猫',
        self::TXY => '腾讯云',
        self::JD => '京东',
        self::XMLA => '喜马拉雅',
        self::SHMS => '上海眸事',
        self::XTC => '小天才',
        self::VIVO => 'vivo',
        self::OPPO => 'oppo',
        self::SG => '搜狗',
        self::SM => '神马',
        self::HDGG => '互动广告',
        self::ONLINE => '线上',
        self::OFFLINE => '线下',
        self::XHS => '小红书',
        self::XHSAPPGJ => '小红书管家',
        self::XHSSM => '小红书社媒',
        self::CLJXCPA => '磁力聚星CPA',
        self::CLJXSM => '磁力聚星社媒',
        self::XHSSMGJ => '小红书社媒管家',
        self::QMM => '券妈妈',
        self::SK => '寺库',
        self::ZFB => '支付宝',
        self::WXLB => '微信裂变',
        self::AQY => '爱奇艺',
        self::AQYGJ => '爱奇艺管家',
        self::KUAISHOU => '快手',
        self::KUAISHOUGJ => '快手-管家',
        self::CILIJINNIU => '磁力金牛',
        self::WPS => 'wps',
        self::HHSQ => '呼呼省钱',
        self::RWTF => '软文投放',
        self::XIAOMI => '小米',
        self::BJXZQ => '北京行至全',
        self::SP => '视频',
        self::DX => '短信',
        self::BMCG => '报名成功',
        self::ZFCG => '支付成功',
        self::XCXZFCG => '小程序支付成功',
        self::WXPAY => '微信支付',
        self::SYTC => '首页弹窗',
        self::BBS => '宝宝树',
        self::SQ => '社群',
        self::APP => 'app',
        self::PFXYK => '浦发信用卡',
        self::HUAWEI => '华为',
        self::APPSTORE => 'appstore',
        self::MEIZU => '魅族',
        self::YYB => '应用宝',
        self::BDZLHX => '百度知了好学',
        self::BDQSX => '百度轻松学',
        self::SUNING => '苏宁',
        self::GZH => '公众号',
        self::YUANDING => '园钉',
        self::HSDZJJ => '花生电子竞技',
        self::HBKY => '河北快银',
        self::XDZJ => '小度在家',
        self::XHLY => '星火燎原',
        self::BILIBILI => 'B站',
        self::WXW => '微校网',
        self::HMXX => '虎妈心选',
        self::GMTS => '瓜满图书',
        self::YXC => '渝轩彩',
        self::XRKMM => '向日葵妈妈',
        self::AIYUE => '爱阅',
        self::SHXT => '三好学堂',
        self::XK101 => '选课101',
        self::DDXT => '叮当学堂',
        self::XHJX => '薪火精选',
        self::YKK => '优课库',
        self::YIZHONG => '易众',
        self::QZZM => '亲子周末',
        self::WKQX => '悟空晴选',
        self::KBK => '可比课',
        self::XZHK => '小猪好课',
        self::XGX => '习惯熊',
        self::SHZS => '上海钟书',
        self::KXYB => '开心壹佰',
        self::SDBK => '山东布克',
        self::XZWH => '讯之文化',
        self::ZXQZ => '掌心亲子',
        self::FANLIAPP => '返利APP',
        self::WXXS => '无限向溯',
        self::DBYX => '逗爸优选',
        self::XMXQ => '喜喵星球',
        self::LMSH => '六米生活',
        self::TANSUOJIA => '探索加',
        self::ZQBM => '蒸汽爸妈',
        self::YHCQZT => '萤火虫亲子淘',
        self::BXYJY => '广西贝佰相杨教育科技有限公司',
        self::KAODAOWANG => '考道网',
        self::MMXP => '妈妈小铺',
        self::WLSZ => '未来商智',
        self::XLKP => '星庐课评',
        self::SMZDM => '什么值得买',
        self::INMOBI => '畅思',
        self::YOUJU => '优聚',
        self::QMSJ => '七麦数据',
        self::HESHENG => '合声',
        self::YOUMI => '有米',
        self::FANZHUO => '凡卓',
        self::BMYX => '爸妈严选',
        self::FJYL => '福建银联',
        self::YSJZQ => '鱼sir家长圈',
        self::DXXK => '大熊选课',
        self::KTT => '课推推',
        self::CCKT => '橙才课堂',
        self::YMB => '幼咪宝',
        self::ZYHK => '状元好课',
        self::BCSX => '伯偲尚学',
        self::FQABC => '番茄ABC',
        self::YDMM => '雨点妈妈',
        self::GQTFT => '丰台共青团',
        self::XMKJ => '校萌科技',
        self::SDKY => '师德凯艺',
        self::SCQX => '四川千行',
        self::QSZJ => '轻松早教',
        self::CSKX => '城市酷选',
        self::XALFS => '西安立丰晟',
        self::NJBS => '南京斌晟',
        self::ZCZX => '庄城在线',
        self::DYXD => '抖音小店',
        self::XDJY => '溪达教育',
        self::WOCHENG => '沃橙',
        self::YIDU => '意读',
        self::CSZG => '城市之光',
        self::MDYX => '麦豆严选',
        self::QZSTEAM => '亲子steam',
        self::YXJYZSC => '育想家有赞商城',
        self::ZKSC => '尊课商城',
        self::FYXD => '枫叶小店',
        self::DYXCX => '抖音小程序',
        self::ZWX => '找我学',
        self::QZZXSE => '亲子这些事儿',
        self::BMHKYX => '爸妈好课优选',
        self::TXCCM => '腾讯乘车码',
        self::YKZAN => '云客赞',
        self::HSRJ => '花生日记',
        self::MMPD => '妈觅频道',
        self::MLMMZX => '麦荔妈妈在线',
        self::CYTS => '聪源图书',
        self::BQZX => '北青在线',
        self::JXKANG => '京小康',
        self::XQZX => '兴青在线',
        self::BJZYUE => '北京卓越',
        self::WHBOYA => '武汉博雅',
        self::AIXUEBA => '爱学霸',
        self::XNHWU => '犀牛好物',
        self::CAIBEIKE => '彩贝壳',
        self::CTJZXY => '春藤家长学院',
        self::CMZX => '超码在线',
        self::PINGJIAN => '平简',
        self::XMGH => '小马过河',
        self::HXHR => '华夏浩然',
        self::ZHENYU => '振宇',
        self::XZBAO => '行知宝',
        self::TLYX => '桃李云校',
        self::MRFX => '自然分享',
        self::XCXMR => '自然流量',
        self::ZYHYZX => '中友华宇在线',
        self::SRSM => '善若商贸',
        self::LYZX => '灵优智学',
        self::LMHD => '乐猫互动',
        self::NMYZ => '内蒙邮政',
        self::SDJY => '山东教育',
        self::XBYJY => '新博源教育',
        self::CXSHE => '尝鲜社',
        self::BMSHUO => '爸妈说',
        self::XXHK => '星选好课',
        self::TXWS => '腾讯微视',
        self::FJAFD => '福建艾孚达',
        self::SSKE => '上上课',
        self::TX365 => '童学365',
        self::ZHWH => '智恒文化',
        self::NMGHC => '内蒙古华辰',
        self::SCJY => '盛初教育',
        self::WQJY => '万擎教育',
        self::SSYK => '松鼠优课',
        self::QLXF => '全朗新风',
        self::CTXX => '宬桃信息',
        self::HKY => '获客易',
        self::HJHY => '黑竞互娱',
        self::NIWOXIAO => '你我校',
        self::ZHANGXIN => '掌心',
        self::JFWH => '疾风文化',
        self::YRZX => '悠苒在线',
        self::MUYUZ => '木语者',
        self::MKMK => '每刻美课',
        self::WENJUANW => '问卷网',
        self::CYKJ => '畅言科技',
        self::MYXK => '蚂蚁选课',
        self::MZJY => '铭征教育',
        self::XUEHAOW => '学好网',
        self::YUNYI => '云旖',
        self::TXYINYUE => '腾讯音乐',
        self::XNHK => '犀牛好课',
        self::YOUZAN => '有赞商城',
        self::MMJ => '喵喵机',
        self::XRKQZ => '向日葵亲子平台',
        self::XZY => '学之优',
        self::FCWL => '富辰网络',
        self::YOUQF => '佑前方',
        self::HTPF => '惠同普方',
        self::RYSH => '瑞亿生活',
        self::CWJY => '椿维教育',
        self::PSZX => '普思在线',
        self::JUGAO => '聚告',
        self::XDD => '星朵朵',
        self::TUIA => '推啊',
        self::SHLZ => '上海力舟',
        self::NLKJ => '宁陆科技',
        self::DMZZ  => '多么字在',
        self::WXWL  => '万学网络',
        self::DTWL  => '大唐网络',
        self::PYYD  => '便宜一点',
        self::HGWH  => '怀古文化',
        self::SXUEE  => '升学E',
        self::JXHK  => '精选好课',
        self::JRTTGJ => '今日头条-管家',
        self::HAOXUEZX  => '好学在线',
        self::PEIYOUKT  => '培优课堂',
        self::TXHK  => '同学好课',
        self::WEILAIZX  => '未来在线',
        self::QSXK  => '轻松选课',
        self::YOUHAOK  => '有好课',
        self::JCWEILAI  => '杰创未来',
        self::XKZX  => '星空在线',
        self::QJZX  => '旗舰在线',
        self::YYZX  => '远洋在线',
        self::HJIALAN  => '皇家蓝',
        self::ZHIJIAGE  => '芝加哥',
        self::OUHEJY  => '欧合教育',
        self::ZXTS  => '志学图书',
        self::ZHIYUAN => '志远',
        self::HHMT => '辉煌明天',
        self::JWZX => '九五众信',
        self::MOLIL => '魔力龙',
        self::FABEN => '法本',
        self::SDONG => '声洞',
        self::XHSD => '新华书店',
        self::ZHAOSUW => '兆速网',
        self::SHYK => '上海游昆',
        self::JJKJ => '嘉佳科技',
        self::JZYX => '家长优学',
        self::SHOUPIN => '首聘',
        self::GZGW => '广州够玩',
        self::DOUYIN => '抖音',
        self::WEIXIN => '微信',
        self::KRW => '快任务',
        self::BXM => '变现猫',
        self::ASA => 'ASA',
        self::ASAGJ => 'ASA管家',
        self::BDGJ => '百度搜索-管家',
        self::BDXXLGJ => '百度-管家',
    );

    public static function getChannelMap($channel = '')
    {
        if(!empty($channel)){
            return Qdlib_Const_LastFrom::$lv3_names['channel'];
        }
        return Qdlib_Const_LastFrom::$lv3_names;
    }

    static public $formMap = array(
        "yk_xxl" => "一课信息流",
        "yk_pz" => "一课品专"
    );
    static public $pyqNativeAccountMap = array(
        'wx04c3353e6a1d52f6' => 'a799dc92082fb1cb3b5ba8b391c257ca',
        'wxe2395c0bd7c881ee' => 'c6550dbc6590b5f357a8a97e682db1cd',
        'wx2a3797bc617b636f' => '42701b2ae94a992fbaacdc3cc7e587c7',
        'wx56d19e00c4ebe11c' => '4add4505eac8aab68451bb5e2368f3ab',
        'wxad332a89218f4fac' => '4926dfe9a5bf53dccfd0e46e7700ea39',
        //'wx8c5d23bba559e37d' => '13b5852f4f977df617ec941a9843d383',
        //   'wxde67e492a5b52cfd' => '182dd13fff3f71b3b06e9fb1a1334280'
        //   'wx25df3ba4e81e01c3' => 'e523387767f6f389105789f4cbb5870b',
        'wx66419d8c4001d35f' => 'ee42b0080f1cad695f01df574bda9efd',
    );
    static public $pyqNativePromotePlan = [
        'wx04c3353e6a1d52f6' => 'yk_xxl_pyq_ykcz_01',
        'wxe2395c0bd7c881ee' => 'yk_xxl_pyq_yk_01',
        'wx2a3797bc617b636f' => 'yk_xxl_pyq_gh_01',//'weixin_pyqgh',
        'wx56d19e00c4ebe11c' => 'yk_xxl_pyq_srcz_01',
        'wxad332a89218f4fac' => 'yk_xxl_pyq_sr_01',
        // 'wx8c5d23bba559e37d' => 'yk_xxl_pyq_srsnb',//'weixin_pyqsrsnb',
        // 'wxde67e492a5b52cfd' => 'weixin_pyqsrcz'
        // 'wx25df3ba4e81e01c3' => 'weixin_ykqwkt',
        'wx66419d8c4001d35f' => 'yk_xxl_pyq_jgcz_01',

    ];

    /*
     * todo asa管家账号的clientSecret
     */
    static public $asaAccountClientSecret = [
        "2673470" => "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImRjN2ZkNzlmLTBmYmEtNDA1Yy1hMzgzLTMzYzA4MTc5MDI3ZiJ9.***********************************************************************************************************************************************************************************************************************************************.kuV08BnY51nTGqrkHVY-sWHZz85CjL1nEGt-IIsaVWmv5vP4psC-gph7qXI7RwBw7YIZlSuT6-2v8KP_WMjaFQ",
        "2677420" => "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjkwOWY2ZmNiLTQ0YjEtNGJiZS04YzA3LTEwNDc5YjNmYjNhNiJ9.***********************************************************************************************************************************************************************************************************************************************.myfgQJq-CliVNnO-MGMxKWa-FuV7eDxj6g6FhhxS2X9pHiX2o7veBQVVLkcYGGMbzLZqBn8AaehBf28_UoaSpA",
        "2678800" => "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhiZjIwZDFjLTIwZmEtNDIyNS05YmZmLWZmZWRkMjE1MmI2ZiJ9.***********************************************************************************************************************************************************************************************************************************************.oBlK2IkJNWTOAtWyWSpBFE2ZrghBbSlsKIPmd2Rq7E8TD2j2ruGFJ1D5bJNAUiC8yjCJuhq1-b0cXkjkG5zndw",
    ];

    /*
     * todo asa管家账号的clientId、keyId、teamId
     */
    static public $asaClientId = [
        "2673470" => [
            "clientId" => "SEARCHADS.c436c56c-dcdd-44aa-933a-460889389f1a",
            "teamId" => "SEARCHADS.c436c56c-dcdd-44aa-933a-460889389f1a",
            "keyId" => "dc7fd79f-0fba-405c-a383-33c08179027f",
        ],
        "2677420" => [
            "clientId"  => "SEARCHADS.1239d192-7f19-45f0-adb8-eb9a503bd96d",
            "teamId" => "SEARCHADS.1239d192-7f19-45f0-adb8-eb9a503bd96d",
            "keyId" => "909f6fcb-44b1-4bbe-8c07-10479b3fb3a6",
        ],
        "2678800" => [
            "clientId"  => "SEARCHADS.bd227d26-1a2d-4c5e-8ef8-1cbc431c6458",
            "teamId" => "SEARCHADS.bd227d26-1a2d-4c5e-8ef8-1cbc431c6458",
            "keyId" => "8bf20d1c-20fa-4225-9bff-ffedd2152b6f",
        ],
    ];

    public static function getAsaClientSecret($account)
    {

        if (empty($account)) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::PARAM_ERROR, "account");
        }
        if (empty(self::$asaAccountClientSecret[$account]) || empty(self::$asaClientId[$account])) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::PARAM_ERROR, "未定义父账号account");
        }

        return [
            "clientId" => self::$asaClientId[$account]['clientId'],
            "teamId" => self::$asaClientId[$account]['teamId'],
            "keyId" => self::$asaClientId[$account]['keyId'],
            "clientSecret" => self::$asaAccountClientSecret[$account],
        ];
    }

    static public $monitorChannel = [
        self::PYQ => [
            'cate_lv1s' => ['yk_xxl_pyq'],
            'name' => '朋友圈',
            'is_monitor' => 1,
        ],
        self::JRTT => [
            'cate_lv1s' => ['yk_xxl_jrtt'],
            'name' => '今日头条',
            'is_monitor' => 1,
        ]
    ];

    static public $batchCreateChannel = [
        self::KUAISHOU,
        self::JRTT,
        self::GDT,
    ];

    //不仅仅是管家、子账号概念。业务逻辑是直接拼接管家标识 +gj，需小心有特殊情况
    static public $childAccount = [
        self::GDT,
        self::JRTT,
        self::QIHU,
        self::ASA,
        self::BD,
        self::BDXXL,
    ];

    //子账户对应的管家或者店铺，可取代上方的业务逻辑侵入
    static public $childAccountMap = [
        self::GDT    => self::GDTUNIT,
        self::JRTT   => self::JRTTGJ,
        self::QIHU   => self::QIHUGJ,
        self::ASA    => self::ASAGJ,
        self::BD     => self::BDGJ,
        self::BDXXL  => self::BDXXLGJ,
        self::JLQCZH => self::JLQCGJ,   //巨量千川管家
        self::WEIBO  => self::WEIBOGJ,
        self::WEIBOAPP  => self::WEIBOAPPGJ, // 微博app管家（开发者申请的应用APP_ID）
        self::AQY    => self::AQYGJ, // 爱奇艺管家 AppId
        self::XHSAPP    => self::XHSAPPGJ, // 小红书管家 AppId
        self::XHSSM    => self::XHSSMGJ, // 小红书社媒管家 AppId
        self::KUAISHOU => self::KUAISHOUGJ,
    ];

    // 一个子账号渠道对应多个管家渠道的情况
    static public $childAccountMutiMajorMap = [
        self::GDT => [self::GDTUNIT, self::GDTGJ], // 广点通
        self::JLQCZH => [self::JLQCGJ, self::JLQCDP],   // 巨量千川
    ];

    static public $gjAccount = [
        self::JRTTGJ,
        self::GDTGJ,
        self::GDTUNIT,
        self::QIHUGJ,
        self::ASAGJ,
        self::BDXXLGJ,
        self::BDGJ,
        self::JLQCDP,
        self::JLQCGJ,
        self::WEIBOGJ,
        self::WEIBOAPPGJ,
        self::AQYGJ,
        self::XHSAPPGJ,
        self::XHSSMGJ,
        self::KUAISHOUGJ,
    ];

    public static function getGjAccount()
    {
        $ret = [];
        foreach (self::$gjAccount as $item) {
            $ret[$item] = Qdlib_Const_LastFrom::$lv3_names[$item];
        }
        return $ret;
    }

    // 千川店铺/管家 account_role 枚举 , 即接口 https://open.oceanengine.com/labels/12/docs/****************?origin=left_nav 返回值的account_role枚举
    const qcAccountRoleDp = 'PLATFORM_ROLE_SHOP_ACCOUNT';  // 店铺账户
    const qcAccountRoleAdminGj = 'CUSTOMER_ADMIN';  // 管理员授权的纵横组织
    const qcAccountRoleOperatorGj = 'CUSTOMER_OPERATOR';  // 协作者授权的纵横组织

    static public $deliveryType = [
        "install" => "拉新",
        "activity" => "拉活",
        "unknown" => "未知",
    ];
    static public $deliveryTeam = [
        "app" => "APP投放",
        "social" => "社媒投放",
    ];
}