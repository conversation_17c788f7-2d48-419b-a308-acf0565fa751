<?php

class Qdlib_Const_ProjectAttr
{
    //学部
    const PRIMARY = 1;
    const JUNIORHIGH = 20;
    const HIGH = 30;
    const GRADESECTION_KINDERGARTEB = 60;
    const ADULT = 70;
    const FACULTY_OTHER = 255; //其他

    const YK = 'yk';
    const GZSC = 'gzsc';
    const CZXB = 'czxb';
    const XXXB = 'xxxb';
    const BBSZ = 'bbsz';

    const JF = 'jf';
    const YJ = 'yj';
    const GX = 'gx';

    const HM = 'hm';
    const DYYW = 'dyyw';
    const DYYY = 'dyyy';
    const DYXZ = 'dyxz';
    const TFDYSW = 'tfdysw';
    const TFYYDY = 'tfyydy';
    const TFBBVIP = 'tfbbvip';//帮帮vip

    //成人  -  成人
    const TFCRYY = 'tfcryy';//成人教育-实用英语
    const TFCRCK = 'tfcrck';//成人教育-财会
    const TFCRJS = 'tfcrjs';//成人教育-教师资格证
    const TFCRGK = 'tfcrgk';//成人教育-公考
    const TFCRKY = 'tfcrky';//成人教育-考研
    const TFCRZJ = 'tfcrzj';//成人教育-职业教育

    //商品类型
    const GOOD_TYPE_COURSE      = 1; //课程
    const GOOD_TYPE_INVENTED    = 2; //虚拟
    const GOOD_TYPE_MATERIAL    = 3; //实物


    public static $attrSubject = [
        //雨燕后台有英文映射 20200908
        'sub_math' => '数学',
        'sub_chinese' => '语文',
        'sub_english' => '英语',
        'sub_physics' => '物理',
        'sub_chemistry' => '化学',
        'sub_biology' => '生物',
        7 => '政治',
        8 => '历史',
        9 => '地理',
        10 => '兴趣课',//直播课使用
        11 => '思想品德',//直播课使用
        12 => '讲座',//直播课使用
        13 => '理综',//试卷用
        14 => '文综',//试卷用
        15 => '奥数',
        16 => '科学',

        //20201012
        17 => '口语',//成人-实用英语
        18 => '写作',//成人-实用英语
        19 => '阅读',//成人-实用英语
        20 => '词汇',//成人-实用英语
        21 => '语法',//成人-实用英语
        22 => '听力',//成人-实用英语
        23 => '综合',//成人-实用英语
        24 => '中级财会',//成人-财会
        25 => '初级财会',//成人-财会
        26 => '教资笔试',//成人-教师资格证
        27 => '教资面试',//成人-教师资格证
        28 => '省考',
        29 => '国考',
        4 => '思维',

        255 => '其他',
        'sub_write' => '习字',

        32 => 'MBA',
        39 => '专业课',
        43 => 'CPA',
        44 => '副业',
        45 => '兴趣',
        46 => '职场',
        //小鹿编程
        40 => 'Scratch编程',
        41 => 'Python编程',
    ];

    public static $businessSubject = [
        self::YK => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::JF => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::CZXB => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::XXXB => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::GZSC => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::BBSZ => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],

        self::HM => [
            'sub_math' => '数学',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            'sub_physics' => '物理',
            'sub_chemistry' => '化学',
            'sub_biology' => '生物',
            7 => '政治',
            8 => '历史',
            9 => '地理',
            10 => '兴趣课',//直播课使用
            11 => '思想品德',//直播课使用
            12 => '讲座',//直播课使用
            13 => '理综',//试卷用
            14 => '文综',//试卷用
            15 => '奥数',
            16 => '科学',
            255 => '其他',
            'sub_write' => '习字',
        ],
        self::DYXZ => [
            'sub_write' => '习字',
            //小鹿编程
            40 => 'Scratch编程',
            41 => 'Python编程',
            255 => '其他',
        ],
        self::DYYY => [
            'sub_english' => '英语',
            255 => '其他',
        ],
        self::DYYW => [
            'sub_chinese' => '语文',
            255 => '其他',
        ],
        self::TFDYSW => [
            4 => '思维',
            255 => '其他',
        ],
        self::TFYYDY => [
            4 => '思维',
            'sub_chinese' => '语文',
            'sub_english' => '英语',
            255 => '其他',
        ],
        self::TFCRJS => [
            26 => '教资笔试',//成人-教师资格证
            27 => '教资面试',//成人-教师资格证
            255 => '其他',
        ],
        self::TFCRYY => [
            17 => '口语',//成人-实用英语
            18 => '写作',//成人-实用英语
            19 => '阅读',//成人-实用英语
            20 => '词汇',//成人-实用英语
            21 => '语法',//成人-实用英语
            22 => '听力',//成人-实用英语
            23 => '综合',//成人-实用英语
            255 => '其他',
        ],
        self::TFCRCK => [
            24 => '中级财会',//成人-财会
            25 => '初级财会',//成人-财会
            32 => 'MBA',
            43 => 'CPA',
            255 => '其他',
        ],
        self::TFCRGK => [
            28 => '省考',
            29 => '国考',
            255 => '其他',
        ],
        self::TFCRKY => [
            39 => '专业课',
            255 => '其他',
        ],
        self::TFCRZJ => [
            44 => '副业',
            45 => '兴趣',
            46 => '职场',
            255 => '其他',
        ],
        self::TFBBVIP => [
            255 => '其他',
        ],
    ];

    public static $attrClassType = [
        2 => '短训班',  //短训班
        3 => '正价课',  //正价课
        1 => '特惠课',  //特惠课
        5 => '体验课',  //体验课   4建站测试
        6 => '表单',    //20201021 表单
        49 => '非课程商品', //非课程商品
    ];

    public static $schoolSeason = [
        4 => '寒',
        1 => '春',
        2 => '暑',
        3 => '秋',
    ];

    public static $grade = [
        self::PRIMARY => array(
            11 => '一年级',
            12 => '二年级',
            13 => '三年级',
            14 => '四年级',
            15 => '五年级',
            16 => '六年级',
        ),
        self::JUNIORHIGH => array(
            2 => '初一',
            3 => '初二',
            4 => '初三',
        ),
        self::HIGH => array(
            5 => '高一',
            6 => '高二',
            7 => '高三',
        ),
        self::GRADESECTION_KINDERGARTEB => array(
            61 => '学前班',
            62 => '大班',
            63 => '中班',
        ),
        self::FACULTY_OTHER => array(
            255 => '其他',
        ),
        self::ADULT => array(
            71 => '成人',
        ),
    ];

    public static $business = array(
        'yk' => '作业帮直播课',
        'dyxz' => '小鹿素养',
        'dyyw' => '鸭鸭低幼-语文',
        'dyyy' => '鸭鸭低幼-英语',
        'tfdysw' => '鸭鸭低幼-思维',
        'tfcryy' => '成人教育-实用英语',
        'tfcrck' => '成人教育-财会',
        'tfcrjs' => '成人教育-教师资格证',
        'tfcrgk' => '成人教育-公考',
        'tfyydy' => '鸭鸭低幼',
        'tfcrky' => '成人教育-考研',
        'tfcrzj' => '成人教育-职业教育',
        'tfbbvip' => '帮帮VIP',
        'yj'  => '碳氧智能',
        'jf'  => '教辅业务',
        'cs' => 'CS',
        'gx' => 'GX',
        'hm' => 'HM',
        'gzsc' => '高中市场',
        'czxb' => '初中学部',
        'xxxb' => '小学学部',
        self::BBSZ => '帮帮识字',
        'vm' => 'VM',
    );

    public static $goodsType = array(
        self::GOOD_TYPE_COURSE      => '课程',
        self::GOOD_TYPE_INVENTED    => '虚拟',
        self::GOOD_TYPE_MATERIAL    => '实物',
    );
    /*
     * 业务线 对应学部关系
     */
    public static $faculty = array(
        self::YK => array(
            self::GRADESECTION_KINDERGARTEB => '学前',
            1 => '小学',
            20 => '初中',
            30 => '高中',
        ),
        self::HM => array(
            self::GRADESECTION_KINDERGARTEB => '学前',
            1 => '小学',
            20 => '初中',
            30 => '高中',
        ),
        self::DYXZ => [self::FACULTY_OTHER => '其他'],
        self::DYYW => [self::FACULTY_OTHER => '其他'],
        self::DYYY => [self::FACULTY_OTHER => '其他'],
        self::TFDYSW => [self::FACULTY_OTHER => '其他'],
        self::TFYYDY => [self::FACULTY_OTHER => '其他'],
        self::TFCRCK => [self::ADULT => '成人'],
        self::TFCRJS => [self::ADULT => '成人'],
        self::TFCRYY => [self::ADULT => '成人'],
        self::TFCRGK => [self::ADULT => '成人'],
        self::TFCRKY => [self::ADULT => '成人'],
        self::TFCRZJ => [self::ADULT => '成人'],
        self::CZXB => [20 => '初中'],
        self::GZSC => [30 => '高中'],
        self::XXXB => [1 => '小学'],
        self::BBSZ => array(
            self::GRADESECTION_KINDERGARTEB => '学前',
            1 => '小学',
            20 => '初中',
            30 => '高中',
        ),
        self::JF => array(
            self::GRADESECTION_KINDERGARTEB => '学前',
            1 => '小学',
            20 => '初中',
            30 => '高中',
        ),
        self::TFBBVIP => [self::FACULTY_OTHER => '其他'],
    );
    //全部学部
    public static $allFaculty = array(
        self::PRIMARY => '小学',
        self::JUNIORHIGH => '初中',
        self::HIGH => '高中',
        self::GRADESECTION_KINDERGARTEB => '学前',
        self::ADULT => '成人',
        self::FACULTY_OTHER => '其他',
    );

    public static $allGrade = [
        61 => '学前班',
        62 => '大班',
        63 => '中班',
        11 => '一年级',
        12 => '二年级',
        13 => '三年级',
        14 => '四年级',
        15 => '五年级',
        16 => '六年级',
        2 => '初一',
        3 => '初二',
        4 => '初三',
        5 => '高一',
        6 => '高二',
        7 => '高三',
        71 => '成人',
        255 => '其他',
    ];

    public static $GradeField = [
        71 => 'grade71',
        61 => 'grade61',
        62 => 'grade62',
        63 => 'grade63',
        11 => 'grade1',
        12 => 'grade2',
        13 => 'grade3',
        14 => 'grade4',
        15 => 'grade5',
        16 => 'grade6',
        2 => 'grade7',
        3 => 'grade8',
        4 => 'grade9',
        5 => 'grade10',
        6 => 'grade11',
        7 => 'grade12',
    ];

    public static $dyLevel = [
        "pre level" => 'pre level',
        "level0" => 'level0',
        "level1" => 'level1',
        "level2" => 'level2',
        "level3" => 'level3',
        "level4" => 'level4',
    ];

    public static $youngLevel = [
        'pre' => 'pre level',
        0 => 'level0',
        1 => 'level1',
        2 => 'level2',
        3 => 'level3',
        4 => 'level4',
    ];

    public static $skuSubject = [
        1 => '语文',
        2 => '数学',
        3 => '英语',
        4 => '物理',
        5 => '化学',
        6 => '生物',
        7 => '政治',
        8 => '历史',
        9 => '地理',
        10 => '兴趣课',//直播课使用
        11 => '思想品德',//直播课使用
        12 => '讲座',//直播课使用
        13 => '理综',//试卷用
        14 => '文综',//试卷用
        15 => '奥数',
        16 => '科学',
        17 => '口语',//成人-实用英语
        18 => '写作',//成人-实用英语
        19 => '阅读',//成人-实用英语
        20 => '词汇',//成人-实用英语
        21 => '语法',//成人-实用英语
        22 => '听力',//成人-实用英语
        23 => '综合',//成人-实用英语
        24 => '中级财会',//成人-财会
        25 => '初级财会',//成人-财会
        26 => '教资笔试',//成人-教师资格证
        27 => '教资面试',//成人-教师资格证
        28 => '省考',
        29 => '国考',
        32 => 'MBA',
        43 => 'CPA',
        255 => '其他',
        39 => '专业课',
        44 => '副业',
        45 => '兴趣',
        46 => '职场',
        //小鹿编程
        40 => 'Scratch编程',
        41 => 'Python编程',
    ];

    // 伏羲机构报备-学部
    const GradeDeptXX = 1; // 小学
    const GradeDeptCZ = 20; // 初中
    const GradeDeptGZ = 30; // 高中
    const GradeDeptOther = 99; // 其他

    public static $gradePromoteGradeDept = [
        self::GradeDeptXX => array(
            11 => '一年级',
            12 => '二年级',
            13 => '三年级',
            14 => '四年级',
            15 => '五年级',
            16 => '六年级',
        ),
        self::GradeDeptCZ => array(
            2 => '初一',
            3 => '初二',
            4 => '初三',
        ),
        self::GradeDeptGZ => array(
            5 => '高一',
            6 => '高二',
            7 => '高三',
        ),
    ];

    // 根据年级id获取 伏羲机构报备-学部
    public function getPromoteGradeDept($grade) {
        $promoteGradeDept = self::GradeDeptOther;
        foreach (self::$gradePromoteGradeDept as $gradeDept => $grades) {
            if (isset($grades[$grade])) {
                $promoteGradeDept = $gradeDept;
            }
        }
        return $promoteGradeDept;
    }

    // 伏羲机构报备-业务线 // 1-K9，2-K10，3-素养市场，4-素养进校，5-小学学部，6-初中学部，7-GX，8-智能教辅，9-智能硬件，10-APP，11-其他
    const BusinessLineK9    = 1 ; // K9
    const BusinessLineK10   = 2 ; // K10
    const BusinessLineSYSC  = 3 ; // 素养市场
    const BusinessLineSYJX  = 4 ; // 素养进校
    const BusinessLineXXXB  = 5 ; // 小学学部
    const BusinessLineCZXB  = 6 ; // 初中学部
    const BusinessLineGX    = 7 ; // GX
    const BusinessLineZNJF  = 8 ; // 智能教辅
    const BusinessLineZNYJ  = 9 ; // 智能硬件
    const BusinessLineAPP   = 10; // APP
    const BusinessLineOther = 11; // 其他


    // lastfrom的一级业务线 和 伏羲机构报备-业务线 关系
    public static $businessPromoteBusinessMap = [
        self::YK    => self::BusinessLineK9, // K9
        self::GZSC  => self::BusinessLineK10, // K10
        self::JF    => self::BusinessLineZNJF, // 智能教辅
        self::YJ    => self::BusinessLineZNYJ, // 智能硬件
        self::XXXB  => self::BusinessLineXXXB, // 小学学部
        self::CZXB  => self::BusinessLineCZXB, // 初中学部
//        self::DYXZ  => -1, // 3-素养市场 或 4-素养进校
//        self::GX    => -1, // 3-素养市场 或 4-素养进校
    ];

    // 区分素养市场和素养进校 的两个部门
    const orgLv1SYSC = 74; // 素养业务部
    const orgLv1SYJX = 89; // 素养进校业务部

    // 旧投放二级项目 和 新投放二级项目 的映射关系
    public static $oldProLv2NewProLv2Map = [
        1418 => 1581, // 母题提分大师 => 提分大师
        1445 => 1583, // 外采书夹课 => 外采书带量0元
        1446 => 1584, // 外部硬件夹课 => 硬件带量0元
        1447 => 1585, // 物流包裹夹课 => 物流广告带量0元
        1141 => 1582, // 29元小初2022春 => 19元课包
        1511 => 1588, // 自然拼读魔法书二级项目 => 英语自然拼读魔法书
        1494 => 1590, // 29元满分笔记 => 小课包达播
        1343 => 1712, // 自播课程存放壳子 => 抖音人群包对应壳子
        1241 => 1565, // 编程进校业务0转正 => 常规进校课程
        1309 => 1565, // 测试测试接口 => 常规进校课程
        1310 => 1565, // 渠道体验 => 常规进校课程
        1328 => 1565, // 进校编程 => 常规进校课程
        1348 => 1566, // 24年春新疆新课 => 新疆进校课程
        1360 => 1565, // 24年南通定制新课 => 常规进校课程
        1420 => 1565, // 非进校渠道 => 常规进校课程
        1439 => 1565, // 学科一年级 => 常规进校课程
        1452 => 1565, // 11进校 => 常规进校课程
        1496 => 1568, // 0低正 => 0转低转正
        1497 => 1568, // 0低正 => 0转低转正
    ];

    // 伏羲机构报备-新推广产品枚举
    const PromoteProductXKB   = 1; // 小课包
    const PromoteProductSKB   = 2; // 书课包
    const PromoteProductZJK   = 3; // 正价课
    const PromoteProductZZZ   = 4; // 0转正
    const PromoteProductZDZ   = 5; // 0低正
    const PromoteProductZZZ2  = 6; // 0中正
    const PromoteProductOther = 7; // 其他

    // 河图应用方id和业务线的对应关系
    public static $appIdPromoteBusinessMap = [
        17 => self::BusinessLineK10, // 高中市场
        15 => self::BusinessLineSYJX, // 小鹿进校
        7 => self::BusinessLineSYSC, // 素养商务
        1 => self::BusinessLineK9, // K12低价课
        2 => self::BusinessLineK9, // K12对公
        3 => self::BusinessLineK9, // K12拉新
        4 => self::BusinessLineK9, // K9商务
        6 => self::BusinessLineK9, // K12暑期
        11 => self::BusinessLineK9, // 0转正-北京商务
        13 => self::BusinessLineK9, // 宣老师组
        103 => self::BusinessLineGX, // 鲸鱼爱学
    ];

    public function getPromoteBusinessByAppId($appId)
    {
        if (isset(self::$appIdPromoteBusinessMap[$appId])) {
            return self::$appIdPromoteBusinessMap[$appId];
        }
        return self::BusinessLineOther;
    }

}
