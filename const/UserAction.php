<?php
/**
 * Created by PhpStorm.
 * @file UserAction.php
 * <AUTHOR>
 * @date 19-4-3
 * @version
 * @brief
 *
 **/


class Qdlib_Const_UserAction
{
    const ADD_LOING                  = 1;  // 登录
    const ADD_ORDER                  = 2;  // 订单
    const ADD_CLUE                   = 3;  // 表单线索
    const ADD_APP_ANDROID_ACTIVATE   = 4; // 安卓APP激活
    const ADD_APP_IOS_ACTIVATE       = 5; // IOS app激活
    const ADD_REG                    = 6; // 注册
    const ADD_FIRST_REG              = 7; // 首次注册及拉新召回
    const ADD_TRANSFORM              = 8; //确认有效转化
    const ADD_ORDER_UNPAID           = 9; // 微信小程序下单未支付类型
    const ADD_FAILED_LIMIT_ORDER     = 10; // 限购
    const ADD_REFUNDED_ORDER         = 11; // 退单
    const ADD_APP_ANDROID_SEC_ACTIVATE   = 14; // 安卓APP二次激活
    const ADD_APP_IOS_SEC_ACTIVATE       = 15; // IOS app二次激活
    const ADD_APP_REG                = 16; //app注册及首登
    const ADD_APP_RETENTION_NEXTDAY  = 19; //次留

    const ADD_COSTOM_LOW_PRICE       = 17; // 自定义-低价课
    const ADD_COSTOM_REGULAR_PRICE   = 18; // 自定义-正价课

    const ADD_LOW_PRICE_ARRIVE_CLASS = 20; // 深度转化低价课到课
    const ADD_REGULAR_PRICE_ORDER = 21; // 深度转化正价课购买
    const ADD_APP_RETRY_INSTALL = 25;// 广义新增-卸载激活
    const ADD_APP_SILENT_LAHUO = 26;// 广义新增-沉默拉活
    const ADD_APP_GUAN_JIAN_XINGWEI = 109;// 关键行为

    const APP_ACTIVATE               = 0; //上报APP激活
    const FIRST_REG                  = 1; //上报首次注册

    public static $actionTypeList = [
        Qdlib_Const_ChannelMap::GDT  => [self::ADD_LOING,self::ADD_ORDER,self::ADD_CLUE,self::ADD_APP_ANDROID_ACTIVATE,self::ADD_APP_IOS_ACTIVATE,self::ADD_REG,self::ADD_TRANSFORM, self::ADD_ORDER_UNPAID, self::ADD_FAILED_LIMIT_ORDER,self::ADD_APP_ANDROID_SEC_ACTIVATE,self::ADD_APP_IOS_SEC_ACTIVATE, self::ADD_APP_REG, self::ADD_COSTOM_LOW_PRICE, self::ADD_COSTOM_REGULAR_PRICE,self::ADD_LOW_PRICE_ARRIVE_CLASS,self::ADD_REGULAR_PRICE_ORDER],
        Qdlib_Const_ChannelMap::PYQ  => [self::ADD_LOING,self::ADD_ORDER,self::ADD_CLUE,self::ADD_REG,self::ADD_TRANSFORM,self::ADD_ORDER_UNPAID,self::ADD_FAILED_LIMIT_ORDER],
        Qdlib_Const_ChannelMap::JRTT => [self::ADD_LOING,self::ADD_ORDER,self::ADD_CLUE,self::ADD_REG,self::ADD_APP_ANDROID_ACTIVATE,self::ADD_APP_IOS_ACTIVATE,self::ADD_APP_ANDROID_SEC_ACTIVATE,self::ADD_APP_IOS_SEC_ACTIVATE, self::ADD_APP_REG, self::ADD_APP_RETENTION_NEXTDAY,self::ADD_LOW_PRICE_ARRIVE_CLASS,self::ADD_REGULAR_PRICE_ORDER],
        Qdlib_Const_ChannelMap::QIHU => [self::ADD_APP_ANDROID_ACTIVATE,self::ADD_APP_IOS_ACTIVATE,self::ADD_FIRST_REG],
        Qdlib_Const_ChannelMap::BD => [self::ADD_LOING,self::ADD_ORDER,self::ADD_CLUE,self::ADD_REG,self::ADD_ORDER_UNPAID,self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::BDXXL => [self::ADD_LOING,self::ADD_ORDER,self::ADD_CLUE],
        Qdlib_Const_ChannelMap::WYYD => [self::ADD_LOING,self::ADD_ORDER],
        Qdlib_Const_ChannelMap::OPPO  => [self::ADD_ORDER,self::ADD_APP_ANDROID_ACTIVATE,self::ADD_FIRST_REG],
        Qdlib_Const_ChannelMap::KUAISHOU => [self::ADD_LOING,self::ADD_ORDER],
        Qdlib_Const_ChannelMap::XIAOMI => [self::ADD_LOING],
        Qdlib_Const_ChannelMap::WEIBO => [self::ADD_ORDER],
        Qdlib_Const_ChannelMap::INMOBI => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::YOUJU => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::QMSJ => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::HESHENG => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::YOUMI => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::FANZHUO => [self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::SM => [self::ADD_LOING, self::ADD_ORDER, self::ADD_APP_IOS_ACTIVATE],
        Qdlib_Const_ChannelMap::TUIA => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::SG => [self::ADD_ORDER],
        Qdlib_Const_ChannelMap::HUAWEI => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::HUAWEISD => [self::ADD_APP_ANDROID_ACTIVATE, self::ADD_APP_REG, self::ADD_APP_RETENTION_NEXTDAY],
        Qdlib_Const_ChannelMap::KRW => [self::ADD_ORDER],
        Qdlib_Const_ChannelMap::UC => [self::ADD_LOING, self::ADD_ORDER],
        'bcjh' => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::CAT => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::XHS => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::ONLINE => [self::ADD_LOING, self::ADD_ORDER],
        Qdlib_Const_ChannelMap::XMLA => [self::ADD_LOING, self::ADD_ORDER],
    ];

    public static $flowSwitch = [];

    //上报无需验证账号的第三方渠道
    public static $noCheckaccountReport = [
        Qdlib_Const_ChannelMap::JRTT,
        Qdlib_Const_ChannelMap::HUAWEISD,
        Qdlib_Const_ChannelMap::QIHU,
        Qdlib_Const_ChannelMap::KUAISHOU,
        Qdlib_Const_ChannelMap::XIAOMI,
        Qdlib_Const_ChannelMap::INMOBI,
        Qdlib_Const_ChannelMap::YOUJU,
        Qdlib_Const_ChannelMap::QMSJ,
        Qdlib_Const_ChannelMap::HESHENG,
        Qdlib_Const_ChannelMap::YOUMI,
        Qdlib_Const_ChannelMap::FANZHUO,
        Qdlib_Const_ChannelMap::SM,
        Qdlib_Const_ChannelMap::TUIA,
        Qdlib_Const_ChannelMap::KRW,
        Qdlib_Const_ChannelMap::UC,
        'bcjh',
        Qdlib_Const_ChannelMap::CAT,
        Qdlib_Const_ChannelMap::XHS,
        Qdlib_Const_ChannelMap::ONLINE,
        Qdlib_Const_ChannelMap::WYYD,
        Qdlib_Const_ChannelMap::XMLA,
    ];


    //监控分级，分fatal与warning错误
    //日志上显示fatal错误通过noah及时报警
    public static $fatalCods = [
        //Qdlib_Common_ExceptionCodes::DB_ERROR, //暂时该错误没到一出现就报警级别
        Qdlib_Common_ExceptionCodes::USERACTION_GET_ACCOUNTINFO_BY_ACCONT,
        Qdlib_Common_ExceptionCodes::WX_GET_TOKEN_ERROR,
        Qdlib_Common_ExceptionCodes::GDT_GET_TOKEN_ERROR,
        Qdlib_Common_ExceptionCodes::JRTT_GET_TOKEN_ERROR,
        Qdlib_Common_ExceptionCodes::PYQ_GET_TOKEN_ERROR,
    ];

    //日志中连续出现以下错误几次后报警
    public static $errorCods = [
        Qdlib_Common_ExceptionCodes::DB_ERROR,
        Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED,
        Qdlib_Common_ExceptionCodes::USERACTION_CLICKID_HASH_ERROR,
        Qdlib_Common_ExceptionCodes::USERACTION_API_UNDEFINED_ERROR,
        Qdlib_Common_ExceptionCodes::USERACTION_MISSING_REQUIRED_ARGUMENT,
    ];

    //上报无需重报错误码
    public static $noRepeatCods = [
        Qdlib_Common_ExceptionCodes::USERACTION_GET_ACCOUNTINFO_BY_LASTFROM,
        Qdlib_Common_ExceptionCodes::USERACTION_CHANNEL_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_ORIFROM_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_TRANSFORMID_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_TYPE_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_EXTDATA_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_CLICKID_EMPTY,
        Qdlib_Common_ExceptionCodes::USERACTION_ORIFROM_FOMAT_ERROR,
        Qdlib_Common_ExceptionCodes::USERACTION_TYPE_NO_REPORT,
        Qdlib_Common_ExceptionCodes::USERACTION_ALREADY_REPORTED,
        Qdlib_Common_ExceptionCodes::USERACTION_WHITE_NOEXIST,
        Qdlib_Common_ExceptionCodes::USERACTION_NO_PAGEID,
        Qdlib_Common_ExceptionCodes::USERACTION_MISSING_REQUIRED_ARGUMENT,
        Qdlib_Common_ExceptionCodes::USERACTION_DEEPLINK,
        Qdlib_Common_ExceptionCodes::USERACTION_ACTION_TIME_EXPIRED,
    ];

    //朋友圈有效转化（type=8）白名单
    public static $pyqTransformWhiteAccount = [
        'wx44e67998bb52a484',
        'wxab7e8ba3296442ad',
        'wxc8329c2bef0ca92b',
        'wx04cc3d4d04a7dc5c',
        'wxaa770004e6cb045d',
        'wx7d3b0cea823d5435',
        'wx01c1ca95ee114ee4',
        'wxc30ba41291021e13',
        'wx2a3797bc617b636f',
        'wx674f3df3d373b679',
        'wxe2395c0bd7c881ee',
        'wx04c3353e6a1d52f6',
        'wx6611139991c95740',
        'wx4d16fb2b77cd94e6',
        'wxf4ec66547830558f',
        'wx56d19e00c4ebe11c',
        'wx0650c9c568c1ccdf',
        'wx5b0832470949a549',
        'wxd5b8d286d19ae6c8',
        'wxc56ccbdec6301188',
        'wxcdf1d06ae403e45c',
        'wx092345364deea8d3',
        'wxd0498deb359bebcc',
        'wxdc0d27e7ae0d8211',
        'wxb9d47b247b958b2f',
        'wx57f6863c2e31a948',
        'wxc051a7a95d1e21e4',
        'wxc00410ef335cbad1',
        'wx6427ac28caa9f452',
        'wx3d898c02740d9d7c',
        'wx9cef23132b42c0c9',
        'wxe7968150a08abb9a',
        'wxb70c608910d9195f',
        'wx0df172686e91dc4c',
        'wx5bb62b91e1074ed4',
        'wxad332a89218f4fac',
        'wx82f83edb8312c3f5',
        'wx5bb62b91e1074ed4',
        'wx4854c328d9e1ad18',
        'wx9f10ad4be7784e30',
        'wx14df6e1e508923a2',
        'wx7221cbbc4a4ba5be',
        'wx467e6460c3731b2e',
        'wx184054310919ebb9',
        'wx25df3ba4e81e01c3',
        '2802699094',
        '1627834920',
    ];

}
