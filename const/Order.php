<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2019/10/17
 * Time: 4:32 PM
 */

class Qdlib_Const_Order
{
    const EMPTY_PHONE   = 19000000000;
    //order_status 订单状态码 -1、已取消 0、待支付 1、支付成功 2、购买成功 3、购买失败 4、已消费
    const ORDER_STATUS_CANCEL       = -1;
    const ORDER_STATUS_PAYING       = 0;
    const ORDER_STATUS_PAID         = 1;
    const ORDER_STATUS_BUY_SUCCESS  = 2;
    const ORDER_STATUS_BUY_FAILED   = 3;
    const ORDER_STATUS_USED         = 4;

    //refund_status 订单退款状态码 0、未退款 1、退款中 2、退款成功 3、退款失败
    const REFUND_STATUS_NOT         = 0;
    const REFUND_STATUS_GOING       = 1;
    const REFUND_STATUS_SUCCESS     = 2;
    const REFUND_STATUS_FAILED      = 3;

    /**
     * 拉取订单配置
     * @var array
     */
    public static $pullOrderConfig = array(
        'yj'    => array(
            'channel'           => 'yj',    //渠道
            'listApiLimit'      => 50,      //list接口条数限制
            'defer'             => 300,     //订单时效延迟
            'apart'             => 3600,     //订单拉取间隔
            'account'           => [],      //拉取账号列表，空为无账号
            'tasks'          => [
                Qdlib_Ds_ThirdOrderTasks::TYPE_ORDER_SMS => [
                    'batchId' => Qdlib_Const_SmsLogBatchId::YUN_JI, // 短信批次ID
                    'templateId' => 4162, // 短信批次ID
                    'link' => 'https://zybang.com/su/cjkNrw', // 短信批次ID
                ], // 订单发送短信
                Qdlib_Ds_ThirdOrderTasks::TYPE_ORDER_SEND => [], // 订单发送兑换码
            ], // 拉取到订单后生成的任务列表
        ),
    );
}