<?php
/**
 * User: 作业帮
 * Date: 2019/04/15
 * Time: 11:50
 */

/**
 * @file   GlobalGray.php
 * <AUTHOR>
 * @brief  全局灰度配置
 **/
class Hkzb_Const_GlobalGray
{
    //灰度章节id列表：进入教室
    public static $GRAY_ENTER_LESSON_LIST = [
    ];

    //灰度章节id最后一位散列列表：进入教室
    public static $GRAY_ENTER_LESSON_HASH = [];

    //灰度课程id列表：进入教室
    public static $GRAY_ENTER_COURSE_LIST = [
    ];
    //灰度课程id最后一位散列列表：进入教室
    public static $GRAY_ENTER_COURSE_HASH = [];


    /**
     * @param string $name
     * @param int $id 章节id 或 课程id
     * @param string $type 灰度类型，LESSON或COURSE 默认lesson
     * @return bool
     * @brief  获取该章节表扬业务是否为灰度
     */
    public static function isGray($name, $id, $type = 'LESSON')
    {
        if (empty($name) || empty($id)) {
            return false;
        }
        $name = strtoupper($name);
        $type = strtoupper($type);
        $grayName = "GRAY_{$name}_{$type}_HASH";
        if (isset(self::$$grayName) && !empty(self::$$grayName)) {
            $intLast = substr($id, -1);
            return in_array($intLast, self::$$grayName);
        }
        $grayName = "GRAY_{$name}_{$type}_LIST";
        if (isset(self::$$grayName) && !empty(self::$$grayName)) {
            return in_array($id, self::$$grayName);
        }
        return true;
    }

}