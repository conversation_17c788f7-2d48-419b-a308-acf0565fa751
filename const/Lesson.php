<?php
/**
 * @file   Lesson.php
 * <AUTHOR> 
 * @brief  课节常量定义
 *
 **/
class Zb_Const_Lesson {

    //章节删除
    const STATUS_LESSON_UNFINISH = 0; //未结束
    const STATUS_LESSON_FINISHED = 1; //已结束（正常结束）
    const STATUS_LESSON_DELETED  = 2; //已删除（异常结束）
    public static $arrStatusMap = array(
        self::STATUS_LESSON_UNFINISH => '未结束',
        self::STATUS_LESSON_FINISHED => '已结束',
        self::STATUS_LESSON_DELETED  => '已删除',
    );

    const LESSON_BEFORE_TIME  = 1800; //章节提前30分钟开始
    const LESSON_CLASSING_NO  = 0; //章节非上课中
    const LESSON_CLASSING_YES = 1; //章节上课中

}
