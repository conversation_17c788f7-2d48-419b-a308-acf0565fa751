<?php

/**
 * @file   Material.php
 * <AUTHOR>
 * @date   2020/03/24
 * @brief
 */

class Zb_Const_Material {

    const TYPE_CPU = 1;
    const TYPE_COURSE = 2;
    public static $TYPE_DESC_MAP = array(
        self::TYPE_CPU => '课程产品类型',
        self::TYPE_COURSE => '课程类型',
    );

    const SENT_TIME_NO = 0;             //不设置
    const SENT_TIME_FIXED = 1;          //固定时间
    const SENT_TIME_BEFORE_CLASS = 2;   //开课程前
    const SENT_TIME_AFTER_CLASS = 3;    //开课后
    public static $SENT_TIME_DESC_MAP = array(
        self::SENT_TIME_NO => '不设置',
        self::SENT_TIME_FIXED => '固定时间',
        self::SENT_TIME_BEFORE_CLASS => '开课程前',
        self::SENT_TIME_AFTER_CLASS => '开课后',
    );


    const STATUS_OK = 0; // 正常
    const STATUS_DELETED = 1; // 删除
    public static $STATUS_DESC_MAP = array(
        self::STATUS_OK => '正常',
        self::STATUS_DELETED => '删除',
    );
}
