<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Spu.php
 * <AUTHOR>
 * @date   2017/11/23 上午10:19
 * @brief
 **/

class Zb_Const_Spu {
    
    #查询排序
    const SPU_SORT_TYPE_ASC  = 1; 
    const SPU_SORT_TYPE_DESC = 2; 
    
    public static $spuSortTypeMap = [
        self::SPU_SORT_TYPE_ASC  => 'asc',
        self::SPU_SORT_TYPE_DESC => 'desc',
    ];
    
    #spu 展示、隐藏Map 
    const SPU_STATUS_SHOW = 0;
    const SPU_STATUS_HIDE = 1;
    
    public static $spuStatus = [
        self::SPU_STATUS_SHOW => '展示', 
        self::SPU_STATUS_HIDE => '隐藏',
    ]; 
    
    
    #--------------spuV2------------
    #业务方ID
    const PRODUCT_COURSE_APP_ID = 1; //一课
    const PRODUCT_HX_APP_ID     = 2; //浣熊
    
    public static $appIdMap = [
        self::PRODUCT_COURSE_APP_ID => '一课', 
        self::PRODUCT_HX_APP_ID     => '浣熊',
    ]; 
     
    #上下架
    const SPU_ONLINE_STATUS  = 1;
    const SPU_OFFLINE_STATUS = 2; 
    const SPU_PART_ONLINE_STATUS = 3;
    
    public static $spuStatusMap = [
        self::SPU_ONLINE_STATUS   => '上架',
        self::SPU_OFFLINE_STATUS  => '下架',
        self::SPU_PART_ONLINE_STATUS  => '部分上架',  //只spu下部分商品上架 | spu 属于下架状态
    ];
    
    #spu及其sku操作日志相关
    //实体类型--spuId
    const ENTITY_TYPE_SPU_ID = 1;
    //实体类型--skuId
    const ENTITY_TYPE_SKU_ID = 2;
    
    //动作类型--创建
    const ACTION_TYPE_CREATE = 1;
    //动作类型--spu编辑页编辑
    const ACTION_TYPE_UPDATE = 2;
    //动作类型--删除
    const ACTION_TYPE_DELETE = 3;
    //动作类型--spu下sku上架 
    const ACTION_TYPE_ONLINE = 4;
    //动作类型--spu下sku下架 
    const ACTION_TYPE_OFFLINE = 5;
    //动作类型--spu下sku价格修改动作
    const ACTION_TYPE_UPDATE_PRICE = 6;
    //动作类型--sku编辑更新
    const ACTION_TYPE_SKU_DATA = 7;
}

