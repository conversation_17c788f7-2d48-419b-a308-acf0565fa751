<?php


/**
 * bns配置缓存，同时兼容yac和file，会根据环境是否有yac自动选择<br>
 * yac缓存的效率比file缓存的效率高2倍
 *
 * @filesource bd/bns/Cache.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-02-20
 */
class Bd_Bns_Cache {

    private $type;
    private $ttl;

    private $yac = NULL;
    private $dir = NULL;

    public function __construct($ttl) {
        $this->ttl = $ttl;
        if (extension_loaded('yac')) {
            $this->type = "yac";
            $this->yac  = new Yac("bns");
        } else {
            $this->type = "file";
            $this->dir  = DATA_PATH . "/bns";
        }
    }

    /**
     * 设置缓存
     *
     * @param string       $key
     * @param mixed        $value
     * @return boolean
     */
    public function set($key, $value) {
        if ("file" === $this->type) {
            return $this->setFileCache($key, $value);
        }
        return $this->setYacCache($key, $value);
    }

    /**
     * 获取缓存内容
     *
     * @param string       $key
     * @return boolean
     */
    public function get($key) {
        if ("file" === $this->type) {
            return $this->getFileCache($key);
        }
        return $this->getYacCache($key);
    }

    private function setYacCache($key, $info) {
        if (NULL === $this->yac) {
            return false;
        }
        $key = md5($key);
        return $this->yac->set($key, $info, $this->ttl);
    }

    private function getYacCache($key) {
        if (NULL === $this->yac) {
            return false;
        }
        $key = md5($key);
        return $this->yac->get($key);
    }

    private function setFileCache($key, $info) {
        if (NULL === $this->dir) {
            return false;
        }

        $filepath = "{$this->dir}/{$key}";
        $expire   = time() + $this->ttl;
        $value    = @json_encode($info);
        if (@file_put_contents($filepath, $value, LOCK_EX) !== false) {
            @chmod($filepath, 0777);
            return @touch($filepath, $expire);
        }
        return false;
    }

    private function getFileCache($key) {
        if (NULL === $this->dir) {
            return false;
        }

        $filepath = "{$this->dir}/{$key}";
        $filetime = @filemtime($filepath);
        if ($filetime > time()) {
            $ret  = @file_get_contents($filepath);
            return @json_decode($ret, true);
        }
        return false;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */