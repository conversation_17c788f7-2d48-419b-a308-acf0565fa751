<?php


/**
 * 索引操作
 *
 * @see https://www.elastic.co/guide/en/elasticsearch/reference/6.3/docs-index_.html
 *
 * @filesource bd/es/endpoints/Index.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-11-12
 */
class Bd_Es_Endpoints_Index extends Bd_Es_Endpoints_AbstractEndpoint {

    public function getUri() {
        $id    = $this->id;
        $index = $this->index;
        $type  = $this->type;
        $uri   = "/$index/$type";

        if (isset($id) === true) {
            $uri = "/$index/$type/$id";
        }
        return $uri;
    }

    public function getMethod() {
        if (isset($this->id) === true) {
            return self::PUT;
        } else {
            return self::POST;
        }
    }

    public function setParams($params) {
        $id    = $this->extractArgument($params, "id");
        $index = $this->extractArgument($params, "index");
        $type  = $this->extractArgument($params, "type");
        $this->setId($id)
        ->setIndex($index)
        ->setType($type)
        ->setBody($params);
        return $this->checkParam();
    }

    private function checkParam() {
        if (isset($this->index) !== true) {
            Bd_Log::warning("index is required for Index");
            return false;
        }
        if (isset($this->type) !== true) {
            Bd_Log::warning("type is required for Index");
            return false;
        }
        if (empty($this->body)) {
            Bd_Log::warning('document body must be set for index request');
            return false;
        }
        return true;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
