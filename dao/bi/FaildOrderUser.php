<?php

class Qdlib_Dao_Bi_FaildOrderUser extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName  = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db      = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblBiFaildOrderUser";

        $this->arrFieldsMap = [
            'id'        => 'id',
            'userId'    => 'user_id',
            'errorCode' => 'error_code',
            'pageId'    => 'page_id',
            'lastfrom'  => 'lastfrom',
            'skuIds'    => 'sku_ids',
            'createTime'=> 'create_time',
            'clickId'   => 'click_id',
            'isAd'      => 'is_ad',
            'campaignId'=> 'campaign_id',
            'adgroupId' => 'adgroup_id',
            'adId'      => 'ad_id',
            'creativeId'=> 'creative_id',
        ];

        $this->arrTypesMap = [
            'id'        => Hk_Service_Db::TYPE_INT,
            'userId'    => Hk_Service_Db::TYPE_INT,
            'errorCode' => Hk_Service_Db::TYPE_STR,
            'pageId'    => Hk_Service_Db::TYPE_STR,
            'lastfrom'  => Hk_Service_Db::TYPE_STR,
            'skuIds'    => Hk_Service_Db::TYPE_STR,
            'createTime'=> Hk_Service_Db::TYPE_INT,
            'clickId'   => Hk_Service_Db::TYPE_STR,
            'isAd'      => Hk_Service_Db::TYPE_STR,
            'campaignId'=> Hk_Service_Db::TYPE_INT,
            'adgroupId' => Hk_Service_Db::TYPE_INT,
            'adId'      => Hk_Service_Db::TYPE_INT,
            'creativeId'=> Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

    public function __get($name) {
        return $this->$name;
    }
}
