<?php

class Qdlib_Dao_Bi_QudaoAgent extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName  = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db      = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblBiQudaoAgent";

        $this->arrFieldsMap = [
            'id'       => 'id',
            'agentId'  => 'agent_id',
            'agentName'=> 'agent_name',
        ];

        $this->arrTypesMap = [
            'id'       => Hk_Service_Db::TYPE_INT,
            'agentId'  => Hk_Service_Db::TYPE_INT,
            'agentName'=> Hk_Service_Db::TYPE_STR,
        ];
    }
    
    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
    
    public function __get($name) {
        return $this->$name;
    }
}
