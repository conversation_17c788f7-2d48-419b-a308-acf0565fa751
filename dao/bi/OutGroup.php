<?php

class Qdlib_Dao_Bi_OutGroup extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName  = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db      = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblBiQudaoOutAdgroup";

        $this->arrFieldsMap = [
            'id'                     => 'id',
            'channel'                => 'channel',
            'account'                => 'account',
            'campaignId'             => 'campaign_id',
            'adgroupId'              => 'adgroup_id',
            'adgroupName'            => 'adgroup_name',
            'configuredStatus'       => 'configured_status',
            'systemStatus'           => 'system_status',
            'adgroupCreatedTime'     => 'adgroup_created_time',
            'adgroupLastModifiedTime'=> 'adgroup_last_modified_time',
            'adgroupCreatedDate'     => 'adgroup_created_date',
            'adgroupCreatedHour'     => 'adgroup_created_hour',
        ];

        $this->arrTypesMap = [
            'id'                     => Hk_Service_Db::TYPE_INT,
            'channel'                => Hk_Service_Db::TYPE_STR,
            'account'                => Hk_Service_Db::TYPE_STR,
            'campaignId'             => Hk_Service_Db::TYPE_INT,
            'adgroupId'              => Hk_Service_Db::TYPE_INT,
            'adgroupName'            => Hk_Service_Db::TYPE_STR,
            'configuredStatus'       => Hk_Service_Db::TYPE_STR,
            'systemStatus'           => Hk_Service_Db::TYPE_STR,
            'adgroupCreatedTime'     => Hk_Service_Db::TYPE_INT,
            'adgroupLastModifiedTime'=> Hk_Service_Db::TYPE_INT,
            'adgroupCreatedDate'     => Hk_Service_Db::TYPE_STR,
            'adgroupCreatedHour'     => Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

    public function __get($name) {
        return $this->$name;
    }
}
