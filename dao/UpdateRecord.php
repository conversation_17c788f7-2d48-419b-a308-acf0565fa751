<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : UpdateRecord.php
 * Author: <EMAIL>
 * Date: 2018/9/25
 * Time: 16:01
 * Desc: 系统更新记录
 */

class Oplib_Dao_UpdateRecord extends Hk_Common_BaseDao{

    public static $arrFields = array(
        'id',
        'misName',
        'updateInfo',
        'status',
        'operatorUid',
        'operator',
        'createTime',
        'updateTime',
        'extData',
    );


    public function __construct(){
        $this->_dbName      = 'revent/zb_operation';
        $this->_db          = null;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table = 'tblUpdateRecord';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'misName'     => 'mis_name',
            'updateInfo'  => 'update_info',
            'status'     => 'status',
            'operatorUid'=> 'operator_uid',
            'operator'   => 'operator',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'misName'     => Hk_Service_Db::TYPE_STR,
            'updateInfo'  => Hk_Service_Db::TYPE_STR,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_STR,
        );
    }
}
