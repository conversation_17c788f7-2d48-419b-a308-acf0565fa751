<?php


/**
 * @file    CourseConf.php
 * <AUTHOR>
 * @data    2019-02-19
 * @desc    MVP课程配置
 *
 */
class Hkzb_Dao_Xeng_CourseConf extends Hk_Common_BaseDao {

    const CONF_STATUS_DEFAULT = 0;
    const CONF_STATUS_OK = 1;
    const CONF_STATUS_DEL = 2;

    public static $ARR_FIELDS = [
        'id',
        'courseId',
        'lessonId',
        'courseType',
        'confInfo',
        'extraInfo',
        'status',
        'createTime',
        'updateTime',
        'productType',
        'stepType',
        'type'
    ];

    public function __construct(){
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = 'tblMvpCourseConf';
        $this->arrFieldsMap = array(
            'id' => 'id',
            'courseId' => 'course_id',
            'lessonId' => 'lesson_id',
            'courseType' => 'course_type',
            'confInfo' => 'conf_info',
            'extraInfo' => 'extra_info',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'productType' => 'product_type',
            'stepType' => 'step_type',
            'type' => 'type'
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'lessonId' => Hk_Service_Db::TYPE_INT,
            'courseType' => Hk_Service_Db::TYPE_INT,
            'confInfo' => Hk_Service_Db::TYPE_STR,
            'extraInfo' => Hk_Service_Db::TYPE_STR,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'productType' => Hk_Service_Db::TYPE_INT,
            'stepType' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
        );
    }

}