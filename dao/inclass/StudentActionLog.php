<?php


/**
 * @file    StudentActionLog.php
 * <AUTHOR>
 * @date    2018-01-05
 * @brief   课中学生行为记录
 *
 **/
class Hkzb_Dao_Inclass_StudentActionLog extends Hk_Common_BaseMultiDao {

    public function __construct() {
        $this->_dbName = 'zhibo_class/zhibo_class';
        $this->_db = null;
        $this->_table = "tblStudentActionLog";
        $this->_tableName = "tblStudentActionLog";
        $this->_partionKey = "course_id";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum = 1000;
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'courseId'    => 'course_id',
            'lessonId'    => 'lesson_id',
            'classId'     => 'class_id',
            'studentUid'  => 'student_uid',
            'actionType'  => 'action_type',
            'actionValue' => 'action_value',
            'message'     => 'message',
            'description' => 'description',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'classId'     => Hk_Service_Db::TYPE_INT,
            'studentUid'  => Hk_Service_Db::TYPE_INT,
            'actionType'  => Hk_Service_Db::TYPE_INT,
            'actionValue' => Hk_Service_Db::TYPE_INT,
            'message'     => Hk_Service_Db::TYPE_JSON,
            'description' => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        );
    }

}
