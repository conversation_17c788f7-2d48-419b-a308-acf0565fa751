<?php

class Qdlib_Dao_DataReport extends Hk_Common_BaseDao
{
    const DATA_REPORT       = 'tblDataReport';
    const AD_DAY_REPORT     = 'tblMediaReportDay';
    const AD_HOUR_REPORT    = 'tblMediaReportHour';

    public function __construct($table = self::DATA_REPORT)
    {
        parent::__construct();
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = $table;
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'dt'                => 'dt',
            'dtHour'            => 'dt_hour',
            'flag'              => 'flag',
            'qdChannel'         => 'qd_channel',
            'qdAccount'         => 'qd_account',
            'qdThirdAdId'       => 'qd_third_ad_id',
            'qdThirdCampaignId' => 'qd_third_campaign_id',
            'qdOutId'           => 'qd_out_id',
            'qdThirdCreativeId' => 'qd_third_creative_id',
            'qdThirdAdgroupId'  => 'qd_third_adgroup_id',
            'cateLv1'           => 'cate_lv1',
            'cateLv2'           => 'cate_lv2',
            'cateLv3'           => 'cate_lv3',
            'cateLv4'           => 'cate_lv4',
            'cateLv5'           => 'cate_lv5',
            'cateLv6'           => 'cate_lv6',
            'attrClassType'     => 'attr_class_type',
            'attrPageName'      => 'attr_page_name',
            'attrPageType'      => 'attr_page_type',
            'attrSubject'       => 'attr_subject',
            'attrLxWay'         => 'attr_lx_way',
            'qdShowPv'          => 'qd_show_pv',
            'qdShowUv'          => 'qd_show_uv',
            'qdClickUv'         => 'qd_click_uv',
            'qdClickPv'         => 'qd_click_pv',
            'qdCost'            => 'qd_cost',
            'qdCostCash'        => 'qd_cost_cash',
            'qdDownload'        => 'qd_download',
            'qdConversion'      => 'qd_conversion',
            'qdActivation'      => 'qd_activation',
            'pagePv'            => 'page_pv',
            'pageUv'            => 'page_uv',
            'clue'              => 'clue',
            'orderUidNum'       => 'order_uid_num',
            'orderIng'          => 'order_ing',
            'commitOrderUidNum' => 'commit_order_uid_num',
            'allOrderUidNum'    => 'all_order_uid_num',
            'limitUidNum'       => 'limit_uid_num',
            'orderEnd'          => 'order_end',
            'register'          => 'register',
            'registerClue'      => 'register_clue',
            'loginClue'         => 'login_clue',
            'recallUser'        => 'recall_user',
            'retention'         => 'retention',
            'ptActivation'      => 'pt_activation',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',

            'commitOrderUidNum' => 'commit_order_uid_num',
            'allOrderUidNum'    => 'all_order_uid_num',
            'limitUidNum'       => 'limit_uid_num',

            'loginUser'    => 'login_user',
            'registerUser' => 'register_user',
            'qdAgent'     => 'qd_agent',


        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'dt'                => Hk_Service_Db::TYPE_INT,
            'dtHour'            => Hk_Service_Db::TYPE_INT,
            'flag'              => Hk_Service_Db::TYPE_STR,
            'qdChannel'         => Hk_Service_Db::TYPE_STR,
            'qdAccount'         => Hk_Service_Db::TYPE_STR,
            'qdThirdAdId'       => Hk_Service_Db::TYPE_INT,
            'qdThirdCampaignId' => Hk_Service_Db::TYPE_INT,
            'qdOutId'           => Hk_Service_Db::TYPE_INT,
            'qdThirdCreativeId' => Hk_Service_Db::TYPE_INT,
            'qdThirdAdgroupId'  => Hk_Service_Db::TYPE_INT,
            'cateLv1'           => Hk_Service_Db::TYPE_STR,
            'cateLv2'           => Hk_Service_Db::TYPE_STR,
            'cateLv3'           => Hk_Service_Db::TYPE_STR,
            'cateLv4'           => Hk_Service_Db::TYPE_STR,
            'cateLv5'           => Hk_Service_Db::TYPE_STR,
            'cateLv6'           => Hk_Service_Db::TYPE_STR,
            'attrClassType'     => Hk_Service_Db::TYPE_INT,
            'attrPageName'      => Hk_Service_Db::TYPE_STR,
            'attrPageType'      => Hk_Service_Db::TYPE_STR,
            'attrSubject'       => Hk_Service_Db::TYPE_STR,
            'attrLxWay'         => Hk_Service_Db::TYPE_STR,
            'qdShowPv'          => Hk_Service_Db::TYPE_INT,
            'qdShowUv'          => Hk_Service_Db::TYPE_INT,
            'qdClickUv'         => Hk_Service_Db::TYPE_INT,
            'qdClickPv'         => Hk_Service_Db::TYPE_INT,
            'qdCost'            => Hk_Service_Db::TYPE_INT,
            'qdCostCash'        => Hk_Service_Db::TYPE_INT,
            'qdDownload'        => Hk_Service_Db::TYPE_INT,
            'qdConversion'      => Hk_Service_Db::TYPE_INT,
            'qdActivation'      => Hk_Service_Db::TYPE_INT,
            'pagePv'            => Hk_Service_Db::TYPE_INT,
            'pageUv'            => Hk_Service_Db::TYPE_INT,
            'clue'              => Hk_Service_Db::TYPE_INT,
            'orderUidNum'       => Hk_Service_Db::TYPE_INT,
            'orderIng'          => Hk_Service_Db::TYPE_INT,
            'commitOrderUidNum' => Hk_Service_Db::TYPE_INT,
            'allOrderUidNum'    => Hk_Service_Db::TYPE_INT,
            'limitUidNum'       => Hk_Service_Db::TYPE_INT,
            'orderEnd'          => Hk_Service_Db::TYPE_INT,
            'register'          => Hk_Service_Db::TYPE_INT,
            'registerClue'      => Hk_Service_Db::TYPE_INT,
            'loginClue'         => Hk_Service_Db::TYPE_INT,
            'recallUser'        => Hk_Service_Db::TYPE_INT,
            'retention'         => Hk_Service_Db::TYPE_INT,
            'ptActivation'      => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'commitOrderUidNum' => Hk_Service_Db::TYPE_INT,
            'allOrderUidNum'    => Hk_Service_Db::TYPE_INT,
            'limitUidNum'       => Hk_Service_Db::TYPE_INT,
            'loginUser'        => Hk_Service_Db::TYPE_INT,
            'registerUser'        => Hk_Service_Db::TYPE_INT,
            'qdAgent'         => Hk_Service_Db::TYPE_INT,
        );
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}
