<?php

// CREATE TABLE `tblQudaoRtaAPPRelation` (
//  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
//  `strategy_type` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '策略类型',
//  `name` varchar(255) NOT NULL DEFAULT '' COMMENT 'app名字',
//  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
//  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
//  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
//  `operator` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
//  PRIMARY KEY (`id`),
//  UNIQUE KEY `idx_type_name` (`strategy_type`,`name`)
//  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='rta app关系配置表';
class Qdlib_Dao_Rta_APPRelation extends Qdlib_Dao_Qudao
{
    public function __construct()
    {
        parent::__construct();
        $this->_table = "tblQudaoRtaAPPRelation";
        $this->arrFieldsMap = [
            'id' => 'id',
            'strategyType' => 'strategy_type',
            'name' => 'name',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'operator' => 'operator',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'strategyType' => Hk_Service_Db::TYPE_INT,
            'name' => Hk_Service_Db::TYPE_STR,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
        ];
    }
}
