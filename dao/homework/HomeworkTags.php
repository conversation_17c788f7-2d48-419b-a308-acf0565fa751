<?php
/**
 * @file HomeworkTags.php.
 * <AUTHOR>
 * @date: 2018/10/17
 */

class Hkzb_Dao_Homework_HomeworkTags extends Hk_Common_BaseMultiDao
{

    public function __construct()
    {
        $this->_dbName      = 'iknowtiku/homework';
        $this->_db          = null;
        $this->_table       = "tblHomeworkTags";
        $this->_tableName   = "tblHomeworkTags";
        $this->_partionKey  = "tid";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 10000000;
        $this->arrFieldsMap = array(
            'tid'        => 'tid',
            'content'    => 'content',
            'updateTime' => 'updateTime',
        );

        $this->arrTypesMap = array(
            'tid'        => Hk_Service_Db::TYPE_INT,
            'content'    => Hk_Service_Db::TYPE_JSON,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}