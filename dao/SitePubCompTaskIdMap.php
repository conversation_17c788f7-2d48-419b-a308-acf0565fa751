<?php
/**
 * Created by Growth AutoCode.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/09/21
 * Time: 10:30
 */
class Qdlib_Dao_SitePubCompTaskIdMap extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblSitePubCompTaskIdMap";

        $this->arrFieldsMap = array(
            'id'     => 'id',
            'logId'  => 'log_id',
            'pageId' => 'page_id',
            'taskId' => 'task_id'
        );

        $this->arrTypesMap = array(
            'id'     => Hk_Service_Db::TYPE_INT,
            'logId'  => Hk_Service_Db::TYPE_INT,
            'pageId' => Hk_Service_Db::TYPE_STR,
            'taskId' => Hk_Service_Db::TYPE_INT
        );
    }

    public function multiInsert($fields, $arr)
    {
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }
        $fields = Hk_Service_Db::mapField($fields, $this->arrFieldsMap);
        $ret = $this->_db->multiInsert($this->_table, $fields, $arr);
        return $ret === false ? false : true;
    }
}
