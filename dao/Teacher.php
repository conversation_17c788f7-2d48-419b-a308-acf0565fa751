<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Teacher.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 16:53
 */

class Zhi<PERSON>_Dao_Teacher extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'teacherUid',
        'teacherName',
        'teacherAvatar',
        'phone',
        'duty',
        'grade',
        'subject',
        'goodCnt',
        'teachCnt',
        'abstract',
        'deleted',
        'createTime',
        'updateTime',
        'operator_uid',
        'operator',
        'classTime',
        'extData',
        'type',
        'isRecommend',
        'fansCnt',
    );


    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblTeacher";

        $this->arrFieldsMap = array(
            'teacherUid' => 'teacher_uid',
            'teacherName' => 'teacher_name',
            'teacherAvatar' => 'teacher_avatar',
            'phone' => 'phone',
            'grade' => 'grade',
            'subject' => 'subject',
            'goodCnt' => 'good_cnt',
            'type' => 'type',
            'teachCnt' => 'teach_cnt',
            'abstract' => 'abstract',
            'deleted' => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'classTime' => 'class_time',
            'operatorUid' => 'operator_uid',
            'operator' => 'operator',
            'extData' => 'ext_data',
            'isRecommend' => 'is_recommend',
            'fansCnt' => 'fans_cnt',
        );

        $this->arrTypesMap = array(
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'grade' => Hk_Service_Db::TYPE_INT,
            'subject' => Hk_Service_Db::TYPE_INT,
            'goodCnt' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
            'isRecommend' => Hk_Service_Db::TYPE_INT,
            'teachCnt' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'classTime' => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
            'fansCnt' => Hk_Service_Db::TYPE_INT,
        );
    }
}