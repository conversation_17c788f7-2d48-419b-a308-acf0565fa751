<?php

/**
 * @file   AssistantBonus.php
 * <AUTHOR>
 * @date   2017-05-08
 * @brief  辅导老师奖金
 *
 **/
class Hkzb_Dao_Fudao_AssistantBonus extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblAssistantBonus";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'assistantUid'  => 'assistant_uid',
            'packCourseId'  => 'pack_course_id',
            'courseId'      => 'course_id',
            'lessonId'      => 'lesson_id',
            'classId'       => 'class_id',
            'groupId'       => 'group_id',
            'type'          => 'type',
            'learnSeason'   => 'learn_season',
            'attendTotal'   => 'attend_total',
            'attendCnt'     => 'attend_cnt',
            'attendRate'    => 'attend_rate',
            'attendBonus'   => 'attend_bonus',
            'homeworkTotal' => 'homework_total',
            'homeworkCnt'   => 'homework_cnt',
            'homeworkRate'  => 'homework_rate',
            'homeworkBonus' => 'homework_bonus',
            'continueTotal' => 'continue_total',
            'continueCnt'   => 'continue_cnt',
            'continueRate'  => 'continue_rate',
            'continueBonus' => 'continue_bonus',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
	    'extData'       => 'ext_data',
	    'personUid'     => 'person_uid',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'assistantUid'  => Hk_Service_Db::TYPE_INT,
            'packCourseId'  => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'lessonId'      => Hk_Service_Db::TYPE_INT,
            'classId'       => Hk_Service_Db::TYPE_INT,
            'groupId'       => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'learnSeason'   => Hk_Service_Db::TYPE_STR,
            'attendTotal'   => Hk_Service_Db::TYPE_INT,
            'attendCnt'     => Hk_Service_Db::TYPE_INT,
            'attendRate'    => Hk_Service_Db::TYPE_INT,
            'attendBonus'   => Hk_Service_Db::TYPE_INT,
            'homeworkTotal' => Hk_Service_Db::TYPE_INT,
            'homeworkCnt'   => Hk_Service_Db::TYPE_INT,
            'homeworkRate'  => Hk_Service_Db::TYPE_INT,
            'homeworkBonus' => Hk_Service_Db::TYPE_INT,
            'continueTotal' => Hk_Service_Db::TYPE_INT,
            'continueCnt'   => Hk_Service_Db::TYPE_INT,
            'continueRate'  => Hk_Service_Db::TYPE_INT,
            'continueBonus' => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
            'personUid'     => Hk_Service_Db::TYPE_INT,
        );
    }
}
