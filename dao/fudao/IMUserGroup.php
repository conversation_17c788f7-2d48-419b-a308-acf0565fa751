<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file IMUserGroup.php
 * <AUTHOR>
 * @date 2016/11/17
 * @brief 用户群组关系表dao
 *  
 **/

class Hkzb_Dao_Fudao_IMUserGroup extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblIMUserGroup";
        $this->arrFieldsMap = array(
            'groupId'     => 'group_id',
            'uid'         => 'uid',
            'nickName'    => 'nickname',
            'status'      => 'status',
            'innerStatus' => 'inner_status',
            'pushStatus'  => 'push_status',
            'roleId'      => 'role_id',
            'attendTime'  => 'attend_time',
            'leaveTime'   => 'leave_time',
            'operator'    => 'operator',
            'utime'       => 'utime',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'groupId'     => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'nickName'    => Hk_Service_Db::TYPE_STR,
            'status'      => Hk_Service_Db::TYPE_INT,
            'innerStatus' => Hk_Service_Db::TYPE_INT,
            'pushStatus'  => Hk_Service_Db::TYPE_INT,
            'roleId'      => Hk_Service_Db::TYPE_INT,
            'attendTime'  => Hk_Service_Db::TYPE_INT,
            'leaveTime'   => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_INT,
            'utime'       => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}