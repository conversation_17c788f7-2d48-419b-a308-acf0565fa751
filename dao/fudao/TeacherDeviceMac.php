<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/10/30 10:22
 */

class Hkzb_Dao_Fudao_TeacherDeviceMac extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_dbName = 'jxzt_teacher/jxzt_teacher';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherDeviceMac";
        $this->arrFieldsMap = [
            'id'          => 'id',
            'teacherUid'  => 'teacher_uid',
            'macAddress'  => 'mac_address',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'teacherUid'  => Hk_Service_Db::TYPE_INT,
            'macAddress'  => Hk_Service_Db::TYPE_STR,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        ];
    }
}