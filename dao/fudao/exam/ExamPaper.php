<?php

/**
 * 试卷模块
 *
 * 试卷模块分为三部分：试卷资源，试卷绑定关系，考试系统
 * 试卷资源     ：查询试卷信息，对外提供查询试卷信息接口，与题库系统衔接
 * 试卷绑定关系  ：试卷与课程或者章节的绑定关系，与课程系统衔接
 * 考试系统     ：用户考试行为与考试情况，与用户系统，课程系统衔接
 *
 * 底层数据结构
 *
 * 试卷资源: homework_fudao/tblExamPaper 表
 *
 */

/**
 * @file    ExamInfo.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    试卷资源
 *
 * 特殊字段说明
 * tid_list : 题目列表
 * tid_sort_list : 题目顺序
 *
 * content  : 试卷
 */
class Hkzb_Dao_Fudao_Exam_ExamPaper extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_db          = null;
        $this->_table       = "tblExamPaper";
        $this->arrFieldsMap = array(
            'examId'       => 'exam_id',
            'examType'     => 'exam_type',
            'grade'        => 'grade',
            'subject'      => 'subject',
            'tidList'      => 'tid_list',
            'content'      => 'content',
            'title'        => 'title',
            'extData'      => 'ext_data',
            'status'       => 'status',
            'deleted'      => 'deleted',
            'operatorUid'  => 'operator_uid',
            'operatorName' => 'operator_name',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
        );

        $this->arrTypesMap = array(
            'examId'       => Hk_Service_Db::TYPE_INT,
            'examType'     => Hk_Service_Db::TYPE_INT,
            'grade'        => Hk_Service_Db::TYPE_INT,
            'subject'      => Hk_Service_Db::TYPE_INT,
            'tidList'      => Hk_Service_Db::TYPE_JSON,
            'content'      => Hk_Service_Db::TYPE_JSON,
            'title'        => Hk_Service_Db::TYPE_STR,
            'extData'      => Hk_Service_Db::TYPE_JSON,
            'status'       => Hk_Service_Db::TYPE_INT,
            'deleted'      => Hk_Service_Db::TYPE_INT,
            'operatorUid'  => Hk_Service_Db::TYPE_INT,
            'operatorName' => Hk_Service_Db::TYPE_STR,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
        );
    }

}