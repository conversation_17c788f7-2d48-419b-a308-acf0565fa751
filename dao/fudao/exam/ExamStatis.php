<?php

/**
 * @file ExamStatis.php
 * <AUTHOR>
 * @date 2018-05-28
 * @brief 试卷维度的统计数据管理(堂堂测迁移，来自Hk_Dao_Practice_ExamStatis)
 *
 **/

class Hkzb_Dao_Fudao_Exam_ExamStatis extends Hk_Common_BaseDao {
    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_table       = "tblExamStatis";
        $this->_db          = null;
        $this->arrFieldsMap = array(
            'id'      => 'id',
            'examId'  => 'examId',
            'examType'=> 'examType',
            'score'   => 'score',
            'num'     => 'num',
            'graph'   => 'graph',
            'ext'     => 'ext',
            'userScores' => 'userScores',
        );

        $this->arrTypesMap = array(
            'id'      => Hk_Service_Db::TYPE_INT,    
            'examId'  => Hk_Service_Db::TYPE_INT, 
            'examType'=> Hk_Service_Db::TYPE_INT,
            'score'   => Hk_Service_Db::TYPE_INT,
            'num'     => Hk_Service_Db::TYPE_INT, 
            'graph'   => Hk_Service_Db::TYPE_JSON, //图形数据点json{"bottom":["s":得分,"no":排名],"top","top60p","top30p","top10p"}  
            'ext'     => Hk_Service_Db::TYPE_JSON,
            'userScores' => Hk_Service_Db::TYPE_STR,
        );
    }


    /**
     * 用时连接db
     * @param 
     * @return true/false
     **/
    private function _aotuReConn() {
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName);
        }
        return empty($this->_db)? false : true;
    }


    /**
     * 获取试卷维度的数据
     * @param integer $examId 
     * @return mix
     */
    public function getByExamId($examId) {
        if (!$this->_aotuReConn()) {
            return false;
        }
        if (0 >= $examId) {
            return false;
        }

        $arrFields = array('examId', 'score', 'num', 'graph', 'userScores');

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $sql = sprintf("SELECT %s FROM %s WHERE examId=%d  LIMIT 10", 
            $strFields,
            $this->_table,
            intval($examId)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }


    /**
     * 增加试卷-知识点维度的总分
     * @param array $paras  array(array('examId','score'), array(...) )
     * @return mix
     */
    public function incrForScore($paras) {
        if (!$this->_aotuReConn()) {
            return false;
        }
        if ( !is_array($paras) || empty($paras) ) {
            return false;
        }

        $strValues = array();
        foreach ($paras as $key => $val) {
            if ( (0 >= $val['examId']) || !isset($val['score']) ) {
                return false;
            }
            $temp = '\"'.intval($val['uid']).'\":'.intval($val['score']).',';
            $strValues[] = sprintf("%d,%d,%d,'%s'", intval($val['examId']), intval($val['score']), 1, $temp);
        }

        $sql = sprintf("INSERT INTO %s(examId,score,num,userScores) VALUES(%s) ON DUPLICATE KEY UPDATE score=score+VALUES(score),num=num+VALUES(num),userScores=concat(userScores,VALUES(userScores))",
            $this->_table,
            implode("),(", $strValues)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }
}