<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file IMUser.php
 * <AUTHOR>
 * @date 2016/11/17
 * @brief 用户表dao
 *  
 **/

class Hkzb_Dao_Fudao_IMUser extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblIMUserInfo";
        $this->arrFieldsMap = array(
            'uid'         => 'uid',
            'nickName'    => 'nickname',
            'userInfo'    => 'user_info',
            'type'        => 'type',
            'status'      => 'status',
            'friendCount' => 'friend_count',
            'friendGroup' => 'friend_group',
            'groupCount'  => 'group_count',
            'ctime'       => 'ctime',
            'operator'    => 'operator',
            'utime'       => 'utime',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'uid'         => Hk_Service_Db::TYPE_INT,
            'nickName'    => Hk_Service_Db::TYPE_STR,
            'userInfo'    => Hk_Service_Db::TYPE_JSON,
            'type'        => Hk_Service_Db::TYPE_INT,
            'status'      => Hk_Service_Db::TYPE_INT,
            'friendCount' => Hk_Service_Db::TYPE_INT,
            'friendGroup' => Hk_Service_Db::TYPE_JSON,
            'groupCount'  => Hk_Service_Db::TYPE_INT,
            'ctime'       => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_INT,
            'utime'       => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}