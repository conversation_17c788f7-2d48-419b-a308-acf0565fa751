<?php

/**
 * @file        AssistantSmsRecord.php
 * <AUTHOR>
 * @create_date 2017-09-30
 * @brief       短信记录
 *
 **/
class Hkzb_Dao_Fudao_AssistantSmsRecord extends Hk_Common_BaseDao
{

    public static $allFields = array(
        'smsId',
        'sourceType',
        'fromUid',
        'fromPhone',
        'toUid',
        'toPhone',
        'sendTime',
        'sendType',
        'sendResult',
        'messageData',
        'createTime',
        'updateTime',
        'deleted',
        'triggerType',
        'courseId',
        'learnSeason',
        'lessonId',
        'classId',
        'extData',
        'isConfirm',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblAssistantSmsRecord";
        $this->arrFieldsMap = array(
            'smsId'       => 'sms_id',
            'sourceType'  => 'source_type',
            'fromUid'     => 'from_uid',
            'fromPhone'   => 'from_phone',
            'toUid'       => 'to_uid',
            'toPhone'     => 'to_phone',
            'sendTime'    => 'send_time',
            'sendType'    => 'send_type',
            'sendResult'  => 'send_result',
            'messageData' => 'message_data',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'deleted'     => 'deleted',
            'triggerType' => 'trigger_type',
            'courseId'    => 'course_id',
            'learnSeason' => 'learn_season',
            'lessonId'    => 'lesson_id',
            'classId'     => 'class_id',
            'extData'     => 'ext_data',
            'isConfirm'     => 'is_confirm',
        );

        $this->arrTypesMap = array(
            'smsId'       => Hk_Service_Db::TYPE_INT,
            'sourceType'  => Hk_Service_Db::TYPE_INT,
            'fromUid'     => Hk_Service_Db::TYPE_INT,
            'fromPhone'   => Hk_Service_Db::TYPE_STR,
            'toUid'       => Hk_Service_Db::TYPE_INT,
            'toPhone'     => Hk_Service_Db::TYPE_STR,
            'sendTime'    => Hk_Service_Db::TYPE_INT,
            'sendType'    => Hk_Service_Db::TYPE_INT,
            'sendResult'  => Hk_Service_Db::TYPE_INT,
            'messageData' => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'triggerType' => Hk_Service_Db::TYPE_INT,
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'learnSeason' => Hk_Service_Db::TYPE_STR,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'classId'     => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
            'isConfirm'     => Hk_Service_Db::TYPE_INT,
        );
    }
}