<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file LessonState.php
 * <AUTHOR>
 * @date 2015/11/17 13:50:15
 * @brief 课节状态
 *  
 **/

class Hkzb_Dao_Fudao_LessonState extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblLessonState";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'studentUid'   => 'student_uid',
            'courseId'     => 'course_id',
            'lessonId'     => 'lesson_id',
            'teacherUid'   => 'teacher_uid',
            'assistantUid' => 'assistant_uid',
            'classId'      => 'class_id',
            'attend'       => 'attend',
            'rightNum'     => 'right_num',
            'totalNum'     => 'total_num',
            'homework'     => 'homework',
            'report'       => 'report',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'extData'      => 'ext_data',
            'hasCourse'    => 'has_course',
            'attendLong'   => 'attend_long',
            'submitTime'   => 'submit_time',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'studentUid'   => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'lessonId'     => Hk_Service_Db::TYPE_INT,
            'teacherUid'   => Hk_Service_Db::TYPE_INT,
            'assistantUid' => Hk_Service_Db::TYPE_INT,
            'classId'      => Hk_Service_Db::TYPE_INT,
            'attend'       => Hk_Service_Db::TYPE_INT,
            'rightNum'     => Hk_Service_Db::TYPE_INT,
            'totalNum'     => Hk_Service_Db::TYPE_INT,
            'homework'     => Hk_Service_Db::TYPE_INT,
            'report'       => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'hasCourse'    => Hk_Service_Db::TYPE_INT,
            'attendLong'   => Hk_Service_Db::TYPE_INT,
            'extData'      => Hk_Service_Db::TYPE_JSON,
            'submitTime'   => Hk_Service_Db::TYPE_INT,
        );
    }
}