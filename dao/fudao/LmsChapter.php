<?php
/**
 * @filename:      LmsChapter.php
 * @author:        <EMAIL>
 * @desc:          Lecture课程章节表
 * @create:        2017-07-05 17:44:57
 * @last modified: 2017-07-05 17:44:57
 */
class Hkzb_Dao_Fudao_LmsChapter extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblLmsChapter';
        $this->arrFieldsMap = [
            'id'                => 'id',
            'chapterId'         => 'chapterId',
            'courseId'          => 'courseId',
            'courseName'        => 'courseName',
            'teacherUid'        => 'teacherUid',
            'teacherName'       => 'teacherName',
            'lectureAddTime'    => 'lectureAddTime',
            'video'             => 'video',
            'syncStatus'        => 'syncStatus',
            'deleted'           => 'deleted',
            'grade'             => 'grade',
            'subject'           => 'subject',
            'source'            => 'source',
            'semester'          => 'semester',
            'year'              => 'year',
            'chapterName'       => 'chapterName',
            'lecturePdf'        => 'lecturePdf',
            'lecturePic'        => 'lecturePic',
            'lecture'           => 'lecture',
            'studentLecturePdf' => 'studentLecturePdf',
            'studentLecturePic' => 'studentLecturePic',
            'studentLecture'    => 'studentLecture',
            'status'            => 'status',
            'page'              => 'page',
            'startTime'         => 'startTime',
            'stopTime'          => 'stopTime',
            'courseType'        => 'courseType',
            'readNoteStatus'    => 'readNoteStatus',
            'writeNoteStatus'   => 'writeNoteStatus',
            'idx'               => 'idx',
        ];

        $this->arrTypesMap = [
            'id'                => Hk_Service_Db::TYPE_INT,
            'chapterId'         => Hk_Service_Db::TYPE_INT,
            'courseId'          => Hk_Service_Db::TYPE_INT,
            'courseName'        => Hk_Service_Db::TYPE_STR,
            'teacherUid'        => Hk_Service_Db::TYPE_INT,
            'teacherName'       => Hk_Service_Db::TYPE_STR,
            'lectureAddTime'    => Hk_Service_Db::TYPE_INT,
            'video'             => Hk_Service_Db::TYPE_STR,
            'syncStatus'        => Hk_Service_Db::TYPE_INT,
            'deleted'           => Hk_Service_Db::TYPE_INT,
            'grade'             => Hk_Service_Db::TYPE_INT,
            'subject'           => Hk_Service_Db::TYPE_INT,
            'source'            => Hk_Service_Db::TYPE_INT,
            'semester'          => Hk_Service_Db::TYPE_INT,
            'year'              => Hk_Service_Db::TYPE_INT,
            'chapterName'       => Hk_Service_Db::TYPE_STR,
            'lecturePdf'        => Hk_Service_Db::TYPE_JSON,
            'lecturePic'        => Hk_Service_Db::TYPE_JSON,
            'lecture'           => Hk_Service_Db::TYPE_JSON,
            'studentLecturePdf' => Hk_Service_Db::TYPE_JSON,
            'studentLecturePic' => Hk_Service_Db::TYPE_JSON,
            'studentLecture'    => Hk_Service_Db::TYPE_JSON,
            'status'            => Hk_Service_Db::TYPE_INT,
            'page'              => Hk_Service_Db::TYPE_STR,
            'startTime'         => Hk_Service_Db::TYPE_INT,
            'stopTime'          => Hk_Service_Db::TYPE_INT,
            'courseType'        => Hk_Service_Db::TYPE_INT,
            'readNoteStatus'    => Hk_Service_Db::TYPE_INT,
            'writeNoteStatus'   => Hk_Service_Db::TYPE_INT,
            'idx'               => Hk_Service_Db::TYPE_INT,
        ];
    }
}