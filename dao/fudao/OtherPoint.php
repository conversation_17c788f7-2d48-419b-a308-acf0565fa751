<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file OtherPoint.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 讲义电子化
 *
 **/

class Hkzb_Dao_Fudao_OtherPoint extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblOtherPoint";
        $this->arrFieldsMap = array(
            'pid'        => 'pid',
            'picName'    => 'pic_name',
            'tid'        => 'tid',
            'pointName'  => 'point_name',
            'bookName'   => 'book_name',
            'status'     => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'subUid'     => 'sub_uid',
            'checkUid'   => 'check_uid',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'pid'        => Hk_Service_Db::TYPE_INT,
            'picName'    => Hk_Service_Db::TYPE_STR,
            'tid'        => Hk_Service_Db::TYPE_INT,
            'pointName'  => Hk_Service_Db::TYPE_STR,
            'bookName'   => Hk_Service_Db::TYPE_STR,
            'channelId'  => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'subUid'     => Hk_Service_Db::TYPE_INT,
            'checkUid'   => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}