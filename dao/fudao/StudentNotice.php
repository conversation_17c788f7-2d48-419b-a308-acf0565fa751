<?php

/**
 * @file   Class StudentCdnNotice.php
 * <AUTHOR>
 * @date   2017/09/20 17:00:06
 * @brief  学生CDN打点信息
 **/
class Hkzb_Dao_Fudao_StudentNotice extends Hk_Common_BaseMultiDao
{
    public function __construct()
    {
        $this->_dbName      = 'zhibo/zhibo_notice';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentNotice";
        $this->_tableName   = "tblStudentNotice";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 10000;
        $this->_partionKey  = 'lessonId';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'action'      => 'action',
            'lessonId'    => 'lesson_id',
            'studentId'   => 'student_id',
            'source'      => 'source',
            'vcname'      => 'vcname',
            'device'      => 'device',
            'cost'        => 'cost',
            'pullAddress' => 'pull_address',
            'pullIp'      => 'pull_ip',
            'network'     => 'network',
            'clientTime'  => 'client_time',
            'studentIp'   => 'student_ip',
            'extData'     => 'ext_data',
            'createTime'  => 'create_time',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'action'      => Hk_Service_Db::TYPE_INT,
            'studentId'   => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'source'      => Hk_Service_Db::TYPE_STR,
            'vcname'      => Hk_Service_Db::TYPE_STR,
            'device'      => Hk_Service_Db::TYPE_STR,
            'cost'        => Hk_Service_Db::TYPE_INT,
            'pullAddress' => Hk_Service_Db::TYPE_STR,
            'pullIp'      => Hk_Service_Db::TYPE_STR,
            'network'     => Hk_Service_Db::TYPE_STR,
            'clientTime'  => Hk_Service_Db::TYPE_STR,
            'studentIp'   => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
            'createTime'  => Hk_Service_Db::TYPE_INT,
        );
    }
}