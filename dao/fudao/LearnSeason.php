<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file LearnSeason.php
 * <AUTHOR>
 * @date 2016/10/21 15:50:15
 * @brief 学季
 *  
 **/

class Hkzb_Dao_Fudao_LearnSeason extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblLearnSeason";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'learnSeason' => 'learn_season',
            'grade'       => 'grade',
            'subject'     => 'subject',
            'seasonId'    => 'season_id',
            'content'     => 'content',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'grade'      => Hk_Service_Db::TYPE_INT,
            'subject'    => Hk_Service_Db::TYPE_INT,
            'seasonId'   => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}