<?php
/**
 * @file   Class StudentCdnNoticeLog.php
 * <AUTHOR>
 * @date   2018/05/29
 * @brief  学生全局关键打点信息
 **/

class Hkzb_Dao_Fudao_StudentNoticeLog extends Hk_Common_BaseMultiDao {
    public function __construct() {
        $this->_dbName      = 'zhibo_jx/zb_studentnoticelog';
        $this->_db          = null;
        $this->_table       = 'tblStudentNoticeLog';
        $this->_tableName   = "tblStudentNoticeLog";
        $this->_partionType = self::TYPE_TABLE_PARTION_MOD;
        $this->_partionNum  = 20;
        $this->_partionKey  = 'studentUid';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'logId'       => 'log_id',
            'studentUid'  => 'student_uid',
            'lessonId'    => 'lesson_id',
            'os'          => 'os',
            'vc'          => 'vc',
            'vcname'      => 'vcname',
            'appId'       => 'app_id',
            'device'      => 'device',
            'pid'         => 'pid',
            'event'       => 'event',
            't'           => 't',
            'data'        => 'data',
            'requestTime' => 'request_time',
            'createTime'  => 'create_time',
        );
        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'logId'       => Hk_Service_Db::TYPE_INT,
            'studentUid'  => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'os'          => Hk_Service_Db::TYPE_STR,
            'vc'          => Hk_Service_Db::TYPE_INT,
            'vcname'      => Hk_Service_Db::TYPE_STR,
            'appId'       => Hk_Service_Db::TYPE_STR,
            'device'      => Hk_Service_Db::TYPE_STR,
            'pid'         => Hk_Service_Db::TYPE_STR,
            'event'       => Hk_Service_Db::TYPE_STR,
            't'           => Hk_Service_Db::TYPE_INT,
            'data'        => Hk_Service_Db::TYPE_JSON,
            'requestTime' => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
        );
    }
}