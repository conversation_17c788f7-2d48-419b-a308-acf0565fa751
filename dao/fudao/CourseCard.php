<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file CourseCard.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 课程
 *  
 **/

class Hkzb_Dao_Fudao_CourseCard extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblCourseCard";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'name'              => 'name',
            'tagName'           => 'tag_name',
            'grade'             => 'grade',
            'subject'           => 'subject',
            'avatar'            => 'avatar',
            'learnSeason'       => 'learn_season',
            'seasonNum'         => 'season_num',
            'status'            => 'status',
            'registerStartTime' => 'register_start_time',
            'registerStopTime'  => 'register_stop_time',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'operatorUid'       => 'operator_uid',
            'operator'          => 'operator',
            'extData'           => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'name'              => Hk_Service_Db::TYPE_STR,
            'tagName'           => Hk_Service_Db::TYPE_STR,
            'grade'             => Hk_Service_Db::TYPE_INT,
            'subject'           => Hk_Service_Db::TYPE_INT,
            'learnSeason'       => Hk_Service_Db::TYPE_STR,
            'avatar'            => Hk_Service_Db::TYPE_STR,
            'seasonNum'         => Hk_Service_Db::TYPE_INT,
            'registerStartTime' => Hk_Service_Db::TYPE_INT,
            'registerStopTime'  => Hk_Service_Db::TYPE_INT,
            'status'            => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'operatorUid'       => Hk_Service_Db::TYPE_INT,
            'operator'          => Hk_Service_Db::TYPE_STR,
            'extData'           => Hk_Service_Db::TYPE_JSON,
        );
    }
}