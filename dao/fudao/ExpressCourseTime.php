<?php
/**
* @file: ExpressCourseTime.php
* @desc: 课程讲义寄送时间映射表
* @date: 2018年3月28日 下午3:17:21
* @author: <PERSON><PERSON><PERSON><PERSON><PERSON> <lian<PERSON><PERSON><PERSON>@zuoyebang.com>
*/

class Hkzb_Dao_Fudao_ExpressCourseTime extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblExpressCourseTime";
        
        $this->arrFieldsMap = array(
            'id'                   => 'id',
            'courseId'             => 'course_id',
            'sendTime'             => 'send_time',
            'deleted'              => 'deleted',
            'createTime'           => 'create_time',
            'updateTime'           => 'update_time',
            'extData'              => 'ext_data',
        );
        
        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_INT,
            'courseId'              => Hk_Service_Db::TYPE_INT,
            'sendTime'              => Hk_Service_Db::TYPE_INT,
            'deleted'               => Hk_Service_Db::TYPE_INT,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'extData'               => Hk_Service_Db::TYPE_JSON,
        );
    }
}