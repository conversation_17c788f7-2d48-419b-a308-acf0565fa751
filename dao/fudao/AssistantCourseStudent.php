<?php

/**
 * @file    AssistantCourseStudent.php
 * <AUTHOR>
 * @date    2017-11-11
 * @brief   班主任课程学生表
 *
 **/
class Hkzb_Dao_Fudao_AssistantCourseStudent extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblAssistantCourseStudent';
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'studentUid'    => 'student_uid',
            'courseId'      => 'course_id',
            'assistantUid'  => 'assistant_uid',
            'classId'       => 'class_id',
            'new'           => 'new',
            'weixin'        => 'weixin',
            'interview'     => 'interview',
            'backInterview' => 'back_interview',
            'preContinue'   => 'pre_continue',
            'regTime'       => 'reg_time',
            'deleteTime'    => 'delete_time',
            'status'        => 'status',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'extData'       => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'assistantUid'  => Hk_Service_Db::TYPE_INT,
            'classId'       => Hk_Service_Db::TYPE_INT,
            'new'           => Hk_Service_Db::TYPE_INT,
            'weixin'        => Hk_Service_Db::TYPE_INT,
            'interview'     => Hk_Service_Db::TYPE_INT,
            'backInterview' => Hk_Service_Db::TYPE_INT,
            'preContinue'   => Hk_Service_Db::TYPE_INT,
            'regTime'       => Hk_Service_Db::TYPE_INT,
            'deleteTime'    => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}