<?php
/**
 * Created by PhpStorm.
 * User: ya<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/1/25
 * Time: 15:18
 * desc：营销策略表
 */

class Hkzb_Dao_Fudao_Salestrategy_SaleStrategy extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'zbstock/zb_zbbiz';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblSaleStrategy";
        $this->arrFieldsMap = array(
              'strategyId'       => 'strategy_id',
              'strategyName'     => 'strategy_name',
              'salesSlogan'      => 'sales_slogan',
              'startTime'        => 'start_time',
              'stopTime'         => 'stop_time',
              'strategyType'     => 'strategy_type',
              'subDiscountType'  => 'sub_discount_type',
              'userLimit'        => 'user_limit',
              'xuebu'            => 'xuebu',
              'courseType'       => 'course_type',
              'gradeLimit'       => 'grade_limit',
              'subjectLimit'     => 'subject_limit',
              'learnSeasonLimit' => 'learn_season_limit',
              'discountDesc'     => 'discount_desc',
              'deleted'          => 'deleted',
              'channelAttribute' => 'channel_attribute',
              'roleAttribute'    => 'role_attribute',
              'skuAttribute'     => 'sku_attribute',
              'otherAttribute'   => 'other_attribute',
              'recommendRule'    => 'recommend_rule',
              'rule'             => 'rule',
              'extData'          => 'ext_data',
              'createTime'       => 'create_time',
              'createUserId'     => 'create_user_id',
              'updateTime'       => 'update_time',
              'giftType'       => 'gift_type'
        );
        $this->arrTypesMap  = array(
            'strategyId'       => Hk_Service_Db::TYPE_INT,
            'strategyName'     => Hk_Service_Db::TYPE_STR,
            'salesSlogan'      => Hk_Service_Db::TYPE_STR,
            'startTime'        => Hk_Service_Db::TYPE_INT,
            'stopTime'         => Hk_Service_Db::TYPE_INT,
            'strategyType'     => Hk_Service_Db::TYPE_INT,
            'subDiscountType'  => Hk_Service_Db::TYPE_INT,
            'userLimit'        => Hk_Service_Db::TYPE_STR,
            'xuebu'            => Hk_Service_Db::TYPE_INT,
            'courseType'       => Hk_Service_Db::TYPE_INT,
            'gradeLimit'       => Hk_Service_Db::TYPE_STR,
            'subjectLimit'     => Hk_Service_Db::TYPE_STR,
            'learnSeasonLimit' => Hk_Service_Db::TYPE_STR,
            'discountDesc'     => Hk_Service_Db::TYPE_STR,
            'deleted'          => Hk_Service_Db::TYPE_INT,
            'channelAttribute' => Hk_Service_Db::TYPE_JSON,
            'roleAttribute'    => Hk_Service_Db::TYPE_JSON,
            'skuAttribute'     => Hk_Service_Db::TYPE_JSON,
            'otherAttribute'   => Hk_Service_Db::TYPE_JSON,
            'recommendRule'    => Hk_Service_Db::TYPE_JSON,
            'rule'             => Hk_Service_Db::TYPE_JSON,
            'extData'          => Hk_Service_Db::TYPE_JSON,
            'createUserId'     => Hk_Service_Db::TYPE_INT,
            'createTime'       => Hk_Service_Db::TYPE_INT,
            'updateTime'       => Hk_Service_Db::TYPE_INT,
        );
    }
}