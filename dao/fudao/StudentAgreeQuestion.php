<?php
/**
 * Created by PhpStorm.
 * @file StudentAgreeQuestion.php
 * <AUTHOR>
 * @date 18-1-16
 * @version
 * @brief
 *
 **/


class Hkzb_Dao_Fudao_StudentAgreeQuestion extends Hk_Common_BaseDao
{
    public static $arrFields = array(
        'id',
        'studentUid',
        'qid',
        'status',
        'createTime',
        'updateTime',
        'extData',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblStudentAgreeQuestion';
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'qid'          => 'qid',
            'studentUid'   => 'student_uid',
            'status'       => 'status',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'extData'      => 'ext_data'
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'qid'          => Hk_Service_Db::TYPE_INT,
            'studentUid'   => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'extData'      => Hk_Service_Db::TYPE_JSON,
        );
    }

}