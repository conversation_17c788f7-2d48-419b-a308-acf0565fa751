<?php
/**
 * Created by PhpStorm.
 * User: renka<PERSON><EMAIL>
 * Date: 2018/4/19
 * Time: 17:42
 */
class Hkzb_Dao_Fudao_TeacherSaysConf extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherSaysConf";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'stats'         => 'stats',
            'type'          => 'type',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        );

        $this->arrTypesMap  = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'stats'         => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        );
    }
}
