<?php

/**
 * @file   CallRecord.php
 * <AUTHOR>
 * @date   2016-01-06
 * @brief  呼叫记录
 *
 **/
class Hkzb_Dao_Fudao_CallRecord extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_table       = "tblCallRecord";
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'uid'        => 'uid',
            'cid'        => 'cid',
            'product'    => 'product',
            'subProduct' => 'sub_product',
            'fromPhone'  => 'from_phone',
            'toPhone'    => 'to_phone',
            'callTime'   => 'call_time',
            'startTime'  => 'start_time',
            'endTime'    => 'end_time',
            'duration'   => 'duration',
            'recordFile' => 'record_file',
            'evaluate'   => 'evaluate',
            'queue'      => 'queue',
            'errType'    => 'err_type',
            'type'       => 'type',
            'status'     => 'status',
            'createTime' => 'create_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'cid'        => Hk_Service_Db::TYPE_STR,
            'product'    => Hk_Service_Db::TYPE_INT,
            'subProduct' => Hk_Service_Db::TYPE_INT,
            'fromPhone'  => Hk_Service_Db::TYPE_STR,
            'toPhone'    => Hk_Service_Db::TYPE_STR,
            'callTime'   => Hk_Service_Db::TYPE_INT,
            'startTime'  => Hk_Service_Db::TYPE_INT,
            'endTime'    => Hk_Service_Db::TYPE_INT,
            'duration'   => Hk_Service_Db::TYPE_INT,
            'recordFile' => Hk_Service_Db::TYPE_STR,
            'errType'    => Hk_Service_Db::TYPE_INT,
            'type'       => Hk_Service_Db::TYPE_INT,
            'evaluate'   => Hk_Service_Db::TYPE_INT,
            'queue'      => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}