<?php
/**
 * Created by PhpStorm.
 * User: ni<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/5/8
 * Time: 17:10
 *
 */


/**
 * Class Hkzb_Dao_Fudao_ActivityPageTemplet
 * 运营模块自定义模板保存
 */
class Hkzb_Dao_Fudao_ActivityPageTemplet extends Hk_Common_BaseDao{

    public static $arrFields = [
        'id',
        'act_id',
        'reorder',
        'model_info',
        'create_time',
        'update_time'
    ];

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblActivityPageTemplet';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'reorder'     => 'reorder',
            'actId'       => 'act_id',
            'modelInfo'   => 'model_info',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'reorder'      => Hk_Service_Db::TYPE_INT,
            'actId'        => Hk_Service_Db::TYPE_INT,
            'modelInfo'    => Hk_service_Db::TYPE_JSON,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
        );
    }

}