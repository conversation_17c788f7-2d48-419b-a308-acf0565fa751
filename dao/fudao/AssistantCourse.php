<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file AssistantCourse.php
 * <AUTHOR> @date 2017/08/10 19:05:01
 * @brief 班主任课程分配表
 *
 **/
class Hkzb_Dao_Fudao_AssistantCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblAssistantCourse';
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'assistantUid' => 'assistant_uid',
            'courseId'     => 'course_id',
            'status'       => 'status',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'operatorUid'  => 'operator_uid',
            'operator'     => 'operator',
            'extData'      => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'assistantUid' => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'operatorUid'  => Hk_Service_Db::TYPE_INT,
            'operator'     => Hk_Service_Db::TYPE_STR,
            'extData'      => Hk_Service_Db::TYPE_JSON,
        );
    }
}