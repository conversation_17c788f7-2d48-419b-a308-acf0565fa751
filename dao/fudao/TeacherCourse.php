<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   TeacherCourse.php
 * <AUTHOR>
 * @date   2015/11/17 13:50:15
 * @brief  老师排课表
 *
 **/
class Hkzb_Dao_Fudao_TeacherCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherCourse";
        $this->arrFieldsMap = array(
            'id'                 => 'id',
            'teacherUid'         => 'teacher_uid',
            'courseId'           => 'course_id',
            'duty'               => 'duty',
            'classId'            => 'class_id',
            'studentCnt'         => 'student_cnt',
            'status'             => 'status',
            'startTime'          => 'start_time',
            'createTime'         => 'create_time',
            'updateTime'         => 'update_time',
            'curLessonId'        => 'cur_lesson_id',
            'curLessonName'      => 'cur_lesson_name',
            'curLessonStartTime' => 'cur_lesson_start_time',
            'curLessonStopTime'  => 'cur_lesson_stop_time',
            'operatorUid'        => 'operator_uid',
            'operator'           => 'operator',
            'extData'            => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'                 => Hk_Service_Db::TYPE_INT,
            'teacherUid'         => Hk_Service_Db::TYPE_INT,
            'courseId'           => Hk_Service_Db::TYPE_INT,
            'duty'               => Hk_Service_Db::TYPE_INT,
            'classId'            => Hk_Service_Db::TYPE_INT,
            'studentUid'         => Hk_Service_Db::TYPE_INT,
            'status'             => Hk_Service_Db::TYPE_INT,
            'startTime'          => Hk_Service_Db::TYPE_INT,
            'createTime'         => Hk_Service_Db::TYPE_INT,
            'updateTime'         => Hk_Service_Db::TYPE_INT,
            'curLessonId'        => Hk_Service_Db::TYPE_INT,
            'curLessonName'      => Hk_Service_Db::TYPE_STR,
            'curLessonStartTime' => Hk_Service_Db::TYPE_INT,
            'curLessonStopTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid'        => Hk_Service_Db::TYPE_INT,
            'operator'           => Hk_Service_Db::TYPE_STR,
            'extData'            => Hk_Service_Db::TYPE_JSON,
        );
    }
}