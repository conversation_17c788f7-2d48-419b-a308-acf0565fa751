<?php
/**
 * @file ExpressStatus.php
 * <AUTHOR>
 * @date 2018-08-31
 * @brief 订单状态表
 *
 **/

class Hkzb_Dao_Fudao_ExpressSendStatus extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblExpressSendStatus";

        $this->arrFieldsMap = array(
            'id'            => 'id',
            'expressId'     => 'express_id',
            'expressNumber' => 'express_number',
            'status'        => 'status',
            'statusFlow'    => 'status_flow',
            'extraData'     => 'extra_data',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'expressId'         => Hk_Service_Db::TYPE_INT,
            'expressNumber'     => Hk_Service_Db::TYPE_STR,
            'status'            => Hk_Service_Db::TYPE_INT,
            'statusFlow'        => Hk_Service_Db::TYPE_JSON,
            'extraData'         => Hk_Service_Db::TYPE_JSON,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
        );
    }
}