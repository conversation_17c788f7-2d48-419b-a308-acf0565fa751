<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Phone.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 电话
 *  
 **/

class Hkzb_Dao_Fudao_Phone extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblPhone";
        $this->arrFieldsMap = array(
            'phoneId'    => 'phone_id',
            'callSid'    => 'call_sid',
            'department' => 'department',
            'caller'     => 'caller',
            'called'     => 'called',
            'startTime'  => 'start_time',
            'endTime'    => 'end_time',
            'duration'   => 'duration',
            'byeType'    => 'bye_type',
            'isDown'     => 'is_down',
            'created'    => 'created',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'phoneId'    => Hk_Service_Db::TYPE_INT,
            'callSid'    => Hk_Service_Db::TYPE_STR,
            'department' => Hk_Service_Db::TYPE_INT,
            'caller'     => Hk_Service_Db::TYPE_STR,
            'called'     => Hk_Service_Db::TYPE_STR,
            'startTime'  => Hk_Service_Db::TYPE_INT,
            'endTime'    => Hk_Service_Db::TYPE_INT,
            'duration'   => Hk_Service_Db::TYPE_INT,
            'byeType'    => Hk_Service_Db::TYPE_STR,
            'isDown'     => Hk_Service_Db::TYPE_INT,
            'created'    => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}