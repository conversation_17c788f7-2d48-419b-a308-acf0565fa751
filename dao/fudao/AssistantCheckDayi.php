<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file AssistantCheckDayi.php
 * <AUTHOR>
 * @date 2017/08/28 16:16:24
 * @brief 作业质检信息表
 *
 **/
class Hkzb_Dao_Fudao_AssistantCheckDayi extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblAssistantCheckDayi';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'qid'         => 'qid',
            'answerUid'   => 'answer_uid',
            'qualified'   => 'qualified',
            'reasonCode'  => 'reason_code',
            'operatorUid' => 'operator_uid',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'qid'         => Hk_Service_Db::TYPE_INT,
            'answerUid'   => Hk_Service_Db::TYPE_INT,
            'qualified'   => Hk_Service_Db::TYPE_INT,
            'reasonCode'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}