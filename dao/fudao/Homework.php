<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Homework.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 辅导题库
 *  
 **/

class Hkzb_Dao_Fudao_Homework extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblHomework";
        $this->arrFieldsMap = array(
            'tid'        => 'tid',
            'content'    => 'content',
            'gradeId'    => 'grade_id',
            'subjectId'  => 'subject_id',
            'channel'    => 'channel',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'tid'        => Hk_Service_Db::TYPE_INT,
            'content'    => Hk_Service_Db::TYPE_JSON,
            'gradeId'    => Hk_Service_Db::TYPE_INT,
            'subjectId'  => Hk_Service_Db::TYPE_INT,
            'channel'    => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}