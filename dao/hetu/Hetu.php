<?php

class Qdlib_Dao_Hetu_Hetu extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_HETU;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_db = null;
    }

    public function multiInsert($fields, $values, $onDup = null)
    {
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }
        $fields = Hk_Service_Db::mapField($fields, $this->arrFieldsMap);
        $onDup = Hk_Service_Db::mapRow($onDup, $this->arrFieldsMap);
        return $this->_db->multiInsert($this->_table, $fields, $values, null, $onDup);
    }
}