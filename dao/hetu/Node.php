<?php
/**
 * @file Node.php
 * @Description
 * @Date 2021/07/07 10:54 下午
 * @<NAME_EMAIL>
 */

//文件夹节点
class Qdlib_Dao_Hetu_Node
{
    //子节点为数组
    public $children = [];

    public $id;
    public $name;
    public $level;
    public $parentid;

    //若挂载了素材,素材为数组
    public $materials = [];

    public function __construct($id, $name, $level, $parentid)
    {
        $this->id = $id;
        $this->name = $name;
        $this->level = $level;
        $this->parentid = $parentid;
    }

    public function getBasicInfo()
    {
        return ['id' => $this->id, 'name' => $this->name, 'level' => $this->level, 'parentid' => $this->parentid];
    }

    public function copyBasicInfo()
    {
        $node = new Qdlib_Dao_Hetu_Node($this->id, $this->name, $this->level, $this->parentid);
        return $node;
    }
}