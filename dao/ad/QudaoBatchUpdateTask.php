<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/24
 * Time: 19:05
 */
class Qdlib_Dao_Ad_QudaoBatchUpdateTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoBatchUpdateTask";

        $this->arrFieldsMap = array(
            'id'         => 'id',
            'channel'    => 'channel',
            'flag'       => 'flag',
            'ids'        => 'ids',
            'updateData' => 'update_data',
            'failedNum'  => 'failedNum',
            'successNum' => 'successNum',
            'totalNum'   => 'totalNum',
            'status'     => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'finishTime' => 'finish_time',
            'creator'    => 'creator',

        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'channel'    => Hk_Service_Db::TYPE_STR,
            'flag'       => Hk_Service_Db::TYPE_INT,
            'ids'        => Hk_Service_Db::TYPE_STR,
            'updateData' => Hk_Service_Db::TYPE_STR,
            'failedNum'  => Hk_Service_Db::TYPE_INT,
            'successNum' => Hk_Service_Db::TYPE_INT,
            'totalNum'   => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'finishTime' => Hk_Service_Db::TYPE_INT,
            'creator'    => Hk_Service_Db::TYPE_STR,

        );
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}