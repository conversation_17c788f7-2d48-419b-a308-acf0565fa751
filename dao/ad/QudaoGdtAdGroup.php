<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/25
 * Time: 16:31
 */
class Qdlib_Dao_Ad_QudaoGdtAdGroup extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoGdtAdGroup";

        $this->arrFieldsMap = array(
            'adgroupId'          => 'adgroup_id',
            'channel'            => 'channel',
            'account'            => 'account',
            'campaignId'         => 'campaign_id',
            'adgroupName'        => 'adgroup_name',
            'bidAmount'          => 'bid_amount',
            'pricingMode'        => 'pricing_mode',
            'budget'             => 'budget',
            'deepBidType'        => 'deep_bid_type',
            'deepBidGoal'        => 'deep_bid_goal',
            'deepCpabid'         => 'deep_cpabid',
            'promotedObjectType' => 'promoted_object_type',
            'status'             => 'status',
            'startDate'          => 'start_date',
            'endDate'            => 'end_date',
            'targetingId'        => 'targeting_id',
            'billingEvent'       => 'billing_event',
            'timeSeries'         => 'time_series',
            'thirdCreateTime'    => 'third_create_time',
            'thirdUpdateTime'    => 'third_update_time',
            'adMode'             => 'ad_mode',
            'createTime'         => 'create_time',
            'updateTime'         => 'update_time',
            'deleted'            => 'deleted',
            'systemStatus'       => 'system_status',
            'dmpInclude'         => 'dmp_include',
            'dmpExclude'         => 'dmp_exclude',
            'learningStatus'     => 'learning_status',

        );

        $this->arrTypesMap = array(
            'adgroupId'          => Hk_Service_Db::TYPE_INT,
            'channel'            => Hk_Service_Db::TYPE_STR,
            'account'            => Hk_Service_Db::TYPE_STR,
            'campaignId'         => Hk_Service_Db::TYPE_INT,
            'adgroupName'        => Hk_Service_Db::TYPE_STR,
            'bidAmount'          => Hk_Service_Db::TYPE_INT,
            'pricingMode'        => Hk_Service_Db::TYPE_STR,
            'budget'             => Hk_Service_Db::TYPE_INT,
            'deepBidType'        => Hk_Service_Db::TYPE_STR,
            'deepBidGoal'        => Hk_Service_Db::TYPE_STR,
            'deepCpabid'         => Hk_Service_Db::TYPE_INT,
            'promotedObjectType' => Hk_Service_Db::TYPE_STR,
            'status'             => Hk_Service_Db::TYPE_INT,
            'startDate'          => Hk_Service_Db::TYPE_STR,
            'endDate'            => Hk_Service_Db::TYPE_STR,
            'targetingId'        => Hk_Service_Db::TYPE_INT,
            'billingEvent'       => Hk_Service_Db::TYPE_STR,
            'timeSeries'         => Hk_Service_Db::TYPE_STR,
            'thirdCreateTime'    => Hk_Service_Db::TYPE_INT,
            'thirdUpdateTime'    => Hk_Service_Db::TYPE_INT,
            'adMode'             => Hk_Service_Db::TYPE_INT,
            'createTime'         => Hk_Service_Db::TYPE_INT,
            'updateTime'         => Hk_Service_Db::TYPE_INT,
            'deleted'            => Hk_Service_Db::TYPE_INT,
            'systemStatus'       => Hk_Service_Db::TYPE_INT,
            'dmpInclude'         => Hk_Service_Db::TYPE_STR,
            'dmpExclude'         => Hk_Service_Db::TYPE_STR,
            'learningStatus'     => Hk_Service_Db::TYPE_STR,

        );
     }
}