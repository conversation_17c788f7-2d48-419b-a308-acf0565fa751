<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/18
 * Time: 11:19
 */
class Qdlib_Dao_Ad_QudaoCustomColumn extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoCustomColumn";

        $this->arrFieldsMap = array(
            'id'         => 'id',
            'name'       => 'name',
            'flag'       => 'flag',
            'creator'    => 'creator',
            'sort'       => 'sort',
            'columns'    => 'columns',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted'    => 'deleted',
            'module'     => 'module',
            'subModule'  => 'sub_module',

        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'name'       => Hk_Service_Db::TYPE_STR,
            'flag'       => Hk_Service_Db::TYPE_INT,
            'creator'    => Hk_Service_Db::TYPE_STR,
            'sort'       => Hk_Service_Db::TYPE_INT,
            'columns'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'module'     => Hk_Service_Db::TYPE_STR,
            'subModule'  => Hk_Service_Db::TYPE_STR,

        );
     }
}