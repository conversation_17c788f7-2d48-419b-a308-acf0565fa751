<?php

class  Qdlib_Dao_QudaoOutCampaign extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutCampaign";
        $this->arrFieldsMap = array(
            'id' => 'id',
            'channel' => 'channel',
            'account' => 'account',
            'campaignId' => 'campaign_id',
            'campaignName' => 'campaign_name',
            'adCount' => 'ad_count',
            'ext' => 'ext',
            'configuredStatus' => 'configured_status',
            'systemStatus' => 'system_status',
            'isDeleted' => 'is_deleted',
            'campaignCreatedTime' => 'campaign_created_time',
            'campaignLastModifiedTime' => 'campaign_last_modified_time',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',

        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'channel' => Hk_Service_Db::TYPE_STR,
            'account' => Hk_Service_Db::TYPE_STR,
            'campaignId' => Hk_Service_Db::TYPE_INT,
            'campaignName' => Hk_Service_Db::TYPE_STR,
            'adCount' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'configuredStatus' => Hk_Service_Db::TYPE_INT,
            'systemStatus' => Hk_Service_Db::TYPE_INT,
            'isDeleted' => Hk_Service_Db::TYPE_INT,
            'campaignCreatedTime' => Hk_Service_Db::TYPE_INT,
            'campaignLastModifiedTime' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,


        );
    }

}