<?php
/**
 * file FinanceLog.php.
 * author: <EMAIL>
 * Date: 2020/3/16
 * brief: 财务修改log表
 */

class Qdlib_Dao_Finance_FinanceLog extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblQudaoFinanceLog";

        $this->arrFieldsMap = array(
            'id'                => 'id',
            'dt'                => 'dt',
            'channel'           => 'channel',
            'account'           => 'account',
            'skChannelId'       => 'sk_channel_id',
            'beforeCost'        => 'before_cost',
            'afterCost'         => 'after_cost',
            'renew'             => 'renew',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'reportStatus'      => 'report_status',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'dt'                => Hk_Service_Db::TYPE_INT,
            'channel'           => Hk_Service_Db::TYPE_STR,
            'account'           => Hk_Service_Db::TYPE_STR,
            'skChannelId'       => Hk_Service_Db::TYPE_INT,
            'beforeCost'        => Hk_Service_Db::TYPE_INT,
            'afterCost'         => Hk_Service_Db::TYPE_INT,
            'renew'             => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'reportStatus'      => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}