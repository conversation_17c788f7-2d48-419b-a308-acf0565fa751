<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      models/dao/comment/UserComment.php
 * @author:        <EMAIL>
 * @desc:          用户评论 
 * @create:        2020-04-08 14:06:46
 * @last modified: 2020-04-08 14:06:46
 */
class Saaslib_Dao_Comment_UserComment extends Hk_Common_Dao {
    
    //正常显示
    const DELETED_NORMAL  = 0;
    //删除
    const DELETED_DELETE  = 1;
    //隐藏
    const DELETED_HIDE    = 2;

    public function init() {
        $this->_dbConf         = "saas/snscomment";
        $this->tableName       = "tblComment";
        $this->fields = array('id', 'uid', 'cid', 'qid', 'rid', 'toFloor', 'toRid', 'uname', 'avatar', 'sex', 'content', 'goodNum', 'type', 'deleted', 'createTime', 'updateTime', 'extData');
        $this->maxTableNum     = 20;
        $this->nativeTableName = $this->tableName;
    }
    
    /**
     * 获取总数
     * @param  string  $uid     邮箱前缀
     * @return int 
     */
    public function getTotal($uid){
        $where = array(
            'uid ='     => $uid,
            'deleted =' => self::DELETED_NORMAL,
        );
        $this->switchTable($uid);
        return $this->selectCount($where);
    }
    
    /**
     * 分表方式：crc32($uid) % maxTableNum
     * @param string      $uid
     */
    private function switchTable($uid) {
        $this->tableName = sprintf("%s%d", $this->nativeTableName, crc32($uid) % $this->maxTableNum);
    }
}
