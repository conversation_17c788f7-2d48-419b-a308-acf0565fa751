<?php
class Qdlib_Dao_Monitor_LastfromWarn extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoLastfromWarn";

        $this->arrFieldsMap = array(
            'id'               => 'id',
            'lastfrom'         => 'lastfrom',
            'skuId'            => 'sku_id',
            'dt'               => 'dt',
            'dtHour'           => 'dt_hour',
            'flag'             => 'flag',
            'warnMsg'          => 'warn_msg',
            'warnType'         => 'warn_type',
            'createTime'       => 'create_time',
            'updateTime'       => 'update_time',
            'ext'              => 'ext',
        );

        $this->arrTypesMap = array(
            'id'               => Hk_Service_Db::TYPE_INT,
            'lastfrom'         => Hk_Service_Db::TYPE_STR,
            'skuId'            => Hk_Service_Db::TYPE_INT,
            'dt'               => Hk_Service_Db::TYPE_INT,
            'dtHour'           => Hk_Service_Db::TYPE_INT,
            'flag'             => Hk_Service_Db::TYPE_STR,
            'warnMsg'          => Hk_Service_Db::TYPE_STR,
            'warnType'         => Hk_Service_Db::TYPE_INT,
            'createTime'       => Hk_Service_Db::TYPE_INT,
            'updateTime'       => Hk_Service_Db::TYPE_INT,
            'ext'              => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}