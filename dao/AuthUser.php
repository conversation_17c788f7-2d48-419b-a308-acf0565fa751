<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/07/27
 * Time: 11:44
 */
class Qdlib_Dao_AuthUser extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblAuthUser";

        $this->arrFieldsMap = array(
            'id'          => 'id',
            'userId'      => 'user_id',
            'uname'       => 'uname',
            'name'        => 'name',
            'email'       => 'email',
            'phone'       => 'phone',
            'roles'       => 'roles',
            'hetuRoles'   => 'hetu_roles',
            'addDataAuth' => 'add_data_auth',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'creator'     => 'creator',
            'operator'    => 'operator',
            'status'      => 'status',
            'orgId'       => 'org_id',
            'showCost'    => 'show_cost',
            'highAuth'    => 'high_auth',
            'reportHighAuth' => 'report_high_auth',
            'highScope'      => 'high_scope',
            'reportModule'   => 'report_module',
            'addRule'        => 'add_rule',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'userId'      => Hk_Service_Db::TYPE_STR,
            'uname'       => Hk_Service_Db::TYPE_STR,
            'name'        => Hk_Service_Db::TYPE_STR,
            'email'       => Hk_Service_Db::TYPE_STR,
            'phone'       => Hk_Service_Db::TYPE_STR,
            'roles'       => Hk_Service_Db::TYPE_STR,
            'hetuRoles'   => Hk_Service_Db::TYPE_STR,
            'addDataAuth' => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'creator'     => Hk_Service_Db::TYPE_STR,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'status'      => Hk_Service_Db::TYPE_INT,
            'orgId'       => Hk_Service_Db::TYPE_INT,
            'showCost'    => Hk_Service_Db::TYPE_INT,
            'highAuth'    => Hk_Service_Db::TYPE_STR,
            'reportHighAuth' => Hk_Service_Db::TYPE_STR,
            'highScope'      => Hk_Service_Db::TYPE_INT,
            'reportModule'   => Hk_Service_Db::TYPE_STR,
            'addRule'        => Hk_Service_Db::TYPE_INT,
        );
     }
}