<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2019/07/17
 * Time: 10:30
 */
class Qdlib_Dao_SitePageFolder extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblSitePageFolder";

        $this->arrFieldsMap = array(
            'id'              => 'id',
            'name'            => 'name',
            'businessId'      => 'business_id',
            'category'        => 'category',
            'pageType'        => 'page_type',
            'deleted'         => 'deleted',
            'creator'         => 'creator',
            'operator'        => 'operator',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time'
        );

        $this->arrTypesMap = array(
            'id'              => Hk_Service_Db::TYPE_INT,
            'name'            => Hk_Service_Db::TYPE_STR,
            'businessId'      => Hk_Service_Db::TYPE_INT,
            'category'        => Hk_Service_Db::TYPE_INT,
            'pageType'        => Hk_Service_Db::TYPE_INT,
            'deleted'         => Hk_Service_Db::TYPE_INT,
            'creator'         => Hk_Service_Db::TYPE_STR,
            'operator'        => Hk_Service_Db::TYPE_STR,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT
        );
    }
}
