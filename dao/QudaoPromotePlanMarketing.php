<?php

class  Qdlib_Dao_QudaoPromotePlanMarketing extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = 'tblQudaoPromotePlanMarkting';
        $this->arrFieldsMap = [
            'id' => 'id',
            'flag' => 'flag',
            'channel' => 'channel',
            'account' => 'account',
            'orifrom' => 'orifrom',
            'fileType' => 'file_type',
            'fileUrl' => 'file_url',
            'resourceLocation' => 'resource_location',
            'resourceLocationCn' => 'resource_location_cn',
            'materialCate' => 'material_cate',
            'materialCateCn' => 'material_cate_cn',
            'materialName' => 'material_name',
            'materialNameCn' => 'material_name_cn',
            'material_date' => 'material_date',
            'materialSource' => 'material_source',
            'materialSourceCn' => 'material_source_cn',
            'ext' => 'ext',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'flag' => Hk_Service_Db::TYPE_INT,
            'channel' => Hk_Service_Db::TYPE_STR,
            'account' => Hk_Service_Db::TYPE_STR,
            'orifrom' => Hk_Service_Db::TYPE_STR,
            'fileType' => Hk_Service_Db::TYPE_STR,
            'fileUrl' => Hk_Service_Db::TYPE_JSON,
            'resourceLocation' => Hk_Service_Db::TYPE_STR,
            'resourceLocationCn' => Hk_Service_Db::TYPE_STR,
            'materialCate' => Hk_Service_Db::TYPE_STR,
            'materialCateCn' => Hk_Service_Db::TYPE_STR,
            'materialName' => Hk_Service_Db::TYPE_STR,
            'materialNameCn' => Hk_Service_Db::TYPE_STR,
            'material_date' => Hk_Service_Db::TYPE_STR,
            'materialSource' => Hk_Service_Db::TYPE_STR,
            'materialSourceCn' => Hk_Service_Db::TYPE_STR,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getFields()
    {
        return $this->arrFieldsMap;
    }
}