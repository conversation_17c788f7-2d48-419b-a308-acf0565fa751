<?php

class  Oplib_Dao_ProblemSolvingVideo
{
    private $objDaoProblemSolvingVideo;

    static $allFields = [
        'id',
        'authorType',
        'authorId',
        'authorName',
        'authorImg',
        'authorText',
        'videoImg',
        'videoUrl',
        'videoText',
        'videoInfo',
        'labelId',
        'gradeId64',
        'gradeId63',
        'gradeId62',
        'gradeId61',
        'gradeId11',
        'gradeId12',
        'gradeId13',
        'gradeId14',
        'gradeId15',
        'gradeId16',
        'gradeId2',
        'gradeId3',
        'gradeId4',
        'gradeId5',
        'gradeId6',
        'gradeId7',
        'likeCount',
        'shareCount',
        'rank',
        'status',
        'removed',
        'videoInfo'
    ];

    const STATUS_ONLINE = 1; //上线状态
    const STATUS_OFFLINE = 2; //线下状态

    const REMOVED_YES = 1; //已删除
    const REMOVED_NOT = 0; //未删除

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoProblemSolvingVideo = new Oplib_Dao_ProblemSolvingVideoDao();
    }


    /**
     * Notes:条件查询
     * User: yinxiugui
     * Date: 2020/7/27
     * Time: 12:02
     * @param $spuId
     * @param array $arrFields
     * @return array
     */
    public function getProblemSolvingVideoByCons($arrConds, $arrFields = [], $arrAppends = [])
    {
        if (empty($arrFields)) {
            $arrFields = self::$allFields;
        }

        $arrConds['removed'] = self::REMOVED_NOT;
        $arrConds['status'] = self::STATUS_ONLINE;

        if (false == is_array($arrAppends)) {
            $arrAppends = [];
        }

        $ret = $this->objDaoProblemSolvingVideo->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * @param $conds
     * @param array $condStrArr
     * @param array $arrFields
     * @param array $arrAppends
     * @return mixed
     */
    public function getProblemSolvingVideoCuntByCons($conds, $condStrArr = [])
    {
        foreach (self::$allFields as $field) {
            if (false == empty($conds[$field])) {
                $arrConds[$field] = $conds[$field];
            }
        }
        foreach ((array)$condStrArr as $condStr) {
            $arrConds[] = $condStr;
        }

        $arrConds['removed'] = self::REMOVED_NOT;
        $arrConds['status'] = self::STATUS_ONLINE;


        $ret = $this->objDaoProblemSolvingVideo->getCntByConds($arrConds);

        return $ret;
    }
}