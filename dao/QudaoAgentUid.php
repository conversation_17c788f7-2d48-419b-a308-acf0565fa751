<?php

/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2019/06/18
 * Time: 19:57
 */
class Qdlib_Dao_QudaoAgentUid extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblQudaoAgentUid";

        $this->arrFieldsMap = array(
            'id' => 'id',
            'agentId' => 'agent_id',
            'uid' => 'uid',
            'userName' => 'user_name',
            'deleted' => 'deleted',
            'createdTime' => 'created_time',
            'updateTime' => 'update_time',
            'operator' => 'operator',
            'creator' => 'creator',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'agentId' => Hk_Service_Db::TYPE_INT,
            'uid' => Hk_Service_Db::TYPE_INT,
            'userName' => Hk_Service_Db::TYPE_STR,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'createdTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'creator' => Hk_Service_Db::TYPE_STR,
        );
    }
}