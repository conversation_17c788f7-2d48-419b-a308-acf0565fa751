<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file UserCoupon.php
 * <AUTHOR>
 * @brief 用户优惠码
 *
 **/

class Hkzb_Dao_Pay_UserCoupon extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'pay/zyb_pay';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_PAY;
        $this->_table       = "tblUserCoupon0";
        $this->arrFieldsMap = array(
            'couponId'     => 'coupon_id',
            'uid'          => 'uid',
            'itemId'       => 'item_id',
            'status'       => 'status',
            'unit'         => 'unit',
            'discount'     => 'discount',
            'itemName'     => 'item_name',
            'description'  => 'description',
            'source'       => 'source',
            'orderId'      => 'order_id',
            'productId'    => 'product_id',
            'productName'  => 'product_name',
            'productType'  => 'product_type',
            'productPrice' => 'product_price',
            'startTime'    => 'start_time',
            'endTime'      => 'end_time',
            'consumedTime' => 'consumed_time',
            'rule'         => 'rule',
            'extData'      => 'ext_data',
        );

        $this->arrTypesMap = array(
            'couponId'     => Hk_Service_Db::TYPE_INT,
            'uid'          => Hk_Service_Db::TYPE_INT,
            'itemId'       => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'unit'         => Hk_Service_Db::TYPE_INT,
            'discount'     => Hk_Service_Db::TYPE_STR,
            'itemName'     => Hk_Service_Db::TYPE_STR,
            'description'  => Hk_Service_Db::TYPE_STR,
            'source'       => Hk_Service_Db::TYPE_STR,
            'orderId'      => Hk_Service_Db::TYPE_INT,
            'productId'    => Hk_Service_Db::TYPE_INT,
            'productName'  => Hk_Service_Db::TYPE_STR,
            'prodcutType'  => Hk_Service_Db::TYPE_INT,
            'prodcutPrice' => Hk_Service_Db::TYPE_INT,
            'startTime'    => Hk_Service_Db::TYPE_INT,
            'endTime'      => Hk_Service_Db::TYPE_INT,
            'consumedTime' => Hk_Service_Db::TYPE_INT,
            'rule'         => Hk_Service_Db::TYPE_JSON,
            'extData'      => Hk_Service_Db::TYPE_JSON,
        );

    }
}