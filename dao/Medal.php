<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Medal.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:16
 */

class Zhibo_Dao_Medal extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblMedal";

        $this->arrFieldsMap = array(
            'medalId' => 'medal_id',
            'medalName' => 'medal_name',
            'medalUrl' => 'medal_url',
            'medalNum' => 'medal_num',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator' => 'operator',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'medalId' => Hk_Service_Db::TYPE_INT,
            'medalName' => Hk_Service_Db::TYPE_STR,
            'medalUrl' => Hk_Service_Db::TYPE_STR,
            'medalNum' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}