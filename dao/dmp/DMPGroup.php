<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/06/15
 * Time: 16:06
 */
class Qdlib_Dao_Dmp_DMPGroup extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoDMPGroup";

        $this->arrFieldsMap = array(
            'id'            => 'id',
            'name'          => 'name',
            'taskName'      => 'task_name',
            'sourceType'    => 'source_type',
            'dmpId'         => 'dmp_id',
            'path'          => 'path',
            'channel'       => 'channel',
            'outputType'    => 'output_type',
            'encryptType'   => 'encrypt_type',
            'fileFormat'    => 'file_format',
            'lineCount'     => 'line_count',
            'pushStatus'    => 'push_status',
            'dmpStatus'     => 'dmp_status',
            'thirdDmpId'    => 'third_dmp_id',
            'firstPushTime' => 'first_push_time',
            'accounts'      => 'accounts',
            'warnData'      => 'warn_data',
            'extData'       => 'ext_data',
            'updateType'    => 'update_type',
            'updateCycle'   => 'update_cycle',
            'status'        => 'status',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'creator'       => 'creator',
            'operator'      => 'operator',
            'finishTime'    => 'finish_time',
            'pushTime'      => 'push_time',
            'warn'          => 'warn',
            'zebraId'       => 'zebra_id',
            'projectLv2'    => 'project_lv2',

        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'name'          => Hk_Service_Db::TYPE_STR,
            'taskName'      => Hk_Service_Db::TYPE_STR,
            'sourceType'    => Hk_Service_Db::TYPE_INT,
            'dmpId'         => Hk_Service_Db::TYPE_STR,
            'path'          => Hk_Service_Db::TYPE_STR,
            'channel'       => Hk_Service_Db::TYPE_STR,
            'outputType'    => Hk_Service_Db::TYPE_INT,
            'encryptType'   => Hk_Service_Db::TYPE_INT,
            'fileFormat'    => Hk_Service_Db::TYPE_INT,
            'lineCount'     => Hk_Service_Db::TYPE_INT,
            'pushStatus'    => Hk_Service_Db::TYPE_INT,
            'dmpStatus'     => Hk_Service_Db::TYPE_INT,
            'thirdDmpId'    => Hk_Service_Db::TYPE_INT,
            'firstPushTime' => Hk_Service_Db::TYPE_INT,
            'accounts'      => Hk_Service_Db::TYPE_STR,
            'warnData'      => Hk_Service_Db::TYPE_JSON,
            'extData'       => Hk_Service_Db::TYPE_JSON,
            'updateType'    => Hk_Service_Db::TYPE_INT,
            'updateCycle'   => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'creator'       => Hk_Service_Db::TYPE_STR,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'finishTime'    => Hk_Service_Db::TYPE_INT,
            'pushTime'      => Hk_Service_Db::TYPE_INT,
            'warn'          => Hk_Service_Db::TYPE_INT,
            'zebraId'       => Hk_Service_Db::TYPE_INT,
            'projectLv2'    => Hk_Service_Db::TYPE_INT,
        );
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}