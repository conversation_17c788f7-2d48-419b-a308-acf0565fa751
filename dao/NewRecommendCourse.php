<?php
/**
 * Created by PhpStorm.
 * User: wangying(<EMAIL>)
 * Date: 2019/3/8
 * Time: 16:46
 */
class Oplib_Dao_NewRecommendCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName  = 'zbui_oper/zhibo_ui';
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_db          = null;
        $this->_table       = "tblNewRecommendCourse";
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'courseId'       => 'course_id',
            'skuId'          => 'sku_id',
            'courseName'     => 'course_name',
            'recommendInfo'  => 'recommend_info',
            'recommendType'  => 'recommend_type',
            'status'         => 'status', // 1 上线 2 下线 3 删除
            'startTime'      => 'start_time',
            'createTime'     => 'create_time',
            'updateTime'     => 'update_time',
            'stopTime'       => 'stop_time',
            'extData'        => 'ext_data',
            'brand'          => 'brand',
            'couponId'       => 'coupon_id',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'skuId'         => Hk_Service_Db::TYPE_INT,
            'courseName'    => Hk_Service_Db::TYPE_STR,
            'recommendInfo' => Hk_Service_Db::TYPE_JSON,
            'status'        => Hk_Service_Db::TYPE_INT,
            'startTime'     => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'stopTime'      => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
            'brand'         => Hk_Service_Db::TYPE_INT,
            'couponId'      => Hk_Service_Db::TYPE_STR,
        );
    }
}