<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 18:51
 */

class Qdlib_Dao_Qudao_OutAd extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutAd";
        $this->arrFieldsMap = array(
            'id' => 'id',
            'channel' => 'channel',
            'account' => 'account',
            'campaignId' => 'campaign_id',
            'configuredStatus' => 'configured_status',
            'systemStatus' => 'system_status',
            'adgroupId' => 'adgroup_id',
            'adId' => 'ad_id',
            'adName' => 'ad_name',
            'orifrom' => 'orifrom',
            'pageUrl' => 'page_url',
            'ext' => 'ext',
            'adCreatedTime' => 'ad_created_time',
            'adLastModifiedTime' => 'ad_last_modified_time',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'reportStatus' => 'report_status',

        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'channel' => Hk_Service_Db::TYPE_STR,
            'account' => Hk_Service_Db::TYPE_STR,
            'campaignId' => Hk_Service_Db::TYPE_INT,
            'configuredStatus' => Hk_Service_Db::TYPE_STR,
            'systemStatus' => Hk_Service_Db::TYPE_STR,
            'adgroupId' => Hk_Service_Db::TYPE_INT,
            'adId' => Hk_Service_Db::TYPE_INT,
            'adName' => Hk_Service_Db::TYPE_STR,
            'orifrom' => Hk_Service_Db::TYPE_STR,
            'pageUrl' => Hk_Service_Db::TYPE_STR,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'adCreatedTime' => Hk_Service_Db::TYPE_INT,
            'adLastModifiedTime' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'reportStatus' => Hk_Service_Db::TYPE_INT,


        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}