<?php
/**
 * Created by PhpStorm.
 * User: chenping
 * Date: 2020-03-27
 * Time:
 */

class Qdlib_Dao_Qudao_OutMaterialStat extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoMaterialStat";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'uniqueId'          => 'unique_id',
            'dt'                => 'dt',
            'dtHour'            => 'dt_hour',
            'flag'              => 'flag',
            'channel'           => 'channel',
            'account'           => 'account',
            'thirdAdId'         => 'third_ad_id',
            'thirdCampaignId'   => 'third_campaign_id',
            'thirdAdgroupId'    => 'third_adgroup_id',
            'thirdMaterialId'    => 'third_material_id',
            'showPv'            => 'show_pv',
            'clickPv'           => 'click_pv',
            'cost'              => 'cost',
            'conversion'        => 'conversion',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'ext'               => 'ext',
            'deleted'           => 'deleted',
            'reportStatus'      => 'report_status',
            'appCode'           => 'app_code',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'uniqueId'          => Hk_Service_Db::TYPE_STR,
            'dt'                => Hk_Service_Db::TYPE_INT,
            'dtHour'            => Hk_Service_Db::TYPE_INT,
            'flag'              => Hk_Service_Db::TYPE_STR,
            'channel'           => Hk_Service_Db::TYPE_STR,
            'account'           => Hk_Service_Db::TYPE_STR,
            'appCode'           => Hk_Service_Db::TYPE_STR,
            'thirdAdId'         => Hk_Service_Db::TYPE_INT,
            'thirdCampaignId'   => Hk_Service_Db::TYPE_INT,
            'thirdAdgroupId'    => Hk_Service_Db::TYPE_INT,
            'thirdMaterialId'   => Hk_Service_Db::TYPE_STR,
            'showPv'            => Hk_Service_Db::TYPE_INT,
            'clickPv'           => Hk_Service_Db::TYPE_INT,
            'cost'              => Hk_Service_Db::TYPE_INT,
            'conversion'        => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'ext'               => Hk_Service_Db::TYPE_JSON,
            'deleted'           => Hk_Service_Db::TYPE_INT,
            'reportStatus'      => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}