<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : VideoSeeNum.php
 * Author: <EMAIL>
 * Date: 2018/10/31
 * Time: 13:58
 * Desc: 首页观看视频的次数
 */
class Oplib_Dao_VideoSeeNum extends Hk_Common_BaseDao{

    public static $arrFields = array(
        'id',
        'adId',
        'posId',
        'seeNum',
        'status',
        'createTime',
        'updateTime',
        'extData'
    );

    public function __construct()
    {
        //$this->_dbName      = 'revent/zb_operation';
        $this->_dbName      = Oplib_Const_DbConfig::DB_YK_ZBUI;
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblVideoSeeNum';
        $this->arrFieldsMap = array(
            'id'      => 'id',
            'adId'    => 'ad_id',
            'posId'   => 'pos_id',
            'seeNum'   => 'see_num',
            'status'     => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'adId'       => Hk_Service_Db::TYPE_INT,
            'posId'      => Hk_Service_Db::TYPE_INT,
            'seeNum'     => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}
