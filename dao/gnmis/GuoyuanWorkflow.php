<?php

/**
 * @file   GuoyuanWorkflow.php
 * <AUTHOR>
 * @date   2016-07-23
 * @brief  果园工作流
 *
 **/
class Hkzb_Dao_Gnmis_GuoyuanWorkflow extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'id', 'createUser', 'createTime', 'lastOpTime', 'lastOpUser', 'nextOpRole', 'nextStep',
        'flows', 'type', 'status', 'deleted', 'relTable', 'relId', 'abstract', 'extBit', 'extData'
    );

    public function __construct() {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName,false,"gnmis-sql/gnmis_sql.log");
        $this->_table       = "tblGuoyuanWorkflow";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'createUser' => 'create_user',
            'createTime' => 'create_time',
            'lastOpUser' => 'last_op_user',
            'lastOpTime' => 'last_op_time',
            'nextOpRole' => 'next_op_role',
            'nextStep'   => 'next_step',
            'flows'      => 'flows',
            'type'       => 'type',
            'status'     => 'status',
            'deleted'    => 'deleted',
            'relTable'   => 'rel_table',
            'relId'      => 'rel_id',
            'abstract'   => 'abstract',
            'extBit'     => 'ext_bit',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'createUser' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'lastOpUser' => Hk_Service_Db::TYPE_INT,
            'lastOpTime' => Hk_Service_Db::TYPE_INT,
            'nextOpRole' => Hk_Service_Db::TYPE_INT,
            'currStep'   => Hk_Service_Db::TYPE_INT,
            'flows'      => Hk_Service_Db::TYPE_JSON,
            'type'       => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'relTable'   => Hk_Service_Db::TYPE_STR,
            'relId'      => Hk_Service_Db::TYPE_INT,
            'abstract'   => Hk_Service_Db::TYPE_STR,
            'extBit'     => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}
