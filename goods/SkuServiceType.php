<?php
/**
 * @file: SkuServiceType.php
 *        商品服务类型 类文件
 * @author: dds(liu<PERSON><PERSON>@zuoyebang.com)
 * @date: 2023-06-28 18:00:06
 * @brief: 决定商品履约，生产，售后的分类标识
 *         使用中
 */


class Sp_Dict_Goods_SkuServiceType
{
   //*Const
   const SKU_SERVICE_COURSE_TYPE = 1;//课程 - 使用中
   const SKU_SERVICE_VIRTUAL_TYPE = 2;//虚拟 - 使用中
   const SKU_SERVICE_ENTITY_TYPE = 3;//实物 - 使用中
   const SKU_SERVICE_THIRD_COIN_TYPE = 4;//三方充值 - 使用中

   //*Map
   public static $map = [
       self::SKU_SERVICE_COURSE_TYPE => "课程",
       self::SKU_SERVICE_VIRTUAL_TYPE => "虚拟",
       self::SKU_SERVICE_ENTITY_TYPE => "实物",
       self::SKU_SERVICE_THIRD_COIN_TYPE => "三方充值",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::SKU_SERVICE_COURSE_TYPE => "sku_service_course_type",
       self::SKU_SERVICE_VIRTUAL_TYPE => "sku_service_virtual_type",
       self::SKU_SERVICE_ENTITY_TYPE => "sku_service_entity_type",
       self::SKU_SERVICE_THIRD_COIN_TYPE => "sku_service_third_coin_type",
   ];
}
