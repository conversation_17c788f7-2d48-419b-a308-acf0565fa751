<?php
/**
 * @file: PackageStatus.php
 *        订单商品行包裹状态 类文件
 * @author: dds(<EMAIL>)
 * @date: 2023-06-28 18:00:06
 * @brief: 订单商品行包裹状态
 *         使用中
 */


class Sp_Dict_Order_PackageStatus
{
   //*Const
   const DEFAULT = 0;//默认 - 使用中
   const RECEIVED = 1;//签收 - 使用中
   const REJECTED = 2;//拒收 - 使用中
   const INTERCEPT = 3;//拦截 - 使用中
   const INVALID = 4;//无效 - 使用中
   const OUTBOUND = 5;//出库 - 使用中

   //*Map
   public static $map = [
       self::DEFAULT => "默认",
       self::RECEIVED => "签收",
       self::REJECTED => "拒收",
       self::INTERCEPT => "拦截",
       self::INVALID => "无效",
       self::OUTBOUND => "出库",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::DEFAULT => "default",
       self::RECEIVED => "received",
       self::REJECTED => "rejected",
       self::INTERCEPT => "intercept",
       self::INVALID => "invalid",
       self::OUTBOUND => "outbound",
   ];
}
