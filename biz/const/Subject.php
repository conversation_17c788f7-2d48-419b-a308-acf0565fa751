<?php
class Oplib_Biz_Const_Subject{
    //科目常量定义
    const SUBJECT_CHINESE               = 1;//语文
    const SUBJECT_MATH                  = 2;//数学
    const SUBJECT_ENGLISH               = 3;//英语
    const SUBJECT_PHYSICAL              = 4;//物理
    const SUBJECT_CHEMISTRY             = 5;//化学
    const SUBJECT_BIOLOGY               = 6;//生物
    const SUBJECT_POLITICS              = 7;//政治
    const SUBJECT_HISTORY               = 8;//历史
    const SUBJECT_GEOGRAPHY             = 9;//地理
    const SUBJECT_INTEREST              = 10;//兴趣课
    const SUBJECT_THOUGHT               = 11;//思想品德
    const SUBJECT_LECTURE               = 12;//讲座
    const SUBJECT_SCIENCE_DEPARTMENT    = 13;//理综
    const SUBJECT_LIBERAL_ART           = 14;//文综
    const SUBJECT_MATHEMATICALOLYMPIAD  = 15;//奥数
    const SUBJECT_SCIENCE               = 16;//科学

    //学科映射
    public static $subjectTextMap = array(
        self::SUBJECT_CHINESE               => '语文',
        self::SUBJECT_MATH                  => '数学',
        self::SUBJECT_ENGLISH               => '英语',
        self::SUBJECT_PHYSICAL              => '物理',
        self::SUBJECT_CHEMISTRY             => '化学',
        self::SUBJECT_BIOLOGY               => '生物',
        self::SUBJECT_POLITICS              => '政治',
        self::SUBJECT_HISTORY               => '历史',
        self::SUBJECT_GEOGRAPHY             => '地理',
        self::SUBJECT_INTEREST              => '兴趣课',//直播课使用
        self::SUBJECT_THOUGHT               => '思想品德',//直播课使用
        self::SUBJECT_LECTURE               => '讲座',//直播课使用
        self::SUBJECT_SCIENCE_DEPARTMENT    => '理综',//试卷用
        self::SUBJECT_LIBERAL_ART           => '文综',//试卷用
        self::SUBJECT_MATHEMATICALOLYMPIAD  => '奥数',
        self::SUBJECT_SCIENCE               => '科学',
    );



    static public $subjectOnetMap = array(
        self::SUBJECT_CHINESE               => '语',
        self::SUBJECT_MATH                  => '数',
        self::SUBJECT_ENGLISH               => '英',
        self::SUBJECT_PHYSICAL              => '物',
        self::SUBJECT_CHEMISTRY             => '化',
        self::SUBJECT_BIOLOGY               => '生',
        self::SUBJECT_POLITICS              => '政',
        self::SUBJECT_HISTORY               => '史',
        self::SUBJECT_GEOGRAPHY             => '地',
        self::SUBJECT_INTEREST              => '趣',//直播课使用
        self::SUBJECT_THOUGHT               => '思',//直播课使用
        self::SUBJECT_LECTURE               => '讲',//直播课使用
        self::SUBJECT_MATHEMATICALOLYMPIAD  => '奥',
        self::SUBJECT_SCIENCE               => '科',
    );

    //学科排序映射
    public static $subjectSortMap   = array(
        self::SUBJECT_MATH      => 1,
        self::SUBJECT_ENGLISH   => 2,
        self::SUBJECT_CHINESE   => 3,
        self::SUBJECT_PHYSICAL  => 4,
        self::SUBJECT_CHEMISTRY => 5,
        self::SUBJECT_BIOLOGY   => 6,
        self::SUBJECT_POLITICS  => 7,
        self::SUBJECT_HISTORY   => 8,
        self::SUBJECT_GEOGRAPHY => 9,
    );
}