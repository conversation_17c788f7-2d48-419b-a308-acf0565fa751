<?php

class Oplib_Biz_Front_Util_Image {

    private static $CosName2Prefix = array(
        'haoke' => 'haoke_',
    );

    private static $Ins;
    private static $CosName2Json = array( // 用特定配置代替hk配置
        'haoke' => '{"bucket":"zyb-haoke","app_id":"1253445850","secret_id":"AKIDRgfNoyrTnqc9PP9dqmQnujHYZJmyuZx4","secret_key":"rRlpb7MK1SdW8YhBOJyMTJtCF2YnrhGZ","is_public":"0","region":"bj","picture_region":"picbj","directory":"","file_prefix":"haoke_","filesize_limit":"10485760","thumbnail":"1","tag":{"host":""}}',
    );
    private $cosName2conf;

    protected function __construct() {
        foreach (self::$CosName2Json as $cosName => $json) {
            $value = json_decode($json, true);
            Bd_Conf::setConf("hk/cos/{$cosName}", $value);
            $this->cosName2conf[$cosName] = $value;
        }
    }

    /**
     * 单例
     * @return self
     */
    public static function getIns() {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    public function getFrontImageUrl($pid, $fileType = "jpg") {
        $url = '';
        if (empty($pid)) {
            return $url;
        }

        $txCosName = false;
        foreach ($this->cosName2conf as $cosName => $conf) {
            $prefix = $conf['file_prefix'];
            if (substr($pid, 0, strlen($prefix)) == $prefix) {
                $txCosName = $cosName;
                break;
            }
        }
        if ($txCosName) {
            $fileName = "{$pid}.{$fileType}";
            $url = Hk_Service_Tcos::getObjectUrl($txCosName, $fileName);
        } else {
            $url = Hk_Util_Image::getImgUrlBySrc($pid);
        }

        return $url;
    }

}
