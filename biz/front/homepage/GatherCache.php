<?php

/**
 * brief: 首页等位置有些数据是由前台自己汇总存到缓存里面的，当前由goods模块写，homepage模块读，常量定义到oplib里面
 * @author: <EMAIL>
 */
class Oplib_Biz_Front_Homepage_GatherCache {

    //定义卡片基础信息的缓存key
    const CARD_BASE_DATA_KEY = 'ZHIBO_CARD_BASE_DATA_KEY_V1_';
    const INDEX_CARD_BASE_DATA_KEY = 'ZHIBO_INDEX_CARD_BASE_DATA_KEY_';
    const BACKUP_INDEX_CARD_KEY = 'BACKUP_INDEX_CARD_KEY_';

    // 定义学科页卡片缓存key
    const SUBJECT_INDEX_CARD_BASE_DATA_KEY = 'ZHIBO_SUBJECT_INDEX_CARD_BASE_DATA_KEY_';

    //定义一个卡片无效的变量
    const UNVALID_CARD_KEY = 'unvalidcard';

    // 定义素养课用户观看退出位置lessonId缓存key, 记录用户退出位置章节
    const ATTAINMENT_USER_COURSE_EXIT_LESSON = 'attainment_user_course_exit_lesson_';


    //获取公共缓存key
    static public function getCardDataCacheKey($grade = 0, $tagId = 0, $role = 0) {
        //处理角色,目前只有两种角色，纯新用户和班课用户
        if ($role != Oplib_Biz_Const_Role::USER_ROLE_BAN_OLD_USER) {
            $role = 0;
        }

        $cacheKey = self::CARD_BASE_DATA_KEY . $grade . '_' . $tagId . '_' . $role;

        return $cacheKey;
    }

    public static function getIndexCardDataCacheKey($grade = 0, $tagId = 0) {
        $cacheKey = self::INDEX_CARD_BASE_DATA_KEY . $grade . '_' . $tagId;
        return $cacheKey;
    }

    // 学科卡片缓存key生成
    public static function getSubjectIndexCardDataCacheKey($grade = 0, $tagId = 0) {
        return self::SUBJECT_INDEX_CARD_BASE_DATA_KEY . $grade . '_' . $tagId;
    }

    public static function getAttainmentExitLessonCacheKey($uid, $courseId){
        return self::ATTAINMENT_USER_COURSE_EXIT_LESSON . $uid . '_' . $courseId;
    }
}
