<?php

/**
 * brief: 缓存-暑期大促
 * @author: <EMAIL>
 */
class Oplib_Biz_Front_Gray_Promotion_2021Q2Cache {

    private $redis;

    const MOCK_STUDENT_UID = 999999; // 假uid
    // 默认配置
    private static $DefaultConfig = array(
        'isUseCache' => 0, // 是否使用缓存
        'isAllCache' => 0, // 是否强制全部用缓存(无缓存时不穿透)
        'isPressureCache' => 0, // 压测时使用缓存
        'whiteUids' => '', // 白名单uid(使用缓存)
    );
    // 单一角色
    public static $SingleRole = [
        105, //纯新用户
        102, //班课用户
        198, //短训班用户
        2,   // 专题课用户
    ];
    const CACHE_TTL = 86400; // 缓存有效期
    const SUMMARY_KEY = 'promotion:summary'; //整体缓存情况概览的缓存key

    private static $Ins;
    private $config;

    protected function __construct() {
        $this->config = self::$DefaultConfig;
        $ret = Zb_Service_NCM::get(1, 'goods', 'gray', 'promotion_2021Q2');
        foreach ($this->config as $key => $value) {
            if (empty($ret) || !isset($ret[$key])) {
                continue;
            }
            $this->config[$key] = $ret[$key];
        }
        $arr = explode(',', $this->config['whiteUids']);
        $this->config['whiteUids'] = [];
        foreach ($arr as $one) {
            if (empty($one)) {
                continue;
            }
            $id = intval($one);
            $this->config['whiteUids'][$id] = true;
        }

        $this->redis = Hk_Service_RedisClient::getInstance("zbapp");
        if (empty($this->redis)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, 'get redis failed');
        }
        $this->redis->ping();
        if (!$this->redis->isConnected()) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, 'redis connect failed');
        }
    }

    public static function getIns() {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    /*
     * 挑选单一role
     */
    public static function pickSingleRole($roles) {
        $singleRole = 105;
        $role2any = array_flip($roles);
        foreach (self::$SingleRole as $role) {
            if (isset($role2any[$role])) {
                $singleRole = $role;
            }
        }
        return $singleRole;
    }

    /*
     * 获取缓存数据
     * 不需要缓存时返回false,其它情况返回array
     */
    public function getCacheData(Oplib_Biz_Front_Gray_Promotion_2021Q2Able $ins, $requestParams, $params) {
        if (empty($params['grade'])) {
            $params['grade'] = 11;
        }
        if (empty($params['appId'])) {
            $params['appId'] = 'homework';
        }
        if (empty($params['singleRole'])) {
            $params['singleRole'] = 105;
        }
        if (!empty($requestParams['_promotion_singleRole'])) { // 手工指定role
            $params['singleRole'] = $requestParams['_promotion_singleRole'];
        }
        if (!empty($requestParams['_promotion_grade'])) {
            $params['grade'] = $requestParams['_promotion_grade'];
        }
        if (!empty($requestParams['_promotion_appId'])) {
            $params['appId'] = $requestParams['_promotion_appId'];
        }

        $cacheKey = self::_getCacheKey($ins, $params);
        Bd_Log::addNotice('promotion_key', $cacheKey);
        $isRefresh = 0;
        $output = false;
        do {
            if ($requestParams['_promotion_refresh'] ?? 0 == 1) { // 手工更新
                $isRefresh = 1;
                Bd_Log::addNotice('promotion_status', 'manual');
                break;
            }

            $isUseCache = $this->_isUseCache($requestParams, $params); // 不用缓存
            if (!$isUseCache) {
                Bd_Log::addNotice('promotion_status', 'off');
                break;
            }

            $redis = $this->getRedis();
            $cacheValue = $redis->get($cacheKey);
            if (false !== $cacheValue) { // 命中缓存
                $output = json_decode($cacheValue, true);
                Bd_Log::addNotice('promotion_status', 'use_cache');
                break;
            }
            if ($this->config['isAllCache']) { // 需要降级
                Bd_Log::addNotice('promotion_status', 'demote');
                $output = $ins::getDemoteData4promotion();
                break;
            }
            $isRefresh = 1; // 穿透
            Bd_Log::addNotice('promotion_status', 'pierce');
        } while (false);

        if ($isRefresh) { // 更新缓存
            $output = $ins::getSingleRoleData4promotion($params['grade'], $params['appId'], $params['singleRole']);
            if (empty($output)) {
                $output = [];
            }
            $redis = $this->getRedis();
            $redis->setex($cacheKey, self::CACHE_TTL, json_encode($output));
            $redis->hset(self::SUMMARY_KEY, $cacheKey, time());
            Bd_Log::addNotice('promotion_refresh', $cacheKey);
        }

        return $output;
    }

    private function _isUseCache($requestParams, $params) {
        $is = 0;
        do {
            if ($this->config['isUseCache']) {
                $is = 1;
                break;
            }
            if ($this->config['isPressureCache'] && Zb_Service_Navigator::isPressure()) {
                $is = 1;
                break;
            }
            $studentUid = $params['studentUid'] ?? 0;
            if ($studentUid && isset($this->config['whiteUids'][$studentUid])) {
                $is = 1;
                break;
            }
        } while (false);

        return $is;
    }

    private static function _getCacheKey(Oplib_Biz_Front_Gray_Promotion_2021Q2Able $ins, $params) {
        $key = 'promotion:' . $ins::getType4promotion() . ":{$params['grade']}:{$params['appId']}:{$params['singleRole']}";
        return $key;
    }

    public function getConfig() {
        return $this->config;
    }

    public function getRedis() {
        return $this->redis;
    }

    public function getSummaryInfo() {
        return $this->getRedis()->hgetall(self::SUMMARY_KEY);
    }

    public function getTypeSummary($type) {
        $ret = $this->getSummaryInfo();
        $output = [];
        foreach ($ret as $key => $value) {
            if (0 === strpos($key, 'promotion:' . $type)) {
                $output[$key] = $value;
            }
        }
        return $output;
    }

}
