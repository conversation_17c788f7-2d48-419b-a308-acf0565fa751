<?php

/**
 * brief: 学科tab
 * @author: <EMAIL>
 */
class Oplib_Biz_Pos_Const_SubjectTab {

    const SUBJECT_QIMENG = 30;
    const SUBJECT_XIEZI = 31;
	const SUBJECT_PROGRAM = 32;
	const SUBJECT_SUYANG = 33;
    const SUBJECT_XUEXILI = 48;
    const SUBJECT_KOUCAI = 51;

    /*
     * 特殊学科名称
     */
    private static $SpecialSubjectDesc = array(
        self::SUBJECT_QIMENG => '启蒙',
        self::SUBJECT_XIEZI => '写字',
		self::SUBJECT_PROGRAM => '编程',
		self::SUBJECT_SUYANG => '素养',
        self::SUBJECT_XUEXILI => '学习力',
        self::SUBJECT_KOUCAI => '口才',
    );

    /*
     * 获取学科名称
     */
    public static function getSubjectDesc($subject) {
        $desc = Hk_Util_Category::$COURSE[$subject] ?? '';
        if (isset(self::$SpecialSubjectDesc[$subject])) {
            $desc = self::$SpecialSubjectDesc[$subject];
        }

        return $desc;
    }

}
