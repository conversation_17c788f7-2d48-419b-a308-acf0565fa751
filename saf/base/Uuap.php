<?php


/**
 * 检测获取用户uuap对应cookie信息
 *
 * @since 1.1 2019-02-21 切换到新redis集群
 * @since 1.0 2017-11-21 初始化
 *
 * @filesource saf/base/Uuap.php
 * <AUTHOR>
 * @version 1.1
 * @date    2019-02-21
 */
class Saf_Base_Uuap {

    /**
     * 通过zybuuap获取登录状态，只能获取当前登录状态
     *
     * @return mixed:array|boolean
     */
    public static function checkUuapLogin() {
        $userInfo = array(
            "uname"   => "",        # 内网用户唯一标示
            "email"   => "",        # 内网用户邮箱
            "isLogin" => false,
            "lastLogTime" => 0,
        );

        $session  = self::getSession();
        if (false === $session) {
            return $userInfo;
        }
        if (!empty($session['uname'])) {
            $userInfo            = array_merge($userInfo, $session);
            $userInfo['isLogin'] = true;
        }
        return $userInfo;
    }

    /**
     * 获取zyb内部用户对应的session
     *
     * @return mixed:array|boolean
     */
    private static function getSession() {
        $zybuuap = $_COOKIE['ZYBUUAP'];
        if (strlen($zybuuap) <= 0) {
            return false;
        }
        //$session = self::getSessionByRedis($zybuuap);
        $session = self::getSessionByRpc($zybuuap);
        return $session;
    }
    private static function getSessionByRpc($zybuuap) {
        $header = array(
            'pathinfo' => "/session/api/getuuap",
        );
        $postdata = array('uuap' => $zybuuap);
        $ret    = ral('zybsession', 'POST', $postdata, 123, $header);
        if (empty($ret)) {
            $errno    = ral_get_errno();
            $errmsg   = ral_get_error();
            $protCode = ral_get_protocol_code();
            Bd_Log::addNotice("zybsessionGetuuapRalErrno",$errno);
            Bd_Log::addNotice("zybsessionGetuuapRalErrmsg",$errmsg);
            Bd_Log::addNotice("zybsessionGetuuapRalProtocolStatus",$protCode);
            Bd_Log::addNotice("getUuapErr", "empty");
            return false;
        }

        $ret    = json_decode($ret, true);
        $errno  = intval($ret['errNo']);
        $errmsg = strval($ret['errstr']);
        if ($errno > 0 || empty($ret['data'])) {
            Bd_Log::addNotice("zybsessionGetuuapRespErrno",$errno);
            Bd_Log::addNotice("getUUapErr", "decode");
            return false;
        }
        return $ret['data'];
    }

    private static function getSessionByRedis($zybuuap) {
        $redis = Hk_Service_RedisClient::getInstance("session");
        if (false === $redis) {
            return false;
        }
        $ret   = $redis->get($zybuuap);
        if (false === $ret || empty($ret)) {
            return false;
        }

        $session = @json_decode($ret, true);
        if (empty($session)) {
            return false;
        }
        return $session;
    }
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
