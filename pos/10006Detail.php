<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10006Detail.php
 * Author: <EMAIL>
 * Date: 2018/8/9
 * Time: 11:54
 * Desc: 推广卡片 | 目前用不到
 */

class Oplib_Pos_10006Detail extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval($arrCommand['posId']);
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());
        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }

        $info = Oplib_Common_Cache::getCacheByPosIdInfo($posId,$adId);

        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'courseType' => (in_array($info['courseType'],[21,24])) ? Oplib_Const_Operation::TYPE_PRIVATE_LONG : $info['courseType'],
            'skuDesc'    => $info['skuDesc'],
            'arrSkuId'   => ($info['arrSkuId'][0] !== 0 ) ? $info['arrSkuId'] : [],

            'condition'   => !empty($info['condition']) ? json_decode($info['condition'],true) : [],
            'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($adId),

        );

        return $arrOutput;

    }

}
