<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11006DetailBatch.php
 * Author: <EMAIL>
 * Date: 2018/12/3
 * Time: 15:07
 * Desc: 学科页试听课 | 19.01月版本
 */
class Oplib_Pos_11006DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

       // $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到10006
        $posId = intval($arrCommand['posId']);   //代理到10006
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade);

        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType' => $adInfo['courseType'],
                'subject'    => intval($adInfo['subject']),
                'name'       => $adInfo['name'],
                'skuDesc'    => $adInfo['skuDesc'],
                'skuBgImg'   => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'   => $adInfo['arrSkuId'],
                'condition'  => $adInfo['condition'],
            ];

        }

        return $arrOutPut;
    }
}
