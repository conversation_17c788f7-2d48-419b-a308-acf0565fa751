<?php

/**
 * @file 10002.php
 * <AUTHOR>
 * @date 2018年6月12日
 * @brief 糖果导航
 **/
class Oplib_Pos_10002 extends Oplib_Common_BasePos
{

    const CACHE_KET = 'sugarbean_10002';
    const NEW_YKVC  = 39;
    const NEW_ZBKVC = 55;

    public function invoke($arrCommand)
    {
        $posId = intval($arrCommand['posId']);
        $utype = intval($arrCommand['utype']);
        $grade = intval($arrCommand['grade']);
        $uid   = intval($arrCommand['uid']);
        $appType    = strval($arrCommand['appType']);
        $zbkvc      = isset($arrCommand['zbkvc']) ? intval($arrCommand['zbkvc']) : 0;
        $ykvc       = isset($arrCommand['ykvc']) ? intval($arrCommand['ykvc']) : 0;
        $arrOutput  = array('list'=>array());
        Hk_Util_Log::start('getPosGradeIdCostTime_10002');
        $cacheValue = Oplib_Common_Cache::getCacheByPosGradeId($posId, $grade);
        Hk_Util_Log::stop('getPosGradeIdCostTime_10002');

        Hk_Util_Log::start('formatCacheCostTime_10002');
        $sort = array();
        foreach($cacheValue as $cache){

            if($cache['appType']){  //为了兼容旧数据
                //应用类型
                $arrAppType = explode(',',$cache['appType']);
                if(!in_array($appType,$arrAppType)){
                    continue;
                }
            }

            //用户类型
            $arrUtype = explode(',',$cache['utype']);
            if(!in_array($utype, $arrUtype)){
                continue;
            }

            $pid =  $cache['ext']['arrImgInfo'][Oplib_Const_Operation::NEW_VC];
            if($appType === 'airclass'){
                if($ykvc >= self::NEW_YKVC){
                    $pid =  $cache['ext']['arrImgInfo'][Oplib_Const_Operation::NEW_VC];
                }else{
                    $pid =  ( ($cache['pid'] != "") && !$cache['ext']['arrImgInfo']) ? $cache['pid'] : $cache['ext']['arrImgInfo'][Oplib_Const_Operation::OLD_VC];
                }
            }


            if($appType === 'homework'){

                if($zbkvc >= self::NEW_ZBKVC ){
                    $pid =  $cache['ext']['arrImgInfo'][Oplib_Const_Operation::NEW_VC];
                }else{
                    $pid =  ( ($cache['pid'] != "") && !$cache['ext']['arrImgInfo']) ? $cache['pid'] : $cache['ext']['arrImgInfo'][Oplib_Const_Operation::OLD_VC];
                }
            }

            if($appType == 'minipro'){
                $pid =  ( ($cache['pid'] != "") && !$cache['ext']['arrImgInfo']) ? $cache['pid'] : $cache['ext']['arrImgInfo'][Oplib_Const_Operation::OLD_VC];
            }



            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId' => $cache['id'],
                'posId'=> $posId,
                'name' => $cache['name'],
                'clickType' => $cache['clickType'],
                'clickUrl'  => $cache['clickUrl'],
                'cardImg'   => Hk_Util_Image::getImgUrlBySrc($pid),
                'cardImg2'   => $pid,
                'rank'      => $cache['rank'],
                'courseType'=> $cache['courseType'],
                'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($cache['id']),
                'orifrom'   => Oplib_Const_Operation::$ORI_FROM_ARRAY[$posId] . intval($cache['id']),
                'cardImgTag' => strval($cache['ext']['cardImgTag']),    // 导航标签
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);

        Hk_Util_Log::stop('formatCacheCostTime_10002');

        Hk_Util_Log::start('new_formatCacheCostTime_10002');
        if ($appType === 'homework') {
            // 是否是新版糖果位
            $isNewCandyPos = $zbkvc >= self::NEW_ZBKVC;

            // ucep活动相关的
            $cuid      = strval($arrCommand['cuid']);
            $arrParams = ['cuid' => $cuid, 'zbkvc' => $zbkvc, 'isOldCandyPos' => $isNewCandyPos ? 0 : 1];
            $activityInfoList = Oplib_Util_UcepCandyPosition::getUcepUserAllActivityInfoList($uid, $posId, Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId], $arrParams);

            // 好课偷听0.1入口
            if ($uid > 0) {
                $data = Oplib_Util_HaoKeTouTing::getUserBaoMingInfo($uid, $posId, Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId]);
                if ($data) {
                    $activityInfoList[] = $data;
                }
            }
            $insertPos = 0;
            if ($isNewCandyPos) {
                // 新版放到第四个位置
                $insertPos = min(3, count($arrOutput['list']));
            }
            array_splice($arrOutput['list'], $insertPos, 0, $activityInfoList);
        }

        Hk_Util_Log::stop('new_formatCacheCostTime_10002');

        return $arrOutput;
    }


}
