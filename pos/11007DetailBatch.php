<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11006DetailBatch.php
 * Author: <EMAIL>
 * Date: 2018/12/3
 * Time: 15:07
 * Desc: 学科页试听课 | 19.01月版本
 */
class Oplib_Pos_11007DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到10006
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $bCache  = $arrCommand['bCache'];
        $status  = $arrCommand['status'];
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade,$bCache,$status);

        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            if($adInfo['subject'] == -1 &&(!is_array($adInfo['condition']['seasonList']) || empty($adInfo['condition']['seasonList']))) {
                switch ($adInfo['learnSeason']){
                    case Zb_Const_LearnSeason::LEARN_SEASON_SPRING:
                        $adInfo['condition']['seasonList'] = array(
                            Zb_Const_LearnSeason::LEARN_SEASON_SPRING_1,
                            Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,
                            Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3,
                            Zb_Const_LearnSeason::LEARN_SEASON_SPRING_4,
                        );
                        break;
                    case Zb_Const_LearnSeason::LEARN_SEASON_SUMMER:
                        $adInfo['condition']['seasonList'] = array(
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_0,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_1,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_2,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_3,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_4,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_5,
                            Zb_Const_LearnSeason::LEARN_SEASON_SUMMER_6,
                        );
                        break;
                    case Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN:
                        $adInfo['condition']['seasonList'] = array(
                            Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN_1,
                            Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN_2,
                            Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN_3,
                            Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN_4,
                        );
                        break;
                    case Zb_Const_LearnSeason::LEARN_SEASON_WINTER:
                        $adInfo['condition']['seasonList'] = array(
                            Zb_Const_LearnSeason::LEARN_SEASON_WINTER_1,
                            Zb_Const_LearnSeason::LEARN_SEASON_WINTER_2,
                            Zb_Const_LearnSeason::LEARN_SEASON_WINTER_3,
                            Zb_Const_LearnSeason::LEARN_SEASON_WINTER_4,
                        );
                        break;
                }

            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType' => (in_array($adInfo['courseType'],[21,24])) ? Oplib_Const_Operation::TYPE_PRIVATE_LONG : $adInfo['courseType'],
                'subject'    => intval($adInfo['subject']),
                'name'       => $adInfo['name'],
                'skuDesc'    => $adInfo['skuDesc'],
                'skuBgImg'   => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'   => $adInfo['arrSkuId'],
                'condition'  => $adInfo['condition'],
            ];

        }

        return $arrOutPut;
    }
}
