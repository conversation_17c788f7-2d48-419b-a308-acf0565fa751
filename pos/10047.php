<?php
/**
 * brief: 学科tab
 * @author: <EMAIL>
 */
class Oplib_Pos_10047 extends Oplib_Common_BasePos
{

    public function invoke($arrCommand) {
        $posId = intval($arrCommand['posId']);

        $arrOutput = array(
            'title' => '',
            'list' => [],
        );

        $cacheValue = Oplib_Common_Cache::getPosValidAll($posId);
        $sort = array();
        foreach ($cacheValue as $one) {
            $sort[] = $one['rank'];
        }
        array_multisort($sort, SORT_ASC, $cacheValue);
        $grade2subjects = array();
        foreach ($cacheValue as $one) {
            $grade2subjects[$one['grade']][] = $one['subject'];
        }
        ksort($grade2subjects);
        foreach ($grade2subjects as $grade => $subjects) {
            $arrOutput['list'][] = array(
                'grade' => $grade,
                'subjectList' => $subjects,
            );
        }

        return $arrOutput;
    }

	public static function getCheapCourseInfo($arrCommand) {
    }

}
