<?php
/**
 * Ips
 * <AUTHOR>
 * @DateTime 2020-06-01
 */
class ActPlatMis_Ips
{
    private static $objIps = null;

    /**
     * 生成Ips对象
     * <AUTHOR>
     * @DateTime 2020-06-01
     * @return   [type]                [description]
     */
    public static function initIps()
    {
        // IPS接入文档
        // http://ued.zuoyebang.cc/documents/saas-doc/#/ips/ips?id=%e6%a6%82%e8%bf%b0](http://ued.zuoyebang.cc/documents/saas-doc/#/ips/ips?id=概述
        if (!isset(self::$objIps)) {
            if ($_SERVER['HTTP_HOST'] == 'lxyymis.zuoyebang.cc') {
                self::$objIps = self::createIpsObjectByLxyyMis();
            } elseif(  ral_get_idc() == 'test'  ) {
               self::$objIps = self::createIpsObjectByLxyyMisText();
            } else {
                self::$objIps = self::createIpsObjectByActMis();
            }
        }

        return self::$objIps;
    }

    private static function createIpsObjectByLxyyMisText()
    {
        // $sid    = ActPlatMis_Const_Ips::IPS_SID;
        $secret = ActPlatMis_Const_Ips::IPS_SECRET2TEXT;
        $path   = ActPlatMis_Const_Ips::IPS_PATH2TEXT;

        return new Saaslib_Service_IPSV2(self::getIpsSidByHost(), $secret, $path);
    }

    /**
     * 创建Ips对象基于lxyymis
     * <AUTHOR>
     * @DateTime 2020-06-01
     * @return   [type]                [description]
     */
    private static function createIpsObjectByLxyyMis()
    {
        // $sid    = ActPlatMis_Const_Ips::IPS_SID;
        $secret = ActPlatMis_Const_Ips::IPS_SECRET;
        $path   = ActPlatMis_Const_Ips::IPS_PATH;

        return new Saaslib_Service_IPSV2(self::getIpsSidByHost(), $secret, $path);
    }

    /**
     * 创建Ips对象基于actmis
     * <AUTHOR>
     * @DateTime 2020-06-01
     * @return   [type]                [description]
     */
    private static function createIpsObjectByActMis()
    {
        // $sid    = ActPlatMis_Const_Ips::IPS_SID2;
        $secret = ActPlatMis_Const_Ips::IPS_SECRET2;
        $path   = ActPlatMis_Const_Ips::IPS_PATH2;

        return new Saaslib_Service_IPSV2(self::getIpsSidByHost(), $secret, $path);
    }

    /**
     * 获取sid信息
     * <AUTHOR>
     * @DateTime 2020-06-01
     * @return   [type]                [description]
     */
    public static function getIpsSidByHost()
    {
        if ($_SERVER['HTTP_HOST'] == 'lxyymis.zuoyebang.cc') {
            $sid = ActPlatMis_Const_Ips::IPS_SID;
        } else {
            $sid = ActPlatMis_Const_Ips::IPS_SID2TEXT;
        }

        return $sid;
    }

    public static function getEnviron()
    {
        $env = Hk_Util_Env::getRunEnv();
        //判断是非容器化环境
        if ($env == Hk_Util_Env::RunEnvUnknown) {
            return ral_get_idc();
        }
        return $env;
    }

    public static function getHttpHost(){

        $env = self::getEnviron();
        $host = Hk_Util_Env::getHost();
        if(empty($host) || $host === null){
            $host = $_SERVER['HTTP_X_FORWARDED_HOST'];
        }
        Bd_Log::addNotice("getHttpHost",json_encode(['host'=>$host,'env'=>$env]));

        if($env == Hk_Util_Env::RunEnvTest && stripos($host,'-e.suanshubang.') !==false){
            $urlArr = explode('-',$host);
            $host = sprintf("%s-%s-e.suanshubang.com",$urlArr[0],$urlArr[1]);
            Bd_Log::addNotice("getHttpccTestHost",['host'=>$host,'urlArr'=>$urlArr]);
            return $host;
        }

        if($env == Hk_Util_Env::RunEnvTest && stripos($host,'-cc.suanshubang.') !==false){
            $urlArr = explode('-',$host);
            $host = sprintf("%s-%s-cc.suanshubang.com",$urlArr[0],$urlArr[1]);
            Bd_Log::addNotice("getHttpccTestHost",['host'=>$host,'urlArr'=>$urlArr]);
            return $host;
        }

        if($env == Hk_Util_Env::RunEnvTest && stripos($host,'.suanshubang.') !==false){
            $urlArr = explode('.',$host);
            $host = sprintf("%s-%s-e.suanshubang.com",$urlArr[0],$urlArr[1]);
            Bd_Log::addNotice("getHttpdianTestHost",['host'=>$host,'urlArr'=>$urlArr]);
            return $host;
        }
        return $host;
    }
}