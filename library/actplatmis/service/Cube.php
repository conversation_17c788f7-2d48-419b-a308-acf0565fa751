<?php

class ActPlatMis_Service_Cube
{
    const SERVICE = 'cube';

    /**
     * 分页查询推荐spu列表
     * http://yapi.zuoyebang.cc/project/2197/interface/api/241300
     * @return false|mixed
     */
    public static function getRecommendSpuListPage(array $spuIds, int $actId, int $pn = 1, int $rn = 50)
    {
        $pathInfo = '/cube/api/getspulist';
        $input = [
            'spuIds' => $spuIds,
            'actId' => $actId,
            'pn' => $pn,
            'rn' => $rn,
        ];
        $result = Oplib_Util_RalClient::ralPost(self::SERVICE, $input, $pathInfo, ['Content-Type' => 'application/json']);
        if ($result['errNo'] != 0) {
            $errStr = $result['errStr'];
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, '调用cube系统获取推荐spu列表失败,' . $errStr, json_encode($input));
            return false;
        }
        return $result['data'];
    }

    /**
     * 获取所有的spuid（自身+推荐）
     * @return false|array
     */
    public static function getAllSpuIds(int $spuId, int $actId)
    {
        $spuIds = [$spuId => $spuId];
        $page = 1;
        while (true) {
            $recommendSpuList = self::getRecommendSpuListPage([$spuId], $actId, $page);
            if (false === $recommendSpuList) {
                return false;
            }

            if (!empty($recommendSpuList['spuList']) && is_array($recommendSpuList['spuList'])) {
                foreach ($recommendSpuList['spuList'] as $recommendSpu) {
                    $spuIds[$recommendSpu['spuId']] = $recommendSpu['spuId'];
                }
            }

            if (!isset($recommendSpuList['hasMore']) || false === $recommendSpuList['hasMore']) {
                break;
            }

            $page++;
        }
        return $spuIds;
    }
}