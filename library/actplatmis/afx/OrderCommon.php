<?php

/**
 * 订单通用方法
 */
class ActPlatMis_Afx_OrderCommon
{

    //类型
    const TYPE_FX = 1;//分销单
    const TYPE_DL = 2;//代理单

    // 此处几级代理单，几级分销单指的是结算层级。和用户具体在那个层级没有关系 todo 后面不要用这的了吧
    const TYPE_FX_11 = 11; // 一级分销单
    const TYPE_DL_21 = 21; // 一级代理单
    const TYPE_DL_22 = 22; // 二级代理单
    const TYPE_DL_23 = 23; // 三级代理单

    const TYPE_FX_MAP = [
        self::TYPE_FX_11 => '一级分销单',
    ];
    const TYPE_DL_MAP = [
        self::TYPE_DL_21 => '一级代销单',
        self::TYPE_DL_22 => '二级代理单',
        self::TYPE_DL_23 => '三级代理单',
    ];

    public static $TYPE = array(
        self::TYPE_FX  => '分销单',
        self::TYPE_DL  => '代理单',
    );

    //状态
    const STATUS_ON  = 10;//生效
    const STATUS_OFF = 30;//失效
    const STATUS_GONGHAI = 20;//公海数据

    public static $STATUS = array(
        self::STATUS_ON => '有效',
        self::STATUS_OFF => '失效',
        self::STATUS_GONGHAI => '公海数据',
    );


    //通过代理商uids + 活动id 获取指定时间订单数 默认当天
    public function getOrderCntByActAndUIds($appId, $actId, $uids, $sTime = 0 ,$eTime = 0){
        $env = ral_get_idc();
        $objOrder =  new Service_Data_Afx_AFXDorisOrder();

        if ($env == 'test'){
            $objOrder =  new Service_Data_Afx_AFXOrder();
        }

        if (empty($uids)){
            return 0;
        }

        //公海数据不计数
        $where = ['appId' => $appId, 'actId' => $actId , 'type' => self::TYPE_FX_11, 'status' => self::STATUS_ON];
        $where[] = 'uid in (' . join(',', $uids). ')';

        if (empty($sTime)){
            $sTime = strtotime(date('Y-m-d 00:00:00'));
        }

        if (empty($eTime)){
            $eTime = time();
        }

        $where[] = "buy_time >= {$sTime} and buy_time <= {$eTime}";

//        return $objOrder->getCnt($where); // doris表线下，这里直接返回

        return 0;
    }
}
