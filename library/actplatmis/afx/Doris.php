<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Doris.php
 * <AUTHOR>
 * @date   2020/3/28 19:14
 * @brief
 **/
class ActPlatMis_Afx_Doris
{
    protected $_dbName;
    protected $_table;
    protected $arrFieldsMap;
    protected $arrTypesMap;

    private function searchDoris($sqlQuery)
    {
        return Qdlib_Util_Doris::query(Qdlib_Util_Doris::URI_V2_V1, $sqlQuery);
    }

    public function getCntByConds($arrConds)
    {
        //限制条件字段以及格式的转换
        $arrConds  = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds  = Hk_Service_Db::getConds($arrConds);

        //参数
        $where = $this->getWhere($arrConds);

        //sql生成
        $sqlQuery = "SELECT count(1) as count FROM {$this->_table} {$where} limit 1";

        //查询
        $res = $this->searchDoris($sqlQuery);
        if(!$res){
            Qdlib_Util_Log::warning("hetu", __CLASS__, __FUNCTION__, sprintf("【searchDoris failed.sqlQuery:%s】", $sqlQuery));
            return 0;
        }

        return intval($res['data'][0]['count']);
    }

    public function getGroupByCntByConds($arrConds, $arrAppends = NULL)
    {
        //限制条件字段以及格式的转换
        $arrConds  = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds  = Hk_Service_Db::getConds($arrConds);

        //参数
        $where = $this->getWhere($arrConds);
        $appends = $arrAppends[0];

        //sql生成
        $sqlQuery = "select count(1) as count FROM (SELECT count(1) as count FROM {$this->_table} {$where} {$appends}) a limit 1";

        //查询
        $res = $this->searchDoris($sqlQuery);
        if(!$res){
            Qdlib_Util_Log::warning("hetu", __CLASS__, __FUNCTION__, sprintf("【searchDoris failed.sqlQuery:%s】", $sqlQuery));
           return 0;
        }

        return intval($res['data'][0]['count']);
    }

    public function getListByConds($arrConds, $arrFields, $arrAppends = NULL)
    {
        //限制条件字段以及格式的转换
        $arrConds  = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds  = Hk_Service_Db::getConds($arrConds);

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);

        //参数
        $fields = implode(',',$arrFields);
        $where = $this->getWhere($arrConds);
        $appends = $arrAppends[0];

        //sql生成
        $sqlQuery = "SELECT {$fields} FROM {$this->_table} {$where} {$appends}";

        //查询
        $res = $this->searchDoris($sqlQuery);
        if($res['code']!=0 || empty($res['data'])){
            return [];
        }
        //map格式转换
        if (is_array($res['data']) && is_array($res['data'][0])) {
            foreach ($res['data'] as &$row) {
                if (!empty($row['skuInfo'])){
                    $row['skuInfo'] = str_replace("\n", '', $row['skuInfo']);
                }

                $row = Hk_Service_Db::mapFieldType($row, $this->arrTypesMap);
            }
            unset($row);
        }

        return $res['data'];
    }

    private function getWhere($arr)
    {
        $ret = " where 1=1 ";
        reset($arr);
        while (list($k, $v) = each($arr))
        {
            $tmp = is_numeric($k) ? "$v" : "$k" . "$v";
            $ret .= " and ". $tmp;
        }
        return $ret;
    }
}
