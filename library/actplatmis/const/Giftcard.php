<?php
class ActPlatMis_Const_Giftcard
{
    const IS_CHANGE_CAN = 1;     # 可以退换
    const IS_CHANGE_CAN_NOT = 0; # 不可以退换

    const GET_TYPE_EXCHANGE = 1; # 兑换
    const GET_TYPE_CLAIM = 2;    # 领取

    const MAX_EXCHANGE_COUNT = 99; # 最大兑换次数

    public static $isChangeMap = [
        self::IS_CHANGE_CAN     => '可以退换',
        self::IS_CHANGE_CAN_NOT => '不可以退换',
    ];

    public static $getTypeMap = [
        self::GET_TYPE_EXCHANGE => '兑换',
        self::GET_TYPE_CLAIM    => '领取',
    ];

    // 礼品卡策略有效无效状态
    const TACTICS_AVAILABLE = 1; // 有效
    const TACTICS_OVERDUE = 2; // 无效
    public static $tacticsStatus = [
        self::TACTICS_AVAILABLE => '有效',
        self::TACTICS_OVERDUE => '失效',
    ];


    //学区
    const DEPARTMENT_PRIMARY = 1; // 小学
    const DEPARTMENT_MIDDLE  = 2; // 初中
    const DEPARTMENT_HIGH    = 3; // 高中
    public static $departmentMap = [
        self::DEPARTMENT_PRIMARY => '小学',
        self::DEPARTMENT_MIDDLE  => '初中',
        self::DEPARTMENT_HIGH    => '高中',
    ];

    //全部标签id
    public static $allTabMap = [1, 2, 3];
    //学部和全部标签id对应关系
    public static $allDepartmentTabMap = [
        self::DEPARTMENT_PRIMARY => 1,
        self::DEPARTMENT_MIDDLE  => 2,
        self::DEPARTMENT_HIGH    => 3,
    ];

    //学部字典
    const DEPARTMENT_DICTIONARIES = 100001;

    //特殊策略id
    public static function getSpecialTacticsId()
    {
        $specialTacticsId = Zb_Service_NCM::get(Zb_Service_NCM::APP_YIKE, 'fenxiao', 'common', 'giftcard120');
        $ret = !empty($specialTacticsId) ? (int)$specialTacticsId : 120;
        return $ret;
    }
}
