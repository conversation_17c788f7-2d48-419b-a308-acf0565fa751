<?php

/**
 * brief: 处理sparta的url，单例
 * @author: <EMAIL>
 */
class Hkzb_Service_HybridUrl
{

    const ENV_TYPE_ONLINE = 1;
    const ENV_TYPE_TIPS = 2;
    const ENV_TYPE_DOCKER = 3;

    private static $Ins;
    private $context; // 上下文，默认是$_SERVER
    private $isDocker; // 是否docker
    private $spartaEnv; // sparta的env
    private $module2page2html; // array(模块 => 页面 => html)， docker环境本地sparta的配置
    private $spartaConfigPath; // 斯巴达配置路径

    protected function __construct() {
        $this->setContext($_SERVER);
        $this->module2page2html = array();
        $this->spartaConfigPath = preg_replace('/\/php.*/', '', dirname(__FILE__)) . '/conf/app/sparta';
    }

    /**
     * 单例
     * @return self
     */
    public static function getIns()
    {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    /**
     * 设置上下文
     * @param $context
     */
    private function setContext($context) {
        $this->context = $context;
        $this->isDocker = 0;
        if (ral_get_idc() == 'test' && isset($context['HTTP_HOST']) && preg_match("/docker/", $context['HTTP_HOST'])) {
            $this->isDocker = 1;
        }

        $this->spartaEnv = Hkzb_Service_Hybrid::readFromFile($context);
    }

    /**
     * 批量转换斯巴达返回的url
     * 入参即为斯巴达的返回, 格式：
     *
     * {
     *       "rule|excitation": {
     *           "tips": "https://[HTTP_HOST]/static/hy/excitation/rule-4dc8e464-hycache.html",
     *           "online": "https://[HTTP_HOST]/static/hy/excitation/rule-a8c99a32-hycache.html"
     *       },
     *       "date-table|course-center": {
     *           "tips": "https://[HTTP_HOST]/static/hy/course-center/date-table-0ac1009d-hycache.html",
     *           "online": "https://[HTTP_HOST]/static/hy/course-center/date-table-3cdaf4a1-hycache.html"
     *       }
     *   }
     *
     * @param $spartaUrls
     * @return array
     */
    public function batchTransSpartUrl($spartaUrls) {
        $output = array();
        foreach ($spartaUrls as $pageModule => $envUrlList) {
            $output[$pageModule] = $this->transSpartUrl($pageModule, $envUrlList);
        }
        return $output;
    }

    /**
     * 转换sparta给的url
     * @param $pageModule
     * @param $envUrlList
     * @return string
     */
    public function transSpartUrl($pageModule, $envUrlList) {
        if ($this->isDocker) { // docker环境通过local配置获取url
            $url = $this->_getLocalUrl($pageModule, $envUrlList);
        } else {
            $url = $envUrlList[$this->spartaEnv] ?? '';
            if (empty($url)) {
                Bd_Log::warning("sparta url error, pageModule[$pageModule], urlList:" . json_encode($envUrlList));
                $arr = explode('|', $pageModule);
                $url = "https://[HTTP_HOST]/static/hy/{$arr[1]}/{$arr[0]}.html";
            }
        }
        $host = $this->context['HTTP_HOST'];
        $url = str_replace('[HTTP_HOST]', $host, $url);

        return $url;
    }

    /**
     * 获取local对应的url
     * @param $pageModule
     * @param $envUrlList
     * @return mixed
     */
    private function _getLocalUrl($pageModule, $envUrlList) {
        $url = '';
        do {
            if (empty($envUrlList) || empty($envUrlList['tips'])) {
                Bd_Log::warning("sparta url error, pageModule[$pageModule], urlList:" . json_encode($envUrlList));
                break;
            }
            $url = $envUrlList['tips'];
            $arr = explode('|', $pageModule);
            if (2 != count($arr)) { // 传入的pageModule不对
                Bd_Log::warning("docker_sparta_error pageModule invalid, pageModule[$pageModule], urlList:" . json_encode($envUrlList));
                break;
            }
            $module = $arr[1];
            $page = $arr[0];
            $html = $this->_getLocalPageHtml($module, $page);
            if (empty($html)) { // 模块没有部署过
                Bd_Log::warning("docker_sparta_error local html not found, pageModule[$pageModule]");
                break;
            }
            if (!preg_match('/-([^-]+-hycache)/', $envUrlList['tips'], $apiMatchRet)) {
                if (!preg_match('/-([^-]+-hycache)/', $envUrlList['online'], $apiMatchRet)) {
                    Bd_Log::warning("docker_sparta_error unknown html format of api, pageModule[$pageModule], urlList:" . json_encode($envUrlList));
                    break;
                }
            }
            if (!preg_match('/-([^-]+-hycache)/', $html, $localMatchRet)) {
                Bd_Log::warning("docker_sparta_error unknown html format of local, pageModule[$pageModule], localHtml[$html] urlList:" . json_encode($envUrlList));
                break;
            }
            $url = str_replace($apiMatchRet[1], $localMatchRet[1], $envUrlList['tips']);
        } while (false);

        return $url;
    }

    /**
     * 获取local配置的html名称
     * @param $module
     * @param $page
     * @return string
     */
    private function _getLocalPageHtml($module, $page) {
        if (!isset($this->module2page2html[$module])) {
            $this->module2page2html[$module] = array();
            $file = $this->spartaConfigPath . "/modules/$module.json";
            @$str = file_get_contents($file);
            $config = json_decode($str, true);
            if (!empty($config['pages'])) {
                foreach ($config['pages'] as $one) {
                    $this->module2page2html[$module][$one['key']] = $one['html'];
                }
            }
        }
        $html = '';
        if (isset($this->module2page2html[$module][$page])) {
            $html = $this->module2page2html[$module][$page];
        }

        return $html;
    }

}
