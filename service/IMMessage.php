<?php
/***************************************************************************
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 **************************************************************************/


/**
 * @file IMMessage.php
 * <AUTHOR>
 * @date 2016/11/17
 * @brief 消息ds
 **/
class Hkzb_Service_IMMessage
{
    const REDIS_NOTICE_KEY = "REDIS_IM_NOTICE_KEY_";   //黄条通知KEY
    const REDIS_NOTICE_TIME = 31536000; //一年

    const CMD_SEND_MESSAGE = 1;
    const CMD_FETCH_MSGID = 14;

    const MSG_CLEAN_TYPE_STABLE = 1; //未读不可清理的消息
    const MSG_CLEAN_TYPE_VOLATILE = 2; //未读系统可清理的消息

    const MSG_COMMON = 1;
    const MSG_NOTICE = 2;
    const MSG_CONTROL = 3;

    const MSG_AT_ALL = 'ALL';

    const MSG_TYPE_TEXT = 1001;
    const MSG_TYPE_PIC = 1002;
    const MSG_TYPE_VOICE = 1003;
    const MSG_TYPE_COURSEINFO = 1005;  //课程内容
    const MSG_TYPE_FILEINFO = 1006;  //文件内容
    const MSG_TYPE_NOTICE_SYS = 2001;  //系统消息
    const MSG_TYPE_CONTROL_NOTALK = 3001;  //禁言和解禁
    const MSG_TYPE_CONTROL_LESSONSTART = 3002;  //上下课消息
    const MSG_TYPE_CONTROL_ANNOUNEDIT = 3003;  //群公告变更
    const MSG_TYPE_CONTROL_GMEMBEREDIT = 3004;  //群成员增减
    const MSG_TYPE_CONTROL_MSGDEL = 3005;  //消息删除或撤消
    const MSG_TYPE_CONTROL_CLOSEGROUP = 3006;  //关群
    const MSG_TYPE_CONTROL_KICKOFF = 3007;  //踢人

    static $MSG_TYPE_ARRAY = array(
        self::MSG_TYPE_TEXT => self::MSG_COMMON,
        self::MSG_TYPE_PIC => self::MSG_COMMON,
        self::MSG_TYPE_VOICE => self::MSG_COMMON,
        self::MSG_TYPE_COURSEINFO => self::MSG_COMMON,
        self::MSG_TYPE_FILEINFO => self::MSG_COMMON,
        self::MSG_TYPE_NOTICE_SYS => self::MSG_NOTICE,
        self::MSG_TYPE_CONTROL_NOTALK => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_LESSONSTART => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_ANNOUNEDIT => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_GMEMBEREDIT => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_MSGDEL => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_CLOSEGROUP => self::MSG_CONTROL,
        self::MSG_TYPE_CONTROL_KICKOFF => self::MSG_CONTROL,
    );

    /**
     * 纯文本（非json）类型的消息
     * @var array
     */
    static $MSG_TEXT_FORMAT = array(
        self::MSG_TYPE_TEXT,
        self::MSG_TYPE_CONTROL_CLOSEGROUP
    );

    const MSG_DATA_KEY = 'K_IM_MSG_';
    const MSG_DATA_LENGTH = 800;
    const MSG_DATA_EXPIRE = 604800;

    private $_objMQSMessager;

    function __construct()
    {
        $this->_objMQSMessager = new Hkzb_Service_MQSMessager(Hkzb_Service_MQSMessager::MQS_PRODUCT_IM);
    }

    /**
     * @brief 写入消息
     * @param int $fromUid
     * @param int $toUid
     * @param int $toGroupId
     * @param int $msgType
     * @param string $msgContent
     * @param string $atUids
     * @return array|bool
     */
    public function makeMessage($fromUid = 0, $toUid = 0, $toGroupId = 0, $msgType = 0, $msgContent = '', $atUids = '')
    {
        if (intval($fromUid) <= 0 || (intval($toUid) <= 0 && intval($toGroupId) <= 0) || intval($msgType) <= 0 || trim($msgContent) == '') {
            return false;
        }

        $msgData = array(
            'from_uid' => intval($fromUid),
            'to_uid' => intval($toUid),
            'to_group_id' => intval($toGroupId),
            'msg_type' => intval($msgType),
            'msg_content' => is_array($msgContent) ? json_encode($msgContent) : strval($msgContent),
            'msg_time' => intval(microtime(true) * 1000),
            'at_uids' => empty($atUids) ? "" : ",{$atUids},",
        );

        $ret = $this->_objMQSMessager
            ->setToGroupId(intval($toGroupId))
            ->setMsgExpireTime(self::MSG_DATA_EXPIRE)
            ->setMsgData($msgData)
            ->send();

        if (false === $ret) {
            return false;
        }

        $msgData['msg_id'] = $ret;

        return $msgData;
    }

    /**
     * @brief 设置用户通知内容
     * @param int $uid
     * @param string $title
     * @param string $content
     * @return bool
     */
    public static function setNotice($uid, $title, $content)
    {
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $objCodis = Hk_Service_RedisClient::getInstance("zbcourse");
        $noticeInfo = $objCodis->get(self::REDIS_NOTICE_KEY . $uid);
        $noticeList = json_decode($noticeInfo, true);

        $notice = array(
            'title' => $title,
            'content' => $content,
            'time' => time(),
        );

        if (is_array($noticeList) && !empty($noticeList)) {
            $noticeList[] = $notice;
        } else {
            $noticeList = array($notice);
        }

        //双写stored
        $res = $objStored->setex(self::REDIS_NOTICE_KEY . $uid, self::REDIS_NOTICE_TIME, json_encode($noticeList));

        $res = $objCodis->setex(self::REDIS_NOTICE_KEY . $uid, self::REDIS_NOTICE_TIME, json_encode($noticeList));
        if (false === $res) {
            Bd_Log::warning("Error[set notice error] Detail[uid:$uid title:$title content:$content]");

            return false;
        }

        return true;
    }

    /**
     * @brief 消息发送
     * @param int $fromUid
     * @param int $toGroupId
     * @param int $msgType
     * @param string $msgContent
     * @param int $toUid
     * @return array|bool
     */
    public function _sendMessage($fromUid, $toGroupId, $msgType, $msgContent, $toUid)
    {
        $msgData = $this->makeMessage(intval($fromUid), 0, intval($toGroupId), intval($msgType), strval($msgContent));
        if ($msgData === false || empty($msgData['msg_id'])) {
            return false;
        }

        $ret = $this->sendMessage(intval($msgData['msg_id']), array($toUid), self::MSG_CLEAN_TYPE_VOLATILE);
        if ($ret === false) {
            return false;
        }

        return $msgData;
    }

    /**
     * @brief 发送消息 - 非特殊不要使用这个方法
     * @param int $msgId
     * @param array $toUids
     * @param int $msgType
     * @param array $toUidListPushStatus
     * @param string $pushTitle
     * @param string $pushContent
     * @return bool
     */
    public function sendMessage($msgId = 0, $toUids = array(), $msgCleanType = self::MSG_CLEAN_TYPE_VOLATILE, $toUidListPushStatus = array(), $pushTitle = '', $pushContent = '')
    {
        if (intval($msgId) <= 0 || empty($toUids) || !is_array($toUids) || intval($msgCleanType) <= 0) {
            return false;
        }

        $toUidList = array();
        $pushUidList = array();
        foreach ($toUids as $n => $uid) {
            $intUid = intval($uid);
            if ($intUid <= 0) {
                continue;
            }
            $intPushStatus = intval($toUidListPushStatus[$n]);
            $toUidList[] = $intUid;

            if (1 === $intPushStatus) {
                $pushUidList[] = $intUid;
            }
        }

        if (empty($toUidList)) {
            return false;
        }

        Bd_Log::addNotice("toUidCnt", count($toUidList));

        $ret = $this->_objMQSMessager
            ->setMsgId($msgId)
            ->setMsgCleanType($msgCleanType)
            ->setMsgExpireTime(self::MSG_DATA_EXPIRE)
            ->setToUids($toUidList)
            ->setPushTitle($pushTitle)
            ->setPushContent($pushContent)
            ->setPushUids($pushUidList)
            ->forward();

        Bd_Log::addNotice("msgType", $msgCleanType);

        return $ret;
    }


    /**
     * 判断消息是否可以被Decode
     *
     * @param $msgType 消息类型
     * @return boolean
     */
    public function isNeedDecode($msgType){

        return !in_array($msgType, self::$MSG_TEXT_FORMAT);
    }

}
