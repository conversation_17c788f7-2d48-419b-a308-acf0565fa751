<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-09
 * @brief       订单列表
 **/
class Qdlib_Service_Hetu_AFXSeasonList
{
    public function __construct($objDorisUnionOrder = null)
    {
        if(is_null($objDorisUnionOrder)) {
            $objDorisUnionOrder = new Qdlib_Ds_Hetu_AFXDorisUnionOrder();
        }
        $this->objDorisUnionOrder = $objDorisUnionOrder;
    }

    protected function _buildConds($arrInput)
    {
        $conds = [
            'app_id =' => 'appId',
            'business_line = ' => 'businessLine',
            'project_lv1 = ' => 'projectLv1',
            'project_lv2 = ' => 'projectLv2',
        ];

        $arrConds = [];
        foreach ($conds as $k => $v) {
            if (empty($arrInput[$v])) {
                continue;
            }
            if ($k == 'project_lv1'){
                $arrConds[] = 'project_lv1 in ('.$arrInput['projectLv1'].')';
                continue;
            }

            $value = $arrInput[$v];
            if (is_string($value)) {
                $arrConds[] = $k . ' \'' . $arrInput[$v] . '\'';
            } else {
                $arrConds[] = $k . $arrInput[$v];
            }
        }
        return $arrConds;
    }

    public function execute($arrInput)
    {
        //拼装查询数据
        $arrConds = $this->_buildConds($arrInput);
        $group = 'learn_season';
        $list = $this->objDorisUnionOrder->getList($arrConds, ['learn_season'], $group);
        $arrOutput = $this->format($list);
        return $arrOutput;
    }

    protected function format($seasons)
    {
        $seasonList = [];
        foreach ($seasons as $season) {
            if(empty($season['learn_season']) || strtoupper($season['learn_season']) == 'NULL') {
                continue;
            }

            $learnSeason = $season['learn_season'];
            $seasonList [] = [
                'value' => $learnSeason,
                'label' => Zb_Const_LearnSeason::$learnSeasonMap[$learnSeason] ?? '-',
            ];
        }
        return $seasonList;
    }
}
