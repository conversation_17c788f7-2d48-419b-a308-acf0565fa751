<?php
/**
 * <AUTHOR>
 * @version 2.0
 * @date    2020-11-18
 */

class Saaslib_Service_IPSV2
{
    const LOGIN_URI      = "/static/cas-fe/?version=%s&sdk=%s";
    const LOGOUT_FE_URI  = "/static/cas-fe/?logout=1&version=%s&sdk=%s";
    const LOGOUT_URI     = "/ips/home/<USER>";
    const CHECKLOGIN_URI = "/ips/home/<USER>";
    const ACCTOKEN_URI   = "/ips/oauth/accessToken";
    const COOKIE_KEY     = "ZYBIPSCAS";
    const VERSION        = '2.0';
    const SDK            = 'odp';

    private static $conf = [
        'test' => [
            'host' => 'https://test431.suanshubang.com'
        ],
        'production' => [
            'host' => 'https://ips.zuoyebang.cc'
        ],
    ];

    private $_host;
    private $_appId;
    private $_appSecret;

    public function __construct($appId, $appSecret, $path = '/', $isRootDomain = false)
    {
        if (ral_get_idc() === 'test') {
            $conf = self::$conf['test'];
        } else {
            $conf = self::$conf['production'];
        }
        $this->_appId = $appId;
        $this->_appSecret = $appSecret;
        $this->_host = $conf['host'];

        if (false !== strpos($_SERVER['HTTP_HOST'], '.com')) {
            $this->_host = str_replace('.cc','.com', $this->_host);
        }
    }

    public function logout($url)
    {
        header("Location:" . $this->getLogoutUrl($url));
    }

    public function getLogoutUrl($url)
    {
        $url = !empty($url) ? $url : "";
        $logoutFeUrl = sprintf(self::LOGOUT_FE_URI, self::VERSION, self::SDK);
        $location = urlencode(sprintf("%s&sid=%s&service=%s", $this->_host . $logoutFeUrl, $this->_appId, urlencode($url)));
        return $this->_host . self::LOGOUT_URI . '?location=' . $location;
    }

    public function getLoginUrl($url)
    {
        $loginUrl = sprintf(self::LOGIN_URI, self::VERSION, self::SDK);
        return sprintf("%s&sid=%s&service=%s", $this->_host . $loginUrl, $this->_appId, urlencode($url));
    }

    public function login($url)
    {
        header("Location:" . $this->getLoginUrl($url));
    }

    public function validateAndSession($ticket = '')
    {
        return $this->getSession();
    }

    public function getSession()
    {
        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            Bd_Log::warning("ips getSession cookie empty");
            return false;
        }

        $accessToken = $this->getAccessToken();
        if (empty($accessToken)) {
            Bd_Log::addNotice('getIpsAccTokenErr', 1);
            return false;
        }

        $data = array(
            "appId" => $this->_appId,
            "sessionId" => $_COOKIE[self::COOKIE_KEY],
            'accessToken' => $accessToken
        );
        $sessionUrl = sprintf(self::CHECKLOGIN_URI, self::VERSION, self::SDK);
        $ret = $this->post($this->_host . $sessionUrl, $data, array(), 5);
        if (isset($ret['errNo']) && $ret["errNo"] === 0) {
            return $ret['data'];
        }

        // todo 接口返回 token 过期错误处理
        Bd_Log::warning("get session error");
        return false;
    }

    private function getAccessToken()
    {
        $data = [
            'appId' => $this->_appId,
            'appSecret' => $this->_appSecret,
        ];
        $ret = $this->post($this->_host . self::ACCTOKEN_URI, $data, array(), 5);
        if (isset($ret['errNo']) && $ret['errNo'] === 0 && !empty($ret['data']['accessToken'])) {
            return strval($ret['data']['accessToken']);
        }

        Bd_Log::warning("get accessToken error:". json_encode($ret));
        return false;
    }

    private function post($url, $data, $headers = array(), $timeout = 1, $mtimeout = 0)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        empty($headers) || curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        if ($mtimeout > 0) {
            curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT_MS, $mtimeout);
        } else if ($timeout > 0) {
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        }
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $result = curl_exec($ch);
        Bd_Log::addNotice("$url", $result); // 记录请求返回值
        if (empty($result)) {
            Bd_Log::warning("ips post failed " . curl_error($ch));
        }
        curl_close($ch);
        $result = json_decode($result, true);
        return $result;
    }
}
