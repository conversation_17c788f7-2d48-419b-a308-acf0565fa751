<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   List.php
 * <AUTHOR>
 * @date   2019/5/14 16:45
 * @brief
 **/
 

class Zb_Service_Listing_List {

    private static $service     = 'zbtrade';
    private static $serviceUri  = '/listing/api/list';


    /**
     * 主订单列表信息，以及其子订单、预付订单信息
     *
     * @param  int      $userId
     * @param  int      $displayStatus
     * @param  int      $orderChannel
     * @param  array    $businessTypes
     * @param  int      $sequential
     * @param  int      $startTime
     * @param  int      $endTime
     * @param  array    $tradeFields
     * @param  array    $subTradeFields
     * @param  array    $preTradeFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $options
     * @return array
     */
    public static function getList($userId, $displayStatus, $orderChannel, $businessTypes, $sequential, $startTime, $endTime,$tradeFields=array(), $subTradeFields=array(), $preTradeFields=array(), $page=0, $pageSize=20, $options=array() ) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri);
        $arrParams = array(
            'uid'            => $userId,
            'displayStatus'     => $displayStatus,
            'orderChannel'      => $orderChannel,
            'businessTypes'     => $businessTypes,
            'sequential'        => $sequential,
            'startTime'         => $startTime,
            'endTime'           => $endTime,
            'tradeFields'       => $tradeFields,
            'subTradeFields'    => $subTradeFields,
            'preTradeFields'    => $preTradeFields,
            'page'              => $page,
            'pageSize'          => $pageSize,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


}
