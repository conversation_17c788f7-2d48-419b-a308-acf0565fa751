<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   RecCourse.php
 * <AUTHOR>
 * @date   2018-08-04
 * @brief  服务化 - 推课
 *
 **/
class Oplib_Service_RecCourse
{
    public function __construct() {
    }

    /** 获取指定年级下可售卖的3元专题课
     * @param        $uid
     * @param        $grade
     * @param string $service
     * @return array {'subjectList':[],'courseList':[]}
     */
    public static function getL3CourseList($uid, $grade, $service = 'zyb-http')
    {
        $searchConds = [
            'gradeIds'   => [$grade], //年级
            'courseType' => [Hkzb_Ds_Fudao_Course::TYPE_PRIVATE], // 专题课
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            'skuType'    => [1, 2], // 1:课程 2:课程+教材
            'price'      => [300],  // 价格为3元条件
            'startTime'  => time(), //  第一节课开始时间大于当前时间
        ];

        $objSku = new Zb_Advanced_Interface();
        $skuRet = $objSku->searchSKU(
            $searchConds,
            0,   // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0,   // 分页，偏移量，默认0
            200, // 分页，每次获取记录总数
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
        );
        if (!$skuRet) {
            return false;
        }

        $skuIdList = Oplib_Util_Helper::array_column($skuRet, 'skuId');
        $skuIdList = array_unique($skuIdList);
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp       = time();
        $skuIdsSignalList   = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp, '', '', '', $service);
        $arrCanBuySkuIds    = [];
        $arrCanNotBuySkuIds = [];
        foreach ($skuIdList as $idx => $skuId) {
            if (isset($skuIdsSignalList['skuInfoList'][$skuId]['canBuy']) && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy']) {
                unset($skuIdList[$idx]);
                $arrCanNotBuySkuIds[] = $skuId;
                continue;
            }
            $arrCanBuySkuIds[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySkuIds', json_encode($arrCanBuySkuIds));
        Bd_Log::addNotice('arrCanNotBuySkuIds', json_encode($arrCanNotBuySkuIds));
        if (!$arrCanBuySkuIds) {
            return false;
        }
        // 获取课程详细信息
        $skuInfoList = $objSku->getAdapterSkuInfoBatch($arrCanBuySkuIds, true);

        $arrOutput   = [
            'subjectList' => [],
            'courseList'  => [],
        ];
        $subjectList  = [];
        $courseList   = [];
        $arrCanUseSkuIds = [];
        foreach ($skuInfoList as $skuInfo) {
            if (!$skuInfo['courseInfoList']) {
                continue;
            }
            // 获取courseId以及开课日期
            $courseId   = 0;
            $lessonInfo = null;
            foreach ($skuInfo['courseInfoList'] as $courseInfo) {
                $lessonInfoList = Oplib_Util_Helper::array_sort_column($courseInfo['lessonInfoList'], 'startTime', SORT_DESC);
                $tempLessonInfo = array_pop($lessonInfoList);
                if ($tempLessonInfo) {
                    if (is_null($lessonInfo)) {
                        $lessonInfo = $tempLessonInfo;
                        $courseId   = $courseInfo['courseId'];
                    } elseif (intval($lessonInfo['startTime']) > intval($tempLessonInfo['startTime'])) {
                        $lessonInfo = $tempLessonInfo;
                        $courseId   = $courseInfo['courseId'];
                    }
                }
            }
            // 课程id和章节信息不合法过滤掉
            if (0 == $courseId || is_null($lessonInfo)) {
                continue;
            }

            $subjectId      = intval($skuInfo['subject'][0]);
            $subjectName    = '';
            $subjectOneName = '';
            if (isset(Zb_Const_GradeSubject::$SUBJECT[$subjectId])) {
                $subjectName = strval(Zb_Const_GradeSubject::$SUBJECT[$subjectId]);
            }
            if (isset(Hkzb_Util_FuDao::$COURSE_ONE[$subjectId])) {
                $subjectOneName = strval(Hkzb_Util_FuDao::$COURSE_ONE[$subjectId]);
            }
            // 获取课程简介，课程内容
            $courseAbstract   = '';
            $arrCourseContent = [];
            $arrContent       = (array)json_decode($skuInfo['content'], true);
            if (isset($arrContent['courseAbstract']) && $arrContent['courseAbstract']) {
                $courseAbstract = strval($arrContent['courseAbstract']);
            }
            if (isset($arrContent['courseContent']) && $arrContent['courseContent']) {
                $arrCourseContent = preg_split('/\n+/', trim($arrContent['courseContent']));
            }
            // 课程简介，课程内容有一个为空过滤掉
            if (empty($courseAbstract) || empty($arrContent)) {
                continue;
            }

            $teacherInfo = $lessonInfo['teacherInfo'];
            if (isset($teacherInfo['teacherAvatar']) && !empty($teacherInfo['teacherAvatar'])) {
                $teacherInfo['teacherAvatar'] = Hk_Util_Image::getImgUrlBySrc($teacherInfo['teacherAvatar']);
            } else {
                $teacherInfo['teacherAvatar'] = '';
            }

            // 课程信息
            $courseList[$subjectId][] = [
                'skuId'          => $skuInfo['productId'],
                'courseId'       => $courseId,
                'subjectId'      => $subjectId,
                'courseName'     => strval($skuInfo['productName']),
                'subjectOne'     => $subjectOneName,
                'courseContent'  => $arrCourseContent,
                'courseAbstract' => $courseAbstract,
                'startTime'      => intval($lessonInfo['startTime']),
                'onlineStart'    => date('n月j日|上课时间: H:i-', intval($lessonInfo['startTime'])) . date('H:i', intval($lessonInfo['stopTime'])),
                'teacherInfo'    => $teacherInfo,
            ];
            // 学科信息
            $subjectList[$subjectId] = [
                'subjectId'   => $subjectId,
                'subjectName' => $subjectName,
            ];
            $arrCanUseSkuIds[] = $skuInfo['productId'];
        }
        Bd_Log::addNotice('arrCanUseSkuIds', json_encode($arrCanUseSkuIds));

        // 排序
        $subjectList = Oplib_Util_Helper::array_sort_column($subjectList, 'subjectId', SORT_ASC);
        ksort($courseList);
        foreach ($courseList as $key => $values) {
            $courseList[$key] = Oplib_Util_Helper::array_sort_column($values, 'startTime', SORT_ASC);
            // 最多保留三门课程
            if (count($courseList[$key]) > 3) {
                $courseList[$key] = array_splice($courseList[$key], 0, 3);
            }
        }

        // 设置数据
        $arrOutput['subjectList'] = $subjectList;
        $arrOutput['courseList']  = $courseList;

        return $arrOutput;
    }
    /**
     * @重复播放视频、重复领课开始时间判断
     */
    public static function getRepeateFromTime() {
        $fromTime     = strtotime(date('Ymd')) - 86400 * 6;
        Bd_Log::addNotice('repeatFromTime', $fromTime);
        return $fromTime;
    }
    
    /**
     * @二次领课 新推课方式 
     *  2.1 按照 上次领课学科、数学、英语、语文、物理、化学、历史、地理、政治、生物的顺序，取前4个7天内有5元以下课程的学科
        2.2 显示每一个学科内时间最近一门5元以下课程，优先显示专题课（两门专题课时间相同rand即可）
        2.3 当没有4个学科满足要求时，按照学科顺序进行第二轮递补，一门学科下的两门课程不允许是同一天的。
        2.4 当完全不满足4门课程时，允许展示课程数少于4门，但需要发布报警邮件
     * @param int $uid
     * @param int $grade
     * @param int $subject
     * @param int $arrTeaUid
     * @param string $service
     */
    public function scondaryCourseRec($uid = 0, $grade, $subject, $arrTeaUid, $service='zyb-http') {
        // 学科优先级
        Hk_Util_Log::start('scondaryCourseRec');
        $arrSubject = [
            2,
            3,
            1,
            4,
            5,
            8,
            9,
            7,
            6,
            10,
        ];
        if ($subject > 0) {
            $subjectKey = array_search($subject, $arrSubject);
            if (false !== $subjectKey) {
                unset($arrSubject[$subjectKey]);
            }
            // 指定学科放在开头 最高优先级
            array_unshift($arrSubject, $subject);
        }
        // 去重检查 以学科+开课day为key
        $arrRepeatKey = [];
        // 取200个线上可售卖课程
        $stopTime = strtotime(date('Ymd')) + 86400 * 7;
        $searchConds = [
            'gradeIds'   => [$grade],
            //'subjectIds' => [$subject],
            'courseType' => [Zb_Const_Course::TYPE_PRIVATE, Zb_Const_Course::TYPE_PRE_LONG],
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            'price'      => [0,500],
            'skuType'    => [1,2], // 1:课程 2:课程+教材
            'startTime'  => time(),
            'stopTime'   => $stopTime, // 7天内的课程
        ];
        if ($arrTeaUid) {
            // 指定老师的uid
            $searchConds['teacherUids'] = $arrTeaUid;
        }
        $objDsInterface = new Zb_Advanced_Interface();
        $skuRet = $objDsInterface->searchSKU(
            $searchConds,
            0, // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0, // 分页，偏移量，默认0
            200, // 分页，每次获取记录总数，默认10
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
        );
        if (!$skuRet) {
            return false;
        }
        $skuIdList = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuIdList[] = $oneSku['skuId'];
        }
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp     = time();
        $skuIdsSignalList = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp, '', '', '', $service);
        $arrCanBuySku    = [];
        $arrCanNotBuySku = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuId = $oneSku['skuId'];
            if ( $skuIdsSignalList['skuInfoList'][$skuId] && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy'] ) {
                unset($skuRet[$idx]);
                $arrCanNotBuySku[] = $skuId;
                continue;
            }
            $arrCanBuySku[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySku', json_encode($arrCanBuySku));
        Bd_Log::addNotice('arrCanNotBuySku', json_encode($arrCanNotBuySku));
        if (!$skuRet) {
            return false;
        }
        
        // sku按照学科聚合
        $arrSubjectSku = [];
        $arrRank = []; // 重新排序依次  按照开始时间升序 专题课>体验课
        $arrSortSkuRet = [];
        foreach ($skuRet as $oneSku) {
            $arrSortSkuRet[$oneSku['skuId']] = $oneSku;
            if (Zb_Const_Course::TYPE_PRIVATE == $oneSku['courseType']) {
                // 专题课 给一个较高的权重
                $arrRank[$oneSku['skuId']] = [$oneSku['firstLessonTime'], 100];
                continue;
            }
            if (Zb_Const_Course::TYPE_PRIVATE == $oneSku['courseType']) {
                // 体验课给一个较低的权重
                $arrRank[$oneSku['skuId']] = [$oneSku['firstLessonTime'], 10];
                continue;
            }
            // 做一个容错吧
            $arrRank[$oneSku['skuId']] = [$oneSku['firstLessonTime'], 0];
        }
        uasort($arrRank, 'self::sortByField1A1');
        foreach ($arrRank as $skuId => $arrSort) {
            $arrRank[$skuId] = $arrSortSkuRet[$skuId];
        }
        foreach ($arrRank as $oneSku) {
            $oneSkuSubject = explode(',', trim($oneSku['subject'], ','));
            $arrSubjectSku[$oneSkuSubject[0]][] = $oneSku;
        }
        
        // 返回的推荐列表
        $retSkuList = [];
        $loopCnt    = 4;
        do {
            foreach ($arrSubject as $sjKey => $subject) {
                // 没有指定学科的课程
                if (!$arrSubjectSku[$subject]) {
                    unset($arrSubjectSku[$subject]);
                    unset($arrSubject[$sjKey]);
                    continue;
                }
                foreach ($arrSubjectSku[$subject] as $idx => $oneSku) {
                    // 按照学科和日期去重
                    $repeatKey = $subject . date('Ymd', $oneSku['firstLessonTime']);
                    if ($arrRepeatKey[$repeatKey]) {
                        unset($arrSubjectSku[$subject][$idx]);
                        continue;
                    }
                    // 加入到推荐列表
                    $retSkuList[] = $oneSku['skuId'];
                    $arrRepeatKey[$repeatKey] = 1;
                    unset($arrSubjectSku[$subject][$idx]);
                    $loopCnt--;
                    break;
                }
                
            }
        }while($loopCnt>0 && $arrSubjectSku && $arrSubject);
        
        Hk_Util_Log::stop('scondaryCourseRec');
        Bd_Log::addNotice('scondaryCourseRecSkuId', $retSkuList);
        return $retSkuList;
    }
    
    /**
     * 按开课时间正序，专题课>体验课排序
     * @param $a
     * @param $b
     * @return int
     */
    private static function sortByField1A1($a, $b) {
        if ($a[0] == $b[0]) {
            if ($a[1] == $b[1]) {
                return 0;
            }
            return $a[1] < $b[1] ? -1 : 1;
        }
        return $a[0] < $b[0] ? -1 : 1;
    }
    
    
    
    /**
     * @新推课方式 最多推3门课
     *  必须要有年级学科 最好有uid 有些课程uid级别不可售卖
     *  3元以下课程
     *  根据开课时间，优先最近开课
     *  优先体验课
     *  时间或者课程名称去重
     * @param int $uid
     * @param int $grade
     * @param int $subject
     * @param int $arrTeaUid
     * @param string $service
     */
    public function receiveCourseList ($uid = 0, $grade, $subject, $arrTeaUid = [], $service='zyb-http') {
        $grade   = intval($grade);
        $subject = intval($subject);
        if (0 >= $grade || 0 >= $subject) {
            return false;
        }
        Hk_Util_Log::start('rcvCourseList');
        // 取50个线上可售卖课程
        $searchConds = [
            'gradeIds'   => [$grade],
            'subjectIds' => [$subject],
            'courseType' => [Zb_Const_Course::TYPE_PRIVATE, Zb_Const_Course::TYPE_PRE_LONG],
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            'price'      => [0,500],
            'skuType'    => [1,2], // 1:课程 2:课程+教材
            'startTime'  => time(),
        ];
        if ($arrTeaUid) {
            // 指定老师的uid
            $searchConds['teacherUids'] = $arrTeaUid;
        }
        $objDsInterface = new Zb_Advanced_Interface();
        $skuRet = $objDsInterface->searchSKU(
            $searchConds,
            1, // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0, // 分页，偏移量，默认0
            50, // 分页，每次获取记录总数，默认10
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
        );
        if (!$skuRet) {
            return false;
        }
        $skuIdList = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuIdList[] = $oneSku['skuId'];
        }
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp     = time();
        $skuIdsSignalList = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp, '', '', '', $service);
        $arrCanBuySku    = [];
        $arrCanNotBuySku = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuId = $oneSku['skuId'];
            if ( $skuIdsSignalList['skuInfoList'][$skuId] && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy'] ) {
                unset($skuRet[$idx]);
                $arrCanNotBuySku[] = $skuId;
                continue;
            }
            $arrCanBuySku[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySku', json_encode($arrCanBuySku));
        Bd_Log::addNotice('arrCanNotBuySku', json_encode($arrCanNotBuySku));
        
        // 按照开课时间聚合
        $retSkuList    = [];
        $formatSkuList = [];
        foreach ($skuRet as $idx => $oneSku) {
            // 按照开课时间+课程类型排序一下
            if (Zb_Const_Course::TYPE_PRIVATE == $oneSku['courseType']) {
                $formatSkuList[$oneSku['firstLessonTime']]['zhuan'][] = $oneSku;
            }
            if (Zb_Const_Course::TYPE_PRE_LONG == $oneSku['courseType'])
            $formatSkuList[$oneSku['firstLessonTime']]['tiyan'][] = $oneSku;
        }
        $repeatTime = [];
        $repeatName = [];
        foreach ($formatSkuList as $ftime => $arrCourse) {
            // 最多3门课
            if (count($retSkuList) >= 3) {
                break;
            }
            $chooseSkuId = 0;
            // 先处理体验课
            if ($arrCourse['tiyan']) {
                foreach ($arrCourse['tiyan'] as $chooseRow) {
                    if ($repeatTime[$chooseRow['firstLessonTime']] || $repeatName[$chooseRow['detail']['skuName']]) {
                        // 重复
                        continue;
                    }
                    // 选中
                    $chooseSkuId  = $chooseRow['skuId'];
                    $repeatTime[$chooseRow['firstLessonTime']]   = 1;
                    $repeatName[$chooseRow['detail']['skuName']] = 1;
                    break;
                }
            }
            // 处理专题课
            if ($arrCourse['zhuan'] && 0 >= $chooseSkuId) {
                foreach ($arrCourse['zhuan'] as $chooseRow) {
                    if ($repeatTime[$chooseRow['firstLessonTime']] || $repeatName[$chooseRow['detail']['skuName']]) {
                        // 重复
                        continue;
                    }
                    // 选中
                    $chooseSkuId  = $chooseRow['skuId'];
                    $repeatTime[$chooseRow['firstLessonTime']]   = 1;
                    $repeatName[$chooseRow['detail']['skuName']] = 1;
                    break;
                }
            }
            if ($chooseSkuId > 0) {
                $retSkuList[] = $chooseSkuId;
            }
        }
        
        
        Hk_Util_Log::stop('rcvCourseList');
        Bd_Log::addNotice('rcvCourseListSkuId', $retSkuList);
        return $retSkuList;
    }
    
    /**
     * @领取一门课，针对单视频录播课
     *  必须要有年级学科 最好有uid 有些课程uid级别不可售卖
     *  3元以下课程
     *  根据开课时间，优先最近开课
     *  开课时间相同情况下，体验课优先
     *  相同类型课程有多门，按照售卖量大的推荐
     * @返回1个skuId
     */
    public function receiveOneCourse($uid = 0, $grade, $subject, $arrTeaUid = [], $service='zyb-http') {
        $grade   = intval($grade);
        $subject = intval($subject);
        if (0 >= $grade || 0 >= $subject) {
            return false;
        }
        Hk_Util_Log::start('rcvOneCourse');
        // 缓存 mc
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $md5Params    = md5($uid . $grade . $subject . json_encode($arrTeaUid));
        $cacheKey     = Oplib_Const_Cache::UCEP_REC_COURSE_PRE . $md5Params;
        $skuId        = $objMemcached->get($cacheKey);
        if ($cacheKey && 0 < $skuId) {
            Hk_Util_Log::stop('rcvOneCourse');
            Bd_Log::addNotice('recCourseSkuId', $skuId);
            Bd_Log::addNotice('recCourseHitMc', 1);
            return $skuId;
        }
        Bd_Log::addNotice('recCourseSkuIdHitMc', 0);
        // 取100个线上可售卖课程
        $searchConds = [
            'gradeIds'   => [$grade],
            'subjectIds' => [$subject],
            'courseType' => [Zb_Const_Course::TYPE_PRIVATE, Zb_Const_Course::TYPE_PRE_LONG],
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            'price'      => [0,500],
            'skuType'    => [1,2], // 1:课程 2:课程+教材
            'startTime'  => time(),
        ];
        if ($arrTeaUid) {
            // 指定老师的uid
            $searchConds['teacherUids'] = $arrTeaUid;
        }
        $objDsInterface = new Zb_Advanced_Interface();
        $skuRet = $objDsInterface->searchSKU(
            $searchConds,
            0, // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0, // 分页，偏移量，默认0
            50, // 分页，每次获取记录总数，默认10
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
        );
        if (!$skuRet) {
            return false;
        }
        $skuIdList = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuIdList[] = $oneSku['skuId'];
        }
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp     = time();
        $skuIdsSignalList = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp, '', '', '', $service);
        $arrCanBuySku    = [];
        $arrCanNotBuySku = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuId = $oneSku['skuId'];
            if ( $skuIdsSignalList['skuInfoList'][$skuId] && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy'] ) {
                unset($skuRet[$idx]);
                $arrCanNotBuySku[] = $skuId;
                continue;
            }
            $arrCanBuySku[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySku', json_encode($arrCanBuySku));
        Bd_Log::addNotice('arrCanNotBuySku', json_encode($arrCanNotBuySku));
        
        // 排序 按照开课时间和售卖情况
        $lastTime  = 0;
        $arrTiyan  = [];
        $arrZhuan  = [];
        foreach ($skuRet as $idx => $oneSku) {
            if (0 == $lastTime) {
                // 第一个元素的开始时间
                $lastTime = $oneSku['firstLessonTime'];
            }
            if ($lastTime < $oneSku['firstLessonTime']) {
                break;
            }
            $courseType = $oneSku['courseType'];
            // 专题课
            if ( Zb_Const_Course::TYPE_PRIVATE == $courseType ) {
                $arrZhuan[] = $oneSku;
            }
            // 体验课
            if ( Zb_Const_Course::TYPE_PRE_LONG == $courseType ) {
                $arrTiyan[] = $oneSku;
            }
            
        }
        // 选择课程
        $skuId = 0;
        do {
            if ( empty($arrTiyan) && empty($arrZhuan) ) {
                $skuId = 0;
                break;
            }
            // 优先体验课
            if ($arrTiyan) {
                $skuId = $this->_getMaxSaleSku($arrTiyan);
                break;
            }
            // 专题课
            if ($arrZhuan) {
                $skuId = $this->_getMaxSaleSku($arrZhuan);
                break;
            }
            break;
        }while(0);
        // 写缓存
        if (0 < $skuId) {
            $objMemcached->set($cacheKey, $skuId, Oplib_Const_Cache::UCEP_REC_COURSE_TIME);
        }
        
        
        Hk_Util_Log::stop('rcvOneCourse');
        Bd_Log::addNotice('recCourseSkuId', $skuId);
        return $skuId;
    }
    
    /**
     * @冒泡销量最高的一个出来
     * @param array $arrSku
     * @return skuId
     */
    private function _getMaxSaleSku($arrSku) {
        $maxSaleSku = 0;
        $saleCnt    = 0;
        foreach ($arrSku as $idx => $oneSku) {
            if (0 == $idx || $saleCnt < $oneSku['saleCnt']) {
                $maxSaleSku = $oneSku['skuId'];
                $saleCnt    = $oneSku['saleCnt'];
            }
        }
        return $maxSaleSku;
    }
    /**
     * 领课用户课程推荐, 价格≤5元的、高二年级、专题课，按学科分类 (数学，英语，语文，物理，化学，生物，地理，政治，历史)
     * @param uid
     * @param grade 年级编号 默认 6-高二年级
     * @return boolean|array 
     * array(array("subjectId" => 1, "skuIds" => array("1001","1002","1003")),)
     */
    public function receiveCourseListL1($uid, $grade = 6) {
        $searchConds = [
            'gradeIds'   => [$grade],// 6-高二年级
            'courseType' => [Zb_Const_Course::TYPE_PRIVATE],
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            'price'      => [0,500],
            'skuType'    => [1,2], // 1:课程 2:课程+教材
            'startTime'  => time(),
        ];
        
        $objDsInterface = new Zb_Advanced_Interface();
        $skuRet = $objDsInterface->searchSKU(
            $searchConds,
            1, // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0, // 分页，偏移量，默认0
            200, // 分页，每次获取记录总数，默认10
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
            );
        if (!$skuRet) {
            return false;
        }
        
        $skuIdList = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuIdList[] = $oneSku['skuId'];
        }
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp     = time();
        $skuIdsSignalList = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp);
        $arrCanBuySku    = [];
        $arrCanNotBuySku = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuId = $oneSku['skuId'];
            if ( $skuIdsSignalList['skuInfoList'][$skuId] && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy'] ) {
                unset($skuRet[$idx]);
                $arrCanNotBuySku[] = $skuId;
                continue;
            }
            $arrCanBuySku[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySku', json_encode($arrCanBuySku));
        Bd_Log::addNotice('arrCanNotBuySku', json_encode($arrCanNotBuySku));
        if (!$skuRet) {
            return false;
        }
        
        $skuIdListTemp = [];
        $skuIdList = [];
        foreach ($skuRet as $oneSku) {
            $subject = trim($oneSku['subject'], ',');
            $arrOneSubject = explode(",", $subject);
            foreach($arrOneSubject as $oneSubject){
                $skuIdListTemp[$oneSubject][] = $oneSku['skuId'];
            }
             //array("1" =>array('',''),"2" =>array('',''))
        }
        $arrSubject = [2,3,1,4,5,6,9,7,8]; //数学，英语，语文，物理，化学，生物，地理，政治，历史
        foreach ($arrSubject as $subject){
            if(isset($skuIdListTemp[$subject])){
                $skuIdList[] = array("subjectId" => $subject, "skuIds" => $skuIdListTemp[$subject]);
            }
        }
        return $skuIdList;
    }
    
    /** 指定老师的长班课
     * @param        $uid
     * @param        $grade
     * @param string $service
     * @return array {'subjectList':[],'courseList':[]}
     */
    public static function teaLongCourse($uid, $arrTeaUid = [])
    {
        if ( empty($arrTeaUid) ) {
            return false;
        }
        Hk_Util_Log::start('teaLongCourse');
        $searchConds = [
            'courseType' => [Zb_Const_Course::TYPE_PRIVATE_LONG], // 班课
            'status'     => 1, // 售卖状态，0 下线，1 在线。默认1
            //'hasRemain'  => 1, // 是否有库存，0无， 1有。默认1
            //'skuType'    => [1, 2], // 1:课程 2:课程+教材
            'teacherUids' => $arrTeaUid,
        ];
    
        $objSku = new Zb_Advanced_Interface();
        $skuRet = $objSku->searchSKU(
            $searchConds,
            0,   // 是否显示单品详细数据；1 是 即tblSKU表中数据，0 否 只显示tblSKUList中数据
            0,   // 分页，偏移量，默认0
            100, // 分页，每次获取记录总数
            'first_lesson_time', // 列表排序的列，默认是first_lesson_time
            1 // 1 顺序，2逆序
        );
        Hk_Util_Log::stop('teaLongCourse');
        if (!$skuRet) {
            return false;
        }

        $skuIdList = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuIdList[] = $oneSku['skuId'];
        }
        // 走策略判断下课程是否可以售卖
        $nowTimeStamp     = time();
        $skuIdsSignalList = Zb_Service_Zbbiz::getSignalHitBiz($skuIdList, $uid, $nowTimeStamp);
        $arrCanBuySku    = [];
        $arrCanNotBuySku = [];
        foreach ($skuRet as $idx => $oneSku) {
            $skuId = $oneSku['skuId'];
            if ( $skuIdsSignalList['skuInfoList'][$skuId] && 1 != $skuIdsSignalList['skuInfoList'][$skuId]['canBuy'] ) {
                unset($skuRet[$idx]);
                $arrCanNotBuySku[] = $skuId;
                continue;
            }
            $arrCanBuySku[] = $skuId;
        }
        Bd_Log::addNotice('arrCanBuySku', json_encode($arrCanBuySku));
        Bd_Log::addNotice('arrCanNotBuySku', json_encode($arrCanNotBuySku));
        if (!$skuRet) {
            return false;
        }
    
        return $arrCanBuySku;
    }
}
