<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsSku.php
 * <AUTHOR>
 * @date   2019/7/27 上午10:45
 * @brief
 **/


class
Zb_Service_Goodsplatform_GoodsSku
{

	private static $service     = 'newgoodsplatform';


	const TCC_STOCK_TYPE_SALE         = 0; //正常购买
	const TCC_STOCK_TYPE_PRE_SALE     = 1; //预售
	const TCC_STOCK_TYPE_PRE_SALE_END = 2; //结尾款

    public static function getArchSwitch(){
        $switch = Zb_Service_NCM::get(1, 'dak', 'public', 'archSwitch');
        if(false === $switch) {
            $switch = 0;
        }
        return intval($switch);
    }
    public static function getArchWhitelist(){
        $whitelist = Zb_Service_NCM::get(1,'dak', 'public', 'archWhitelists');
        if(false === $whitelist) {
            $whitelist = [];
        }
        return json_decode($whitelist,true);
    }

    public static function getArchDegradedState() {
        $state = Zb_Service_NCM::get(1, 'newgoodsplatform', 'public', 'TccDegradedRunning');
        if (false == $state) {
            $state = 0;
            return $state;
        }
        return intval($state);
    }

    public static function getArchDegradedExcludeList() {
        $excludeList = Zb_Service_NCM::get(1, 'newgoodsplatform', 'public', 'TccDegradedExcludeList');
        if (false == $excludeList) {
            $excludeList = [];
            return $excludeList;
        }
        return json_decode($excludeList, true);
    }

    /**
     * tcc 是否降级
     * @return bool
     */
    public static function isTccDegradedRun() {
        $degradedState = Zb_Service_Goodsplatform_GoodsSku::getArchDegradedState();
        //如果没有开启降级
        if ($degradedState == 0) {
            return false;
        }
        return true;
    }

    /**
     * 是否命中 降级的排除列表
     * @param $skuIds
     * @return bool
     */
    public static function hitTccDegradeExcludeList($skuIds) {
        $excludeList = Zb_Service_Goodsplatform_GoodsSku::getArchDegradedExcludeList();
        if (empty($skuIds)) {
            return false;
        }
        $hitList = array_intersect($excludeList, $skuIds);
        if (empty($hitList)) {
            return false;
        }
        return true;
    }

    public static function shouldSwitchArchimedes($skuIds)
    {
        $switchStatus = Zb_Service_Goodsplatform_GoodsSku::getArchSwitch();
        if($switchStatus == 2){
            return true;
        }
        if ($switchStatus == 1) {
            $useArchSkuList = Zb_Service_Goodsplatform_GoodsSku::getArchWhitelist();
            $hit = !array_diff($skuIds, $useArchSkuList);
            return $hit;
        }
        return false;
    }

	/**
	 * ES 多条件检索 sku
	 * @param $arrInput => [
	 *              "queryFields"   => [], //固定字段且关系检索
	 *              "itemFields"    => [], //固定字段或关系检索
	 *              "channelType"   => [], //tag渠道信息
	 *              "tags"          => [], //tag检索
	 *              "isOnSaleTime"  => 1,  //默认0 所有商品；1在售商品
	 *              "pn"            => 0,  //分页页码：默认0 开始
	 *              "rn"            => 10, //偏移量：默认10
	 *        ]
	 * @return array
	 */
	public static function getSkuIdListByCondtion($arrInput,$options=array())
	{
		$serviceUri = '/newgoodsplatform/goodsskuapi/getskuidlistbycondtion';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
		$arrParams = array(
			'queryFields'  => !isset($arrInput['queryFields']) || empty($arrInput['queryFields']) ? [] : $arrInput['queryFields'],
			'itemFields'   => !isset($arrInput['itemFields']) || empty($arrInput['itemFields']) ? [] : $arrInput['itemFields'],
			'tags'         => !isset($arrInput['tags']) || empty($arrInput['tags']) ? [] : $arrInput['tags'],
			'channelType'  => !isset($arrInput['channelType']) || empty($arrInput['channelType']) ? [] : $arrInput['channelType'],
			'isOnSaleTime' => !isset($arrInput['isOnSaleTime']) ? 0 : intval($arrInput['isOnSaleTime']),
			'pn'    => !isset($arrInput['pn']) ? 0 : intval($arrInput['pn']),
			'rn'    => !isset($arrInput['rn']) ? 10 : intval($arrInput['rn']),
		);

		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);

	}

	/**
	 * 通过spuId获取对应的skuIdList
	 * @param $spuIds
	 * @param bool $isCache
	 * @param array $options
	 * @return array
	 *          [
	 *              spuId1 => [skuId1,skuId2],
	 *              spuId2 => [skuId3,skuId4],
	 *          ]
	 */
	public static function getSkuIdListBySpuIds($spuIds,$isCache = true,$options=array())
	{
        $serviceUri = '/newgoodsplatform/goodsskuapi/getskuidlistbyspuids';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);

		$arrParams = [
			"spuIds"    => $spuIds,
			"isCache"  => $isCache
		];
		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
	}

	/**
	 * 平台sku KV接口
	 * @param array $arrInput  入参：
	 *                          skuIds   形如：[1,2,3]
	 *                          isCache  缓存开关：true 开启；false 关闭
     *                          saleChannelId 销售渠道参数 默认为普通渠道
	 *                          interfaceId  接口标识  对齐 array_keys(Zb_Const_GoodsPlatform::$interfaceListMap) 注：默认 0 返回所有
	 * @param array $options
	 * @return array
	 */
	public static function getSkuInfoBySkuId($arrInput = [],$options=array())
	{
        $inParams = [
            "skuIds" => $arrInput['skuIds'],
            "isCache" => isset($arrInput['isCache']) ? boolval($arrInput['isCache']) : true,
            "interfaceId" => isset($arrInput['interfaceId']) ? intval($arrInput['interfaceId']) : 0,
            "saleChannelId" => isset($arrInput['saleChannelId']) ? intval($arrInput['saleChannelId']) : 0,
        ];
        $serviceUri = '/newgoodsplatform/goodsskuapi/getgoodsskukvbyskuid';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);

		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $inParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $inParams, $arrHeader);
	}

    /**
     * 获取总库存 + 原始kv信息
     * @param $skuIds
     * @return array
     */
	public static function getSkuInfoList($skuIds) {
	    $inParams = [
	        'skuIds' => json_encode($skuIds),
        ];
        $serviceUri = '/newgoodsplatform/goodsskuapi/getskuinfolist';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        return Zb_Util_ZbServiceTools::post(self::$service, $inParams, $arrHeader);
    }
	/**
	 *  预锁库存操作  Tcc - try
	 * @param array $entityList 待操作库存的实物列表
	 * [
	 *      [
	 *           skuId => 1,
	 *           cnt   => 1,
	 *      ],
	 *      [
	 *           skuId => 2,
	 *           cnt   => 1,
	 *      ],
	 * ]
	 * @param string $lockKey 幂等 加锁key (tccId)
	 */
	public static function  tryLockSaleCnt($skuList = [] , $lockKey = '', $type = self::TCC_STOCK_TYPE_SALE)
	{
        $serviceUri = '/newgoodsplatform/goodsskuapi/trylocksalecnt';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
		$arrParams = array(
			'entityList' => $skuList,
			'lockKey' => $lockKey,
			'type'    => $type,
		);

		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
	}

	/**
	 * 提交库存 TCC-confirm
	 * @param string $lockKey  tccId
	 * @return array
	 */
	public static function confirmSaleCnt($lockKey = '')
	{
        $serviceUri = '/newgoodsplatform/goodsskuapi/confirmsalecnt';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
		$arrParams = array(
			'lockKey' => $lockKey,
		);

		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
	}

	/**
	 * Tcc - cancel 取消已锁定的库存
	 * @param string $lockKey tccId
	 * @return array
	 */
	public static function cancelLockSaleCnt($lockKey = '')
	{
        $serviceUri = '/newgoodsplatform/goodsskuapi/cancellocksalecnt';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
		$arrParams = array(
			'lockKey' => $lockKey,
		);

		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
	}

	/**
	 * 业务方SKU同步 商品服务SKU
	 * @param $arrInput array
	 *  skuName:
		skuType:
		appId:
		categoryId:
		stockCnt:
		itemId:1
		skuContent:[['productId'=>1,'productName'=>'提升课','price'=>30,'costPrice'=>30,'type'=>1]]
		canChange:1
		canRefund:1
		specTags:[grade=>[2,3],subject=>[4,5],skuId=>"20190101",pintuan=>"拼团商品"]
		labelTags:[grade=>[2,3],subject=>[4,5],skuId=>"20190101",pintuan=>"拼团商品"]
		isInner:0
		price:1
		skuOriginPrice:1
		startTime:11111
		stopTime:22222
		onlineStrategyId:1

	 * @param $options
	 * @return array
	 */
	public static function syncGoodsSku($arrInput, $options=array()) {
        $serviceUri = '/newgoodsplatform/goodssku/syncgoodssku';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
		if (!empty($options['isRalMulti'])){
			return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrInput, $arrHeader);
		}

		return Zb_Util_ZbServiceTools::post(self::$service, $arrInput, $arrHeader);
	}

    public static function getSkuIdListByProductId($arrInput,$options=array()){
        $serviceUri = '/newgoodsplatform/goodssku/getskuidlistbyproductid';
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'productId'  => !isset($arrInput['productId']) || empty($arrInput['productId']) ? [] : $arrInput['productId'],
            'productType'   => !isset($arrInput['productType']) || empty($arrInput['productType']) ? [] : $arrInput['productType'],
            'pn'    => !isset($arrInput['pn']) ? 0 : intval($arrInput['pn']),
            'rn'    => !isset($arrInput['rn']) ? 200 : intval($arrInput['rn']),
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);

    }
}