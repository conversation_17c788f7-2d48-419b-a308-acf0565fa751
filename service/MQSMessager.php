<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: MQSMessager.php
 * @author: huanghe <<EMAIL>>
 * @date: 2017/8/1 上午11:51
 * @brief: MQS消息统一接口
 */
class Hkzb_Service_MQSMessager
{
    const CMD_SEND_MSG = 12; //命令号：发送消息
    const CMD_FORWARD_MSG = 13; //命令号：转发消息
    const CMD_DELETE_MSG = 15; //命令号：删除消息
    const CMD_GET_MSG = 14; //命令号：读取消息

    const MSG_CLEAN_TYPE_STABLE = 1; //消息清理等级：未读不可清理
    const MSG_CLEAN_TYPE_VOLATILE = 2; //消息清理等级：未读系统可清理

    const MSG_DELIVERY_TYPE_ALL = 0; //消息投递类型--全部投递
    const MSG_DELIVERY_TYPE_ONLINE = 1; //消息投递类型--只投递在线

    const MSG_DATA_EXPIRE = 604800; //消息生存时常(单位：秒)

    const MQS_PRODUCT_IM = 'IM';
    const MQS_PRODUCT_FUDAO = 'FUDAO';
    const MQS_PRODUCT_DAYI = 'DAYI';
    const MQS_PRODUCT_DEFAULT = 'DEFAULT';


    /**
     * 消息id
     * @var integer
     */
    private $msgId;

    /**
     * 消息内容
     * @var array
     */
    private $msgData;

    /**
     * 消息有效时间
     * @var integer
     */
    private $msgExpireTime;

    /**
     * 消息清理等级
     * @var integer
     */
    private $msgCleanType;

    /**
     * 消息投递类型
     * @var integer
     */
    private $msgDeliveryType;

    /**
     * 待转发的用户id
     * @var array
     */
    private $toUids;

    /**
     * 推送标题
     * @var string
     */
    private $pushTitle;

    /**
     * 推送内容
     * @var string
     */
    private $pushContent;

    /**
     * 接收推送消息的用户id
     * @var array
     */
    private $pushUids;

    /**
     * 业务标识
     * @var string
     */
    private $product;

    /**
     * 待发送的组id
     * @var integer
     */
    private $toGroupId;

    /**
     * 用户消息漫游类型（选填，暂时仅适用于IM， 0：不开启 1：开启）
     * @var integer
     */
    private $userRoamType;


    public function __construct($product)
    {
        if (!in_array($product, array(self::MQS_PRODUCT_FUDAO, self::MQS_PRODUCT_IM, self::MQS_PRODUCT_DAYI, self::MQS_PRODUCT_DEFAULT))) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'product is invalid');
        }
        $this->product = $product;
    }

    /**
     * 发送消息
     *
     * @return boolean|int 失败：false 成功：返回消息id
     * @throws Hk_Util_Exception
     */
    public function send()
    {
        if (empty($this->msgData)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'Msg Data is empty');
        }
        //清空有可能被使用方设置的消息id
        $this->msgId = null;

        $requestPack = array(
            'cmdno' => self::CMD_SEND_MSG,
            'product' => $this->product,
            'msg_data' => json_encode($this->msgData),
            'msg_expire_time' => intval($this->msgExpireTime) > 0 ? $this->msgExpireTime : self::MSG_DATA_EXPIRE,
        );

        $this->_addAdditionalOptions($requestPack);

        $ret = $this->_requestMqs($requestPack);

        $this->msgId = (false !== $ret && intval($ret['msg_id']) > 0) ? intval($ret['msg_id']) : null;

        return is_null($this->msgId) ? false : $this->msgId;
    }

    /**
     * 转发消息
     *
     * @return boolean 转发是否成功
     * @throws Hk_Util_Exception
     */
    public function forward()
    {
        if (!is_int($this->msgId) || $this->msgId <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "msgId:" . $this->msgId);
        }

        if (empty($this->toUids)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'Param toUids is needed');
        }

        $requestPack = array(
            'cmdno' => self::CMD_FORWARD_MSG,
            'product' => $this->product,
            'msg_id' => $this->msgId,
            'msg_expire_time' => intval($this->msgExpireTime) > 0 ? $this->msgExpireTime : self::MSG_DATA_EXPIRE,
            'to_uids' => $this->toUids
        );

        $this->_addAdditionalOptions($requestPack);

        $ret = $this->_requestMqs($requestPack);

        return false !== $ret;
    }

    /**
     * 删除消息
     *
     * @return boolean 是否删除成功
     * @throws Hk_Util_Exception
     */
    public function delete()
    {
        if (!is_int($this->msgId) || $this->msgId <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "msgId:" . $this->msgId);
        }

        $requestPack = array(
            'cmdno' => self::CMD_DELETE_MSG,
            'product' => $this->product,
            'msg_id' => $this->msgId,
        );

        $ret = $this->_requestMqs($requestPack);

        return false !== $ret;

    }


    /**
     * 获取mqs的消息信息
     *
     * @return false|array 获取失败：false  获取成功：消息信息数组
     * @throws Hk_Util_Exception
     */
    public function get()
    {
        if (!is_int($this->msgId) || $this->msgId <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "msgId:" . $this->msgId);
        }

        $requestPack = array(
            'cmdno' => self::CMD_GET_MSG,
            'product' => $this->product,
            'msg_id' => $this->msgId,
        );

        $ret = $this->_requestMqs($requestPack);

        return false === $ret ? false : $ret['msg_data'];
    }

    /**
     * 设置消息id
     *
     * @param integer $msgId 消息id
     * @return Hkzb_Service_MQSMessager
     */
    public function setMsgId($msgId)
    {
        $msgId = intval($msgId);
        if ($msgId > 0) {
            $this->msgId = $msgId;
        }
        return $this;
    }


    /**
     * 设置消息内容
     *
     * @param array $msgData 消息内容
     * @return Hkzb_Service_MQSMessager
     */
    public function setMsgData($msgData)
    {
        if (is_array($msgData) && !empty($msgData)) {
            unset($msgData['msg_id']);
            $this->msgData = $msgData;
        }
        return $this;
    }

    /**
     * 设置有效时间
     *
     * @param int $msgExpireTime 消息有效时间
     * @return Hkzb_Service_MQSMessager
     */
    public function setMsgExpireTime($msgExpireTime)
    {
        $this->msgExpireTime = intval($msgExpireTime);
        return $this;
    }

    /**
     * 设置清理等级
     *
     * @param int $msgCleanType 清理等级
     * @return Hkzb_Service_MQSMessager
     */
    public function setMsgCleanType($msgCleanType)
    {
        $this->msgCleanType = $msgCleanType;
        return $this;
    }

    /**
     * 设置投递类型
     *
     * @param int $msgDeliveryType 消息投递类型
     * @return Hkzb_Service_MQSMessager
     */
    public function setMsgDeliveryType($msgDeliveryType)
    {
        $this->msgDeliveryType = $msgDeliveryType;
        return $this;
    }

    /**
     * 设置待转发用户id
     *
     * @param array|integer $toUids 待转发用户id/id列表
     * @return Hkzb_Service_MQSMessager
     */
    public function setToUids($toUids)
    {
        if (is_array($toUids) && !empty($toUids)) {
            $this->toUids = $toUids;
        } else if (is_int($toUids) && $toUids > 0) {
            $this->toUids = array($toUids);
        }
        return $this;
    }

    /**
     * 设置推送标题
     *
     * @param string $pushTitle 推送标题
     * @return Hkzb_Service_MQSMessager
     */
    public function setPushTitle($pushTitle)
    {
        $this->pushTitle = $pushTitle;
        return $this;
    }

    /**
     * 设置推送内容
     *
     * @param string $pushContent 推送内容
     * @return Hkzb_Service_MQSMessager
     */
    public function setPushContent($pushContent)
    {
        $this->pushContent = $pushContent;
        return $this;
    }

    /**
     * 设置推送用户id
     *
     * @param array|integer $pushUids 待接收推送消息的用户id/id列表
     * @return Hkzb_Service_MQSMessager
     */
    public function setPushUids($pushUids)
    {
        if (is_array($pushUids) && !empty($pushUids)) {
            $this->pushUids = $pushUids;
        } else if (is_int($pushUids) && $pushUids > 0) {
            $this->pushUids = array($pushUids);
        }
        return $this;
    }

    /**
     * 设置待接收的组id
     *
     * @param integer $toGroupId 群组id
     * @return Hkzb_Service_MQSMessager
     */
    public function setToGroupId($toGroupId)
    {
        $this->toGroupId = intval($toGroupId);
        return $this;
    }

    /**
     * 设置消息漫游类型
     *
     * @param integer $userRoamType 用户漫游类型
     * @return Hkzb_Service_MQSMessager
     */
    public function setUserRoamType($userRoamType)
    {
        $this->userRoamType = $userRoamType ? 1 : 0;
        return $this;
    }

    /**
     * 向mqs请求参数增加选填项
     *
     * @param array $requestPack 请求mqs的参数
     */
    private function _addAdditionalOptions(&$requestPack)
    {
        if ($this->msgCleanType) {
            $requestPack['msg_clean_type'] = $this->msgCleanType;
        } else {
            $requestPack['msg_clean_type'] = self::MSG_CLEAN_TYPE_VOLATILE;
        }

        if ($this->msgDeliveryType) {
            $requestPack['msg_delivery_type'] = $this->msgDeliveryType;
        }

        if (empty($requestPack['to_uids']) && !empty($this->toUids)) {
            $requestPack['to_uids'] = $this->toUids;
        }

        if ($this->userRoamType) {
            $requestPack['user_roam_type'] = $this->userRoamType;
        }

        if (!empty($this->pushTitle) && !empty($this->pushContent) && !empty($this->pushUids)) {
            $requestPack['push_info'] = array(
                'push_title' => $this->pushTitle,
                'push_content' => $this->pushContent,
                'push_uids' => $this->pushUids
            );
        }

        if (!is_null($this->toGroupId)) {
            $requestPack['groupid'] = intval($this->toGroupId);
        }
    }

    /**
     * 请求Mqs并获取返回结果
     *
     * @param $requestPack
     * @return mixed
     */
    private function _requestMqs($requestPack)
    {
        Hk_Util_Log::start('ds_send_msg');
        $ret = ral('mqs_' . strtolower($this->product), MAIN_APP, $requestPack);
        Hk_Util_Log::stop('ds_send_msg');

        if (false === $ret) {

            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocolStatus = ral_get_protocol_code();

            Bd_Log::warning("Request mqs failed! [Request pack]: " . json_encode($requestPack) . "[Ral Error]: (errno:$errno errmsg:$errmsg protocol_status:$protocolStatus)");

            $this->_clearOptions($requestPack);

            return false;
        }

        if (0 != $ret['err_no']) {

            Bd_Log::warning("Request mqs failed! [Error Msg]: ({$ret['err_no']}) " . $ret['err_msg'] . " [Request pack]: " . json_encode($requestPack));
            $this->_clearOptions($requestPack);

            return false;
        }

        $this->_clearOptions($requestPack);
        return $ret;
    }

    /**
     * 消息发送和转发后，执行变量的清理操作，以免多次调用出错
     *
     * @param array $requestPack 请求mqs的参数
     */
    private function _clearOptions(&$requestPack)
    {
        $this->toUids = null;
        $this->pushUids = null;
        unset($requestPack['to_uids']);
        unset($requestPack['push_info']['push_uids']);
    }

}