<?php
/***************************************************************************
 *
 * Copyright (c) 2019 zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   NCM.php
 * <AUTHOR>
 * @date   2019/07/24
 * @brief
 **/

class Zb_Service_NCM
{
    // 产品线
    const APP_YIKE             = 1;       //一课业务线
    const APP_HUANXIONG        = 2;      //浣熊业务线
    const APP_MALL             = 3;      //商城业务线
    const APP_SCORESHOP        = 4;      //积分商城业务线
    const APP_VIP              = 5;      //VIP业务线
    const APP_YY_CHINESE       = 103;    //鸭鸭语文
    const APP_DAXUE_SOUTI      = 116;    //大学搜题酱
    const APP_PLAT             = 8;      //平台
    public static $_appMap    = array(
        self::APP_YIKE                => '一课',
        self::APP_HUANXIONG           => '浣熊',
        self::APP_MALL                => '商城',
        self::APP_SCORESHOP           => '积分商城',
        self::APP_VIP                 => 'VIP',
        self::APP_YY_CHINESE          => '鸭鸭语文',
        self::APP_DAXUE_SOUTI         => '大学搜题酱',
        self::APP_PLAT                => '平台',
    );

    const KEY_FORMAT = '/%s/%s/%s/%s';

    // NCM统计数据 @liuxiao ********
    public static $askCount = 0;//请求count
    public static $yacCount = 0;//命中yac
    public static $ncmCount = 0;//命中ncm

    // 注意：
    // 假定 SERVICE_NAME=NCM
    // Yac限制key长度为 48Byte, Hk_Service_YacClient会将 SERVICE_NAME 尾部增加一个':'
    // 故， Zb_Service_NCM 可以向 Yac 存储的最长key大小为： 48-1-len(SERVICE_NAME) = 44
    const SERVICE_NAME = 'NCM';
    const TTL = 5;
    const TTL_FAIL = 10;

    /**
     * 获取配置信息
     * 成功返回ID；失败返回False，成功返回array
     * @param int       $app 产品线，详见 $_appMap
     * @param string    $module ODP框架下APP的名称，如 napi course zbcore 等
     * @param string    $group 自定义配置分组
     * @param string    $key
     * @return int
     */
    public static function Get($app, $module, $group, $key) {
        if (!array_key_exists($app, self::$_appMap)) {
            return False;
        }
        if ("" === $module || "" === $group || "" === $key) {
            return False;
        }
        $fullKey = sprintf(self::KEY_FORMAT, $app, $module, $group, $key);
        return self::getEx($fullKey, Cmd0x01::MODE_QUICK);
    }

    /**
     * 强制获取配置信息
     * 在失败较多的情况下，获取配置会比较慢
     * 成功返回ID；失败返回False，成功返回array
     * @deprecated
     * @param $key
     * @return int
     */
    public static function ForceGet($app, $module, $group, $key) {
        if (!array_key_exists($app, self::$_appMap)) {
            return False;
        }
        if ("" === $module || "" === $group || "" === $key) {
            return False;
        }
        $fullKey = sprintf(self::KEY_FORMAT, $app, $module, $group, $key);
        return self::getEx($fullKey, Cmd0x01::MODE_WAIT);
    }

    /**
     * 获取配置信息
     * 不建议使用， 内部方法
     * 成功返回ID；失败返回False，成功返回array
     * @param $key
     * @param $mode 0, 1
     * @return int
     */
    protected static function getEx($key, $mode) {
        Zb_Service_NCM::$askCount++;
        $objYacCache = Hk_Service_YacClient::getInstance(self::SERVICE_NAME);
        $md5Key = md5($key);
        $cacheValue = $objYacCache->get($md5Key);
        if ($cacheValue != False) {
            Zb_Service_NCM::$yacCount++;
            Bd_Log::addNotice('ncmCount', Zb_Service_NCM::$askCount.','.Zb_Service_NCM::$yacCount.','.Zb_Service_NCM::$ncmCount);
            return $cacheValue;
        }
        $objNCMSDK = SDKIns::getInstance();
        $sdkValue = $objNCMSDK->Get($key, $mode);
        if ($sdkValue == False) {
            $objYacCache->set($md5Key, False, self::TTL_FAIL);
            Bd_Log::addNotice('ncmCount', Zb_Service_NCM::$askCount.','.Zb_Service_NCM::$yacCount.','.Zb_Service_NCM::$ncmCount);
            return False;
        }

        $strYamlValue = $sdkValue['value'];

        $arr = Zb_Util_Spyc::YAMLLoadString($strYamlValue);

        $value = $arr['value'];
        $objYacCache->set($md5Key, $value, self::TTL);
        Zb_Service_NCM::$ncmCount++;
        Bd_Log::addNotice('ncmCount', Zb_Service_NCM::$askCount.','.Zb_Service_NCM::$yacCount.','.Zb_Service_NCM::$ncmCount);
        return $value;
    }
}

class SDKIns {

    const SERVICE_NAME = 'NCMSDK';
    const NCM_AGENT_DUMP_DIR = "/odin/ncmagent/var/dump";
    const NCM_AGENT_ADDR = "unix:///odin/ncmagent/var/ncmagent.sock";
//    const NCM_AGENT_ADDR = "unix:///tmp/sock.sock";
    const NCM_AGENT_CTIMEOUT    = 1000000;    // 超时 1s
    const NCM_AGENT_WTIMEOUT    = 100000;    // 超时 100ms
    const NCM_AGENT_RHTIMEOUT   = 200000;    // 超时 300ms
    const NCM_AGENT_RTIMEOUT    = 100000;    // 超时 100ms

    private static $_ins;

    private function __construct() {}

    /**
     * 获取实例
     *
     * @return mixed:object|boolean
     */
    public static function getInstance() {
        if (self::$_ins) {
            return self::$_ins;
        }

        self::$_ins = new SDK(self::NCM_AGENT_ADDR, self::NCM_AGENT_DUMP_DIR, self::NCM_AGENT_CTIMEOUT, self::NCM_AGENT_WTIMEOUT, self::NCM_AGENT_RHTIMEOUT, self::NCM_AGENT_RTIMEOUT);
        return self::$_ins;
    }
}

/**
 * ncm php sdk<br>
 * 使用 Unix Socket 同 ncm agent 进行通信<br>
 * 不支持自动断线重连<br>
 *
 * @since 1.0 2019-07-27 初始化
 *
 * @filesource zb/service/NCM.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-07-27
 */
class SDK
{
    private $_conn;
    private $_ncmAgentDumpDir;
    private $_ncmAgentAddr;

    // 链接超时默认100ms
    private $_ncmAgentConnectTimeout        = 100000;
    // 写超时默认100ms
    private $_ncmAgentWriteTimeout          = 100000;
    // 读头超时默认100ms
    private $_ncmAgentReadHeaderTimeout     = 100000;
    // 读超时默认200ms
    private $_ncmAgentReadTimeout           = 200000;


    public function __construct($addr, $dir, $ct, $wt, $rht, $rt) {
        $this->_ncmAgentAddr = $addr;
        $this->_ncmAgentDumpDir = $dir;
        if ($ct) {
            $this->_ncmAgentConnectTimeout = $ct;
        }
        if ($wt) {
            $this->_ncmAgentWriteTimeout = $wt;
        }
        if ($rht) {
            $this->_ncmAgentReadHeaderTimeout = $rht;
        }
        if ($rt) {
            $this->_ncmAgentReadTimeout = $rt;
        }
        $this->connect();
    }

    public function __destruct() {
        if($this->_conn) {
            fclose($this->_conn);
        }
    }

    private function connect() {
        $errNo = null;
        $errStr = null;
        $conn = stream_socket_client($this->_ncmAgentAddr, $errNo, $errStr, $this->_ncmAgentConnectTimeout);
        if (!$conn) {
            Bd_Log::warning(sprintf('Error:[connect %s error], Detail:[errno:%s errstr:%s]',
                $this->_ncmAgentAddr,$errNo,$errStr));
            return;
        }
        $this->_conn = $conn;
    }

    public function Get($key, $mode) {
        if(!$this->_conn) {
            return $this->readFromLocal($key);
        }
        if(strlen($key) <= 0) {
            Bd_Log::warning(sprintf('Error:[param error], Detail:[key:%s server:%s]',
                $key, $this->_ncmAgentAddr));
            return False;
        }

        // 1. request
        $ret = $this->sendRequest(new Cmd0x01(), [
            "mode" => $mode,
            "key"  => $key,
        ]);
        if ($ret == False) {
            Bd_Log::warning(sprintf('Error:[sendRequest error], Detail:[key:%s server:%s]',
                $key, $this->_ncmAgentAddr));
            return False;
        }

        // 2. response
        $resp = $this->recvResponse(new Cmd0x81());
        if ($resp == False) {
            Bd_Log::warning(sprintf('Error:[recvResponse error], Detail:[key:%s server:%s]',
                $key, $this->_ncmAgentAddr));
            return False;
        }

        if ($resp['len'] <= 0) {
            Bd_Log::warning(sprintf('Error:[recvResponse empty], Detail:[key:%s value:%s]',
                $key, json_encode($resp)));
            return False;
        }
        return $resp;
    }

    private function sendRequest(Cmd $objCmd, $params) {
        $cmd = $objCmd->Marshal($params);
        $head = Header::Marshal($objCmd->CmdNo(), strlen($cmd));
        $ret = $this->writeWithTimeout($head . $cmd, $this->_ncmAgentWriteTimeout);
        if ($ret == False) {
            Bd_Log::warning(sprintf('Error:[ncm sendRequest failed], Detail:[cmd:%s head:%s]', var_export($cmd, 1), var_export($head, 1)));
            return False;
        }
        return $ret;
    }

    private function recvResponse(Cmd $objCmd) {
        $data = $this->readWithTimeout(Header::Length, $this->_ncmAgentReadHeaderTimeout);
        if ($data == False) {
            Bd_Log::warning(sprintf('Error:[ncm recvResponse failed], Detail:[%s]', var_export($this->_ncmAgentReadHeaderTimeout, 1)));
            return False;
        }
        $header = Header::UnMarshal($data);
        if ($header == False) {
            Bd_Log::warning(sprintf('Error:[ncm UnMarshal failed], Detail:[%s]', var_export($data, 1)));
            return False;
        }
        if ($header['command'] != $objCmd->CmdNo()) {
            Bd_Log::warning(sprintf('Error:[ncm command error], Detail:[%s]', var_export($header, 1)));
            return False;
        }
        if ($header[Header::PayloadLen] <= 0) {
            Bd_Log::warning(sprintf('Error:[ncm PayloadLen error], Detail:[%s]', var_export($header, 1)));
            return False;
        }

        $data = $this->readWithTimeout($header[Header::PayloadLen], $this->_ncmAgentReadTimeout);

        if ($data == False) {
            Bd_Log::warning(sprintf('Error:[ncm readWithTimeout], Detail:[header:%s, agent:%s],', var_export($header, 1), var_export($this->_ncmAgentReadTimeout, 1)));
            return False;
        }

        $resp = $objCmd->UnMarshal($data);
        return $resp;
    }

    private function writeWithTimeout($data, $timeout) {
        stream_set_timeout($this->_conn, 0, $timeout);
        $ret = fwrite($this->_conn, $data, strlen($data));
        $info = stream_get_meta_data($this->_conn);
        if ($info['timed_out']) {
            Bd_Log::warning('Error:[writeWithTimeout timeout]');
            return False;
        }
        return $ret;
    }

    private function readWithTimeout($len, $timeout) {
        stream_set_timeout($this->_conn, 0, $timeout);
        $data = fread($this->_conn, $len);
        $info = stream_get_meta_data($this->_conn);
        if ($info['timed_out']) {
            Bd_Log::warning('Error:[readWithTimeout timeout]');
            return False;
        }
        return $data;
    }

    private function readFromLocal($key) {
        $fPath = $this->buildPathByKey($key);
        Bd_Log::warning(sprintf('Warning:[not connect, try read local], Detail:[key:%s server:%s file:%s]',
            $key, $this->_ncmAgentAddr, $fPath));
        $data = file_get_contents($fPath);
        if ($data == False) {
            Bd_Log::warning(sprintf('Error:[read local fail], Detail:[key:%s server:%s file:%s]',
                $key, $this->_ncmAgentAddr, $fPath));
            return False;
        }
        return [
            'value' => $data,
        ];
    }

    protected function buildPathByKey($key) {
        $key = $this->merge_paths($this->_ncmAgentDumpDir, $key);
        return sprintf('%s.yaml', $key);
    }

    /**
     * Merge several parts of URL or filesystem path in one path
     * Examples:
     *  echo merge_paths('stackoverflow.com', 'questions');           // 'stackoverflow.com/questions' (slash added between parts)
     *  echo merge_paths('usr/bin/', '/perl/');                       // 'usr/bin/perl/' (double slashes are removed)
     *  echo merge_paths('en.wikipedia.org/', '/wiki', ' Sega_32X');  // 'en.wikipedia.org/wiki/Sega_32X' (accidental space fixed)
     *  echo merge_paths('etc/apache/', '', '/php');                  // 'etc/apache/php' (empty path element is removed)
     *  echo merge_paths('/', '/webapp/api');                         // '/webapp/api' slash is preserved at the beginnnig
     *  echo merge_paths('http://google.com', '/', '/');              // 'http://google.com/' slash is preserved at the end
     * @param string $path1
     * @param string $path2
     */
    private function merge_paths($path1, $path2){
        $paths = func_get_args();
        $last_key = func_num_args() - 1;
        array_walk($paths, function(&$val, $key) use ($last_key) {
            switch ($key) {
                case 0:
                    $val = rtrim($val, '/ ');
                    break;
                case $last_key:
                    $val = ltrim($val, '/ ');
                    break;
                default:
                    $val = trim($val, '/ ');
                    break;
            }
        });

        $first = array_shift($paths);
        $last = array_pop($paths);
        $paths = array_filter($paths); // clean empty elements to prevent double slashes
        array_unshift($paths, $first);
        $paths[] = $last;
        return implode('/', $paths);
    }
}

/**
 * ncm agent交互命令 cmd 接口<br>
 *
 * @since 1.0 2019-07-27 初始化
 *
 * @filesource zb/service/NCM.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-07-27
 */
interface Cmd {
    // 序列化
    public function Marshal($params);
    // 反序列化
    public function UnMarshal($data);
    // 命令号
    public function CmdNo();
}

/**
 * ncm agent交互命令 cmd0x01<br>
 * 命令字节数: 2 + 2 + len(data)<br>
 * 命令结构为: Mode[uint16]KeyLen[uint16]Key[]<br>
 *
 * @since 1.0 2019-07-27 初始化
 *
 * @filesource zb/service/NCM.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-07-27
 */
class Cmd0x01 implements Cmd {
    const CMD_NO     = 0x01;
    const MODE_QUICK = 0x01;        // 获取一次， 失败则直接返回失败。
    const MODE_WAIT  = 0x02;        // 获取失败后， 会进行等待并重试。等待时间为ms级别。
    public function CmdNo() {
        return self::CMD_NO;
    }

    public function Marshal($params) {
        return pack("vva*", $params['mode'], strlen($params['key']), $params['key']);
    }

    public function UnMarshal($data) {
        if (!$data) {
            return False;
        }
        return unpack("vmode/vlen/a*key", $data);
    }
}


/**
 * ncm agent交互命令 cmd0x81<br>
 * 命令字节数: 4 + len(data)<br>
 * 命令结构为: ValueLen[uint16]Value[]<br>
 *
 * @since 1.0 2019-07-27 初始化
 *
 * @filesource zb/service/NCM.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-07-27
 */
class Cmd0x81 implements Cmd{
    const CMD_NO = 0x81;
    public function CmdNo() {
        return self::CMD_NO;
    }

    public function Marshal($params) {
        return pack("Va*", strlen($params['value']), $params['value']);;
    }

    public function UnMarshal($data) {
        if (!$data) {
            return False;
        }
        return unpack("Vlen/a*value", $data);
    }
}

/**
 * ncm agent交互命令 协议头<br>
 * 命令字节数: 1 + 1 + 2 + 4 <br>
 * 命令结构为: Version[uint8]Command[uint8]MagicNum[uint16]PayloadLen[uint32]<br>
 *
 * @since 1.0 2019-07-27 初始化
 *
 * @filesource zb/service/NCM.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-07-27
 */
class Header {
    const Length = 8;
    const Version = 0x01;
    const MAGIC_NUMBER = 0x4E43;
    const PayloadLen = 'payloadLen';
    public static function Marshal($command, $payloadLen) {
        $command = intval($command);
        if ($command > 0xFF || $command < 0) {
            return False;
        }
        $payloadLen = intval($payloadLen);
        if ($payloadLen < 0) {
            return False;
        }
        return pack("CCvl", self::Version, $command, self::MAGIC_NUMBER, $payloadLen);
    }

    public static function UnMarshal($data) {
        if (!$data) {
            return False;
        }
        $ret = unpack('Cversion/Ccommand/vmagicNumber/lpayloadLen', $data);
        if ($ret['magicNumber'] != self::MAGIC_NUMBER) {
            Bd_Log::warning(sprintf('Error:[ncm UnMarshal failed], Detail:[%s]', var_export($ret, 1)));
            return False;
        }
        return $ret;
    }
}

//function TestSDK() {
//    $start = time();
//    $objSDK = SDKIns::getInstance();
//    $ret = $objSDK->Get("/yike/a/b/c", 1);
//    $strYamlValue = $ret['value'];
////    var_export($strYamlValue);
////    for ($i=0; $i < 10; $i++) {
////        $objSDK->Get("/demo", 1);
////    }
////    $stop = time();
//    var_export($ret);
//
////    var_export($stop - $start);
//}
