<?php
/**
 * Created by IntelliJ IDEA.
 * @Description moat公共方法
 * @Date 2021/10/26 11:08 上午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_Shennong_Common {

    const SERVICE = 'shennong';
    const APP_KEY = 'cube';
    const APP_SECRET = 'e9fcd68ed13050609aa997dcb5b5d811';
    const BUSINESS_SYSTEM_YK_TOUFANG = 80;
    const SIGN = 'sign';

    //发送请求
    public static function request($uri, $params, $method = 'post')
    {
        return self::send(['pathinfo' => $uri,'Content-Type'=>'application/json'], $params, $method);
    }

    //发送请求
    public static function send($header, $params, $method = 'post', $extra = null)
    {
        if (is_null($extra)) {
            $extra = [
                'bk' => mt_rand(),
                'retry' => 2,
                'ctimeout' => 500,
                'wtimeout' => 500,
                'rtimeout' => 4000,
            ];
        }
        //$params['businessSystem'] = self::BUSINESS_SYSTEM_YK_TOUFANG;
        $params['appkey'] = self::APP_KEY;;
        $params['sign'] = self::getSign($params);
        $logKey = '_' . $header['pathinfo'] . '_' . $params['sign'];
        Qdlib_Util_Log::addNotice('shennongReq' . $logKey, json_encode([$header, $params], JSON_UNESCAPED_UNICODE));
        $timeKey = 'shennongApi' . $logKey;
        Hk_Util_Log::start($timeKey);
        $result = Qdlib_Util_Tool::sendRal($header, $params, $method, Qdlib_Util_Ral::SERVICE_SHENNONG, $extra);
        Hk_Util_Log::stop($timeKey);
        Qdlib_Util_Log::addNotice('shennong' . $logKey, json_encode($result, JSON_UNESCAPED_UNICODE));

        list($isTrue, $errno, $data) = $result;
        if (!$isTrue) {     // 记录错误日志用于监控
            $uri = $header['pathinfo'] ?? '';
            Qdlib_Util_Log::warning("shennong", "Qdlib_Service_Shennong_Common", $uri, "请求{$uri}接口失败",
                json_encode(['isTrue' => $isTrue, 'errno' => $errno, 'msg' => $data, 'arrParams' => $params]));
        }
        return $result;
    }

    //获取签名
    protected static function getSign($params = [])
    {
        ksort($params);
        $aliParams = [];
        foreach ($params as $key => $value) {
            if (is_string($value)) {
                $aliParams[] = $key . $value;
            } elseif (is_array($value)) {
                $aliParams[] = $key . json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            } else {
                $aliParams[] = $key . (string)$value;
            }
        }
        $key = self::APP_KEY . implode('', $aliParams) . self::APP_SECRET;
        $sign = strtolower(md5($key));
        return $sign;
    }

    public static function returnDataOrFalse($response)
    {
        list($isTrue, , $data) = $response;
        return $isTrue ? $data : false;
    }

    public static function returnApiObject($response)
    {
        list($isTrue, $errNo, $data) = $response;
        if ($isTrue) {
            return [
                'errNo' => 0,
                'errStr' => '',
                'data' => $data,
            ];
        }
        return [
            'errNo' => $errNo,
            'errStr' => $data,
            'data' => [],
        ];
    }
}