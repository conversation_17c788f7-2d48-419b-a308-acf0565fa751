<?php
/**
 * Created by IntelliJ IDEA.
 * @Description Sku信息
 * @Date 2021/11/3 10:27 上午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_Moat_Sku
{
    /**
     * 获取sku信息 （后续业务用此方法）
     * @param $skuIds
     * @return array
     */
    public static function skuInfoBatch($skuIds)
    {
        if (empty($skuIds)) {
            return [];
        }
        $skuInfoList = Qdlib_Service_Moat_Newgoodsplatform::getGoodsSkuKvBySkuIdV2($skuIds);
        $stockList = Qdlib_Service_Moat_Common::returnDataOrFalse(Qdlib_Service_Moat_Newgoodsplatform::getEntityStock($skuIds));
        if (!empty($stockList)) {
            $stockList = array_column($stockList, null, 'entityId');
        }
        $ret = [];
        foreach ($skuInfoList as $skuInfo) {
            $ret[$skuInfo['skuId']] = $skuInfo;
            $ret[$skuInfo['skuId']]['stockDetail'] = $stockList[$skuInfo['skuId']] ?? [];
        }
        return $ret;
    }

    /**
     * 待废弃 新业务禁用
     * 获取sku信息，格式化为Zb_Advanced_Sku::getSkuInfoBatch
     * 后续逻辑需要迁移至 self::skuInfoBatch 方法
     * @param $skuIds
     * @return array
     */
    public static function getSkuInfoBatch($skuIds)
    {
        $skuInfoList = self::skuInfoBatch($skuIds);
        if (empty($skuInfoList)) {
            return [];
        }
        $ret = [];
        foreach ($skuInfoList as $k => $v) {
            $ret[$k] = self::formatSkuToOld($v);
        }
        return $ret;
    }

    protected static function formatSkuToOld($skuInfo)
    {
        if ($skuInfo['status'] == 0) {  // status 旧版 1 2上架 3 4 下架 并需要与startTime、stopTime 关联判断， 新版0为上架 1为下架
            $skuInfo['status'] = 3;
        }
        $skuInfo['saleCnt'] = $skuInfo['stockDetail']['saleStock'] ?? 0;
        $skuInfo['stockCnt'] = $skuInfo['stockDetail']['originStock'] ?? 0;
        $skuInfo['leftCnt'] = $skuInfo['stockDetail']['remainCnt'] ?? 0;
        foreach ($skuInfo['attributeTags'] as $val) {
            if (in_array($val['key'], ['classType', 'moduleType', 'systemType', 'versionType', 'bookVer'])) {
                $skuInfo[$val['key']] = self::tagTwoToOne($val['values']);
            }
        }
        $skuInfo['versionType'] = $skuInfo['bookVer'];
        $skuInfo['playType'] = self::getTag($skuInfo['attributeTags'], 'brandId')[0]['code'] ?? 1; //1 直播课 2 录播课
        $skuInfo['specialSellType'] = self::getTag($skuInfo['labelTags'], 'specialSellType')[0]['code'] ?? 1; //0 非特惠课 1 特惠课

        $skuInfo['courseIds'] = $skuInfo['courseInfoList'] = [];
        foreach ($skuInfo['productContent'] as $val) {
            $skuInfo['courseIds'][] = $val['thirdId'];
            $skuInfo['courseInfoList'][$val['thirdId']] = $val;
        }

        // 项目内未使用，目前写死字段
        $skuInfo['hasBK'] = 0;
        $skuInfo['forSale'] = 1;
        $skuInfo['preSaleCnt'] = 1;
        $skuInfo['skuPrice'] = 99999999999;
        $skuInfo['skuOriginalPrice'] = 99999999999;
        $skuInfo['skuCostPrice'] = 99999999999;
        return $skuInfo;
    }

    protected static function tagTwoToOne($tagList)
    {
        if (empty($tagList)) {
            return [0];
        }
        return array_column($tagList, 'code');
    }

    protected static function getTag($arr, $key)
    {
        foreach ($arr as $val) {
            if ($key == $val['key']) {
                return $val['values'];
            }
        }
        return [];
    }


}