<?php

/**
 * 短链接服务
 */
class Qdlib_Service_Moat_Su
{
    /**
     * 创建短链接
     * https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1689753009&node=4_123_125_&docType=1
     */
    public static function createshorturl(string $longUrl, string $source = '')
    {
        if(empty($source)){
            $source = 'tf_cp';
        }
        return Qdlib_Service_Moat_Common::request('/su/api/createunifyshorturl', [
            'longUrl' => $longUrl,
            'source' => $source,
            'expire' => 31536000,
        ]);
    }

    public static function retrunShortUrlOrFalse(string $longUrl, string $source = '')
    {
        list($isTrue, , $data) = self::createshorturl($longUrl, $source);
        if (!$isTrue || $data['shortUrl']===false) {
            return false;
        }
        return $data['shortUrl'];
    }
}