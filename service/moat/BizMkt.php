<?php
/**
 * Created by IntelliJ IDEA.
 * @Description 营销策略
 * @Date 2021/10/26 4:31 下午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_Moat_BizMkt {

    /**
     * 下单页算价接口
     * http://ssv.zuoyebang.cc/static/open-sell/#/detail-api?releaseId=1620809301
     * @param $saleChannelId
     * @param $skuIdCntMap
     * @param int $uid
     * @param array $omsPriceMap
     * @return array|false
     */
    public static function placeOrder($saleChannelId, array $skuIdCntMap, $uid = 0, array $omsPriceMap = [])
    {
        $skuIdSaleModelMap = [];
        foreach ($skuIdCntMap as $skuId => $count) {
            $skuIdSaleModelMap[$skuId] = 1;
        }
        $params = [
            'uid' => $uid,
            'skuIdCntMap' => $skuIdCntMap,
            'businessType' => 0,
            'skuIdSaleModelMap' => $skuIdSaleModelMap,
            'currencyType' => 1,
            'saleChannelId' => $saleChannelId,
            'isOms' => empty($omsPriceMap) ? 0 : 1,
            'omsPriceMap' => $omsPriceMap,
        ];
        return Qdlib_Service_Moat_Common::request('/bizmkt/coreapi/placeorder', $params);
    }
}