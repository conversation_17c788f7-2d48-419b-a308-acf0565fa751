<?php

class Qdlib_Service_Doris_Doris
{
    public function query($sql, $todo = 0)
    {
        $repeatCnt = 0;
        $responseInfo = false;
        // 重试 2次
        while (true) {
            $responseInfo = $this->queryInner($sql);
            if ($repeatCnt == 2) {
                break;
            }
            if ($responseInfo == false || !isset($responseInfo['code']) || 0 !== $responseInfo['code'] || !isset($responseInfo['data'])) {
                $repeatCnt++;
                continue;
            } else {
                break;
            }
        }

        if (!isset($responseInfo['code']) || 0 !== $responseInfo['code'] || !isset($responseInfo['data'])) {
            $code = $responseInfo['code'] ?? 'error';
            Qdlib_Util_Log::warning("doris_api_{$code}", "$code", 'query', "查询失败:$code", json_encode($responseInfo));
            return $this->error(isset($responseInfo['code']) ? $responseInfo['code'] : 1, isset($responseInfo['msg']) ? $responseInfo['msg'] : '未知错误', $responseInfo);
        }

        return $this->success($responseInfo['data']);
    }

    private function queryInner($sql) {
        $uri = Qdlib_Util_Doris::URI_V2_V1;
        if (strstr($sql, 'doris.') >= 1) {
            $uri = Qdlib_Util_Doris::URI_TRIAL_V1;
        }
        return Qdlib_Util_Doris::query($uri, $sql);
    }

    public function queryV2($sql, $todo = 0)
    {
        $repeatCnt = 0;
        $responseInfo = false;
        // 重试 2次
        while (true) {
            $responseInfo = $this->queryInnerV2($sql);
            if ($repeatCnt == 2) {
                break;
            }
            if ($responseInfo == false || !isset($responseInfo['code']) || 0 !== $responseInfo['code'] || !isset($responseInfo['data'])) {
                $repeatCnt++;
                continue;
            } else {
                break;
            }
        }

        if (!isset($responseInfo['code']) || 0 !== $responseInfo['code'] || !isset($responseInfo['data'])) {
            $code = $responseInfo['code'] ?? 'error';
            Qdlib_Util_Log::warning("doris_api_{$code}", "$code", 'query', "查询失败:$code", json_encode($responseInfo));
            return $this->error(isset($responseInfo['code']) ? $responseInfo['code'] : 1, isset($responseInfo['msg']) ? $responseInfo['msg'] : '未知错误', $responseInfo);
        }

        return $this->success($responseInfo['data']);
    }

    private function queryInnerV2($sql) {
        $uri = Qdlib_Util_Doris::URI_V2OL_V1;
        if (strstr($sql, 'doris.') >= 1) {
            $uri = Qdlib_Util_Doris::URI_TRIAL_V1;
        }
        return Qdlib_Util_Doris::query($uri, $sql);
    }

    public function upserts($database, $table, $data)
    {
        $response = Qdlib_Util_Doris::upserts(Qdlib_Util_Doris::URI_V2_V1, $database, $table, $data);
        if ($response['code'] == 0) {
            return $this->success($response['data']);
        }
        return $this->error($response['code'], $response['msg'], $response['data']);
    }

    private function success($data)
    {
        return $this->error(0, '', $data);
    }

    private function error($errNo, $errStr, $data = [])
    {
        return [
            'errNo'  => $errNo,
            'errStr' => $errStr,
            'data'   => $data,
        ];
    }
}
