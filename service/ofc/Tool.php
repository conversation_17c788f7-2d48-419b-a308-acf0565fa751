<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Tool.php
 * <AUTHOR>
 * @date   2019/8/29
 * @brief  履约工具接口
 **/
class Zb_Service_Ofc_Tool
{
    /**
     * 查询履约详情
     * @param $businessId 业务编号
     * @param $op 操作行为 Zb_Const_Command::COMMAND_OFC_176001 - 176006 相关
     * @return array rs
     */
    public static function detail($businessId=0, $op=0){
        if(!$businessId ||
            !$op){
            Bd_Log::warning('Error:[ral zbcore::/ofc/api/detail error], Detail:[errno:1, error:参数错误, businessId:'.$businessId.', op:'.$op.']');
            $rs = [
                'errNo' => 1,
                'errStr' => '参数错误',
            ];
            return $rs;
        }
        $data = [
            'businessId' => $businessId,
            'op' => $op,
        ];
        $header = [
            'pathinfo' => '/ofc/tool/detail',
        ];
        $ret = ral('zbofc', 'POST', $data, mt_rand(), $header);
        if(false === $ret){
            $errno = ral_get_errno();
            $error = ral_get_error();
            Bd_Log::warning('Error:[ral zbcore::/ofc/tool/retry error], Detail:[errno:'.$errno.', error:'.$error.', data:'.json_encode($data).']');
            $rs = [
                'errNo' => 0-$errno,
                'errStr' => $error,
            ];
            return $rs;
        }
        $rs = json_decode($ret, 1);
        return $rs;
    }

    /**
     * 针对制定履约进行重试
     * @param int $ofcId
     * @return true
     */
    public static function retry($ofcId=0){
        if(!$ofcId){
            Bd_Log::warning('Error:[ral zbcore::/ofc/tool/retry error], Detail:[errno:1, error:参数错误, ofcId:'.$ofcId.']');
            $rs = [
                'errNo' => 1,
                'errStr' => '参数错误',
            ];
            return $rs;
        }
        $data = [
            'ofcId' => $ofcId,
        ];
        $header = [
            'pathinfo' => '/ofc/tool/retry',
        ];
        $ret = ral('zbofc', 'POST', $data, mt_rand(), $header);
        if(false === $ret){
            $errno = ral_get_errno();
            $error = ral_get_error();
            Bd_Log::warning('Error:[ral zbcore::/ofc/tool/retry error], Detail:[errno:'.$errno.', error:'.$error.', data:'.json_encode($data).']');
            $rs = [
                'errNo' => 0-$errno,
                'errStr' => $error,
            ];
            return $rs;
        }
        $rs = json_decode($ret, 1);
        return $rs;
    }
}
