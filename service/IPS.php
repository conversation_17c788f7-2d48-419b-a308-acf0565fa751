<?php

class Saaslib_Service_IPS {
    const IPS_LOGIN = "https://ips.zuoyebang.com/static/cas-fe/";
    const IPS_LOGOUT = "https://ips.zuoyebang.com/static/cas-fe/?logout=1";
    const IPS_VALIDATE = "https://ips.zuoyebang.cc/cas/home/<USER>";
    const COOKIE_KEY = "ZYBIPSCAS";
    const COOKIE_VAL_PRE = "IPS_";

    private $_sid;
    private $_secret;
    private $_path;
    private $_redisName;
    private $_isRootDomain;

    public function __construct($sid, $secret, $path, $isRootDomain=false) {
        $this->_sid = $sid;
        $this->_secret = $secret;
        $this->_redisName = "saascommon";
        $this->_path = "/";//!empty($path) ? $path : "/";
        $this->_isRootDomain = true;//!empty($isRootDomain);
    }

    public function logout($url) {
        $url = $this->getLogoutUrl($url);
        header("Location:" . $url);
    }

    public function getLogoutUrl($url) {
        $url = !empty($url) ? $url : "";
        $res = $this->delSession();
        if (empty($res)) {
            Bd_Log::warning("ips delSession failed");
        }
        $this->delCookie();
        return sprintf("%s&sid=%s&service=%s", self::IPS_LOGOUT, $this->_sid, urlencode($url));
    }

    public function login($url) {
        $url = sprintf("%s?sid=%s&service=%s", self::IPS_LOGIN, $this->_sid, urlencode($url));
        header("Location:" . $url);
    }

    public function validateAndSession($ticket) {
        if (empty($ticket)) {
            return false;
        }

        $data = array(
            "sid" => $this->_sid,
            "ticket" => $ticket,
            "secret" => $this->_secret,
        );
        $res = self::post(self::IPS_VALIDATE, $data, array(), 5);
        $valid = @json_decode($res, true);
        if (empty($valid)) {
            Bd_Log::warning("ips validate json failed {$res}");
            return false;
        }
        if ($valid["errNo"] !== 0) {
            Bd_Log::warning("ips validate header {$res}");
            return false;
        }

        $username = $valid["data"]["username"];
        $expire = $valid["data"]["expire"];
        $mobile = !empty($valid["data"]["mobile"]) ? $valid["data"]["mobile"] : 0;
        $uid = !empty($valid["data"]["uid"]) ? $valid["data"]["uid"] : 0;

        $data = $this->setSession($username, $expire, $mobile, $uid);
        if (empty($data)) {
            Bd_Log::warning("ips validate setSession failed");
            return false;
        }

        Bd_Log::notice("ips validate success");
        return $data;
    }

    public function getSession() {
        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            Bd_Log::notice("ips getSession cookie empty");
            return false;
        }

        $redis = Hk_Service_RedisClient::getInstance($this->_redisName);
        if (empty($redis)) {
            Bd_Log::warning("ips getSession redis empty");
            return false;
        }

        $zybips = $_COOKIE[self::COOKIE_KEY];
        $ret = $redis->get($zybips);
        if (empty($ret)) {
            $this->delCookie();
            Bd_Log::warning("ips getSession session empty");
            return false;
        }

        $session = @json_decode($ret, true);
        if (empty($session)) {
            return false;
        }

        // 安卓端session不能跨系统使用
        if (!empty($session["platform"])
        && $session["platform"] == "sdk"
        && $session["loginFrom"] != $this->_sid) {
            Bd_Log::warning("ips getSession sdk limit sid");
            return false;
        }
        // 兼容java sdk把uname种成username的问题
        if (!empty($session["username"]) && empty($session["uname"])) {
            $session["uname"] = $session["username"];
        }
        // 访客session不能跨系统使用
        if (preg_match("/^1[13456789]{1}\d{9}$/", $session["uname"]) 
        && $session["loginFrom"] != $this->_sid) {
            $this->delCookie();
            Bd_Log::warning("ips getSession visitor limit sid");
            return false;
        }

        return $session;
    }

    private function setSession($username, $expire, $mobile, $uid) {
        $email = strpos($username, "@") > 0 ? $username : sprintf("%<EMAIL>", $username);
        $content  = array(
            "uname" => $username,
            "username" => $username,
            "email" => $email,
            "mobile" => $mobile,
            "uid" => $uid,
            "loginFrom"   => $this->_sid,
            "lastLogTime" => time(),
        );
        $redis = Hk_Service_RedisClient::getInstance($this->_redisName);
        if (empty($redis)) {
            return false;
        }

        $sessionExpire = 86400;
        if ($expire > 0) {
            $sessionExpire = $expire;
        }
        $zybips = $this->genZybips($username);
        $ret = $redis->setEx($zybips, $sessionExpire, @json_encode($content));
        if (empty($ret)) {
            return false;
        }

        $cookieExpire = $expire;
        if ($expire > 0) {
            $cookieExpire += time();
        }
        $domain = $this->getDomain();
        setcookie(self::COOKIE_KEY, $zybips, $cookieExpire, $this->_path, $domain, false, true);
        return $content;
    }

    private function delSession() {
        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            return true;
        }
        $redis = Hk_Service_RedisClient::getInstance($this->_redisName);
        if (empty($redis)) {
            return false;
        }

        $zybips = $_COOKIE[self::COOKIE_KEY];
        return $redis->delete($zybips);
    }


    private function getDomain($current=false) {
        $domain = $_SERVER["HTTP_HOST"];
        if (false !== strpos($domain, ":")) {
            $domain = explode(":", $domain)[0];
        }
        if ($current) {
            return $domain;
        }
        if ($this->_isRootDomain) {
            if (false !== stripos($domain, "zuoyebang.cc")) {
                $domain = '.zuoyebang.cc';
            } else if (false !== stripos($domain, "suanshubang.com")) {
                $domain = '.suanshubang.com';
            } else if (false !== stripos($domain, "zuoyebang.com")) {
                $domain = '.zuoyebang.com';
            }
        }
        return $domain;
    }

    private function delCookie() {
        $domain = $this->getDomain(true);
        setcookie(self::COOKIE_KEY, "", time()-86400, "/", $domain, false, true);
        setcookie(self::COOKIE_KEY, "", time()-86400, "/", ".zuoyebang.cc", false, true);
        setcookie(self::COOKIE_KEY, "", time()-86400, "/", ".zuoyebang.com", false, true);
        setcookie(self::COOKIE_KEY, "", time()-86400, "/", ".suanshubang.com", false, true);
        setcookie("ZYBUSS", "", time()-86400, "/", ".zuoyebang.cc", false, true);
        setcookie("ZYBUSS", "", time()-86400, "/", ".zuoyebang.com", false, true);
        setcookie("ZYBUSS", "", time()-86400, "/", ".suanshubang.com", false, true);
    }

    private function genZybips($username) {
        $rand    = rand(10000000, 99999999);
        $zybips = sprintf("%s_%d_%d_%d", $username, $rand, $rand & 0xAABBCCDD, time());
        return self::COOKIE_VAL_PRE . md5($zybips);
    }

    private function post($url, $data, $headers=array(), $timeout=1, $mtimeout=0) {
        $ch = curl_init();
        curl_setopt( $ch, CURLOPT_URL, $url );
        empty($headers) || curl_setopt( $ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        if ($mtimeout > 0) {
            curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT_MS, $mtimeout);
        } else if ($timeout > 0){
            curl_setopt( $ch, CURLOPT_TIMEOUT, $timeout);
        }
        curl_setopt( $ch, CURLOPT_POST, 1);
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $data );
        $result = curl_exec($ch);

        if (empty($result)) {
            Bd_Log::warning("ips post failed ".curl_error($ch));
        }

        curl_close($ch);
        return $result;
    }
}