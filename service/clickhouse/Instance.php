<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2020/1/6
 * Time: 8:57 PM
 */
class Qdlib_Service_Clickhouse_Instance {

    /**
     * @var Qdlib_Service_Clickhouse_Client
     */
    private static $instance = null;

    private static $conf = array(
        'host'      => '***************',
        'port'      => 8123,
        'protocol'  => 'http',
        'database'  => 'homework_zhibo_qudao',
        'user'      => 'toufang',
        'password'  => '6x2yF_M9ln',
    );

    private static $confTest = array(
        'host'      => '***************',
        'port'      => 8123,
        'protocol'  => 'http',
        'database'  => 'homework_zhibo_qudao',
        'user'      => 'toufang',
        'password'  => '6x2yF_M9ln',
    );

    /**
     * 获取ClickHouse句柄
     * @param bool $new 是否返回新句柄
     * @return Qdlib_Service_Clickhouse_Client
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public static function getCK($new = false)
    {
        $conf = Qdlib_Util_Tool::getEnviron() == 'test' ? self::$confTest : self::$conf;
        $config = new Qdlib_Service_Clickhouse_Config(
            ['host' => $conf['host'], 'port' => $conf['port'], 'protocol' => 'http']
        );

        $config->change('database', $conf['database']);
        $config->setUser($conf['user']);
        $config->setPassword($conf['password']);

        if ($new) {
            return new Qdlib_Service_Clickhouse_Client($config);
        }

        if (self::$instance instanceof Qdlib_Service_Clickhouse_Client) {
            self::$instance->ping();
            return self::$instance;
        }
        self::$instance = new Qdlib_Service_Clickhouse_Client($config);
        return self::$instance;
    }

    private function test()
    {
        $sql = 'select * from aaa';
        $ck = self::getCK();
        $ck->query($sql);
    }
}