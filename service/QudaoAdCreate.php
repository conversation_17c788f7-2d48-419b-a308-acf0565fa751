<?php
/**
 * Created by IntelliJ IDEA.
 * @Description TODO
 * @Date 2021/3/1 11:08 上午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_QudaoAdCreate {

    const DATA_TYPE_CAMPAIGN        = 'campaign';
    const DATA_TYPE_AD_GROUP        = 'adgroup';
    const DATA_TYPE_AD              = 'ad';
    const DATA_TYPE_CREATIVE        = 'creative';

    static $objAdCreateDraft = null;
    static $objAdCreateTask = null;

    /**
     * 获取创建爱你广告映射类型
     * @param $channel
     * @param $type
     * @return int
     */
    protected static function getAdCreateType($channel, $type)
    {
        switch ($type) {
            case self::DATA_TYPE_CAMPAIGN:
                $ret = Qdlib_Const_AdCreate::LEVEL_1;
                break;
            case self::DATA_TYPE_AD_GROUP:
                $ret = Qdlib_Const_AdCreate::LEVEL_2;
                break;
            case self::DATA_TYPE_AD:
                $ret = $channel == Qdlib_Const_Ads::GDT ? Qdlib_Const_AdCreate::LEVEL_3 : Qdlib_Const_AdCreate::LEVEL_2;
                break;
            case self::DATA_TYPE_CREATIVE:
                $ret = Qdlib_Const_AdCreate::LEVEL_3;
                break;
            default:
                $ret = 0;
        }
        return $ret;
    }

    /**
     * 获取广告创建任务创建的id
     * @param string $channel 渠道
     * @param string $type 类型 self::DATA_TYPE_CAMPAIGN 等
     * @param array $accounts 账号
     * @param array $ids 媒体id
     * @return array 媒体id数组（快手程序化创意返回的是unit_id）
     */
    public static function getAdCreateIds($channel, $type, $accounts = [], $ids = [])
    {
        if(self::$objAdCreateDraft==null || empty(self::$objAdCreateDraft)){
            self::$objAdCreateDraft = new Qdlib_Ds_QudaoAdCreateDraft();
        }

        $level = self::getAdCreateType($channel, $type);
        $where = compact('channel', 'level');
        if (!empty($accounts)) {
            $where[] = Qdlib_Util_DB::whereIn('account', $accounts);
        }
        if (!empty($ids)) {
            $where[] = Qdlib_Util_DB::whereIn('media_id', $ids);
        }
        $list = self::$objAdCreateDraft->getQudaoAdCreateDraftList($where, ['mediaId', 'mediaExt']);
        if (empty($list)) {
            return [];
        }
        if ($channel == Qdlib_Const_Ads::GDT && $type == self::DATA_TYPE_AD) {
            $ret = [];
            foreach ($list as $val) {
                $ret[] = $val['mediaExt']['data']['adId'];
            }
        }
        return array_column($list, 'mediaId');
    }

    /**
     * @param $channel
     * @param $type
     * @param string $account
     * @param int $id
     * @return int
     */
    public static function getAdMode($channel, $type, $account = '', $id = 0)
    {
        if (self::$objAdCreateDraft == null || empty(self::$objAdCreateDraft)) {
            self::$objAdCreateDraft = new Qdlib_Ds_QudaoAdCreateDraft();
        }
        if (self::$objAdCreateTask == null || empty(self::$objAdCreateTask)) {
            self::$objAdCreateTask = new Qdlib_Ds_QudaoAdCreateTask();
        }

        $level = self::getAdCreateType($channel, $type);

        $where = compact('channel', 'level');
        if (!empty($account)) {
            $where['account'] = $account;
        }
        if ($id > 0) {
            $where['mediaId'] = $id;
        }
        $list = self::$objAdCreateDraft->getQudaoAdCreateDraftList($where, ['taskId'], ['limit 1']);
        //非自投放产生
        if (empty($list)) {
            return Qdlib_Const_TripartiteEnum::AD_MODE_MEDIA;
        }

        $task = self::$objAdCreateTask->getQudaoAdCreateTaskInfoById($list[0]['taskId'], ['fork']);
        //任务表 fork = 1 代表叉乘产生
        return $task['fork'] == 1 ? Qdlib_Const_TripartiteEnum::AD_MODE_FORK : Qdlib_Const_TripartiteEnum::AD_MODE_LOCAL;
    }
}