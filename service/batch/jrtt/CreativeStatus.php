<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2020/2/18
 * Time: 2:22 PM
 */
class Qdlib_Service_Batch_Jrtt_CreativeStatus extends Qdlib_Service_Batch_Base implements Qdlib_Service_Batch_Interface {

    protected $objJrttCreative;

    protected $statusMap;

    public function __construct($task)
    {
        parent::__construct($task);
        $this->objJrttCreative = new Qdlib_Ds_Ad_QudaoJrttCreative();

        //参数-枚举值映射关系
        $this->statusMap = array(
            1   => Qdlib_Const_TripartiteEnum::CREATIVE_STATUS_ENABLE,
            2   => Qdlib_Const_TripartiteEnum::CREATIVE_STATUS_DISABLE,
        );
    }

    public function getUpdateQdapi() :array
    {
        $arrConds = array(
            Qdlib_Util_DB::whereIn('id', $this->ids),
            //'deleted'       => 0,
            'channel'       => Qdlib_Const_Ads::JRTT,
        );
        $ret = $this->objJrttCreative->getQudaoJrttCreativeList($arrConds, ['id', 'account']);
        if (empty($ret)) {
            throw new Exception('查询批量操作id列表失败');
        }

        //写入不存在id
        if (count($ret) != count($this->ids)) {
            $ids = array_column($ret, 'id');
            $noExists = array_diff(array_keys($this->ids), $ids);

            $where = array(
                'taskId' => $this->task['id'],
                Qdlib_Util_DB::whereIn('pid', $noExists),
            );
            $update = $this->objBatchUpdateResult->updateQudaoBatchUpdateResult($where, array(
                'errFlag'       => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errno'         => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errMsg'        => Qdlib_Const_Ads::$errorCodeMap[Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR],
                'updateTime'    => time()
            ));
            if ($update === false) {
                throw new Exception('不存在任务数据库更新失败');
            }
        }

        //写入账号数据
        $aData = [];
        foreach ($ret as $val) {
            $value = $this->getOptStatus($val['id']);
            $aData[$val['account']][$value][] = $val['id'];
        }
        $paramsAll = [];
        foreach ($aData as $account => $val) {
            foreach ($val as $value => $ids) {
                $paramsAll[] = array(
                    'channel'       => Qdlib_Const_Ads::JRTT,
                    'account'       => $account,
                    'creativeIds'   => $ids,
                    'optStatus'     => $value,
                );
            }
        }
        $pathInfo = '/qdapi/adcreative/status';
        return [$pathInfo, $paramsAll, 'creativeIds'];
    }

    public function dealUpdateRet() :bool
    {
        return true;
    }

    public function getCheckErrorIds($checkIds) :array {
        $list = $this->objJrttCreative->getQudaoJrttCreativeList(array(
            Qdlib_Util_DB::whereIn('creative_id', $checkIds),
            'channel'       => Qdlib_Const_Ads::JRTT,
        ), ['id', 'adcreativeStatus']);

        if (empty($list)) {
            throw new Exception('检测状态查询数据库失败');
        }

        $errIds = [];
        foreach ($list as $val) {
            $objective = $this->getOptStatus($val['creativeId']);
            $objective = $this->statusMap[$objective];
            if ($val['adcreativeStatus'] != $objective) {
                Qdlib_Util_Log::warning('script_batch', 'jrttCreative', 'checkErrId', '状态未改变', json_encode(compact('val', 'objective')));
                $errIds[] = $val['id'];
            }
        }
        return $errIds;
    }

    public function getNames($ids) :array
    {
        if (empty($ids)) {
            return [];
        }
        $list = $this->objJrttCreative->getQudaoJrttCreativeList([
            Qdlib_Util_DB::whereIn('id', $ids),
        ], ['id', 'creativeTitle']);
        if (empty($list)) {
            Qdlib_Util_Log::fatal('script', 'Qdlib_Service_Batch_Jrtt_CreativeStatus', 'getNames', '获取名称失败', json_encode(['ids' => $ids]));
            return [];
        }
        return array_column($list, 'creativeTitle', 'id');
    }
}