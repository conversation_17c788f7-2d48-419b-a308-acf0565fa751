<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Sell.php
 * <AUTHOR>
 * @date   2020年1月2日
 * @brief  新阿基米德库存系统 http 调用接口
 **/

class Zb_Service_Archimedes_Sell
{

    private static $service = 'archimedes';
    private static $module      = 'archimedes';
    private static $entity      = 'sell';

    /**
     * 销售-交易-库存Confirm
     * @param $arrParams
     * @return array
     */
    public static function confirmSellTradeStock($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/confirm';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'confirmSellTradeStock');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-库存Cancel
     * @param $arrParams
     * @return array
     */
    public static function cancelSellTradeStock($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/cancel';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'cancelSellTradeStock');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-转班Confirm
     * @param $arrParams
     * @return array
     */
    public static function confirmSellTradeTransfer($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/transfer/confirm';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'confirmSellTradeTransfer');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-转班Confirm
     * @param $arrParams
     * @return array
     */
    public static function cancelSellTradeTransfer($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/transfer/cancel';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'cancelSellTradeTransfer');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-库存Try
     * @param $arrParams
     * @return array
     */
    public static function trySellTradeStock($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/try';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'trySellTradeStock');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-库存Try
     * @param $arrParams
     * @return array
     */
    public static function trySellTradeTransfer($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/transfer/try';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'trySellTradeTransfer');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 销售-交易-退库存
     * @param $arrParams
     * @return array
     */
    public static function returnSellTradeTransfer($arrParams)
    {
        $serviceUri = '/archimedes/sell/trade/return';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri, self::$module, self::$entity, 'returnSellTradeTransfer');

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
