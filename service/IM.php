<?php
/**
 * @file Im.php
 * <AUTHOR>
 * @date 2017年2月10日 下午3:56:17
 * @version $Revision$ 
 * @brief IM服务
 *  
 **/
class Hkzb_Service_IM {
    /**
     * 加群
     * @param array $arrParams
     * @return Ambigous <boolean, mixed>
     */
    public static function memberAdd($arrParams)
    {

        $userRole = 1;
        $groupId  = intval($arrParams['groupId']);
        $addUid   = $arrParams['uid'];
        $courseId = $arrParams['courseId'];
        $classId  = $arrParams['classId'];

        if ($groupId <= 0) {
            return true;
        }

        $arrHeader = array(
            'pathinfo' => '/im/group/memberadd',
        );
        $arrParams = array(
            'groupId'  => intval($groupId),
            'addUid'   => intval($addUid),
            'userRole' => intval($userRole),
            'classId'  => intval($classId),
            'courseId' => $courseId,
        );

        return self::requestIM($arrHeader, $arrParams);
    }
    
    private static function requestIM($arrHeader, $arrParams) {
        $ret = ral('course', 'POST', $arrParams, 123, $arrHeader);
        if(false === $ret) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service im connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return false;
        }
        $ret = json_decode($ret, true);
        $errno = intval($ret['errNo']);
        $errmsg = strval($ret['errstr']);
        if($errno > 0) {
            Bd_Log::warning("Error:[service im process error], Detail:[errno:$errno errmsg:$errmsg]");
            return false;
        }
        return $ret['data'];
    }
    
    
}