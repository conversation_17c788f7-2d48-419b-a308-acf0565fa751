<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Sku.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 **/


class Zb_Service_Dak_Sku {

    private static $service     = 'zbcore';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dak';
    private static $entity      = 'sku';


    /**
     * 根据课程id获取基础sku 【skuType=1,2】
     * @param $courseId array
     * @param $fields
     * @param $options
     * @return array
     */
    public static function getKVByCourseId($courseId, $fields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByCourseId');
        $arrParams = array(
            'courseId'  => $courseId,
            'fields' => $fields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据skuid获取对应可调id列表
     * @param int $skuId
     * @param int $pn
     * @param int $rn (-1为全量 不做分页)
     * @return
     */
    public static function getChangeSkuIdList($skuId = 0, $pn = 0, $rn = -1, $options=array()){
        //return [1,2,3,4];
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getChangeSkuIdList');
        $arrParams = array(
            'skuId'  => $skuId,
            'pn'     => $pn,
            'rn'     => $rn,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    /**
     * 检查skuid是否可调
     * @param int $sourceSkuId
     * @param int $changeSkuId
     * @return bool
     */
    public static function judgeChangeSku($sourceSkuId = 0, $changeSkuId = 0,$options=array()){
        //return true;
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'judgeChangeSku');
        $arrParams = array(
            'sourceSkuId'  => $sourceSkuId,
            'changeSkuId'  => $changeSkuId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    /**
     * 获取实物sku列表
     * @param int $sourceSkuId
     * @param int $changeSkuId
     * @return bool
     */
    public static function getEntitySkuList($searchName = '', $searchId = 0, $pn = 0, $rn = 0){

        //return true;
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getEntitySkuList');
        $arrParams = array(
            'searchName' => $searchName,
            'searchId'   => $searchId,
            'pn'         => $pn,
            'rn'         => $rn,
            'bCache'     => 1,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param array $skuIds
     * @return array
     */
    public static function getKVBySkuIds($skuIds = []){
        //return true;
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySkuIds');
        $arrParams = array(
            'skuIds' => $skuIds
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    /**
     * @param array $skuIds
     * @param int $saleChannelId
     * @return array
     * ps:目前一次请求支持50个skuid
     */
    public static function getDeepKVBySkuIds($skuIds = [], $saleChannelId = 0){
        //return true;
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getDeepKVBySkuIds');
        $arrParams = array(
            'skuIds' => $skuIds,
            'saleChannelId' => $saleChannelId
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param $arrParams
     * $arrParams = array( 
            'gradeIds'           => array(1,2,3),
            'subjectId'         => 1,
            'price'           => 100,//分为单位
            'specialSellType' => 1,//特惠课等标记
            'learnSeason'     => array(11,12,13),//学季
            'isCombination'     => 1,//是否组合
            'pn'              => $pn,
            'rn'              => $rn, 
    );
     * @return array
     */
    public static function getChangeSkuIdListByCondition($arrParams){
        //return true;
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getChangeSkuIdsByConds');


        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    public static function getSKUDeliverTime($skuIds){
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getSKUDeliverTime');

        $arrParams = array(
            'skuIds' => $skuIds,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);

    }

}