<?php
/**
 * 添加策略
 * <AUTHOR>
 * @date 2019-10-21
 */
class Action_AddTactics extends ActPlatMis_Base
{
    /**
     * 默认调用方法
     * <AUTHOR>
     * @DateTime 2019-10-21
     * @return   array
     */
    public function invoke()
    {
        $input = [];
        $input['stopTime']      = $this->_requestParam['stopTime'] ?? 0;
        $input['startTime']     = $this->_requestParam['startTime'] ?? 0;
        $input['awardList']     = $this->_requestParam['awardList'] ?? '';
        // $input['exchangeTime']  = $this->_requestParam['exchangeTime'] ?? 0;
        $input['name']          = $this->_requestParam['name'] ?? '';
        $input['exchangeGroup'] = $this->_requestParam['exchangeGroup'] ?? '';
        $input['userInfo']      = $this->getUserInfoForMisLog();

        $model = new Service_Page_Giftcard_AddTactics();
        return $model->execute($input);
    }
}