<?php
/**
 * 导出兑换码
 * <AUTHOR>
 * @date 2019-11-18
 */
class Action_Export extends ActPlatMis_Base
{
    /**
     * 默认调用方法
     * <AUTHOR>
     * @DateTime 2019-11-18
     * @return   array
     */
    public function invoke()
    {
        $input = [];
        $input['seriesId'] = $this->_requestParam['seriesId'] ?? 0;
        $input['userInfo'] = $this->_userInfo;

        $model = new Service_Page_Cdkey_Export();
        $output = $model->execute($input);

        return $output;
    }
}