<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      BindThirdGoods.php
 *
 * @author:        <EMAIL>
 * @desc:          关联第三方商品
 * @create:        2019-09-04 15:40:40
 * @last modified: 2019-09-04 15:40:40
 */
class Action_BindThirdGoods extends ActPlatMis_Base
{
    public function invoke(){
        $arrInput = [
            'userInfo'         => $this->getUserInfoForMisLog(),
            'seriesId'         => isset($this->_requestParam['seriesId']) ? $this->_requestParam['seriesId'] : 0,
            'thirdPartyGoodId' => isset($this->_requestParam['thirdGoodId']) ? $this->_requestParam['thirdGoodId'] : '',
            'lastfrom'         => $this->_requestParam['lastfrom'] ?? '',
        ];
        Hk_Util_Log::start('ps_BindThirdGoods');
        $obj = new Service_Page_CdKey_BindThirdGoods();
        $arrOutput =  $obj->execute($arrInput);
        Hk_Util_Log::stop('ps_BindThirdGoods');
        return $arrOutput;
    }
}

