<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      GetSeriesOperInfo.php
 *
 * @author:        <EMAIL>
 * @desc:          查看操作记录
 * @create:        2019-09-04 15:42:24
 * @last modified: 2019-09-04 15:42:24
 */
class Action_GetSeriesOperInfo extends ActPlatMis_Base
{
    public function invoke(){
        $arrInput = [
            'seriesId'    => isset($this->_requestParam['seriesId']) ? (int)$this->_requestParam['seriesId'] : 0,
            'startTime'   => isset($this->_requestParam['startTime']) ? (int)$this->_requestParam['startTime'] : 0,
            'stopTime'    => isset($this->_requestParam['stopTime']) ? (int)$this->_requestParam['stopTime'] : 0,
            'operName'    => isset($this->_requestParam['operName']) ? (string)$this->_requestParam['operName'] : '',
            'type'        => !empty($this->_requestParam['type']) ? (int)$this->_requestParam['type'] : -1,
            'pn'            => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 0,
            'rn'            => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
        ];
        Hk_Util_Log::start('ps_GetSeriesOperInfo');
        $obj = new Service_Page_CdKey_GetSeriesOperInfo();
        $arrOutput =  $obj->execute($arrInput);
        Hk_Util_Log::stop('ps_GetSeriesOperInfo');
        return $arrOutput;
    }
}
