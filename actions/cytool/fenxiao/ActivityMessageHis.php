<?php
/**
 * 消息推送历史
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/12/18 10:50
 */
class Action_ActivityMessageHis extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        $paramsInput = [
            'activityId' => $this->_requestParam['activityId'] ?? 0,
            'templateId' => $this->_requestParam['templateId'] ?? '',
            'dateRange' => !empty($this->_requestParam['dateRange']) ? $this->_requestParam['dateRange'] : '',
            'uid' => $this->_requestParam['uid'] ?? 0,
            'page' => $this->_requestParam['page'] ?? 1,
            'pageSize' => $this->_requestParam['perPage'] ?? 10,
        ];


        $objPsMessagePush = new Service_Page_CYTool_MessageEngineTool();
        $ret = $objPsMessagePush->getMessagePushHis($paramsInput);
        return $ret;
    }
}