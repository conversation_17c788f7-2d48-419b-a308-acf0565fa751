<?php
/**
 * 拼团每日业务数据走势图
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/11/5 16:39
 */
class Action_GetTuanDailyCharts extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        $paramsInput = [
            'dateRange' => !empty($this->_requestParam['daterange']) ? $this->_requestParam['daterange'] : '',
            'chartType' => in_array($this->_requestParam['type'], ['line', 'bar']) ? $this->_requestParam['type'] : 'line',
            'legendPos' => in_array($this->_requestParam['legendPos'], ['left', 'right']) ? $this->_requestParam['legendPos'] : 'left',
            'lineArea' => $this->_requestParam['lineArea'] == 'true',
        ];

        $objPs = new Service_Page_CYTool_TuanTool();
        $ret = $objPs->getTuanDailyCharts($paramsInput);

        return $ret;
    }
}