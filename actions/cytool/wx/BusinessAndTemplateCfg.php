<?php
/**
 * 业务方向和消息模板配置联动
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2021/1/25 17:25
 */
class Action_BusinessAndTemplateCfg extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        // 暂时hardcode
        $ret = [
            'options' => [
                [
                    'value' => 'xiaoxuequanke',
                    'label' => '小学全科训练营',
                    'children' => [
                        ['value' => '8017', 'label' => '给老用户推送新的绑定关系信息'],
                        ['value' => '8075', 'label' => '给老用户推送奖励（已购课）信息'],
                    ]
                ]
            ]
        ];

        return $ret;
    }
}