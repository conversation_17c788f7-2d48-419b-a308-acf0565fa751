<?php
/**
 * 业务方向总点数对比
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/12/16 11:33
 */
class Action_GetTotalPointsOfBusiness extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        $paramsInput = [
            'businessLines' => !empty($this->_requestParam['businessLines']) ? $this->_requestParam['businessLines'] : '',
            'dateRange' => !empty($this->_requestParam['daterange']) ? $this->_requestParam['daterange'] : '',
            'chartType' => in_array($this->_requestParam['type'], ['line', 'bar']) ? $this->_requestParam['type'] : 'line',
            'legendPos' => in_array($this->_requestParam['legendPos'], ['left', 'right']) ? $this->_requestParam['legendPos'] : 'left',
            'lineArea' => $this->_requestParam['lineArea'] == 'true',
            'dataType' => $this->_requestParam['dataType'] ?? '1',
        ];

        $objPs = new Service_Page_CYTool_SuTool();
        $ret = $objPs->getTotalPointsOfBusiness($paramsInput);

        return $ret;
    }
}