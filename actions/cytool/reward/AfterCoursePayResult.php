<?php
/**
 * 支付侧明细
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/10/20 20:03
 */
class Action_AfterCoursePayResult extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        $paramsInput = [
            'activityId'  => $this->_requestParam['activityId'] ?? 0,
            'userId' => $this->_requestParam['userId'] ?? 0,
        ];

        $objPs = new Service_Page_CYTool_AfterCourseTool();
        $ret = $objPs->getPayResult($paramsInput);

        return $ret;
    }
}
