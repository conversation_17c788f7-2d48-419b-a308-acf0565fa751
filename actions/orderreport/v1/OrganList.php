<?php


class Action_OrganList extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),
            'founder' => (string)$this->_userInfo['uname'],
        ];
        Hk_Util_Log::start('actplatmis_afx_organlist');
        $obj       = new Service_Page_OrderReport_V1_OrganList();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_organlist');
        return $arrOutPut;
    }
}