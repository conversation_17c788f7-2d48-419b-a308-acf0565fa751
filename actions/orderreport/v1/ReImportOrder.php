<?php
/**
 * Created by PhpStorm
 * User: liyachen<PERSON>@zuoyebang.com
 * Date: 2021/8/11
 * Time: 16:23
 */

class Action_ReImportOrder extends ActPlatMis_Base
{
    protected function invoke()
    {
        $arrInput = [
            'taskId' => isset($this->_requestParam['taskId']) ? intval($this->_requestParam['taskId']) : 0,
        ];
        Hk_Util_Log::start('ReImportOrder');
        $obj = new Service_Page_OrderReport_V1_ReImportOrder();
        $arrOutput = $obj->execute($arrInput);
        Hk_Util_Log::stop('ReImportOrder');
        return $arrOutput;
    }
}