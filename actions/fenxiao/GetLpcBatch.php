<?php
/**
 * 获取例子批次信息
 * <AUTHOR>
 * @date 2020-03-04
 */
class Action_GetLpcBatch extends ActPlatMis_Base
{
    /**
     * 默认调用方法
     * <AUTHOR>
     * @DateTime 2020-03-04
     * @return   array
     */
    public function invoke()
    {
        $input = [];
        $input['configId'] = $this->_requestParam['configId'] ?? 0;

        $model = new Service_Page_Fenxiao_GetLpcBatch();
        return $model->execute($input);
    }
}