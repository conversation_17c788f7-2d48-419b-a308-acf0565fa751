<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief  权限保存
 **/
class Action_AuthSave extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'id'        => isset($this->_requestParam['id']) ? (int)$this->_requestParam['id'] : 0,
            'pid'       => isset($this->_requestParam['pid']) ? (int)$this->_requestParam['pid'] : 0,
            'type'      => isset($this->_requestParam['type']) ? (int)$this->_requestParam['type'] : Service_Data_Afx_AFXAuth::TYPE_MUNE,
            'name'      => isset($this->_requestParam['name']) ? (string)$this->_requestParam['name'] : '',
            'rank'      => isset($this->_requestParam['rank']) ? (int)$this->_requestParam['rank'] : 0,
            'url'       => isset($this->_requestParam['url']) ? (string)$this->_requestParam['url'] : '',
            'icon'      => isset($this->_requestParam['icon']) ? (string)$this->_requestParam['icon'] : '',
            'isJump'    => isset($this->_requestParam['isJump']) ? (int)$this->_requestParam['isJump'] : Service_Data_Afx_AFXAuth::JUMP_NO,
            'status'    => isset($this->_requestParam['status']) ? (int)$this->_requestParam['status'] : Service_Data_Afx_AFXAuth::STATUS_ON,
        ];

        Hk_Util_Log::start('actplatmis_afx_authsave');
        $obj       = new Service_Page_Afx_AuthSave();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_authsave');

        return $arrOutPut;
    }
}
