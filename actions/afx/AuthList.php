<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief  权限列表
 **/
class Action_AuthList extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'pn'        => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 1,
            'rn'        => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
        ];

        Hk_Util_Log::start('actplatmis_afx_authlist');
        $obj       = new Service_Page_Afx_AuthList();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_authlist');

        return $arrOutPut;
    }
}
