<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief  机构列表
 **/
class Action_ActSave extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founder' => (string)$this->_userInfo['uname'],
//            'appId'     => isset($this->_requestParam['appId']) ? (int)$this->_requestParam['appId'] : 0,
            'actId'     => isset($this->_requestParam['actId']) ? (int)$this->_requestParam['actId'] : 0,
            'actName'   => isset($this->_requestParam['actName']) ? (string)$this->_requestParam['actName'] : '',
            'businessLine' => isset($this->_requestParam['businessLine']) ? (int)$this->_requestParam['businessLine'] : 0,
            'actType'   => isset($this->_requestParam['actType']) ? (int)$this->_requestParam['actType'] : Oplib_Const_AFX::ACT_TYPE_H5,
            'url'       => isset($this->_requestParam['url']) ? trim(strval($this->_requestParam['url'])) : '',
            'apiSource' => isset($this->_requestParam['apiSource']) ? (int)$this->_requestParam['apiSource'] : 0,
            'apiPValue' => isset($this->_requestParam['apiPValue']) ? (int)$this->_requestParam['apiPValue'] : 0,
            'mark'      => isset($this->_requestParam['mark']) ? (string)$this->_requestParam['mark'] : '',
            'projectLv1'     => isset($this->_requestParam['projectLv1']) ? (int)$this->_requestParam['projectLv1'] : 0,
            'projectLv2'     => isset($this->_requestParam['projectLv2']) ? (int)$this->_requestParam['projectLv2'] : 0,
            'sTime'     => isset($this->_requestParam['sTime']) ? (int)$this->_requestParam['sTime']/1000 : 0,
            'eTime'     => isset($this->_requestParam['eTime']) ? (int)$this->_requestParam['eTime']/1000 : 0,

            'agents'    => $this->_requestParam['agents'] ?? '',

            'goodsAdd'     => !empty($this->_requestParam['goodsAdd']) && is_array($this->_requestParam['goodsAdd']) ? $this->_requestParam['goodsAdd'] : [],
            'goodsDel'     => !empty($this->_requestParam['goodsDel']) && is_array($this->_requestParam['goodsDel']) ? $this->_requestParam['goodsDel'] : [],
            'thresholdAdd'     => !empty($this->_requestParam['thresholdAdd']) && is_array($this->_requestParam['thresholdAdd']) ? $this->_requestParam['thresholdAdd'] : [],
            'thresholdDel'     => !empty($this->_requestParam['thresholdDel']) && is_array($this->_requestParam['thresholdDel']) ? $this->_requestParam['thresholdDel'] : [],

            'promoteLv1'     => isset($this->_requestParam['promoteLv1']) ? (string)$this->_requestParam['promoteLv1'] : '',
//            'tag'     => isset($this->_requestParam['tag']) ? (int)$this->_requestParam['tag'] : 0, // 废弃
            'channelLabels'     => isset($this->_requestParam['channelLabels']) ? (string)$this->_requestParam['channelLabels'] : '', // 推广标签，逗号隔开

            'promoteGradeDepts'     => isset($this->_requestParam['promoteGradeDepts']) ? (string)$this->_requestParam['promoteGradeDepts'] : '', // 伏羲合作机构-学部
            'promoteLv1New'     => isset($this->_requestParam['promoteLv1New']) ? (int)$this->_requestParam['promoteLv1New'] : 0, // 伏羲合作机构-新推广产品
        ];

        $arrInput['promoteBusiness'] = Qdlib_Const_ProjectAttr::getPromoteBusinessByAppId($arrInput['appId']); // 伏羲合作机构-业务线（基于应用方映射）

        Hk_Util_Log::start('actplatmis_afx_actsave');
        $obj       = new Service_Page_Afx_ActSave();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_actsave');

        return $arrOutPut;
    }
}
