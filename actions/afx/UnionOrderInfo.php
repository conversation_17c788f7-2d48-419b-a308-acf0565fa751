<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   UnionOrderInfo.php
 * <AUTHOR>
 * @brief  订单信息
 * @link   http://yapi.zuoyebang.cc/project/5845/interface/api/214930
 **/
class Action_UnionOrderInfo extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),
            'founder' => (string)$this->_userInfo['uname'],

            // 主订单ID
            'orderId' => isset($this->_requestParam['orderId']) ? (int)$this->_requestParam['orderId'] : 0,
            // 买家uid
            'userId'       => isset($this->_requestParam['userId']) ? (int)$this->_requestParam['userId'] : 0,
        ];
        Hk_Util_Log::start('hetu_afx_unionorderinfo');
        $objAct = new Service_Data_Afx_AFXAct();
        $objUnionOrder = new Qdlib_Service_Hetu_AFXUnionOrderInfo($objAct);
        $arrOutPut = $objUnionOrder->execute($arrInput);
        Hk_Util_Log::stop('hetu_afx_unionorderinfo');

        return $arrOutPut;
    }
}
