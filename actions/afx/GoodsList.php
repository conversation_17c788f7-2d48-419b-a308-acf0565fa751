<?php

/**
 * 分销商品管理-商品列表
 * <AUTHOR>
 */
class Action_GoodsList extends ActPlatMis_Base
{

    public function invoke()
    {
        $arrInput = [
            'uid' => $this->_userInfo['uid'],
            'actId' => isset($this->_requestParam['actId']) ? intval($this->_requestParam['actId']) : 0,
            'appId' => isset($this->_requestParam['appId']) ? intval($this->_requestParam['appId']) : 0,
            'showSaleInfo' => isset($this->_requestParam['showSaleInfo']) ? intval($this->_requestParam['showSaleInfo']) : 0,
            'pn' => isset($this->_requestParam['pn']) ? intval($this->_requestParam['pn']) : 1,
            'rn' => isset($this->_requestParam['rn']) ? intval($this->_requestParam['rn']) : 20,
        ];
        Hk_Util_Log::start('ps_afx_goods_goodslist');
        $obj = new Service_Page_Afx_GoodsList();
        $arrOutput = $obj->execute($arrInput);
        Hk_Util_Log::stop('ps_afx_goods_goodslist');
        return $arrOutput;
    }

}
