<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief  机构列表
 **/
class Action_ActList extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),
            'appId' => isset($this->_requestParam['appId']) ? strval($this->_requestParam['appId']) : '',
            'promoteBusiness' => isset($this->_requestParam['promoteBusiness']) ? (int)($this->_requestParam['promoteBusiness']) : 0,
            'actId' => isset($this->_requestParam['actId']) ? (int)$this->_requestParam['actId'] : 0,
            'actName' => isset($this->_requestParam['actName']) ? (string)$this->_requestParam['actName'] : '',
            'urlKeywords' => isset($this->_requestParam['urlKeywords']) ? (string)$this->_requestParam['urlKeywords'] : '',
            'status' => isset($this->_requestParam['status']) ? (int)$this->_requestParam['status'] : Service_Data_Afx_AFXAct::STATUS_NOT_ALL,
            'sTime' => isset($this->_requestParam['sTime']) ? (int)$this->_requestParam['sTime'] : 0,
            'eTime' => isset($this->_requestParam['eTime']) ? (int)$this->_requestParam['eTime'] : 0,
            'pn' => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 1,
            'rn' => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
            'type' => isset($this->_requestParam['type']) ? (int)$this->_requestParam['type'] : 1,
            'promoteLv1' => isset($this->_requestParam['promoteLv1']) ? (string)$this->_requestParam['promoteLv1'] : '',
//            'tag'        => isset($this->_requestParam['tag']) ? (int)$this->_requestParam['tag'] : 0,
            'channelLabel' => isset($this->_requestParam['channelLabel']) ? (int)$this->_requestParam['channelLabel'] : 0,
        ];

        Hk_Util_Log::start('actplatmis_afx_actlist');
        $obj = new Service_Page_Afx_ActList();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_actlist');

        return $arrOutPut;
    }
}
