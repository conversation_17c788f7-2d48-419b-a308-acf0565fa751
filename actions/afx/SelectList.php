<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   SelectList.php
 * <AUTHOR>
 * @brief  机构列表
 **/
class Action_SelectList extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),

            'appId'    => isset($this->_requestParam['appId']) ? $this->_requestParam['appId'] : '',
            'apiSource'=> isset($this->_requestParam['apiSource']) ? $this->_requestParam['apiSource'] : '',
            'uid'      => $this->_userInfo['uid'],
            'uname'    => $this->_userInfo['uname'] ?? '',
            'dataType' => isset($this->_requestParam['dataType']) ? $this->_requestParam['dataType'] : [],

            'agentRole' => isset($this->_requestParam['agentRole']) ? $this->_requestParam['agentRole'] : 0,
            'agentKeyIsId' => isset($this->_requestParam['agentKeyIsId']) ? $this->_requestParam['agentKeyIsId'] : 0,
            'organProvince' => isset($this->_requestParam['organProvince']) ? (string)$this->_requestParam['organProvince'] : '',
            'organCity' => isset($this->_requestParam['organCity']) ? (string)$this->_requestParam['organCity'] : '',
            'organArea' => isset($this->_requestParam['organArea']) ? (string)$this->_requestParam['organArea'] : '',
            'organName' => isset($this->_requestParam['organName']) ? (string)$this->_requestParam['organName'] : '',
            'agentName' => isset($this->_requestParam['agentName']) ? (string)$this->_requestParam['agentName'] : '',
            'agentShow' => isset($this->_requestParam['agentShow']) ? (int)$this->_requestParam['agentShow'] : 0,
            'businessLine' => isset($this->_requestParam['businessLine']) ? (int)$this->_requestParam['businessLine'] : 0,
            'cadre'     => isset($this->_requestParam['cadre']) ? (string)$this->_requestParam['cadre'] : '',
        ];

        Hk_Util_Log::start('actplatmis_afx_selectlist');
        $obj       = new Service_Page_Afx_SelectList();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_selectlist');

        return $arrOutPut;
    }
}
