<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief  机构列表
 **/
class Action_ActSaleDetail extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founder'   => (string)$this->_userInfo['uname'],
            'appId'     => isset($this->_requestParam['appId']) ? (int)$this->_requestParam['appId'] : 0,
            'actId'     => isset($this->_requestParam['actId']) ? (int)$this->_requestParam['actId'] : 0,
            'agentIds'  => $this->_requestParam['agentIds'] ?? '',
        ];

        $this->check($arrInput);

        Hk_Util_Log::start('actplatmis_afx_ActSaleRulesAdd');
        $obj       = new Service_Page_Afx_ActSaleDetail();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_ActSaleRulesAdd');

        return $arrOutPut;
    }

    private function check($arrInput){
        if (empty($arrInput['actId'])){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '活动id或规则非法');
        }
        return true;
    }
}
