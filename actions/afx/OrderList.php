<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   OrderList.php
 * <AUTHOR>
 * @brief  分销记录列表
 **/
class Action_OrderList extends ActPlatMis_Base
{
    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),
            'founder' => (string)$this->_userInfo['uname'],

            'appId'     => isset($this->_requestParam['appId']) ? (int)$this->_requestParam['appId'] : 1,
            'sourceId' => isset($this->_requestParam['sourceId']) ? (int)$this->_requestParam['sourceId'] : 0,
            'categoryPid' => isset($this->_requestParam['categoryPid']) ? (int)$this->_requestParam['categoryPid'] : 0,
            'categoryId'     => isset($this->_requestParam['categoryId']) ? (int)$this->_requestParam['categoryId'] : 0,
            'skuId'          => isset($this->_requestParam['skuId']) ? (string)$this->_requestParam['skuId'] : '',
            'roleId'          => isset($this->_requestParam['roleId']) ? (int)$this->_requestParam['roleId'] : 0,
            'buyerUid'       => isset($this->_requestParam['buyerUid']) ? (int)$this->_requestParam['buyerUid'] : 0,
            'goodsName'      => isset($this->_requestParam['goodsName']) ? (string)$this->_requestParam['goodsName'] : '',
            'mobile'         => isset($this->_requestParam['mobile']) ? (int)$this->_requestParam['mobile'] : 0,
            'sTime'          => isset($this->_requestParam['sTime']) ? (int)$this->_requestParam['sTime'] : 0,
            'eTime'          => isset($this->_requestParam['eTime']) ? (int)$this->_requestParam['eTime'] : 0,
            'cadre'          => isset($this->_requestParam['cadre']) ? (string)$this->_requestParam['cadre'] : '',
            'modelType'      => isset($this->_requestParam['modelType']) ? (int)$this->_requestParam['modelType'] : 0,
            'cooperationType'=> isset($this->_requestParam['cooperationType']) ? (int)$this->_requestParam['cooperationType'] : 0,
            'agentUid'      => isset($this->_requestParam['agentUid']) ? (int)$this->_requestParam['agentUid'] : 0,
            'uids'           => isset($this->_requestParam['uids']) ? (string)$this->_requestParam['uids'] : '',
            'actId'          => isset($this->_requestParam['actId']) ? (int)$this->_requestParam['actId'] : 0,
            'province'       => isset($this->_requestParam['province']) ? (string)$this->_requestParam['province'] : '',
            'city'           => isset($this->_requestParam['city']) ? (string)$this->_requestParam['city'] : '',
            'area'           => isset($this->_requestParam['area']) ? (string)$this->_requestParam['area'] : '',
            'organId'        => isset($this->_requestParam['organId']) ? (string)$this->_requestParam['organId'] : '',
            'pn'             => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 0,
            'rn'             => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
        ];

        $makeCsv = $this->_requestParam['makeCsv']==true ? true : false;
        if($makeCsv){
            $arrInput['pn'] = 1;
            $arrInput['rn'] = 3000;
        }

        Hk_Util_Log::start('actplatmis_afx_orderlist');
        $obj       = new Service_Page_Afx_OrderListByDoris();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('actplatmis_afx_orderlist');

        return $arrOutPut;
    }
}
