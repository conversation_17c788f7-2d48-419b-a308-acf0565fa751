<?php

/**
 * 数据看板-下单明细
 * <AUTHOR>
 */
class Action_DataOrderList extends ActPlatMis_Base
{

    public function invoke()
    {
        $arrInput = [
            'founderRole' => $this->getUserDataPermissionInfo(),
            'founder' => (string)$this->_userInfo['uname'],

            'appId'     => isset($this->_requestParam['appId']) ? (int)$this->_requestParam['appId'] : 0,
            'brandType' => isset($this->_requestParam['brandType']) ? (int)$this->_requestParam['brandType'] : 0,
            'uid' => isset($this->_requestParam['uid']) ? (int)$this->_requestParam['uid'] : 0,
            'skuId' => isset($this->_requestParam['skuId']) ? (int)$this->_requestParam['skuId'] : 0,
            'agentRole' => isset($this->_requestParam['agentRole']) ? (int)$this->_requestParam['agentRole'] : 0,
            'inst' => isset($this->_requestParam['inst']) ? (int)$this->_requestParam['inst'] : 0,
            'sTime' => isset($this->_requestParam['sTime']) ? (int)$this->_requestParam['sTime'] : 0,
            'eTime' => isset($this->_requestParam['eTime']) ? (int)$this->_requestParam['eTime'] : 0,
            'pn' => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 0,
            'rn' => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 0,
        ];
        Hk_Util_Log::start('ps_afx_dataorderlist');
        $obj = new Service_Page_Afx_DataOrderList();
        $arrOutput = $obj->execute($arrInput);
        Hk_Util_Log::stop('ps_afx_dataorderlist');

        return $arrOutput;
    }

}
