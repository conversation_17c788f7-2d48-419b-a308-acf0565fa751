<?php
/**
 * 编辑任务
 * <AUTHOR>
 * @date 2019-12-25
 */
class Action_ChangeTask extends ActPlatMis_Base
{
    protected $outputErrStr = 'errStr';

    /**
     * 默认执行方法
     * <AUTHOR>
     * @DateTime 2020-11-20
     * @return   [type]                [description]
     */
    public function invoke()
    {
        $input = [];
        $input['id']        = $this->_requestParam['id'] ?? 0;
        $input['source']    = $this->_requestParam['source'] ?? '';
        $input['name']      = $this->_requestParam['name'] ?? '';
        $input['startTime'] = $this->_requestParam['startTime'] ?? 0;
        $input['stopTime']  = $this->_requestParam['stopTime'] ?? 0;
        $input['include']   = $this->_requestParam['include'] ?? '';
        $input['exclude']   = $this->_requestParam['exclude'] ?? '';
        $input['taskInfo']  = $this->_requestParam['taskInfo'] ?? '';
        $input['userInfo']  = $this->getUserInfoForMisLog();

        $model = new Service_Page_Task_ChangeTask();
        return $model->execute($input);
    }
}