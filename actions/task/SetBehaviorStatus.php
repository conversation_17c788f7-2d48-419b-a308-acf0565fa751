<?php
/**
 * 设置动作状态
 * <AUTHOR>
 * @date 2019-12-25
 */
class Action_SetBehaviorStatus extends ActPlatMis_Base
{
	protected $outputErrStr = 'errStr';
	/**
	 * 默认执行方法
	 * <AUTHOR>
	 * @DateTime 2020-11-20
	 * @return   [type]                [description]
	 */
    public function invoke()
    {
        $input = [];
        $input['id']       = $this->_requestParam['id'] ?? 0;
        $input['status']   = $this->_requestParam['status'] ?? 0;
        $input['userInfo'] = $this->getUserInfoForMisLog();

        $model = new Service_Page_Task_SetBehaviorStatus();
        return $model->execute($input);
    }
}