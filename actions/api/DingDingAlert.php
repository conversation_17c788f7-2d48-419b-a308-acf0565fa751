<?php
/**
 * 钉钉报警
 * <AUTHOR>
 * @date 2021/01/21
 */
class Action_DingDingAlert extends ActPlatMis_Base
{
    /**
     * 默认执行方法
     * <AUTHOR>
     * @DateTime 2021-01-21
     * @return   [type]                [description]
     */
    public function invoke()
    {
        $arrInput = [];

        $arrInput['email']   = $this->_requestParam['email'] ?? '';
        $arrInput['message'] = $this->_requestParam['message'] ?? '';
        $arrInput['mark']    = $this->_requestParam['mark'] ?? '';
        
        Hk_Util_Log::start('ps_dingdingalert');
        (new Service_Page_Api_DingDingAlert())->execute($arrInput);
        Hk_Util_Log::stop('ps_dingdingalert');
        return [];
    }
}