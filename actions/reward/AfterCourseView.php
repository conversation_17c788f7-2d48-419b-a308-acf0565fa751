<?php
/**
 * 打款详情
 * User: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/9/15
 * Time: 15:46
 */

class Action_AfterCourseView extends ActPlatMis_MisxBase
{
    public function invoke()
    {
        $paramsInput = [
            'activityId' => $this->_requestParam['activityId'] ?? 0,
        ];

        Hk_Util_Log::stop('ts_ps');
        $objPs = new Service_Page_Reward_AfterCourseView();
        $ret   = $objPs->execute($paramsInput);
        Hk_Util_Log::stop('ts_ps');

        return $ret;
    }
}