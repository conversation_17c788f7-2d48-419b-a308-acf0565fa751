<?php
include_once __DIR__ . '/../../Init.php';

/*
 * https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=336679469
 */

class OrderV8AfterType
{
    private $objDorisUnionOrder;
    private $objOriginOrder;
    private $objSkuOrder;
    private $objRedis;
    private $dsUnionOrder;
    private $dsUnionSku;
    const ORDERV8_AFTERTYPE_REDIS_KEY = "ORDERV8_AFTERTYPE_REDIS_KEY";
    const Token = "27ed53c85cd179a8dfb9bcc04f79cbba2e7ac2b940f7bb449aa2d491c59a6ef6";

    const StartPayTime = 1635696000;//2021-11-01
    //todo 命令上线时间
    const EndTime = 1645718400;//2022-02-25

    public function __construct()
    {
        $this->objDorisUnionOrder = new Qdlib_Ds_Hetu_AFXDorisUnionOrder();
        $this->objOriginOrder = new Qdlib_Ds_Hetu_AFXUnionOrder();
        $this->objSkuOrder = new Qdlib_Ds_Hetu_AFXUnionSkuOrder();
        $this->objRedis = Qdlib_Util_Cache::getQudaoRedis();
        $this->dsUnionSku = new Qdlib_Dao_Hetu_AFXUnionSkuOrder();
        $this->dsUnionOrder = new Qdlib_Dao_Hetu_AFXUnionOrder();

    }

    public function getTestData($list)
    {
        if (Qdlib_Util_Tool::getEnviron() == 'test') {
            $list = [];
//            array_unshift($list,["userId" => 2000005043, "orderId" => 2101755964]);
            $list[] = ["userId" => 2000073441, "orderId" => 2101810603];
            $list[] = ["userId" => 2000005043, "orderId" => 2101755964];
        }
        return $list;
    }

    public function run($argv)
    {
        $finalOrderAfterType = [];//["主订单id":"afterType"]
        $finalSkuAfterType = [];//["sku订单id":["afterType":"","afterExt":"[]"]]

        $t1 = microtime(true);
        Qdlib_Util_Log::addNotice("OrderV8AfterType-start", "start");
        $offset = $this->objRedis->get(self::ORDERV8_AFTERTYPE_REDIS_KEY) ?? 0;
        if ($argv[2] > 0) { //offset
            $offset = $argv[2];
        }
        $limit = $argv[1] ?? 1000;//limit
        $order = ' pay_time asc';

        //doris现有订单数据
        $list = $this->objDorisUnionOrder->getList(["pay_time >= " . self::StartPayTime, "pay_time <= " . self::EndTime], ['orderId', 'userId', 'payTime'], $order, $offset, $limit);
        //todo 线下测试数据
//        $list = $this->getTestData($list);

        if (empty($list)) {
            Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 数据更新已结束：{$offset}", false, [15612161750], self::Token);
            return;
        }
        foreach ($list as $item) {
            list($afterTypeList, $fieldAfterType, $originAfterType) = $this->getAfterType($item['userId'], $item['orderId'], $offset);
            // test 类型：无售后 判断
            // unset($fieldAfterType[445408968]);

            if (!empty($fieldAfterType)) {
                $originAfterType = array_values($originAfterType);
                //判断主订单下的子订单是否有 "无" 售后服务类型
                $orderAfterType = $this->dealOriginAfterFields($fieldAfterType, $originAfterType, $item['orderId'], $item['userId']);
                $finalOrderAfterType[$item['orderId']] = $orderAfterType;
                //子订单fields
                foreach ($fieldAfterType as $skuOrderId => $value) {
                    $finalSkuAfterType[$skuOrderId] = [
                        "afterType" => implode(",", $value),
                        "afterExt" => json_encode($afterTypeList[$skuOrderId]),
                    ];
                }
            }
        }

        echo "主订单afterType \n";
        print_r($finalOrderAfterType);
        echo "\n";
        echo "sku订单afterType \n";
        print_r($finalSkuAfterType);
        echo "\n";

        if (!empty($finalOrderAfterType)) {
            Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 获取skuOrder数据 " . implode(',', array_keys($finalSkuAfterType)), false, [15612161750], self::Token);
            Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 获取Order数据 " . implode(',', array_keys($finalOrderAfterType)), false, [15612161750], self::Token);

            $this->dealSkuUpdate($finalSkuAfterType);
            $this->dealOriginUpdate($finalOrderAfterType);
        }

        $t2 = microtime(true);
        Qdlib_Util_Log::addNotice("OrderV8AfterType-end", "耗时[" . round($t2 - $t1, 3) . "秒]");

        echo "OrderV8AfterType-耗时[" . round($t2 - $t1, 3) . "秒]\n";

        $finalOffset = $offset + $limit;
        Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 扫描数据至第：{$finalOffset} 条", false, [15612161750], self::Token);

        $this->objRedis->set(self::ORDERV8_AFTERTYPE_REDIS_KEY, $finalOffset);
    }

    public function dealOriginUpdate($finalOrderAfterType)
    {
        $sql = "UPDATE tblAFXUnionOrder SET after_type = CASE order_id ";
        foreach ($finalOrderAfterType as $orderId => $afterType) {
            $sql .= sprintf("WHEN %d THEN '%s' ", $orderId, $afterType);
        }
        $idArr = array_keys($finalOrderAfterType);
        $ids = Qdlib_Util_DB::whereIn("order_id", $idArr);
        $sql .= "END WHERE $ids";

        print_r($sql);
        echo "\n";

        $this->dsUnionOrder->query($sql);
    }

    public function dealSkuUpdate($finalSkuAfterType)
    {
        $faildRet = [];
        foreach ($finalSkuAfterType as $skuOrderId => $field) {
            $ret = $this->dsUnionSku->updateByConds(["skuOrderId" => $skuOrderId], $field);
            if ($ret === false) {
                $faildRet[] = $skuOrderId . " " . $field['afterType'] . " " . $field['afterExt'];
            }
        }
        if (!empty($faildRet)) {
            Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 更新子订单afterType失败 " . implode("--", $faildRet), false, [15612161750], self::Token);
        }
    }

    public function dealOriginAfterFields($fieldAfterType, $originAfterType, $orderId, $userId)
    {
        //todo test
//        $orderId = 1077582955;
//        $userId = 2512879390;

        //子订单数
        $skuOrderCnt = $this->objDorisUnionOrder->getCnt(["order_id = " . $orderId, "user_id = " . $userId], true);
        //表tblAFXUnionOrder
        if (count($fieldAfterType) < $skuOrderCnt) {
            array_unshift($originAfterType, -1);
        }

        return implode(',', $originAfterType);
    }

    public function getAfterType($uid, $orderId, $offset)
    {
        $ret = [];
        $skuAfterType = [];
        $originAfterType = [];
        if (Qdlib_Util_Tool::getEnviron() == 'test') {
            //doris是线上数据，测试环境取线上售后服务单
            $url = "http://*************:8099/asc/after/detaillistbyorderid?userId=%d&orderId=%d";
            $url = sprintf($url, $uid, $orderId);
            list($response, $curlErrStr) = Qdlib_Util_Curl::_get($url, 8);

            if (!empty($curlErrStr)) {
                Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 扫描数据脚本：hetu/script/afx/repire/OrderV8AfterType.php 调用 /asc/after/detaillistbyorderid 失败；orderId：$orderId;offset：$offset", false, [15612161750], self::Token);
                exit();
            }
            $responseInfo = json_decode($response, true);
            $data = $responseInfo['data']['afterList'];
        } else {
            $data = Qdlib_Service_Moat_Asc::returnAfterDetailOrFalse($uid, $orderId);
            if ($data === false) {
                Qdlib_Util_DingtalkRobot::sendDingtalk("【售后类型】- 扫描数据脚本：hetu/script/afx/repire/OrderV8AfterType.php 调用 /asc/after/detaillistbyorderid 失败；orderId：{$orderId};offset：{$offset}", false, [15612161750], self::Token);
                exit();
            }
        }

        if (empty($data)) {
            return [$ret, $skuAfterType, $originAfterType];
        }

        if (!empty($data) && is_array($data)) {
            foreach ($data as $afterId => $item) {
                //售后单已经生效
                if ($item['status'] != 3 || empty($item['afterShareSKUOrderList']) || !is_array($item['afterShareSKUOrderList'])) {
                    continue;
                }
                $afterType = $item['afterType'];
                $skuList = $item['afterShareSKUOrderList'];
                foreach ($skuList as $skuOrderId => $skuItem) {
                    if ($skuItem['isGift']) { //过滤赠品
                        continue;
                    }
                    $skuAfterType[$skuOrderId][] = $afterType;
                    $originAfterType[$afterId] = $afterType;//主订单售后服务类型，根据afterId去重
                    $ret[$skuOrderId][] = [
                        "type" => $afterType,
                        "num" => $skuItem['applyCnt'],
                        "afterId" => $afterId,
                    ];
                }
            }
        }
        return [$ret, $skuAfterType, $originAfterType];
    }
}

$toggleCron = new OrderV8AfterType();
$toggleCron->run($argv);

exit();