<?php
Bd_Init::init('hetu');

/**
 * 测试例行主任务
 * Class MainTask
 */
class MainTask
{
    function __construct()
    {
    }

    public function run()
    {
        echo __CLASS__.'::'.__FUNCTION__.PHP_EOL;

        Bd_Log::notice('before run: main task is ready');
        exec('cd /home/<USER>/app/hetu/script/afx/test; php SubTask.php', $output, $retval);
        var_dump($output, $retval);
        Bd_Log::notice('after run: main task is finished');
    }
}

$mainTask = new MainTask();
$mainTask->run();
