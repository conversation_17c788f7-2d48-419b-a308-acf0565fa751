<?php

/**
 * 更新商品分类分销渠道销量,每天 1:00 执行一次
 */
Bd_Init::init('hetu');

class UpateGoodsCatSaleNum
{

    private $fp;
    private $lockFile;
    private $objDaoAFXSkuCategory;
    private $objDaoAFXSku;

    function __construct()
    {
        $this->lockFile = sys_get_temp_dir() . '/actplatmis.' . __CLASS__ . '.lock';
        $this->objDaoAFXSkuCategory = new Dao_Afx_AFXSkuCategory();
        $this->objDaoAFXSku = new Dao_Afx_AFXSku();
    }

    private function lock()
    {
        $this->fp = fopen($this->lockFile, 'w+');
        if (flock($this->fp, LOCK_EX | LOCK_NB)) {
            return $this->fp;
        }
        return false;
    }

    private function unLock()
    {
        flock($this->fp, LOCK_UN);
        fclose($this->fp);
        unlink($this->lockFile);
    }

    private function process()
    {
        $arrFields = [
            "id",
            "appId",
            "sourceId",
            "categoryPid",
            "categoryId",
            "group_concat(sku_id) as skuList",
        ];
        $now = time();
        $arrConds = [
            "startTime" => [$now, '<='],
            "endTime" => [$now, '>'],
        ];

        $offset = 0;
        $len = 1000;
        $loop = true;
        while ($loop) {
            $arrAppends = [
                "group by category_pid,category_id",
                "order by id asc",
                "limit {$offset},{$len}"
            ];
            $results = $this->objDaoAFXSku->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
            if (empty($results)) {
                $loop = false;
            } else {
                foreach ($results as $row) {
                    if (strlen($row['skuList'])) {
                        $sql = "select count(*) as saleNum from tblAFXOrder where app_id ={$row['appId']} and sku_id in (" . $row['skuList'] . ") and status=10";
                        $result = $this->objDaoAFXSku->query($sql);
                        $saleNum = intval($result[0]['saleNum']);
                        if ($saleNum > 0) {
                            $arrConds = [
                                'appId' => $row['appId'],
                                'categoryPid' => $row['categoryPid'],
                                'categoryId' => $row['categoryId'],
                            ];
                            $arrFields = [
                                'saleNum' => $saleNum,
                            ];
                            $this->objDaoAFXSkuCategory->updateByConds($arrConds, $arrFields);
                        }
                    }
                }
                $offset += $len;
            }
        }
    }

    public function run($where = [])
    {
        if (false === $this->lock())
            return false;
        $this->process();
        $this->unLock();
        return true;
    }

}

try {
    echo '更新商品分类分销渠道销量...' . PHP_EOL;
    $obj = new UpateGoodsCatSaleNum();
    $obj->run();
    echo '更新商品分类分销渠道销量完成' . PHP_EOL;
} catch (Exception $e) {
    echo $e->getMessage();
}
