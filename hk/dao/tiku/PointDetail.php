<?php


/**
 * @brief 知识点详细内容
 */
class Hk_Dao_Tiku_PointDetail extends Hk_Common_BaseDao{
    public function __construct(){
        //$this->_dbName = "tikumis/tiku_offline";
        $this->_dbName = "homework/homework";
        //$this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_db = null;
        $this->_table = "tblPointDetail";
        $this->arrFieldsMap = array(
            'pointId' => 'pointId',
            'courseId' => 'courseId',
            'gradeId' => 'gradeId',
            'rank' => 'rank',
            'code' => 'code',
            'title' => 'title',
            'content' => 'content',
            'ext' => 'ext',
            'parentId' => 'parentId',
            'inTree' => 'inTree',//3复习树 1知识树（菁优树）
            'deleted' => 'deleted',
            'parentId_3x' => 'parentId_3x',//3.5级、3.7级知识点的支持
        );

        $this->arrTypesMap = array(
            'pointId' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'gradeId' => Hk_Service_Db::TYPE_INT,
            'rank' => Hk_Service_Db::TYPE_INT,
            'code' => Hk_Service_Db::TYPE_STR,
            'title' => Hk_Service_Db::TYPE_STR,
            'content' => Hk_Service_Db::TYPE_JSON,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'parentId' => Hk_Service_Db::TYPE_INT,
            'parentId_3x' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'inTree' => Hk_Service_Db::TYPE_INT,
        );

    }
}
