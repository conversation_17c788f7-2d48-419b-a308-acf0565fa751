<?php


/**
 * 发布任务dao
 *
 * @since 1.2 增加uda相关内容
 *
 * @filesource hk/dao/misuser/PublishTask.php
 * <AUTHOR>
 * @version 1.2
 * @date    2017-01-05
 */
class Hk_Dao_MisUser_PublishTask extends Hk_Common_Dao {


    public function init() {
        $this->_dbConf   = "mobile/nativeapp";
        $this->tableName = "tblPublishTask";
        $this->fields    = $this->getFields($this->tableName);
    }

    /**
     * 获取指定策略
     *
     * @param int          $taskId
     * @return array
     */
    public function getTaskById($taskId) {
        $where = array(
            "id ="     => $taskId,
            "status =" => 0,
        );
        $task  = $this->find($this->fields, $where);
        if (!empty($task)) {
            $task["pkg_urls"] = @json_decode($task["pkg_urls"], true);
            $task["strategy"] = @json_decode($task["strategy"], true);
            $task["uda_ids"]  = @json_decode($task["uda_ids"],  true);
        }
        return $task;
    }

    /**
     * 获取当前处于【发布中、等待】状态，指定APP产品线的所有发布任务。<br>
     * added 2016-07-08 支持多产品线
     *
     * @param string       $appId
     * @return array
     */
    public function getPublishTasks($appId)
    {
        $tasks  = array();
        $where  = array(
            "app_id =" => $appId,
            $this->createIN(array(
                Hk_Ds_Publish_Const::PUB_WAIT,
                Hk_Ds_Publish_Const::PUB_ING
            ), "pub_status"),
            "status =" => 0,
        );
        $fields = array(
            "id",
            "type",
            "app_id",
            "os_type",
            "vc",
            "tip_title",
            "tip_content",
            "tip_url",
            "pkg_urls",
            "strategy",
            "force_up",
            "up_limit",
            "uda_ids",
            "start_at",
            "end_at",
            "priority",
            "router_version",
        );
        $ret    = $this->select($fields, $where);
        if (!empty($ret)) {
            foreach ($ret as $task) {
                $taskId                 = intval($task["id"]);
                $task["id"]             = $taskId;
                $task["type"]           = intval($task["type"]);
                $task["vc"]             = intval($task["vc"]);
                $task["force_up"]       = intval($task["force_up"]);
                $task["up_limit"]       = intval($task["up_limit"]);
                $task["priority"]       = intval($task["priority"]);
                $task["pkg_urls"]       = @json_decode($task["pkg_urls"], true);
                $task["strategy"]       = @json_decode($task["strategy"], true);
                $task["uda_ids"]        = @json_decode($task["uda_ids"], true);
                $task["router_version"] = intval($task["router_version"]);
                $tasks[$taskId]         = $task;
            }
        }
        return $tasks;
    }

    /**
     * 获取当前所有的等待发布，正在发布的活跃任务。
     *
     * @return array
     */
    public function getActiveTasks()
    {
        $tasks  = array();
        $where  = array(
            $this->createIN(array(
                Hk_Ds_Publish_Const::PUB_WAIT,
                Hk_Ds_Publish_Const::PUB_ING,
                Hk_Ds_Publish_Const::PUB_PAUSE
            ), "pub_status"),
            "status =" => 0,
        );
        $fields = array(
            "id",
            "up_limit",
            "uda_ids",
            "start_at",
            "end_at",
            "pub_status",
            "created_at",
            "router_version",
        );
        $ret    = $this->select($fields, $where);
        if (!empty($ret)) {
            foreach ($ret as $task) {
                $taskId                 = intval($task["id"]);
                $task["id"]             = $taskId;
                $task["up_limit"]       = intval($task["up_limit"]);
                $task["start_at"]       = intval($task["start_at"]);
                $task["end_at"]         = intval($task["end_at"]);
                $task["uda_ids"]        = @json_decode($task["uda_ids"], true);
                $task["pub_status"]     = intval($task["pub_status"]);
                $task["router_version"] = intval($task["router_version"]);
                $tasks[$taskId]         = $task;
            }
        }
        return $tasks;
    }

    /**
     * 获取发布任务列表
     *
     * @param int          $pn
     * @param int          $limit
     * @param array        $pubStatus
     * @return array
     */
    public function getTaskList($pn = 1, $limit = 30, array $pubStatus = array()) {
        $where  = array(
            "status =" => 0,
        );
        if (!empty($pubStatus)) {
            $where[] = $this->createIN($pubStatus, "pub_status");
        }
        $count  = $this->selectCount($where);           # 任务数量
        $append = array(
            sprintf("ORDER BY id DESC"),
            sprintf("LIMIT %d, %d", ($pn - 1) * $limit, $limit),
        );

        $result = array();
        $tasks  = $this->select($this->fields, $where, $append);
        if (!empty($tasks)) {
            foreach ($tasks as $task) {
                $taskId                 = intval($task["id"]);
                $task["uda_ids"]        = @json_decode($task["uda_ids"], true);
                $task["pkg_urls"]       = @json_decode($task["pkg_urls"], true);
                $task["strategy"]       = @json_decode($task["strategy"], true);
                $task["pub_summary"]    = @json_decode($task["pub_summary"], true);
                $task["router_version"] = intval($task["router_version"]);
                $result[$taskId]        = $task;
            }
        }
        return array(
            'total' => $count,
            'tasks' => $result,
        );
    }

    /**
     * 更新任务状态<br>
     * 如果任务状态已完成则删除redis数据
     *
     * @param int        $taskId
     * @param int        $pubStatus
     * @param array      $upDetail 升级任务统计数据，状态为完成时回填该数据
     * @return boolean
     */
    public function updateTaskStatus($taskId, $pubStatus, $upDetail = array()) {
        $task   = $this->getTaskById($taskId);
        if (empty($task)) {
            return true;
        }

        $where  = array(
            "id =" => $taskId,
        );
        $data   = array(
            "pub_status" => $pubStatus,
            "updated_at" => date("Y-m-d H:i:s"),
        );

        $udaIds = $task["uda_ids"];
        $logger = new Hk_Ds_Publish_Logger();
        if (!empty($upDetail) && Hk_Ds_Publish_Const::PUB_FIN === $pubStatus) {         # 任务被标记完成，记录升级统计数据并删除redis/uda中的统计任务
            $logger->delTaskLogger($taskId);                    # 删除redis统计任务
            Hk_Ds_Publish_UdaLogger::delete($udaIds);           # 删除uda任务
            $data["pub_summary"] = @json_encode($upDetail);
        }

        $ret   = $this->update($data, $where);
        return $ret >= 0 ? true : false;
    }

    /**
     * 添加发布任务，同时初始化redis数据结构<br>
     * 2017-01-05:同时新增uda实时激活统计任务
     *
     * @param string     $name
     * @param int        $type
     * @param int        $vc
     * @param string     $tipContent
     * @param string     $tipUrl
     * @param array      $pkgInfo
     * @param array      $strategy
     * @param int        $forceUp
     * @param int        $upLimit
     * @param timestamp  $startAt
     * @param timestamp  $endAt
     * @param int        $priority
     * @param int        $uid
     * @param string     $appId
     * @param string     $osType
     * @return mixed:int|boolean
     */
    public function addPublishTask($name, $type, $vc, $tipTitle, $tipContent, $tipUrl, array $pkgInfo, array $strategy,
                                    $forceUp, $upLimit, $startAt, $endAt, $priority, $uid, $appId, $osType, $routerVersion) {
        $this->startTransaction();
        $data = array(                          # 添加发布任务
            "name"        => $name,
            "type"        => $type,
            "app_id"      => $appId,
            "os_type"     => $osType,
            "vc"          => $vc,
            "tip_title"   => $tipTitle,
            "tip_content" => $tipContent,
            "tip_url"     => $tipUrl,
            "pkg_urls"    => @json_encode($pkgInfo),
            "strategy"    => @json_encode($strategy),
            "force_up"    => $forceUp,
            "up_limit"    => $upLimit,
            "start_at"    => $startAt,
            "end_at"      => $endAt,
            "pub_status"  => 0 == $startAt || time() > $startAt ? Hk_Ds_Publish_Const::PUB_ING : Hk_Ds_Publish_Const::PUB_WAIT,
            "pub_summary" => "[]",
            "priority"    => $priority,
            "uda_ids"     => "[]",
            "uid"         => $uid,
            "status"      => 0,
            "created_at"  => date("Y-m-d H:i:s"),
            "updated_at"  => date("Y-m-d H:i:s"),
            "router_version"  => $routerVersion,
        );
        $ret  = $this->insert($data);
        if (false === $ret) {
            $this->rollback();
            Bd_Log::addNotice("taskErr", "dbFailed");
            return false;
        }
        $taskId = $this->getInsertID();         # 发布任务唯一Id

        $udaIds = array();
        if ("android" === $osType && Hk_Ds_Publish_Const::PUB_TYPE_NORMAL !== $type) {       # 只有android并且是灰度发布（灰度/多包灰度都是灰度）才进行uda统计
            $vcnames  = array();
            foreach ($pkgInfo as $pkg) {
                $vcnames[] = $pkg["vcname"];
            }
            $udaIds   = Hk_Ds_Publish_UdaLogger::create($taskId, $vc, $vcnames, $appId);
            if (false === $udaIds) {
                Bd_Log::addNotice("taskErr", "udaFailed");
                $this->rollback();
                return false;
            }
        }

        if (!empty($udaIds)) {
            $data = array(
                "uda_ids" => $udaIds,
            );
            $ret  = $this->updatePublishTask($taskId, $data);
            if (false === $ret) {
                Bd_Log::addNotice("taskErr", "udaIdUpdateFailed");
                $this->rollback();
                return false;
            }
        }

        # 初始化发布任务
        $logger = new Hk_Ds_Publish_Logger();
        $logger->initPublishTaskLogger($taskId, $upLimit);
        $this->commit();
        return $taskId;
    }

    /**
     * 更新任务信息
     *
     * @param int         $taskId
     * @param array       $data
     * @return boolean
     */
    public function updatePublishTask($taskId, $data) {
        $where = array(
            "id =" => $taskId,
        );
        if (isset($data["start_at"])) {         # 修改时间时，需要判断时间并改状态
            $start = $data["start_at"];
            $data["pub_status"] = 0 == $start || time() > $start ? Hk_Ds_Publish_Const::PUB_ING : Hk_Ds_Publish_Const::PUB_WAIT;
        }
        if (isset($data["pkg_urls"])) {
            $data["pkg_urls"]   = @json_encode($data["pkg_urls"]);
        }
        if (isset($data["strategy"])) {
            $data["strategy"]   = @json_encode($data["strategy"]);
        }
        if (isset($data["uda_ids"])) {
            $data["uda_ids"]    = @json_encode($data["uda_ids"]);
        }
        if(isset($data["router_version"])){
            $data["router_version"] = intval($data["router_version"]);
        }

        $data["updated_at"]     = date("Y-m-d H:i:s");

        $ret = $this->update($data, $where);
        return $ret >= 1 ? true : false;
    }

    /**
     * 删除发布任务<br>
     * 同时删除redis相关打点和队列数据，并同时删除uda对应的任务
     *
     * @param int          $taskId
     * @return boolean
     */
    public function deletePublishTask($taskId) {
        $task   = $this->getTaskById($taskId);
        if (empty($task)) {
            return true;
        }

        $udaIds = $task["uda_ids"];

        $this->startTransaction();
        $where  = array(
            "id =" => $taskId,
        );
        $data   = array(
            "status"     => 2,
            "updated_at" => date("Y-m-d H:i:s"),
        );
        $ret    = $this->update($data, $where);
        if (false === $ret) {
            Bd_Log::addNotice("delTaskErr", "dbFailed");
            $this->rollback();
            return false;
        }

        $udaIds = array_values($udaIds);
        $ret    = Hk_Ds_Publish_UdaLogger::delete($udaIds);    # 删除uda统计任务
        if (false === $ret) {
            Bd_Log::addNotice("delTaskErr", "udaFailed");
            $this->rollback();
            return false;
        }

        $logger = new Hk_Ds_Publish_Logger();
        $logger->delTaskLogger($taskId);
        $this->commit();
        return true;
    }
}