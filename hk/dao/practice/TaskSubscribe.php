<?php

/**
 * @file TaskSubscribe.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 微课任务订阅
 *
 * @deprecated  已废弃 2020/06/18
 **/

class Hk_Dao_Practice_TaskSubscribe extends Hk_Common_BaseDao {

    /*状态*/
    const STATUS_INIT = 0; //初始状态
    const STATUS_PAYING = 1; //支付中
    const STATUS_DONE = 2; //订阅成功
    const STATUS_FAIL = 3; //订阅失败
    const STATUS_DELETED = 4; //删除状态
    /*读取状态*/
    const READ_STATUS_INIT = 0;//未读状态
    const READ_STATUS_DONE = 1;//已读状态
    /*订单状态*/
    const ORDERSTATUS_INIT = 0;//支付初始状态
    const ORDERSTATUS_PAY = 1;//支付中状态
    const ORDERSTATUS_DONE = 2;//支付成功状态
    const ORDERSTATUS_FAIL = 3;//支付失败状态

    public function __construct()
    {
        $this->_dbName = 'mobile/nativeapp';
        $this->_table = "tblWKTaskSubscribe";
    }
}
