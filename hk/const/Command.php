<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file Command.php
 * <AUTHOR>
 * @date 2014/12/01 20:41:54
 * @brief
 **/
class Hk_Const_Command {


    const CMD_QUESTION_ASK        = 220001; //提问
    const CMD_QUESTION_PICASK     = 220002; //图片前置提问
    const CMD_QUESTION_BLUR_AUDIT = 220003; //模糊多题审核
    const CMD_DEL_CACHE           = 220004; //延迟清理cache
    const CMD_QUESTION_PICASKSYN  = 220005; //提问记录同步
    const CMD_QUESTION_UPDATE     = 220006; //ugc问题有更新
    const CMD_QUESTION_ADD        = 220007; //ugc有新增问题

    const CMD_QUESTION_SEARCH     = 220008; //拍图搜索
    const CMD_QUESTION_SEARCH_1   = 220009; //拍图搜索
    const CMD_QUESTION_SEARCH_2   = 220010; //拍图搜索
    const CMD_QUESTION_SEARCH_3   = 220011; //拍图搜索
    const CMD_QUESTION_SEARCH_4   = 220012; //拍图搜索
    const CMD_QUESTION_SEARCH_5   = 220013; //拍图搜索
    const CMD_QUESTION_SEARCH_6   = 220014; //拍图搜索
    const CMD_QUESTION_SEARCH_7   = 220015; //拍图搜索

    const CMD_QUESTION_SEARCH_PICASK   = 220016; //拍图搜索_不走nmq
    //const CMD_UIDMAP_DEL_CACHE    = 220017; //更新用户映射关系更新双边机房缓存
    const CMD_NAPI_MSG            = 220018; //消息双写
    const CMD_USER_BZBIND         = 220019; //新手机注册用户自动绑定数据
    //const CMD_DOUBLE_DEL_CACHE    = 220020; //缓存内外网双写
    const CMD_EMPTY_MY_QUESTION   = 220021; //清空我的提问记录
    const CMD_DOUBLE_REDIS_RANK   = 220022; //日榜总榜redis数据双写
    const CMD_DOUBLE_MIS_MSG      = 220023; //后台推送消息双写
    const CMD_COMMON              = 220024; //通用消息，短期
    const CMD_FOLLOW_SYNC         = 220025; //收藏数据同步

    const CMD_DOUBLE_SET_CACHE    = 220026; //写多(异地)机房cache-set
    const CMD_DOUBLE_MSET_CACHE    = 220027; //写多(异地)机房cache-mset
    const CMD_DOUBLE_INCR_CACHE    = 220028; //写多(异地)机房cache-increment
    const CMD_DOUBLE_DECR_CACHE    = 220029; //写多(异地)机房cache-decrement
    const CMD_DOUBLE_DELETE_CACHE  = 220030; //写多(异地)机房cache-delete
    const CMD_DOUBLE_MDELETE_CACHE = 220031; //写多(异地)机房cache-mdelete
    const CMD_DOUBLE_SET_REDIS     = 220032; //写多(异地)机房redis写操作
    const CMD_DOUBLE_SET_CACHE_BATCH     = 220033; //写多(异地)机房cache-set批量操作，与cache-mset不同

    //230001 ~239999 题库相关命令
    //const CMD_TIKU_FEEDBACK         = 230001;//题库点赞点踩数更新
    /////////////////////////////////////////

    const CMD_USERINFO_UPDATE       = 500001; //公共库里用户模块的操作

    const CMD_LAXIN = 500003;   # 渠道登录线索命令号
    const CMD_LAXIN_ORDER = 500004;   # 渠道订单命令号

    //501000 - 501999 同步练习使用
    const CMD_PRACTICE_SYNC                = 501001; //用户登录后，同步练习题相关数据
    const CMD_PRACTICE_DOUBLE_REDIS_RANK   = 501002; //用户登录后，同步练习题相关数据
    const CMD_PRACTICE_NEWEXAM_SUBMIT      = 501010; //考试系统，提交结果
    const CMD_PRACTICE_FAVORITE_SYNC_ES    = 501011; //错题本收藏，同步ES

    const CMD_SEARCHMISTAKE_SYNC           = 501009;

    //100000~199999 检索
    const CMD_QUESTION_SEARCH_NEW       = 100001;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_1     = 100002;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_2     = 100003;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_3     = 100004;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_4     = 100005;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_5     = 100006;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_6     = 100007;//新拍题搜索
    const CMD_QUESTION_SEARCH_NEW_7     = 100008;//新拍题搜索
    const CMD_SEARCH_PICASK_OCRRESULT   = 100016; //新增拍题搜索ocr结果
    const CMD_SEARCH_PICASK_ADD        = 100017; //新增拍题记录
    const CMD_SEARCH_PICASK_UPDATE     = 100018; //更新拍题记录
    const CMD_SEARCH_RESULT_DEL_MSG    = 100019; //搜索结果页清理搜索消息
    const CMD_QUESTION_SEARCH_PICASK_NEW = 100020; //拍图搜索_不走nmq
    const CMD_SEARCH_PICASK_UPDATE_CONTENT = 100021; //更新检索记录内容

    const CMD_SEARCH_PICASK_ADD_0        = 100022; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_1        = 100023; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_2        = 100024; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_3        = 100025; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_4        = 100026; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_5        = 100027; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_6        = 100028; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_7        = 100029; //新增拍题记录
    const CMD_SEARCH_PICASK_ADD_8        = 100030; //新增拍题记录
    const CMD_SEARCH_PICASK_DELRECORD    = 100031; //删除搜题记录
    const CMD_SEARCH_MOVE_RECORD         = 100032; //迁移搜题记录prezybuid-->zybuid

    //拍搜结果题目反馈
    const CMD_SEARCH_ANSWER_FEEDBACK     = 101001;

    // 700000~799999 session服务
    const CMD_SESSION_ADDCACHE  = 700001;   # 过渡期间给yun机房增加session缓存
    const CMD_SESSION_DELCACHE  = 700002;   # 过渡期间给yun机房清理session缓存
    const CMD_SESSION_DELAYDEL  = 700010;   # 同步删除session失败后，延迟删除命令

    // 800000~889999 辅导相关
    const CMD_FUDAO_CHATMESSAGE = 800001; //聊天记录
    const CMD_FUDAO_COURSEADD   = 800002; //添加课程
    const CMD_FUDAO_ADD_SCORE   = 800003; //加积分内部命令
    const CMD_FUDAO_SYS_PUSH    = 800004; //系统推送消息命令

    const CMD_FUDAO_ADD_STAR    = 800006; //章节星添加命令

    const CMD_FUDAO_NOTICE_PUSH   = 800005; //新连麦打点命令号
    const CMD_FUDAO_NOTICE_WEBRTC = 800007; //新连麦打点命令号
    const CMD_FUDAO_NOTICE_CDN    = 800008; //学生cdn打点命令号
    const CMD_FUDAO_STUDNET_WINDOWS_LOG = 800009; //windows学生端学生log

    const CMD_FUDAO_UPLOAD_RECORD_VOICE = 800010; //小学英语更读题上传录音接口
    const CMD_FUDAO_FORWARD_TEACHER_MSG = 800011; //转发主讲消息
    const CMD_FUDAO_ADD_TASK_800012     = 800012; //回放生成任务

    //辅导班主任相关
    const CMD_FUDAO_ADD_STUDENT_ASSISTANT = 800020;//学生报名成功通知班主任工作台
    const CMD_FUDAO_DEL_STUDENT_ASSISTANT = 800021;//学生退课成功通知班主任工作台
    const CMD_FUDAO_ADD_STUDENT_ASSISTANT_LESSON = 800022;//重开章节通知班主任工作台

    //拉新销售相关
    const CMD_FUDAO_STU_CONSULT_CLUE_BIND  = 800501;//学生报名成功通知绑定sc
    const CMD_FUDAO_STU_CONSULT_CLUE_TRANS = 800502;//学生报名班课后转化

    //活动相关
    const CMD_FUDAO_ACTIVITY_DATI_APPOINT  = 800601; //百万学霸报名

    //学生重要日志上传
    const CMD_FUDAO_STU_ADD_NOTICE  = 801652;

    //890000~899999 IM相关

    const CMD_IM_COVERT_VOICE  = 890000; //聊天消息转码mp3

    // 900000~999999 支付系统
    const CMD_PAY_PAYSUCCESS    = 900001; //支付成功
    /***********************91xxxx，平台VIP套餐使用*************************/
    const CMD_PAY_PACKAGE_SUCCESS = 910001;//平台偷听套餐支付成功
    const CMD_PAY_PACKAGE_REFUND  = 910002;//平台偷听套餐退款成功

    /***********************30xxxx，平台ucenter使用*************************/

    // 30xxxx为新增用户各种动作callback
    const CMD_USER_SIGNUP      = 301000;   # 用户注册
    const CMD_NICKNAME_MOD     = 301001;   # 修改昵称
    const CMD_AVATAR_MOD       = 301002;   # 修改头像
    const CMD_SCHOOL_MOD       = 301003;   # 修改学校信息
    const CMD_TITLE_MOD        = 301004;   # 修改称号
    const CMD_GRADE_MOD        = 301005;   # 修改年级信息
    const CMD_COMPLETE_INFO    = 301006;   # 完成资料完善 10-17
    const CMD_USER_TASK_NEW    = 301007;   # 悬赏任务发奖

    const CMD_USER_LOGIN       = 301100;   # 用户登录
    const CMD_USER_SIGNIN      = 301101;   # 用户签到
    const CMD_USER_ADDFRIEND   = 301102;   # 添加好友,TODO 废弃
    const CMD_SHARE_LINK       = 301103;   # 用户分享,TODO 废弃
    const CMD_COLLECT_ACTICLE  = 301104;   # 收藏文章,TODO 废弃
    const CMD_JUVENILE_HEARING_LISTEN_AUDIO = 301105;   # 少年听闻听音频,TODO 废弃
    const CMD_ACTIVITY_CEREMONY_MEDAL = 301106;    # 2018开学活动任务勋章

    const CMD_ANSWER_REPLY     = 301110;   # 回答问题
    const CMD_ANSWER_ACCEPT    = 301111;   # 回答被采纳
    const CMD_POST_ADD         = 301112;   # 同学圈发帖/回复,TODO 废弃
    const CMD_POST_RECOMMEND   = 301113;   # 帖子加精,TODO 废弃
    const CMD_ACCEPT_ANSWER    = 301114;   # 采纳回答
    const CMD_ANSWER_BETHANKED = 301115;   # 回答被感谢
    const CMD_CYCLE_FOCUS      = 301116;   # 关注圈子 10-17,TODO 废弃
    const CMD_CHANGE_SKIN      = 301117;   # 用户使用、更换皮肤 10-17
    const CMD_OPEN_ABOUT_EGG   = 301118;   # 触发关于彩蛋 10-17

    # 用户动态相关回调命令号
    const CMD_USER_MEDAL       = 301119;   # 用户勋章
    const CMD_ARTICLE_CIRCLE   = 301120;   # 作文圈
    const CMD_USER_QUESTION    = 301121;   # 问答提问
    const CMD_STUDY_CIRCLE     = 301122;   # 学习圈
    const CMD_ADX_APP_DOWNLOAD = 301123;   # adx使用好应用推荐
    const CMD_FEEDS_ARTICLE    = 301124;   # feed文章

    const CMD_USE_FUDAO        = 301200;   # 使用辅导
    const CMD_BUY_FUDAO        = 301201;   # 购买辅导
    const CMD_USE_AIRCLASS     = 301202;   # 使用直播课
    const CMD_BUY_AIRCLASS     = 301203;   # 购买直播课
    const CMD_AIRCLASS_TEACHER_EVALUE = 301204; # 直播课评价老师 10-17
    const CMD_AIRCLASS_TEACHER_FOCUS  = 301205; # 直播课关注老师 10-17
    const CMD_FUDAO_TEACHER_COLLECT   = 301206; # 收藏辅导老师 10-17

    const CMD_LEARN_WENYANWEN  = 301300;   # 学习文言文
    const CMD_RECITE_WENYANWEN = 301301;   # 背诵文言文

    # 活动等相关的特定一段时间的命令
    const CMD_ACT_THIRDYEAR      = 301401;   # 三周年限量勋章命令 01-15 ~ 02-15
    const CMD_ACT_FOURTHYEAR     = 301402;   # 四周年限量勋章命令 01-12 ~ 01-20
    const CMD_ACT_REGISTERREWARD = 301403;   # 新用户注册礼包命令 2019-04-24 添加

    const CMD_UNIVERSE_LISTEN_STORY = 301501; # 知识宇宙听故事,TODO 废弃
    const CMD_UNIVERSE_BUY_ALBUM    = 301502; # 知识宇宙购买专辑,TODO 废弃

    const CMD_USER_WEALTH_EXPIRE = 301999;   # 帮帮币过期命令
    const CMD_UCLOUD_UPDELAY     = 309999;   # ucloud异步更新命令 12-03

    /***********************311xxx，平台游戏使用*************************/
    const CMD_GAME_CALLBACK    = 311000;   # 游戏回调
    const CMD_GAME_DAILYTASK   = 311001;   # 游戏每日任务回调

    /***********************312xxx，平台增值服务使用*************************/
    const CMD_MACAN_ACHIEVE    = 312000;   # 增值服务成就回调
    const CMD_LOTTERY_REWARD   = 312001;   # 抽奖平台异步发奖

    /***********************32xxxx，平台其他使用*************************/
    const CMD_KDAPP_SUMMARY    = 320000;   # kdapp结算异步命令
    const CMD_MAINSEARCH_RECORDADD = 320001;   # 主端大学拍搜 添加搜题记录

    /***********************323XXX,平台社区使用*************************/
    const CMD_PT_UGC_ZHIFOU_GROUP_DEL = 323001;  //知否小组删除

    /***********************33xxxx，家长端*************************/
    const CMD_PARENT_ACTIVITY     = 330000;
    const CMD_PARENT_ASYNC_RECORD = 330001;  # 登录回调

    /***********************95XXXX,题库生产使用*************************/
    const CMD_ZBTIKU_PRODUCE_TAG_BATCH = 950001; //批量投产




}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=0: */
