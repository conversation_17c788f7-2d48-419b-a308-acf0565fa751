<?php

/**
 * @file ExamRanking.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 试卷的排行榜相关业务服务
 *
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Ds_Practice_ExamRanking {

    private $_objDaoExamRanking;

    public function __construct() {
        $this->_objDaoExamRanking = new Hk_Dao_Practice_ExamRanking();
    }


    /**
     * 增加积分
     * @param  int    $uid
     * @param  string $cuid
     * @param  int    $courseId
     * @param  int    $score
     * @param  int    $isDoubleWrite 1-开启双写
     * @return bool
     */
    public function addScore($examId, $member, $score, $isDoubleWrite=1) {
        if ( (0 >= $examId) || ("" == $member) ) {
            Bd_Log::warning("Error[redisPrepare] Detail[examId:{$examId} member:{$member}]");
            return false;
        }

        Hk_Util_Log::start('ds_incrScore');
        $data = $this->_objDaoExamRanking->addScore($examId, $member, $score);
        Hk_Util_Log::stop('ds_incrScore');
        return $data;
    }


    /**
     * 获取总人数
     * @param  int    $examId
     * @return int
     */
    public function getMemberCount($examId) {
        if (0 >= $examId) {
            Bd_Log::warning("Error[redisPrepare] Detail[examId:{$examId}]");
            return false;
        }
        Hk_Util_Log::start('ds_getMemberCount');
        $data = $this->_objDaoExamRanking->getMemberCount($examId);
        Hk_Util_Log::stop('ds_getMemberCount');
        return $data;
    }


    /**
     * 获取指定排名段内的成员(逆序排名)
     * @param  integer $examId
     * @param  integer $start 起始下标 0开始
     * @param  integer $stop  结束下标
     * @return mix
     */
    public function getRevRange($examId, $start, $stop) {
        if (0 >= $examId) {
            Bd_Log::warning("Error[redisPrepare] Detail[examId:{$examId}]");
            return false;
        }
        Hk_Util_Log::start('ds_getMemberCount');
        $data = $this->_objDaoExamRanking->getRevRange($examId, $start, $stop);
        Hk_Util_Log::stop('ds_getMemberCount');
        return $data;
    }


    /**
     * 获取用户排名
     * @param  int    $examId
     * @param  string $member
     * @return int
     */
    public function getUserRank($examId, $member) {
        if ( (0 >= $examId) || ("" == $member) ) {
            Bd_Log::warning("Error[redisPrepare] Detail[examId:{$examId} member:{$member}]");
            return false;
        }
        Hk_Util_Log::start('ds_getUserRank');
        $data = $this->_objDaoExamRanking->getUserRank($examId, $member);
        Hk_Util_Log::stop('ds_getUserRank');
        return $data;
    }


    /**
     * 内网机器与云机器的redis双写
     * @param string $function 接口名称
     * @param mix $arrParams 输入参数
     * @return true/false
     *
     */
    /*
    private function _doubleProcess($function, $arrParams) {
        $arrCommand = array(
            'command_no' => Hk_Const_Command::CMD_PRACTICE_DOUBLE_REDIS_RANK,
            'function'   => $function,
            'params'     => $arrParams,    
        );
        $idc = ral_get_idc();
        if('yun' === $idc) {
            $ret = Hk_Service_CommandProxy::talkToInternal(Hk_Const_Command::CMD_PRACTICE_DOUBLE_REDIS_RANK, $arrCommand);  
        }else {
            $ret = Hk_Service_CommandProxy::talkToExternal(Hk_Const_Command::CMD_PRACTICE_DOUBLE_REDIS_RANK, $arrCommand);  
        }

        Bd_Log::addNotice("doubleWrite", $function);

        if(false === $ret) {
            $params = json_encode($arrParams);
            Bd_Log::warning("Error:[doubleProcessError], Detail:[function:{$function} params:{$params}]");
            return false;
        }

        return true;
    }*/
}
