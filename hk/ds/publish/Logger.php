<?php


/**
 * 发布任务打点实时信息，使用redis存储相关打点数据。<br>
 * 实时版本激活人数由uda平台进行统计，不在此再进行统计
 *
 * 包含：<br>
 * 1、当前点击任务升级数<br>
 * 2、pkg升级数<br>
 *
 * 使用redis的hash存储，结构：<br>
 * taskId(hash-key)<br>
 * |- uptotal   - int 当前升级数<br>
 * |- limit     - int 同意升级数<br>
 * |- reject    - int 拒绝升级数<br>
 * |- activated - int 拒绝升级数<br>
 * |- vcname    - int 指定vcname包升级数<br>
 * |- ......<br>
 *
 * 以下逻辑废弃：<br>
 * 使用redis list作为队列控制升级下发<br>
 * 创建任务时如果有数量控制，会先初始化任务数量大小的list，然后根据list逐步下放任务<br>
 * 当用户拒绝升级时，会将升级指标返还给队列，供后续人升级。
 *
 * @since 3.2 2019-03-22 不再进行点击升级和不升级打点，uptotal|reject已经不再更新
 * @since 3.1 2019-03-21 迁移redis到新集群
 * @since 3.0 2017-10-10 重构整个逻辑
 * @since 2.0 2017-01-05 支持uda统计
 * @since 1.0 2016-06-21 初始化
 *
 * @filesource hk/ds/publish/Logger.php
 * <AUTHOR>
 * @version 3.2
 * @date    2019-03-22
 */
//class Hk_Ds_Publish_Logger {
//
//    private $redisObj;
//
//    public function __construct() {
//        $this->redisObj = Hk_Service_RedisClient::getInstance("plat");
//    }
//
//    /**
//     * 初始化发布任务所需的数据结构<br>
//     * hash用于存储打点数据，可以不用存储
//     *
//     * @param int         $taskId
//     * @param int         $upLimit
//     * @return boolean
//     */
//    public function initPublishTaskLogger($taskId, $upLimit = 0) {
//        $key = $this->getKey($taskId);
//        $this->redisObj->hMSet($key, array(
//            "uptotal" => 0,             # 点击升级
//            "reject"  => 0,             # 点击拒绝
//            "activated" => 0,           # uda同步的激活人数
//            "limit"     => $upLimit ,   # 升级限制
//        ));
//        return true;
//    }
//
//    /**
//     * 删除升级任务打点数据，设定过期时间
//     *
//     * @param int         $taskId
//     * @return boolean
//     */
//    public function delTaskLogger($taskId) {
//        $key = $this->getKey($taskId);
//        return $this->redisObj->expire($key, 7200);
//    }
//
//    /**
//     * 更新升级任务实时激活人数
//     *
//     * @param int         $taskId
//     * @param string      $name
//     * @param int         $actNum
//     * @return boolean
//     */
//    public function updateUdaActivateNum($taskId, $name, $actNum) {
//        $key = $this->getKey($taskId);
//        return $this->redisObj->hSet($key, $name, $actNum);
//    }
//
//    /**
//     * 获取指定任务当前的升级情况
//     *
//     * @param int         $taskId
//     * @return mixed:array|boolean
//     */
//    public function getUpdateStats($taskId) {
//        $key = $this->getKey($taskId);
//        $ret = $this->redisObj->hGetAll($key);
//        if (false === $ret) {
//            return false;
//        }
//
//        $stat = array();
//        foreach ($ret as $k => $v) {
//            $stat[$k] = intval($v);
//        }
//        return $stat;
//    }
//
//    /**
//     * 记录用户点击升级|拒绝点击数
//     * 这函数已经不再使用
//     * @deprecated
//     *
//     * @param int         $taskId
//     * @param int         $upStat
//     * @return boolean
//     */
//    public function updateReportLog($taskId, $upStat) {
//        if (1 === $upStat) {
//            $name = "uptotal";
//        } else {
//            $name = "reject";
//        }
//        $key = $this->getKey($taskId);
//        return $this->redisObj->hIncrBy($key, $name, 1);
//    }
//
//    private function getKey($taskId) {
//        return sprintf("publish:task:id:%s", $taskId);
//    }
//}
