<?php


/**
 * 基础错误码定义
 *
 * @filesource hk/rpc/ErrorCodes.php
 * <AUTHOR>
 * @version 1.0
 */
class Hk_Rpc_ErrorCodes {


    const SUCC = 0;                         # 成功
    const PARAM_ERROR       = 100;          # 参数错误

    const CONF_NOT_EXIST    = 201;          # 配置不存在
    const CONF_INVALID      = 202;          # 配置出错
    const SERVICE_NOT_EXIST = 203;          # 请求服务不存在
    const REQUESTID_ERR     = 204;          # rpc的requestId错误
    const REQUEST_REPEAT    = 205;          # rpc请求重复
    const REQUEST_EXPIRED   = 206;          # rpc请求失效

    const ACCESS_DENIED     = 403;          # 权限受限
    const INTERNAL_ERROR    = 999;          # 公共错误

    public static $codes = array(
        self::SUCC => "ok",

        self::PARAM_ERROR       => "param error",
        self::CONF_NOT_EXIST    => "config not exist",
        self::CONF_INVALID      => "rpc config invalid",
        self::SERVICE_NOT_EXIST => "service not exist",
        self::REQUESTID_ERR     => 'requestId error',
        self::REQUEST_REPEAT    => 'request repeat',
        self::REQUEST_EXPIRED   => 'request expired',

        self::ACCESS_DENIED  => "access denied",
        self::INTERNAL_ERROR => "internal error",
    );
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=0: */
