<?php


/**
 * rpc插件接口，根据需要配置，可取消的插件能在配置中取消执行
 *
 * @filesource hk/rpc/Plugin.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-11-08
 */
class Hk_Rpc_Plugin extends Ap_Plugin_Abstract {

    public function dispatchLoopStartup(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        if ("Rpc" !== $request->controller) {           # 非rpc请求不执行
            return;
        }
        Hk_Rpc_SmartMain::startup($request, $response);
    }

    public function dispatchLoopShutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        if ("Rpc" !== $request->controller) {           # 非rpc请求不执行
            return;
        }
        Hk_Rpc_SmartMain::shutdown($request, $response);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */