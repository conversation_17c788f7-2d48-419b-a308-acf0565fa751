<?php


/**
 * rpc请求基类<br>
 * 负责读取上下文，执行对应的服务方法，并设置结果
 *
 * @since 2.0 整理重构代码，拆分业务到各自插件执行，只保留执行逻辑
 * @since 1.0 初始化
 *
 * @filesource hk/rpc/controller/Base.php
 * <AUTHOR>
 * @version 2.0
 * @date    2018-11-09
 */
abstract class Hk_Rpc_Controller_Base extends Ap_Controller_Abstract {

    /**
     * 根据上下文参数创建并执行业务逻辑
     *
     * @throws Hk_Rpc_Error
     */
    public final function callAction() {
        $resp = Hk_Rpc_SmartMain::getFilter();        # 请求已有返回值，直接返回
        if (!empty($resp)) {
            $this->setResp($resp);
            return;
        }

        $ctx     = Hk_Rpc_SmartMain::getContext();
        $conf    = $ctx["conf"];
        $input   = $ctx["input"];
        $service = $ctx["service"];

        # 初始化并校验服务对应ps，判断执行函数是否存在
        $ps      = new $conf["ps"]();
        $func    = $conf["func"];
        if (false === method_exists($ps, $func)) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::SERVICE_NOT_EXIST, array(
                "service" => $service,
            ));
        }

        # 执行业务逻辑，业务逻辑异常会统一捕获
        try {
            $ret    = call_user_func_array(array($ps, $func), $input);
            $errno  = Hk_Rpc_ErrorCodes::SUCC;
            $errmsg = Hk_Rpc_ErrorCodes::$codes[$errno];
            $data   = NULL === $ret ? array() : $ret;
        } catch (Hk_Rpc_Error $error) {
            $errno  = $error->getErrno();
            $errmsg = $error->getErrDetail();
            $data   = $error->getErrData();
        }
        $resp = array(
            "errno"  => $errno,
            "errmsg" => $errmsg,
            "data"   => $data,
        );
        $this->setResp($resp);
        Hk_Util_Log::printLog();            # 增加耗时打点
    }

    /**
     * 设置请求返回值
     *
     * @param array       $resp
     */
    private function setResp($resp) {
        Bd_Log::addNotice("errno",  $resp["errno"]);
        Bd_Log::addNotice("errmsg", $resp["errmsg"]);
        Hk_Rpc_SmartMain::setCtxResp($resp);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=0: */
