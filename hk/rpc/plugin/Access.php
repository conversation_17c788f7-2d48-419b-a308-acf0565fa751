<?php


/**
 * Rpc访问权限控制插件<br>
 * 1、只限制controller为rpc，其他接口不进行限制<br>
 * 2、rpc只能通过内网访问，可以在access.conf中关闭此选项，默认强制打开<br>
 * 3、服务白名单功能，配置在app/conf/rpc/access.conf中
 *
 * @scine 2.1 2018-11-19 增加是否内网选项，内网可关闭
 * @since 2.0 2018-10-30 增加权限访问控制到模块级别
 * @since 1.0 初始化
 *
 * @filesource hk/rpc/plugin/Access.php
 * <AUTHOR>
 * @version 2.1
 * @date    2018-11-19
 */
class Hk_Rpc_Plugin_Access extends Hk_Rpc_Plugin_Base {


    private $accConf   = array();
    private $innerOnly = true;

    public final function __construct() {
        $conf = Bd_Conf::getAppConf("rpc/access");
        if (false === $conf) {          # 只有配置了访问控制的才进行控制，否则只进行内网权限控制
            return;
        }
        # 初始化参数，默认强制内网访问，只有手动配置内网开启才能使用
        if (isset($conf["global"]["inner"])) {
            if (is_numeric($conf["global"]["inner"]) && 0 === intval($conf["global"]["inner"])) {
                $this->innerOnly = false;
            }
            unset($conf["global"]["inner"]);
        }
        $this->accConf = $conf;
        $this->init();
    }

    /**
     * 启动阶段，执行：<br>
     * 1、判断是否内网<br>
     * 2、获取请求模块，根据配置判断是否有权限访问
     *
     * {@inheritDoc}
     * @see Hk_Rpc_Plugin_Base::startup()
     */
    public function startup(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        # 内网控制
        if ($this->innerOnly && false === Hk_Util_Ip::isInnerIp()) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::ACCESS_DENIED, array(
                "denyIp" => CLIENT_IP,
            ));
        }
        # 未配置权限
        if (empty($this->accConf)) {
            return;
        }
        # 验证模块权限
        $method     = $request->getParam('method');
        $reqModule  = isset($_SERVER["HTTP_X_BD_MODULE"]) ? $_SERVER["HTTP_X_BD_MODULE"] : "unknown-app";
        Bd_Log::addNotice("reqModule", $reqModule);
        if (false === $this->accessCtrl($reqModule, $method)) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::ACCESS_DENIED, array(
                "denyApp" => $reqModule,
            ));
        }
    }

    public function shutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
    }

    /**
     * 判断请求app是否有权限，服务权限使用global+service校验<br>
     * 1、判断访问模块是否有全局模块权限<br>
     * 2、如果用户无全局权限，判断是否配置单独服务权限<br>
     * 3、单独权限acc，all：全部放开；deny：全部拒绝；array：名单列表<br>
     * 4、如果未传递reqModule字段，默认为unknown-app，会被自动拒绝
     *
     * @param string       $reqModule
     * @param string       $name
     * @return boolean
     */
    private function accessCtrl($reqModule, $name) {
        $conf = $this->accConf;
        # global全局权限
        $gAcc = isset($conf["global"]["acc"]) && is_array($conf["global"]["acc"]) ? $conf["global"]["acc"] : array();
        if (!empty($gAcc) && in_array($reqModule, $gAcc)) {      # 已配置全局权限，未配置则跳过
            return true;
        }
        # service单独服务权限，若无配置，代表所有人都能访问
        if (!isset($conf["service"][$name]["acc"])) {            # 无配置，直接放行
            return true;
        }
        # 根据配置判断是否放行
        $acc  = $conf["service"][$name]["acc"];
        if (is_string($acc)) {          # allow校验权限，只允许填all|deny，all: 允许所有；deny: 禁止所有
            if ("all" !== $acc && "deny" !== $acc) {
                return false;
            }
            return "all" === $acc ? true : false;
        }
        if (is_array($acc)) {           # 权限白名单
            return in_array($reqModule, $acc) ? true : false;
        }
        return false;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
