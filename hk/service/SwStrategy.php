<?php


/**
 * 封装后的小流量逻辑
 *
 * @see Hk_Ds_MisStrategy_Strategy
 *
 * @since 2.0 2018-12-17 增加是否通过接口下发字段，选项意义在后台配置
 * @since 1.2 2018-10-31 参数统一通过saf获取，saf如果无法获取则通过参数传递
 * @since 1.1 2018-10-26 更简洁调用，策略参数可以 通过cgi获取（saf支持）
 * @since 1.0 2017-06-01 初始化
 *
 * @filesource hk/service/SwStrategy.php
 * <AUTHOR>
 * @version 1.1
 * @date    2018-10-26
 */
class Hk_Service_SwStrategy extends Hk_Ds_MisStrategy_Const {

    const SW_ON  = 1;
    const SW_OFF = 0;

    /**
     * 通过透传参数和上下文生成小流量开关参数<br>
     * 如果传入参数，会使用传入参数，如果未传入则使用上下文参数<br>
     * 以下是传入参数结构：<br>
     * <code>
     * array(<br>
     *     "ip"       => string,<br>
     *     "uid"      => int,<br>
     *     "os"       => string,<br>
     *     "cuid"     => string,<br>
     *     "vc"       => int,<br>
     *     "vcname"   => string,<br>
     *     "province" => string,<br>
     *     "city"     => string,<br>
     *     "phone"    => string,<br>
     *     "grade"    => int,<br>
     *     "device"   => string,<br>
     * );<br>
     * </code>
     *
     * @param array       $ctx
     * @return array
     */
    public static function buildSwInput($ctx = array()) {
        $reqParam = Saf_SmartMain::getCgi()["request_param"];
        $userInfo = Saf_SmartMain::getUserInfo();
        $cuid = strval($reqParam["cuid"]);
        if(strlen(trim($cuid)) <= 0){
            $gradeCUG = 0;
        }else{
            $userTag = Hk_Service_UserTag::getByCuid($cuid);
            if(!is_array($userTag) || !isset($userTag['grade'])){
                //Bd_Log::addNotice("userTagGetByCuid", @json_encode($userTag));
                $gradeCUG = 0;
            }else {
                $gradeCUG = intval($userTag['grade']);
            }
        }

        if(isset($reqParam['iOSVersion']) && !empty($reqParam['iOSVersion'])){
            $osVersion = $reqParam['iOSVersion'];
        }else if(!empty($reqParam['sdk']) && !empty($reqParam['sdk'])){
            $osVersion = $reqParam['sdk'];
        }else{
            $osVersion = '';
        }
        //2021-07-22 ip支持从参数获取
        if(!empty($reqParam["swStrategyIp"])){
            $swStrategyIp = $reqParam["swStrategyIp"];
            $ipFrom = 'pram';
        }else{
            $swStrategyIp = Hk_Util_Ip::getClientIp();
            $ipFrom = 'client';
        }
        Bd_Log::addNotice('swStrategyIpFrom', $ipFrom);
        //2021-07-22从ip获取地理信息
        $address = Hk_Service_IpAddress::getAddressByIp($swStrategyIp);
        if (is_array($address) && isset($address['province']) && isset($address['city'])) {
            $province       = $address['province'];
            $city           = $address['city'];
            $swLocationFrom = 'ip';
        }else{
            $province = '';
            $city     = '';
            $swLocationFrom = 'default';
        }
        Bd_Log::addNotice('swLocationFrom', $swLocationFrom);



        $p = array(         # 初始化参数
            "ip"                => $swStrategyIp,
            "uid"               => intval($userInfo["uid"]),
            "phone"             => "",
            "city"              => $city,
            "province"          => $province,
            "vc"                => intval($reqParam["vc"]),
            "vcname"            => strval($reqParam["vcname"]),
            "grade"             => intval($reqParam["grade"]),
            "os"                => strval($reqParam["os"]),
            "cuid"              => strval($reqParam["cuid"]),
            "channel"           => strval($reqParam["channel"]),
            "device"            => strval($reqParam["device"]),
            "gradeCUG"          => intval($gradeCUG),
            "osVersion"         => $osVersion,
        );
        foreach ($p as $name => $val) {         # 使用传递上下文覆盖自动获取的参数
            if (isset($ctx[$name])) {
                $p[$name] = $ctx[$name];
            }
        }
        //地理信息处理
        if(!empty($p["province"]) || !empty($p["city"])){
            $converted = Hk_Util_IpLocation::convertProvinceCity($p["province"], $p["city"]);
            Bd_Log::addNotice("locationConverted", intval($converted));
        }
        $swInput = array(
            "ip"        => $p["ip"],
            "uid"       => $p["uid"],
            "cuid"      => $p["cuid"],
            "grade"     => $p["grade"],
            "gradeCUG"  => $p["gradeCUG"],
            "model"     => $p["device"],
            "channel"   => $p["channel"],
            "os_type"   => $p["os"],
            "telephone" => "",
            "version"   => empty($p["vc"]) && empty($p["vcname"]) ? [] : ["vc" => $p["vc"], "vcname" => $p["vcname"]],
            "location"  => empty($p["province"]) && empty($p["city"]) ? [] : ["province" => $p["province"], "city" => $p["city"]],
            "osVersion" => $p["osVersion"],
        );
        Bd_Log::addNotice("swInput", @json_encode($swInput));
        return $swInput;
    }

    /**
     * 生成小流量通用参数，老版本，不建议再使用
     *
     * @deprecated
     *
     * @param array        $params
     * @return array
     */
    public static function buildSwitchParam($params = array()) {
        if (!isset($params["location"])) {
            $city     = isset($params["city"])     ? $params["city"]     : "";
            $province = isset($params["province"]) ? $params["province"] : "";
            $location = empty($province) && empty($city) ? [] : ["province" => $province, "city" => $city];
        } else {
            $location = $params["location"];
        }

        $ip      = Hk_Util_Ip::getClientIp();
        $vc      = isset($params["vc"])     ? $params["vc"]     : 0;
        $vcname  = isset($params["vcname"]) ? $params["vcname"] : "";
        $version = empty($vc) && empty($vcname) ? [] : ["vc" => $vc, "vcname" => $vcname];
        if (empty($params["os"])) {
            $os  = isset($params["appType"]) ? $params["appType"] : "";
        } else {
            $os  = $params["os"];
        }
        $swInput = array(
            "ip"            => $ip,
            "uid"           => isset($params["uid"])   ? intval($params["uid"]) : 0,
            "cuid"          => isset($params["cuid"])  ? strval($params["cuid"])  : "",
            "grade"         => isset($params["grade"]) ? intval($params["grade"]) : 255,
            "gradeCUG"      => isset($params["gradeCUG"]) ? intval($params["gradeCUG"]) : 255,
            "telephone"     => isset($params["phone"]) ? strval($params["phone"]) : "",
            "channel"       => isset($params["channel"]) ? strval($params["channel"]) : "",
            "os_type"       => $os,
            "version"       => $version,
            "location"      => $location,
        );
        Bd_Log::addNotice("swInput", @json_encode($swInput));
        return $swInput;
    }

    /**
     * 获取指定策略指定用户uid|cuid|phone是否命中
     *
     * @see Hk_Ds_MisStrategy_UserStrategy
     *
     * @param string      $mark
     * @param mixed       $key
     * @return boolean
     */
    public static function userCheck($mark, $key) {
        return Hk_Ds_MisStrategy_UserStrategy::check($mark, $key);
    }

    /**
     * 获取指定类型的开关状态，返回数据结构：<br>
     * <code>
     * array(
     *     "mark" => array(
     *         "sw"    => 0/1,         # 开关状态
     *         "fSync" => 0/1,         # 是否强制同步服务端开关
     *     ),
     *     ...
     * )
     * </code>
     *
     * @see Hk_Ds_MisStrategy_Strategy
     *
     * @param array       $params
     * @param int         $type
     * @param array       $marks
     * @return array
     */
    public static function getSwitches($params, $type, array $marks = array()) {
        $appFlag = false;
        return Hk_Ds_MisStrategy_Strategy::getSwitches($params, $type, $marks, $appFlag);
    }

    /**
     * 获取app下发开关列表，供后端napi|pluto使用
     *
     * @param string      $appId
     * @param array       $params
     * @param int         $type
     * @return array
     */
    public static function getDspSwitches($appId, $params, $type) {
        $appFlag = true;
        return Hk_Ds_MisStrategy_Strategy::getSwitches($params, $type, array(), $appFlag, $appId);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
