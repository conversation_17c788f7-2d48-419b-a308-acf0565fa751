<?php


/**
 * @file Tcos.php
 * @brief 腾讯云对象存储服务
 * <AUTHOR>
 * @version 1.0
 * @date 2017-11-11
 */
require_once LIB_PATH . "/ext/tcossdk/include.php";
//require_once LIB_PATH . "/ext/cos-sdk-v5-x.phar';

use QCloud\Cos\Api;
use QCloud\Cos\HttpClient;
use QCloud\Cos\Auth;
use QCloud\Cos\SignatureV5;
use QCloud\Cos\Sts;

class Hk_Service_Tcos
{

    const COS_NAMESPACE_IMAGE_SRC = 'picbj.myqcloud.com';  //cos原图域名
    const COS_NAMESPACE_IMAGE_SMALL = 'picbj.myqcloud.com'; //cos缩略图域名

    const REGION_V5_BEIJING = 'ap-beijing'; // 北京节点区域值

    const PUT_DIRECTIVE_COPY = 'Copy';//忽略设置的用户元数据信息直接复制
    const PUT_DIRECTIVE_REPLACED = 'Replaced';//按设置的元信息修改元数据，当目标路径和源路径一样时，必须设置为 Replaced

    private static $CosConfig = null;

    private static $CosType = "tcos";

    /**
     * @brief 上传本地文件到Cos
     *
     * @param $cosName   cos服务名
     * @param $filePath  本地文件路径
     * @param $fileType  上传到cos的文件后缀，默认为jpg
     * @param $fileName  上传到cos的文件名，默认为空，自动生成文件名
     * @param $overwrite 同名文件是否强制覆盖，0-不强制覆盖，1-强制覆盖
     * @param $needsize  是否需要在pid中写宽高，0-不需要，1-需要
     *
     * @return boolean|string  正常返回可以访问的url
     */
    public static function uploadFile($cosName, $filePath, $fileType = "jpg", $fileName = null, $overwrite = 0, $needsize = 0)
    {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc" => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot" => self::$CosType,
            "service" => $cosName,
            "method" => __METHOD__,
        ];

        $config = self::getCosConfig($cosName);
        if (false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        if (!file_exists($filePath)) {
            $ralArg["errmsg"] = "local file $filePath not exist";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }
        $fileSize = filesize($filePath);
        if ($fileSize > intval($config['filesize_limit'])) {
            $ralArg["errmsg"] = "local file $filePath exceeds size limit " . $config['filesize_limit'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $dstFilePath = '';
        if (!empty($fileName)) {
            $dstFilePath = "/$fileName.$fileType";
        } else {
            $fileName = $config['file_prefix'] . md5_file($filePath);
            if ($needsize) {
                $imgInfo = getimagesize($filePath);
                $fileName = $fileName . "_" . $imgInfo[0] . "_" . $imgInfo[1];
            }
            $dstFilePath = "/$fileName.$fileType";
        }
        if (!empty($config['directory'])) {
            $dstFilePath = '/' . $config['directory'] . $dstFilePath;
        }

        $cosApi = new Api($config);
        $insertOnly = $overwrite == 0 ? 1 : 0;
        $ret = $cosApi->upload($config['bucket'], $filePath, $dstFilePath, null, null, $insertOnly);
        //记录上传时的详情信息
        if (isset($ret["response_info"])) {
            $ralArg['cos_info'] = json_encode($ret["response_info"]);
        }

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);

        if ($ret['code'] !== 0) {
            $ralArg["errmsg"] = "upload file to $dstFilePath failed, detail: " . $ret['message'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $ralArg["err_no"] = $ret['code'];
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return $ret['data'];
    }

    // 这个版本的sdk使用了腾讯V4版本的sdk，腾讯那边不怎么维护了。历史上传后返回的是 access_url(cdn地址）对于有些bucket访问失败。
    // 再后面新加一个方法返回source url，供业务使用。
    public static function uploadLocalFile($cosName, $filePath, $fileType = "jpg", $fileName = null, $overwrite = 0, $needsize = 0)
    {
        $ret = self::uploadFile($cosName, $filePath, $fileType, $fileName, $overwrite, $needsize);
        return $ret['access_url'];
    }

    public static function getSourceURL($cosName, $fileName, $fileType, $expire = 1800) {
        $cfg = self::getCosConfig($cosName);
        if (false === $cfg) {
            return false;
        }

        $appId = strval($cfg['app_id']);
        $bucket = $cfg["bucket"];
        $region = $cfg["region"];

        $dstFilePath = "/$fileName.$fileType";

        if (!empty($cfg['directory'])) {
            $dstFilePath = '/' . $cfg['directory'] . $dstFilePath;
        }

        return sprintf("https://%s-%s.cos%s.myqcloud.com/%s", $bucket, $appId, $region, $dstFilePath);
    }
    /**
     * @brief 上传文件内容到cos
     *
     * @param string      $cosName    cos服务名
     * @param string      $content    文件内容
     * @param string      $fileType   文件类型
     * @param string|null $fileName   指定文件名
     * @param int         $overwrite  是否强制覆盖
     * @param int         $needsize   是否需要在pid中写宽高，0-不需要，1-需要
     *
     * @return boolean|string  正常返回可以访问的url
     */
    public static function uploadFileContent($cosName, $content, $fileType = "jpg", $fileName = null, $overwrite = 0, $needsize = 0)
    {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc" => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot" => self::$CosType,
            "service" => $cosName,
            "method" => __METHOD__,
        ];

        if (empty($content)) {
            $ralArg["errmsg"] = "file content is empty!";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $fileSize = strlen($content);
        if ($fileSize > intval($config['filesize_limit'])) {
            $ralArg["errmsg"] = "file content length exceeds size limit " . $config['filesize_limit'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }
        $dstFilePath = '';
        if (empty($fileName)) {
            $fileName = $config['file_prefix'] . md5($content);
            if ($needsize) {
                $imgInfo = getimagesize('data://image/jpeg;base64,' . base64_encode($content));
                $fileName = $fileName . "_" . $imgInfo[0] . "_" . $imgInfo[1];
            }
        }

        if (empty($config['directory'])) {
            $dstFilePath = "/$fileName.$fileType";
        } else {
            $dstFilePath = "/" . $config['directory'] . "/$fileName.$fileType";
        }
        $cosApi = new Api($config);
        $insertOnly = $overwrite == 0 ? 1 : 0;
        $ret = $cosApi->uploadBuffer($config['bucket'], $content, $dstFilePath, null, $insertOnly);

        //记录上传时的详情信息
        if (isset($ret["response_info"])) {
            $ralArg['cos_info'] = json_encode($ret["response_info"]);
        }

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);


        if ($ret['code'] !== 0) {
            $ralArg["errmsg"] = "upload file content to $dstFilePath failed, detail: " . $ret['message'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $ralArg["err_no"] = $ret['code'];
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return $ret['data']['access_url'];
    }

    public static function getUrlByFileName($cosName, $fileName, $fileType, $expire = 1800)
    {
        $file = "$fileName.$fileType";
        return self::getObjectUrl($cosName, $file, $expire);
    }

    /**
     * @brief 根据文件名和文件类型获取cos url地址
     *
     * @param string $cosName
     * @param string $file
     * @param int $expire
     * @return string|boolean
     */
    public static function getObjectUrl($cosName, $file, $expire = 1800)
    {
        if (empty($file)) {
            return '';
        }
        $cfg = self::getCosConfig($cosName);
        if (false === $cfg) {
            return false;
        }
        $directory = strval($cfg['directory']);
        $appId = strval($cfg['app_id']);
        $isPublic = 1 === $cfg['is_public'] ? true : false;
        $bucket = $cfg["bucket"];
        $host = $cfg["host"];
        $region = $cfg["ap_region"];
        $signHost = "${bucket}-${appId}.cos.{$region}.myqcloud.com";  # 签名host
        if (empty($host)) {
            $host = $signHost;
        }

        $fpath = empty($directory) ? "/$file" : "/$directory/$file";
        if ($isPublic) {            # 公共，可以直接访问
            return "https://$host$fpath";
        }
        $sign = self::buildSignature($cosName, "GET", $signHost, $fpath, $expire);
        return "https://$host$fpath?sign=$sign";
    }

    /**
     * @brief 根据pid获取cos原图url地址
     *
     * @param string $cosName   cos服务名
     * @param string $pid       图片pid
     * @param string $fileType  在cos的文件名后缀，默认jpg
     *
     * @return boolean|string 正常返回原图url
     */
    public static function getImageUrlByPid($cosName, $pid, $fileType = 'jpg')
    {
        if (empty($pid)) {
            return '';
        }
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            return false;
        }
        $bucket = strval($config['bucket']);
        $appId = strval($config['app_id']);
        $directory = strval($config['directory']);
        //        $prefix = strval($config['file_prefix']);
        if (!empty($directory)) {
            return "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SRC . "/$directory/$pid.$fileType";
        } else {
            return "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SRC . "/$pid.$fileType";
        }
    }

    //返回图片的meta信息（宽／高）
    public static function getImageMeta($bucket, $pid, $type = 'jpg')
    {
        $url = self::getImageUrlByPid($bucket, $pid, $type);
        $imageInfo = $url . "?imageInfo";
        //请求curl
        $timeout = 1000;//ms
        $header = array();
        $method = "GET";
        $header[] = 'Method:' . $method;
        $header[] = 'User-Agent: cos-php-sdk-v4.3.7';
        $header[] = 'Connection: keep-alive';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        curl_setopt($ch, CURLOPT_URL, $imageInfo);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, $timeout);//连接超时，ms
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, $timeout); //执行超时间，ms
        curl_setopt($ch, CURLOPT_DNS_USE_GLOBAL_CACHE, true);
        curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 60);

        $output = curl_exec($ch);
        if (false === $output) {
            $error = curl_error($ch);
            Bd_Log::warning("Tcos getImageMeta error[$error] url[$imageInfo]");
            curl_close($ch);
            return false;
        }
        curl_close($ch);
        $arrOutput = json_decode($output, true);
        if (empty($arrOutput)) {
            Bd_Log::warning('Tcos getImageMeta return error' . $output);
            return false;
        }
        $arrRes = array(
            'ext' => $arrOutput['format'],# 图片扩展名
            'width' => $arrOutput['width'],
            'height' => $arrOutput['height'],
            'size' => $arrOutput['size'],
            'bucket' => $bucket,
            'pid' => $pid,
            'sourceUrl' => $url,
        );
        return $arrRes;
    }

    /**
     * @brief 根据pid获取cos（实际是万象优图服务）缩略图url地址
     *
     * @param string $cosName   cos服务名
     * @param string $pid       图片pid
     * @param string $thumbnail 缩略方案，可以指定限制最大宽度或最大高度或同时限制，格式为"w/number","h/number","w/num/h/num"
     * @param string $fileType  在cos的文件名后缀，默认为jpg
     * @param string $outType   指定缩略图的格式，默认和原图一致
     *
     * @return boolean|string 正常返回缩略图url
     */
    public static function getThumbnailUrlByPid($cosName, $pid, $thumbnail = "w/270", $fileType = 'jpg', $outType = '')
    {
        if (empty($pid)) {
            return '';
        }
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            return false;
        }
        // 先检查是否支持缩略图
        if (!isset($config['thumbnail']) || intval($config['thumbnail']) != 1) {
            Bd_Log::warning("CosServ $cosName not support thumbnail!");
            return false;
        }
        $bucket = strval($config['bucket']);
        $appId = strval($config['app_id']);
        $directory = strval($config['directory']);
        $prefix = strval($config['file_prefix']);
        if (preg_match("/${prefix}/", $pid, $out) == 1) {
            if (!empty($directory)) {
                $url = "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SMALL . "/$directory/$pid.$fileType?imageView2/2/$thumbnail";
            } else {
                $url = "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SMALL . "/$pid.$fileType?imageView2/2/$thumbnail";
            }
        } else {
            // 解决测试环境为正式环境pid拼接url失败的问题
            $config = self::getCosConfig($cosName);
            if (false === $config) {
                return false;
            }
            $bucket = strval($config['bucket']);
            $appId = strval($config['app_id']);
            $directory = strval($config['directory']);
            $prefix = strval($config['file_prefix']);
            if (preg_match("/${prefix}/", $pid, $out) == 1) {
                if (!empty($directory)) {
                    $url = "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SMALL . "/$directory/$pid.$fileType?imageView2/2/$thumbnail";
                } else {
                    $url = "http://$bucket-$appId." . self::COS_NAMESPACE_IMAGE_SMALL . "/$pid.$fileType?imageView2/2/$thumbnail";
                }
            } else {
                return false;
            }
        }
        if (!empty($outType)) {
            $url .= "/format/$outType";
        }
        return $url;
    }

    /**
     * @brief 简单检查下pid是否符合命名规范
     *
     * @param $pid
     *
     * @return
     */
    public static function checkPidValid($pid)
    {
        $pattern = '/^(zyb|qa)([\d]*)_[0-9a-zA-Z]+(\.[0-9a-zA-Z]+)?$/';
        if (preg_match($pattern, $pid, $out) === 1) {
            return true;
        }
        return false;
    }

    /**
     * @brief 删除腾讯云cos上面的文件
     *
     * @param $cosName
     * @param $pid
     * @param $fileType
     *
     * @return
     */
    public static function deleteYunFile($cosName, $pid, $fileType = 'jpg')
    {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc" => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot" => self::$CosType,
            "service" => $cosName,
            "method" => __METHOD__,
        ];

        if (empty($pid)) {
            return true;
        }
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            return false;
        }
        $bucket = strval($config['bucket']);
        $directory = strval($config['directory']);
        if (!empty($directory)) {
            $dstFile = "/$directory/$pid.$fileType";
        } else {
            $dstFile = "/$pid.$fileType";
        }
        $cosApi = new Api($config);
        $res = $cosApi->delFile($bucket, $dstFile);

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);

        if (!isset($res['code']) || $res['code'] !== 0) {
            $ralArg["errmsg"] = "deleteYunFile failed, Detail : " . $res['message'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $ralArg["err_no"] = $res['code'];
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return true;
    }

    /**
     * 获取临时的秘钥
     * @param  string $cosName
     * @param  int $expire 过期时间，默认1800秒
     * @return boolean|array
     */
    public static function getTempKeys($cosName, $expire = 1800)
    {
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            return false;
        }
        $secretId = $config["secret_id"];
        $secretKey = $config["secret_key"];
        $bucket = $config["bucket"];
        $appId = $config["app_id"];
        $region = $config['ap_region'];
        $cosSts = new Sts();
        $res = $cosSts->getTempKeys($secretId, $secretKey, $bucket, $appId, $region, $expire);
        return $res;
    }

    /**
     * @brief 生成上传一个对象的签名地址
     *
     * @return  mixed 上传文件所需url和header
     * @param  string $bucket 文件存储桶
     * @param  string $objectKey 上传后对象的名称
     * @param  int $expired 上传超时控制，大文件上传需要
     * @param  array $header 业务自定义header
     * @see
     * @note
     * <AUTHOR> @date
     **/
    public function putObjectSignUrl($bucket, $objectKey, $expired = 5000, $header= array()) {
        $conf = $this->getCosConfig($bucket);
        $host = self::genTcosHost($conf);
        $header['Content-Type'] = 'application/octet-stream';
        $header['Host'] = $host;

        $sign = self::buildSignature($bucket, "put", $host, "/" . $objectKey, $expired);

        return array(
            "url" => "https://". $host. "/" .$objectKey."?sign=" . $sign,
            "httpheader"     => $header,
        );
    }

    /**
     * @brief 读取指定cos服务配置
     *
     * @param $cosName
     * @return
     */
    private static function getCosConfig($cosName)
    {
        if (self::$CosConfig === null) {
            self::$CosConfig = Bd_Conf::getConf('hk/cos');
        }
        $cosConfig = self::$CosConfig;
        if (!isset($cosConfig[$cosName])) {
            Bd_Log::warning("bucket $cosName is null");
            return false;
        }
        $config = $cosConfig[$cosName];
        if (empty($config['bucket'])) {
            Bd_Log::warning("bucket $cosName need bucket");
            return false;
        }
        if (empty($config['app_id'])) {
            Bd_Log::warning("bucket $cosName need app_id");
            return false;
        }
        if (empty($config['secret_id'])) {
            Bd_Log::warning("bucket $cosName need secret_id");
            return false;
        }
        if (empty($config['secret_key'])) {
            Bd_Log::warning("bucket $cosName need secret_key");
            return false;
        }
        if (empty($config['region'])) {
            Bd_Log::warning("bucket $cosName need region");
            return false;
        }
        if (empty($config['ap_region'])) {
            $config['ap_region'] = static::getApRegionByConf($config);
        }
        if (empty($config['file_prefix'])) {
            Bd_Log::warning("bucket $cosName need file_prefix");
            return false;
        }
        /*
        if (empty($config['directory'])) {
            Bd_Log::warning("CosConfig of $cosName need directory");
            return false;
        }*/
        if (!isset($config['is_public'])) {
            Bd_Log::warning("bucket $cosName need is_public");
            return false;
        }
        if (!isset($config['filesize_limit'])) {
            Bd_Log::warning("bucket $cosName need filesize_limit");
            return false;
        }

        # 对需要idc tag的参数进行特殊处理
        $tag = isset($config["tag"]) && is_array($config["tag"]) ? $config["tag"] : array();
        unset($config["tag"]);

        $config["host"] = strval($tag["host"]);
        $config['is_public'] = intval($config['is_public']);
        $config['filesize_limit'] = intval($config['filesize_limit']);
        return $config;
    }

    /**
     * @param $srcBucket
     * @param $dstBucket
     * @param $srcPid
     * @param $dstPid
     * @param bool $overwrite
     * @return array|bool|mixed
     * 只能在一个bucket内部移动, 测试发现无法在两个bucket中移动
     */
    public static function copyFile($srcBucket, $dstBucket, $srcPid, $dstPid, $overwrite = false)
    {
        $srcConfig = self::getCosConfig($srcBucket);
        if (false === $srcConfig) {
            return false;
        }
        if (empty($srcConfig['directory'])) {
            $srcFilePath = "/$srcPid.jpg";
        } else {
            $srcFilePath = "/" . $srcConfig['directory'] . "/$srcPid.jpg";
        }

        $dstConfig = self::getCosConfig($dstBucket);
        if (false === $dstConfig) {
            return false;
        }
        if (empty($dstConfig['directory'])) {
            $dstFilePath = "/$dstPid.jpg";
        } else {
            $dstFilePath = "/" . $dstConfig['directory'] . "/$dstPid.jpg";
        }

        $cosApi = new Api($dstConfig);
        $ret = $cosApi->copyFile($srcConfig['bucket'], $srcFilePath, $dstFilePath, $overwrite);

        if ($ret['code'] !== 0) {
            Bd_Log::warning("upload file content to $dstFilePath failed, detail: " . $ret['message']);
            return false;
        }
        return $ret;
    }

    /**
     * @param $srcBucket
     * @param $dstBucket
     * @param $srcPid
     * @param $dstPid
     * @param $fileType string 文件后缀
     * @return bool
     * 不同的bucket之间迁移
     */
    public static function copyBucket2Bucket($srcBucket, $dstBucket, $srcPid, $dstPid, $fileType = "jpg")
    {
        $srcConfig = self::getCosConfig($srcBucket);
        if (false === $srcConfig) {
            return false;
        }
        if (empty($srcConfig['directory'])) {
            $srcFilePath = "/{$srcPid}.{$fileType}";
        } else {
            $srcFilePath = "/" . $srcConfig['directory'] . "/{$srcPid}.{$fileType}";
        }


        $cosApi = new Api($srcConfig);
        $ret = $cosApi->getDownloadUrl($srcConfig['bucket'], $srcFilePath, 1800);
        $url = $ret['data']['source_url']; // 下载的地址

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $res = curl_exec($ch); // 发送请求
        curl_close($ch);// 释放句柄

        $ret = self::uploadFileContent($dstBucket, $res, $fileType, $dstPid);
        return $ret;
    }

    /*************以下内容是为了实现COS上传指定内网域名改写的, 是对QCloud\Cos\Api, Auth, HttpClient同名函数的改写****************/
    public static function uploadFileContentToCos($cosName, $content, $fileType = "jpg", $fileName = null, $overwrite = 0, $needsize = 0)
    {
        if (empty($content)) {
            Bd_Log::warning("file content is empty!");
            return false;
        }
        $config = self::getCosConfig($cosName);
        if (false === $config) {
            return false;
        }
        $fileSize = strlen($content);
        if ($fileSize > intval($config['filesize_limit'])) {
            Bd_Log::warning("file content length exceeds size limit " . $config['filesize_limit']);
            return false;
        }

        if (empty($config['directory'])) {
            $dstFilePath = "/$fileName.$fileType";
        } else {
            $dstFilePath = "/" . $config['directory'] . "/$fileName.$fileType";
        }

        // 现在不能使用api了，要仿照api的实现来完成
        $insertOnly = $overwrite == 0 ? 1 : 0;
        Hk_Util_Log::start("img_upload_cos");
        $ret = self::uploadBuffer($config['bucket'], $content, $dstFilePath, null, $insertOnly, $config);
        Hk_Util_Log::stop("img_upload_cos");
        if ($ret['code'] !== 0) {
            Bd_Log::addNotice("cos_upload_err", 1);
            Bd_Log::warning("upload file content to $dstFilePath failed, detail: " . $ret['message']);
            return false;
        }

        //

        return $ret['data'];
    }

    /* *
     * 上传内存中的内容 仿照QCloud\Cos\Api的uploadBuffer方法
     * @param  string  $bucket      bucket名称
     * @param  string  $content     文件内容，二进制安全
     * @param  string  $dstPath     上传的文件路径
     * @param  string  $bizAttr     文件属性
     * @param  int     $insertOnly  是否覆盖同名文件:0 覆盖,1:不覆盖
     * @param  array   $config      当前bucket的配置文件
     * @param  object  $httpClient  QCloud\
     * */
    public static function uploadBuffer(
        $bucket, $content, $dstPath,
        $bizAttr = null, $insertOnly = null, $config = NULL)
    {

        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc" => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot" => self::$CosType,
            "service" => $bucket,
            "method" => __METHOD__,
        ];

        if (strlen($content) >= Api::MAX_UNSLICE_FILE_SIZE) {
            return array(
                'code' => Api::COSAPI_PARAMS_ERROR,
                'message' => 'content larger then 20M, not supported',
                'data' => array()
            );
        }

        if (!$dstPath || !is_string($dstPath)
            || $dstPath[strlen($dstPath) - 1] == '/') {
            return array(
                'code' => Api::COSAPI_PARAMS_ERROR,
                'message' => 'dstPath ' . $dstPath . ' invalid',
                'data' => array()
            );
        }
        // 根据QCloud\Cos\Api的cosUrlEncode方法生成$dstPath
        $dstPath = str_replace('%2F', '/', rawurlencode($dstPath));;
        $expired = time() + Api::EXPIRED_SECONDS;
        // 生成url
        $url = self::generateResUrl($bucket, $dstPath, $config);
        // 生成签名
        $auth = new Auth($config['app_id'], $config['secret_id'], $config['secret_key']);
        $signature = $auth->createReusableSignature($expired, $bucket);
        $fileSha = sha1($content);

        $data = array(
            'op' => 'upload',
            'sha' => $fileSha,
            'biz_attr' => (isset($bizAttr) ? $bizAttr : ''),
            'filecontent' => $content,
        );

        if (isset($insertOnly) && strlen($insertOnly) > 0) {
            $data['insertOnly'] = (($insertOnly == 0 || $insertOnly == '0') ? 0 : 1);
        }

        $req = array(
            'url' => $url,
            'method' => 'post',
            'timeout' => $config['timeout'] ? intval($config['timeout']) : 60,
            'data' => $data,
            'header' => array(
                'Authorization: ' . $signature,
            ),
        );

        $ret = self::sendRequest($req);

        //记录上传时的详情信息
        if (isset($ret["response_info"])) {
            $ralArg['cos_info'] = json_encode($ret["response_info"]);
        }

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);

        if ($ret['code'] !== 0) {
            $ralArg["errmsg"] = "upload file content to $dstPath failed, detail: " . $ret['message'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return $ret;
        }

        $ralArg["err_no"] = $ret['code'];
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return $ret;
    }


    /*
     * 内部公共方法, 构造URL
     * @param  string  $bucket
     * @param  string  $dstPath
     */
    public static function generateResUrl($bucket, $dstPath, $config)
    {
        $endPoint = "http://${bucket}-1253445850.zuoyebang.cc/files/v2/";
        return $endPoint . $config['app_id'] . '/' . $bucket . $dstPath;
    }

    // 仿照QCloud\Cos\Api的sendRequest方法
    public static function sendRequest($req, $jsonResponse = true)
    {
        $httpClient = new HttpClient();
        $rsp = $httpClient->sendRequest($req);

        $info = $httpClient->info();

        if ($rsp === false) {
            return array(
                'code' => Api::COSAPI_NETWORK_ERROR,
                'message' => 'network error',
                'response_info' => $info
            );
        }

        if ($jsonResponse) {
            $ret = json_decode($rsp, true);
            if ($ret === NULL) {
                return array(
                    'code' => Api::COSAPI_NETWORK_ERROR,
                    'message' => $rsp,
                    'data' => array(),
                    'response_info' => $info
                );
            }

            if (is_array($ret)) {
                //将访问详情带回，记录日志
                $ret["response_info"] = $info;
            }

            return $ret;
        }

        return $rsp;
    }

    /**
     * 生成临时签名
     *
     * @param string $bucketName
     * @return object|boolean
     */
    private static function buildSignature($bucketName, $httpMethod, $host, $path, $expire = 1800)
    {
        $cfg = self::getCosConfig($bucketName);
        if (false === $cfg) {
            return false;
        }
        $secretId = $cfg["secret_id"];
        $secretKey = $cfg["secret_key"];
        $signature = new SignatureV5($secretId, $secretKey);
        $expiration = "+${expire} seconds";
        $sign = $signature->createAuthorization($httpMethod, $host, $path, $expiration);
        return urlencode($sign);
    }

    /**
     * 可跨存储桶的对象复制，实现对象移动、重命名、修改对象元数据和创建副本等，建议对象大小为1M到5G，超过5G的对象使用其它分块上传方法
     * 参见：https://cloud.tencent.com/document/product/436/10881#.E5.93.8D.E5.BA.94
     * @param $destBucket 目标存储桶名称，格式：BucketName
     * @param $destkey 目标对象Key,如dest_pic.jpg
     * @param $sourceBucket 源存储桶名称，格式：BucketName
     * @param $sourceKey 源对象Key,如source_pic.jpg
     * @param string $Metairective 设置为 Copy 时，忽略设置的用户元数据信息直接复制，设置为 Replaced 时，按设置的元信息修改元数据，当目标路径和源路径一样时，必须设置为 Replaced
     * @param array $optionHeaders 可查阅腾讯文档实现扩展功能：如'Replaced'方式时时，更改用户数据信息等
     * @param boolean $needTcosError 默认为false,只在put成功才返回详情，否则返回false; 为满足个别业务要求，传true时，tcos返回的信息不管对错原样返回，需自行区分
     * @return array|mixed
     */
    public static function putFile($destBucket, $destkey, $sourceBucket, $sourceKey, $metaDirective = self::PUT_DIRECTIVE_COPY, $optionHeaders = array(), $needTcosError = false)
    {
        if (!$destBucket || !$destkey || !$sourceBucket || !$sourceKey) {
            return array(
                'code' => Api::COSAPI_PARAMS_ERROR,
                'message' => 'params err',
                'data' => array()
            );
        }

        $destCfg = self::getBucketTcosConfig($destBucket);
        $sourceCfg = self::getBucketTcosConfig($sourceBucket);

        if (!$destCfg || !$sourceCfg) {
            return array(
                'code' => Api::COSAPI_PARAMS_ERROR,
                'message' => 'can not find bucket',
                'data' => array()
            );
        }


        // 生成sourceFile地址
        $sourceHost = self::genTcosHost($sourceCfg);
        $sourcePath = "/" . $sourceKey;
        if ($sourceCfg['directory']) {
            $sourceDir = str_replace('%2F', '/', rawurlencode($sourceCfg['directory']));
            $sourcePath = "/{$sourceDir}/$sourceKey";
        }

        $sourceFile = $sourceHost . $sourcePath;

        // 生成签名
        $expired = time() + Api::EXPIRED_SECONDS;

        //目标bucket生成签名
        if ($destCfg['directory']) {
            $destDir = str_replace('%2F', '/', rawurlencode($destCfg['directory']));
            $destkey = $destDir . "/" . $destkey;
        }

        $destHost = self::genTcosHost($destCfg);
        $url = "https://" . $destHost . "/" . $destkey;

        if (!$destCfg['is_public']) {
            $sign = self::buildSignature($destBucket, "PUT", $destHost, "/" . $destkey, $expired);
            $url .= "?sign=" . $sign;
        }

        //目标bucket签名
        $auth = new Auth($destCfg['app_id'], $destCfg['secret_id'], $destCfg['secret_key']);
        $signature = $auth->createReusableSignature($expired, $destCfg['bucket']);


        $req = array(
            'url' => $url,
            'method' => 'put',
            'timeout' => $destCfg['timeout'] ? intval($destCfg['timeout']) : 60,
            'header' => array(
                'Authorization: ' . $signature,
                'x-cos-copy-source: ' . $sourceFile,
                'x-cos-metadata-directive: ' . $metaDirective,
            ),
        );


        //可作扩展操作
        if ($optionHeaders) {
            foreach ($optionHeaders as $k => $v) {
                $req['header'][] = $k . ": " . $v;
            }
        }

        $resp = self::sendRequest($req, false);
        if (!$resp || is_array($resp)) {
            Bd_Log::warning("putFile error, destBucket:{$destBucket}, sourceBucket:{$sourceBucket}, sourceKey:{$sourceKey}, response: {$resp}");
            return false;
        }


        $xml = (array)simplexml_load_string($resp);
        if ($xml && isset($xml['ETag'])) {
            return $xml;
        }

        Bd_Log::warning("putFile error, destBucket:{$destBucket}, sourceBucket:{$sourceBucket}, sourceKey:{$sourceKey}, response: {$resp}");

        return $needTcosError ? $xml : false;
    }


    private static function getBucketTcosConfig($bucketName)
    {
        $cfg = self::getCosConfig($bucketName);
        if (!$cfg) {
            return false;
        }

        $realCfg = [
            'bucket' => $cfg['bucket'],
            'app_id' => $cfg['app_id'],
            'secret_id' => $cfg['secret_id'],
            'secret_key' => $cfg['secret_key'],
            'is_public' => $cfg['is_public'],
            'region' => $cfg['ap_region'],
            'picture_region' => $cfg['picture_region'],
            'directory' => $cfg['directory'],
            'file_prefix' => $cfg['file_prefix'],
            'thumbnail' => $cfg['thumbnail'],
        ];

        if (isset($cfg['timeout'])) {
            $realCfg['timeout'] = $cfg['timeout'];
        }

        return $realCfg;
    }


    private static function genTcosHost($tcosConf)
    {
        $namespace = "myqcloud.com";

        return "{$tcosConf['bucket']}-{$tcosConf['app_id']}.cos.{$tcosConf['region']}.{$namespace}";
    }

    /**
     * 获取v5版本的区域配置
     * @author: <EMAIL>
     * @dateTime: 2022/3/10 2:42 PM
     * @param array $tCosConf cos配置
     * @return string
     */
    public static function getApRegionByConf($tCosConf)
    {
        if (!empty($tCosConf['ap_region'])) {
            return $tCosConf['ap_region'];
        }
        $regionMap = [
            'tj'  => 'ap-beijing-1', // 北京一区（华北）
            'bj'  => 'ap-beijing', // 北京
            'sh'  => 'ap-shanghai', // 上海（华东）
            'gz'  => 'ap-guangzhou', // 广州（华南）
            'cd'  => 'ap-chengdu', // 成都（西南）
            'hk'  => 'ap-hongkong', // 中国香港
            'sgp' => 'ap-singapore', // 新加坡
            'ca'  => 'ap-toronto', // 多伦多
            'ger' => 'ap-frankfurt', // 法兰克福
        ];
        return $regionMap[$tCosConf['region']] ?? static::REGION_V5_BEIJING;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
