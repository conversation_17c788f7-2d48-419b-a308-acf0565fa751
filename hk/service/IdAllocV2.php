<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file IdAllocV2.php
 * <AUTHOR>
 * @date 2019/02/27 21:07:18
 * @brief 基于snowflake算法实现的id分配器
 * @brief 每个产品线下全局唯一 趋势递增
 * @brief http://wiki.afpai.com/pages/viewpage.action?pageId=25932888
 *
 **/

class Hk_Service_IdAllocV2 {

    //产品线id -- search record迁移到hbase --for <PERSON><PERSON><PERSON><PERSON><PERSON>
    const PRODUCT_ID_SEARCH_RECORD_TO_HBASE = 100;
    //产品线id -- 家长版微信口算练习生成记录ID --for zhangbo01
    const PRODUCT_ID_PARENT_WEIXIN_PRACTICE = 101;
    //产品线id -- vip练习 --for likelei
    const PRODUCT_ID_VIP_PRACTICE = 102;
    //产品线id -- 家长版记录id --for zhangbo01
    const PRODUCT_ID_PARENT_RECORD = 103;
    //产品线id -- 家长版搜索id --for zhangbo01
    const PRODUCT_ID_PARENT_SEARCH = 104;
    //产品线id -- 积分商城 --for guojianxiang
    const PRODUCT_ID_POINT_MALL = 105;
    //产品线id -- 抽奖平台 --for guojianxiang
    const PRODUCT_ID_PLAT_LOTTERY = 106;
    //产品线id -- 帮帮英语试卷提交 --for wangzhenlu 
    const PRODUCT_ID_BANG_EXAM_ANSWER = 107;
    //产品线id -- 用于公司运营活动的唯一ID --for duhaifeng
    const PRODUCT_ID_COMPANY_MARKET = 108;
    //产品线id -- 用于平台练习业务--for wanglipeng
    const PRODUCT_ID_PLAT_PRACTICE = 109;
    //产品线id -- 用于平台抽奖业务交易id--for guojianxiang
    const PRODUCT_ID_PLAT_LOTTERY_TRADEID = 110;
    //#作业帮口算批改 -- for 家长版的xingfu
    const PRODUCT_ID_CALCULATE_CORRECT = 111;
    //#作业帮口算记录 -- for 家长版的xingfu
    const PRODUCT_ID_CALCULATE_RECORD = 112;
    //#vip问答 生成vip免费提问卡的唯一id-- for zengxianbo
    const PRODUCT_ID_VIP_FREE_QUESTION_CARD_ID = 113;


    static $hostName = '';


    const BNS_NAME_BJCQ = 'iddistributor';
    const BNS_NAME_BJDD = 'iddistributor_dd';


    /**
     * 生成100产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForSearchrecord() {
        return self::createId(self::BNS_NAME_BJDD, self::PRODUCT_ID_SEARCH_RECORD_TO_HBASE);
    }
    /**
     * 生成101产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForParentWeixinPractice() {
        $bns = self::getBnsByHostname();
        return self::createId($bns, self::PRODUCT_ID_PARENT_WEIXIN_PRACTICE);
    }
    /**
     * 生成102产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForVipPractice() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_VIP_PRACTICE);
    }
    /**
     * 生成103产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForParentRecord() {
        $bns = self::getBnsByHostname();
        return self::createId($bns, self::PRODUCT_ID_PARENT_RECORD);
    }
    /**
     * 生成104产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForParentSearch() {
        $bns = self::getBnsByHostname();
        return self::createId($bns, self::PRODUCT_ID_PARENT_SEARCH);
    }
    /**
     * 生成105产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForPointMall() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_POINT_MALL);
    }
    /**
     * 生成106产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForPlatLottery() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_PLAT_LOTTERY);
    }
    /**
     * 生成107产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForBangEngExamAnswer() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_BANG_EXAM_ANSWER);
    }
    /**
     * 生成108产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForCompanyMarket() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_COMPANY_MARKET);
    }
    /**
     * 生成109产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForPlatPractice() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_PLAT_PRACTICE);
    }
    /**
     * 生成110产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForPlatLotteryTradeId() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_PLAT_LOTTERY_TRADEID);
    }
    /**
     * 生成111产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForCalculateCorrect() {
        return self::createId(self::BNS_NAME_BJDD, self::PRODUCT_ID_CALCULATE_CORRECT);
    }
    /**
     * 生成112产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForCalculateRecord() {
        return self::createId(self::BNS_NAME_BJDD, self::PRODUCT_ID_CALCULATE_RECORD);
    }
    /**
     * 生成113产品线的id
     *
     * @return int64 0表示失败 其他表示生成后的id
     */
    public static function createIdForVipFreeQuestionCardId() {
        return self::createId(self::BNS_NAME_BJCQ, self::PRODUCT_ID_VIP_FREE_QUESTION_CARD_ID);
    }
    /**
     * 根据本机的hostname获取使用哪个bns
     * @return string 
     */
    public static function getBnsByHostname() {
        if (empty(self::$hostName)) {
            self::$hostName = gethostname();
        }
        if (stripos(self::$hostName,'.bjdd.') !== false) {
            return self::BNS_NAME_BJDD;
        }
        return self::BNS_NAME_BJCQ;
    }
    /**
     * 生成一个id
     *
     * @param  int    $productId 产品线id
     * @param  string $bnsName bns名称
     * @return int64 0表示生成失败 其他为成功
     */
    private static function createId($bnsName,$productId) {
        Hk_Util_Log::setLog('idUseBns', $bnsName);
        ral_set_pathinfo('/idalloc/create/'.$productId);

        Hk_Util_Log::start('idAllocV2Create');
        $ret = ral($bnsName, 'POST', [], 123, []);
        Hk_Util_Log::stop('idAllocV2Create');

        if (false === $ret) {
            $errNo  = ral_get_errno();
            $errMsg = ral_get_error();
            $status = ral_get_protocol_code();
            Bd_Log::warning('Error[idallocv2RalConnect]Detail[errno:'.$errNo.' errmsg:'.$errMsg.' protocol_status:'.$status.' productId:'.$productId.']');
            return 0;
        }

        if (isset($ret['errNo']) && $ret['errNo'] == 0
            && !empty($ret['data']) && !empty($ret['data']['id'])
            && $ret['data']['id'] > 0) {
            return $ret['data']['id'];
        } else {
            Bd_Log::warning('Error[idallocv2Process]Detail[response:'.json_encode($ret).']');
            return 0;
        }
    }
}
