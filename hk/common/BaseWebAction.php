<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Base.php
 * <AUTHOR>
 * @date 2013/12/06 13:57:15
 * @brief 抽象基类
 *
 **/

abstract class Hk_Common_BaseWebAction extends Ap_Action_Abstract {

    // 日志变量
    protected $logVars = array(
        //'app_version' => '',
        //'app_terminal' => '',
        'api_version' => '',
        'api_module' => '',
        'api_action' => '',
        'pro_errno' => '',
        'pro_un' => '',
        'pro_uid' => '',
        'pv' => '',
    );

    // 用户信息
    protected $_userInfo = array(
        'isLogin' => false,
        'uid' => 0,
        'uname' => '',
    );

    // 使用Filter过滤过的参数列表
    protected $_requestParam;

    // 配置信息项
    protected $_actionName = null;

    // controller name
    protected $_controllor = 'action';

    // api version
    protected $_apiVersion = 'v1';

    // app version 两位版本后端接口映射版本号
    //protected $_appVersion = 0;

    // app versionCode 具体客户端中的版本号
    //protected $_appVersionCode = 0;

    // app terminal
    //protected $_appTerminal = '';

    // app type
    //protected $_appType = 0;

    // app source
    //protected $_appSource = 0;

    //是否跳过输入输出校验
    //protected $_skipValidation = 0;

    //输出转码
    //protected $_outIconv = null;

    //出入转码
    //protected $_iconv = null;

    //输入验证配置
    //protected $_input = null;

    //输出验证配置
    //protected $_output = null;

    //是否需要用户信息
    protected $_needNAUserInfo = 0;

    // request
    protected $_request = null;

    // response
    protected $_tplData = array(
        'errNo' => 0,
        'errstr' => 'success',
        'data' => array(),
    );


    /*
     * 子类特有逻辑，强制子类必须实现
     */
    abstract protected function invoke();


    //action配置文件读取,参数初始化
    protected function _init() {
        // 初始化配置文件选项
        $objRequest = $this->getRequest();
        $this->_controllor = strtolower($objRequest->getControllerName());
        $this->_actionName = strtolower($objRequest->getActionName());
        $this->_apiVersion = strtolower($objRequest->getParam('apiversion', 'v1'));
        Bd_AppEnv::setCurrAction($this->_actionName);
        Bd_AppEnv::setCurrCtl($this->_controllor);

        //获取用户信息
        $arrUserInfo = Saf_SmartMain::getUserInfo();
        if (intval($arrUserInfo['uid']) > 0) {
            $this->_userInfo = $arrUserInfo;
            if ($this->_needNAUserInfo) {
                $objDsUcloud = new Hk_Ds_User_Ucloud ();
                $arrUcloud = $objDsUcloud->getUserInfo ($this->_userInfo['uid']);
                if ($arrUcloud) {
                    $this->_userInfo['naUcloud'] = $arrUcloud;
                } else {
                    $this->_userInfo['naUcloud'] = array ();
                }
            }
        }

        //获取请求参数
        $arrRequestParam = Saf_SmartMain::getCgi();
        $this->_requestParam = !is_null($arrRequestParam['request_param'])
        ? $arrRequestParam['request_param'] : array();

        Hk_Util_Log::setLog ('request_param', json_encode ($this->_requestParam));
    }

    //参数校验
    protected function _before() {
        return true;
    }


    //输出参数校验
    protected function _after() {
    }
    //统计处理
    protected function _processLog() {

        $this->logVars['api_version'] = $this->_apiVersion;
        $this->logVars['api_module'] = $this->_controllor;
        $this->logVars['api_action'] = $this->_actionName;
        $this->logVars['pro_errno'] = $this->_tplData['errNo'];
        $this->logVars['pro_un'] = $this->_userInfo['uname'];
        $this->logVars['pro_uid'] = $this->_userInfo['uid'];
//         $this->logVars['app_version'] = $this->_appVersion;
//         $this->logVars['app_terminal'] = $this->_appTerminal;
        if (empty($this->logVars['pv'])) {
            $this->logVars['pv'] = sprintf("%s/%s/%s",$this->_controllor, $this->_apiVersion, $this->_actionName);
        }

        foreach ($this->logVars as $key => $value) {
            Hk_Util_Log::setLog($key, $value);
        }

        $idc = Bd_Conf::getConf('idc/cur');
        Hk_Util_Log::setLog('idc', $idc);

        Hk_Util_Log::printLog();
    }

    protected function _antispam () {}

    public function execute() {
        Hk_Util_Log::start('ts_all');
        try {
            Hk_Util_Log::start('ts_init');
            $this->_init();
            Hk_Util_Log::stop('ts_init');

            Hk_Util_Log::start('ts_antispam');
            $this->_antispam();
            Hk_Util_Log::stop('ts_antispam');

            Hk_Util_Log::start('ts_before');
            $ret = $this->_before();
            Hk_Util_Log::stop('ts_before');
            if ($ret === true) {
                Hk_Util_Log::start('ts_invoke');
                $res = $this->invoke();
                Hk_Util_Log::stop('ts_invoke');

                Hk_Util_Log::start('ts_after');
                $ret = $this->_after();
                Hk_Util_Log::stop('ts_after');
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo']  = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
        }
        Hk_Util_Log::stop('ts_all');
        $this->_processLog();
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
