<?php
/**
 * Copyright (c) 1998-2018 作业帮. (http://www.zybang.com)
 * o开头的变量是对象,a开头的变量是数组
 * Author: MaRongcai
 * Date: 19/8/21
 * Class Navigator
 * 全链路压测判断是否是压测流量的SDK
 */

class Hk_Util_Navigator
{
    const NAVIGATOR_OCS_URI = '/qa/test';
    //满足2^N 10*N分表算法；保持取余均匀
    const SHIFT_MOD_ELEMENT = 102400;

    /**
     * UID 偏移算法
     * @param     $uid
     * @param     $shiftTimes
     * @return int|null
     * @throws Zb_Util_Exception
     */
    public static function shiftUid($uid, $shiftTimes)
    {
        $oMagician              = new HkBitMagic(self::SHIFT_MOD_ELEMENT);
        return $oMagician->puzzle($uid,$shiftTimes);
    }

    /**
     * UID 回归算法
     * @param $uid
     * @return int|null
     * @throws Zb_Util_Exception
     */
    public static function regressUid($uid)
    {
        $isPressure             = self::isPressure();
        if ($isPressure) {
            $oMagician          = new HkBitMagic(self::SHIFT_MOD_ELEMENT);
            return $oMagician->regress($uid);
        }
        return $uid;
    }

    public static function isPressure()
    {
        $callerURI              = isset($_SERVER['HTTP_X_BD_CALLER_URI']) ? $_SERVER['HTTP_X_BD_CALLER_URI'] : null;
        if (empty($callerURI)) {
            return false;
        }

        $aCallerURI             = explode(',',$callerURI);
        if ($aCallerURI[1] == self::NAVIGATOR_OCS_URI && $aCallerURI[2] == 1) {
            return true;
        }
        return false;
    }
}



/**
 * Class BitMagic
 * 正整数 M 经过 N 次偏移、进行一些运算得Z，Z满足 Z%X=M%x
 * |-- 9位保留 --| |-- 15位做偏移 --| |---       定长40位，初始值的容量        --|
 *    0000000 0     000000 0000000    0000000 0000000 0000000 0000000 0000000
 */
class HkBitMagic
{
    protected $modElement       = null;
    protected $shiftBit         = 40;
    public function __construct ($mod = 1024)
    {
        $mod                    = intval($mod);

        //模数不能为0且必须是正整数
        if (empty($mod) || ($mod < 0) || !is_integer($mod)) {
            throw new Zb_Util_Exception(Zb_Util_ExceptionCodes::PARAM_ERROR);
        }
        $this->modElement       = $mod;
    }

    /**
     * 偏移
     * @param $puzzle
     * @param $puzzleTimes
     * @return int|null
     */
    public function puzzle($puzzle, $puzzleTimes)
    {
        //移位数
        $shiftElement           = ($puzzleTimes << $this->shiftBit);
        //移位数的余数；其实就是shiftBase%modElement; 位运算效率高于%运算；%运算是效率最低的
        $remainder              = ($shiftElement % $this->modElement);
        //原始值余数的补数
        $complement             = $this->modElement - $remainder;
        return $shiftElement + $puzzle + $complement;
    }

    /**
     * 回元
     * @param $shiftElement
     * @return int|null
     */
    public function regress($shiftElement)
    {
        //N次移位的初阶值
        $shiftBase              = (($shiftElement >> $this->shiftBit) << $this->shiftBit);
        //初阶的余数
        $reminder               = ($shiftBase % $this->modElement);
        $complement             = $this->modElement - $reminder;
        return $shiftElement - $shiftBase - $complement;
    }
}