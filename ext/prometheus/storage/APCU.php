<?php

class Ext_Prometheus_Storage_APCU implements Ext_Prometheus_Storage_Adapter {

    const PROMETHEUS_PREFIX = 'prom';

    /**
     * @return Ext_Prometheus_MetricFamilySamples[]
     */
    public function collect() {
        $aMetric = $this->collectHistograms();
        $aMetric = array_merge($aMetric, $this->collectGauges());
        $aMetric = array_merge($aMetric, $this->collectCounters());
        return $aMetric;
    }

    public function updateHistogram(array $data) {
        // Initialize the sum
        $sumKey = $this->histogramBucketValueKey($data, 'sum');
        $isExists = apcu_add($sumKey, $this->toInteger(0));

        // If sum does not exist, assume a new histogram and store the metadata
        if($isExists) {
            apcu_store($this->metaKey($data), json_encode($this->metaData($data)));
        }

        // Atomically increment the sum
        $isDone = false;
        while(!$isDone) {
            $oldItem = apcu_fetch($sumKey);
            $isDone = apcu_cas($sumKey, $oldItem, $this->toInteger($this->fromInteger($oldItem) + $data['value']));
        }

        // Figure out in which bucket the observation belongs
        $bucketToIncrease = '+Inf';
        foreach($data['buckets'] as $bucket) {
            if($data['value'] <= $bucket) {
                $bucketToIncrease = $bucket;
                break;
            }
        }

        // Initialize and increment the bucket
        apcu_add($this->histogramBucketValueKey($data, $bucketToIncrease), 0);
        apcu_inc($this->histogramBucketValueKey($data, $bucketToIncrease));
    }

    public function updateGauge(array $data) {
        $valueKey = $this->valueKey($data);
        if($data['command'] == Ext_Prometheus_Storage_Adapter::COMMAND_SET) {
            apcu_store($valueKey, $this->toInteger($data['value']));
            apcu_store($this->metaKey($data), json_encode($this->metaData($data)));
        } else {
            $isExists = apcu_add($valueKey, $this->toInteger(0));
            if($isExists) {
                apcu_store($this->metaKey($data), json_encode($this->metaData($data)));
            }
            $isDone = false;
            while(!$isDone) {
                $oldItem = apcu_fetch($valueKey);
                $isDone = apcu_cas($valueKey, $oldItem, $this->toInteger($this->fromInteger($oldItem) + $data['value']));
            }
        }
    }

    public function updateCounter(array $data) {
        $isExists = apcu_add($this->valueKey($data), 0);
        if($isExists) {
            apcu_store($this->metaKey($data), json_encode($this->metaData($data)));
        }
        apcu_inc($this->valueKey($data), $data['value']);
    }

    public function flushAPCU() {
        apcu_clear_cache();
    }

    /**
     * @param array $data
     * @return string
     */
    private function metaKey(array $data) {
        return implode(':', array(self::PROMETHEUS_PREFIX, $data['type'], $data['name'], 'meta'));
    }

    /**
     * @param array $data
     * @return string
     */
    private function valueKey(array $data) {
        return implode(':', array(
            self::PROMETHEUS_PREFIX,
            $data['type'],
            $data['name'],
            $this->encodeLabelValues($data['labelValues']),
            'value'
        ));
    }

    /**
     * @param array $data
     * @return string
     */
    private function histogramBucketValueKey(array $data, $bucket) {
        return implode(':', array(
            self::PROMETHEUS_PREFIX,
            $data['type'],
            $data['name'],
            $this->encodeLabelValues($data['labelValues']),
            $bucket,
            'value'
        ));
    }

    /**
     * @param array $data
     * @return array
     */
    private function metaData(array $data) {
        $aMetaData = $data;
        unset($aMetaData['value']);
        unset($aMetaData['command']);
        unset($aMetaData['labelValues']);
        return $aMetaData;
    }

    /**
     * @return array
     */
    private function collectCounters() {
        $aCounter = array();
        $oAPCUCounter = new \APCUIterator('/^prom:counter:.*:meta/');
        if(empty($oAPCUCounter)) {
            return $aCounter;
        }


        foreach($oAPCUCounter as $counter) {
            $aMetaData = json_decode($counter['value'], true);
            $data = array(
                'name'       => $aMetaData['name'],
                'help'       => $aMetaData['help'],
                'type'       => $aMetaData['type'],
                'labelNames' => $aMetaData['labelNames'],
            );

            $oSampleCounter = new \APCUIterator('/^prom:counter:'.$aMetaData['name'].':.*:value/');
            if($oSampleCounter) {
                foreach($oSampleCounter as $value) {
                    $parts = explode(':', $value['key']);
                    $aLabelItem = $parts[3];
                    $data['samples'][] = array(
                        'name'        => $aMetaData['name'],
                        'labelNames'  => array(),
                        'labelValues' => $this->decodeLabelValues($aLabelItem),
                        'value'       => $value['value']
                    );
                }
                $this->sortSamples($data['samples']);
            }

            $aCounter[] = new Ext_Prometheus_MetricFamilySamples($data);
        }

        return $aCounter;
    }

    /**
     * @return array
     */
    private function collectGauges() {
        $aGauge = array();
        $oAPCUGauge = new \APCUIterator('/^prom:gauge:.*:meta/');
        if(empty($oAPCUGauge)) {
            return $aGauge;
        }

        foreach($oAPCUGauge as $gauge) {
            $metaData = json_decode($gauge['value'], true);
            $data = array(
                'name'       => $metaData['name'],
                'help'       => $metaData['help'],
                'type'       => $metaData['type'],
                'labelNames' => $metaData['labelNames'],
            );

            $oSampleGauge = new \APCUIterator('/^prom:gauge:'.$metaData['name'].':.*:value/');
            if(!empty($oSampleGauge)) {
                foreach($oSampleGauge as $value) {
                    $parts = explode(':', $value['key']);
                    $labelValues = $parts[3];
                    $data['samples'][] = array(
                        'name'        => $metaData['name'],
                        'labelNames'  => array(),
                        'labelValues' => $this->decodeLabelValues($labelValues),
                        'value'       => $this->fromInteger($value['value'])
                    );
                }
                $this->sortSamples($data['samples']);
            }

            $aGauge[] = new Ext_Prometheus_MetricFamilySamples($data);
        }
        return $aGauge;
    }

    /**
     * @return array
     */
    private function collectHistograms() {
        $aHistogram = array();
        $oAPCUHistogram = new \APCUIterator('/^prom:histogram:.*:meta/');
        if(empty($oAPCUHistogram)) {
            return $aHistogram;
        }
        foreach($oAPCUHistogram as $histogram) {
            $metaData = json_decode($histogram['value'], true);
            $data = array(
                'name'       => $metaData['name'],
                'help'       => $metaData['help'],
                'type'       => $metaData['type'],
                'labelNames' => $metaData['labelNames'],
                'buckets'    => $metaData['buckets']
            );

            // Add the Inf bucket so we can compute it later on
            $data['buckets'][] = '+Inf';

            $histogramBuckets = array();
            $oSampleHistogram = new \APCUIterator('/^prom:histogram:'.$metaData['name'].':.*:value/');
            if($oSampleHistogram) {
                foreach($oSampleHistogram as $value) {
                    $parts = explode(':', $value['key']);
                    $labelValues = $parts[3];
                    $bucket = $parts[4];
                    // Key by labelValues
                    $histogramBuckets[$labelValues][$bucket] = $value['value'];
                }

                // Compute all buckets
                $labels = array_keys($histogramBuckets);
                sort($labels);

                foreach($labels as $labelValues) {
                    $acc = 0;
                    $decodedLabelValues = $this->decodeLabelValues($labelValues);
                    foreach($data['buckets'] as $bucket) {
                        $bucket = (string)$bucket;
                        if(!isset($histogramBuckets[$labelValues][$bucket])) {
                            $data['samples'][] = array(
                                'name'        => $metaData['name'].'_bucket',
                                'labelNames'  => array('le'),
                                'labelValues' => array_merge($decodedLabelValues, array($bucket)),
                                'value'       => $acc
                            );
                        } else {
                            $acc += $histogramBuckets[$labelValues][$bucket];
                            $data['samples'][] = array(
                                'name'        => $metaData['name'].'_'.'bucket',
                                'labelNames'  => array('le'),
                                'labelValues' => array_merge($decodedLabelValues, array($bucket)),
                                'value'       => $acc
                            );
                        }
                    }

                    // Add the count
                    $data['samples'][] = array(
                        'name'        => $metaData['name'].'_count',
                        'labelNames'  => array(),
                        'labelValues' => $decodedLabelValues,
                        'value'       => $acc
                    );

                    // Add the sum
                    $data['samples'][] = array(
                        'name'        => $metaData['name'].'_sum',
                        'labelNames'  => array(),
                        'labelValues' => $decodedLabelValues,
                        'value'       => $this->fromInteger($histogramBuckets[$labelValues]['sum'])
                    );

                }
            }

            $aHistogram[] = new Ext_Prometheus_MetricFamilySamples($data);
        }
        return $aHistogram;
    }

    /**
     * @param mixed $val
     * @return int
     */
    private function toInteger($val) {
        return unpack('Q', pack('d', $val))[1];
    }

    /**
     * @param mixed $val
     * @return int
     */
    private function fromInteger($val) {
        return unpack('d', pack('Q', $val))[1];
    }

    /**
     * @param array $aSample
     */
    private function sortSamples(array &$aSample) {
        usort($aSample, function($a, $b) {
            return strcmp(implode("", $a['labelValues']), implode("", $b['labelValues']));
        });
    }

    /**
     * @param array $aLabelItem
     * @return string
     * @throws RuntimeException
     */
    private function encodeLabelValues(array $aLabelItem) {
        $json = json_encode($aLabelItem);
        if(false === $json) {
            throw new RuntimeException(json_last_error_msg());
        }
        return base64_encode($json);
    }

    /**
     * @param string $aLabelItem
     * @return array
     * @throws RuntimeException
     */
    private function decodeLabelValues($aLabelItem) {
        $json = base64_decode($aLabelItem, true);
        if(false === $json) {
            throw new RuntimeException('Cannot base64 decode label values');
        }

        $aDecodeLabel = json_decode($json, true);
        if(false === $aDecodeLabel) {
            throw new RuntimeException(json_last_error_msg());
        }
        return $aDecodeLabel;
    }
}
