<?php
/**
 * @name    Oplib_Ds_OperationTehuike
 * @desc    特惠课
 * <AUTHOR>
 */
class Oplib_Ds_OperationTehuike
{
    const ALL_FILEDS = 'id,pageType,classType,subject,courseType,courseList,status,createTime,updateTime,extData';

    const STATUS_ENABLE     = 1;  // 上线
    const STATUS_OFFLINE    = 2;  // 下线
    const STATUS_DISABLE    = 3;  // 删除

	static $STATUS_ARRAY = array(
		self::STATUS_ENABLE => '上线',
		self::STATUS_OFFLINE=> '下线',
		self::STATUS_DISABLE=> '删除',
	);

    const COURSE_TYPE_SKU = 1; // 课程类型常量SKU
    const COURSE_TYPE_SPU = 2; // 课程类型常量SPU

    const CLASS_TYPE_THK = 1;   // 特惠课
    const CLASS_TYPE_STK = 2;   // 试听课
    const CLASS_TYPE_ZTK = 3;   // 专题课

    const PAGE_TYPE_ZHB = 1;    // 综合版
    const PAGE_TYPE_QSP = 2;    // 强视频版

    // 所有的班型
    static $ALL_CLASS_TYPE = [self::CLASS_TYPE_THK,self::CLASS_TYPE_STK,self::CLASS_TYPE_ZTK,];

	private $_objDaoTehuike;
	private $_objRedis;

	public function __construct()
	{
		$this->_objDaoTehuike = new Oplib_Dao_OperationTehuike();
        $this->_objRedis = Hk_Service_RedisClient::getInstance("zhibo");
	}
	public function getActById($actId,$arrFields = [],$useCache = true)
    {
        if (intval($actId) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$actId]");
            return false;
        }

        if($useCache) {
            $ret = $this->_objRedis->get(sprintf(Oplib_Const_Cache::TEHUIKE_ACT_INFO,$actId));
            if ($ret) {
                return json_decode($ret,1);
            }
        }

        $arrConds['id'] = $actId;

        $arrFields = $arrFields ? $arrFields : explode(',',static::ALL_FILEDS);
        $ret = $this->_objDaoTehuike->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    public function getActByActTypeAndPageType($actType,$pageType)
    {
        if (intval($actType) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$actType]");
            return false;
        }
        $arrConds['classType'] = $actType;
        $arrConds['pageType']  = $pageType;
        $arrConds['status'] = static::STATUS_ENABLE;
        $ret = $this->_objDaoTehuike->getListByConds($arrConds,explode(',',static::ALL_FILEDS));
        if (false === $ret) {
            Bd_Log::warning("Error:[DB error],Detail:[id:$actType]");
            return false;
        }
        return $ret;
    }


    public function getTehuikeListBySomeConds($id,$subject=false,$classType=false,$pageType=false,$arrFields=[])
    {
    	$arrConds = [];
    	if(intval($id) > 0){
    		$arrConds['id'] = intval($id);
    	}
    	if(intval($subject) > 0){
    		$arrConds['subject'] = intval($subject);
    	}
    	if(intval($classType) > 0){
    		$arrConds['classType'] = intval($classType);
    	}
    	if(intval($pageType) > 0){
    		$arrConds['pageType'] = intval($pageType);
    	}
        $arrConds['status'] = static::STATUS_ENABLE;
        $arrFields = $arrFields ? $arrFields : explode(',',static::ALL_FILEDS); 
        $ret = $this->_objDaoTehuike->getListByConds($arrConds,$arrFields,NULL,['ORDER BY id DESC ']);
        if (false === $ret) {
        	$strConds = json_encode($arrConds);
        	$strFields = json_encode($arrFields);
            Bd_Log::warning("Error:[DB error],Detail:[arrConds:$strConds][arrFields:$strFields]");
            return false;
        }
        return $ret;
    }
    public function getTehuikeListByConds($_arrConds,$arrFields=[])
    {
    	$arrConds = [];
    	if(intval($_arrConds['id']) > 0){
    		$arrConds['id'] = intval($_arrConds['id']);
    	}
    	if(intval($_arrConds['subject']) > 0){
    		$arrConds['subject'] = intval($_arrConds['subject']);
    	}
    	if(intval($_arrConds['classType']) > 0){
    		$arrConds['classType'] = intval($_arrConds['classType']);
    	}
    	if(intval($_arrConds['pageType']) > 0){
    		$arrConds['pageType'] = intval($_arrConds['pageType']);
    	}
    	if( isset($_arrConds['status']) ){
    		$arrConds['status'] = $_arrConds['status'];
    	}

        $arrFields = $arrFields ? $arrFields : explode(',',static::ALL_FILEDS); 
        $ret = $this->_objDaoTehuike->getListByConds($arrConds,$arrFields,NULL,['ORDER BY id DESC ']);
        if (false === $ret) {
        	$strConds = json_encode($arrConds);
        	$strFields = json_encode($arrFields);
            Bd_Log::warning("Error:[DB error],Detail:[arrConds:$strConds][arrFields:$strFields]");
            return false;
        }
        return $ret;
    }

	/**
	 * 新增记录
	 *
	 * @param  mix  $arrParams 属性
	 * @return bool true/false
	 */
	public function addTehuike($arrParams){
		// todo添加字段验证
		$arrFields = array(
			'pageType'  =>isset($arrParams['pageType']) ? intval($arrParams['pageType']):0,
			'classType' =>isset($arrParams['classType']) ? intval($arrParams['classType']):'',
			'subject'   =>isset($arrParams['subject']) ? intval($arrParams['subject']):'',
			'courseType'=>isset($arrParams['courseType']) ? intval($arrParams['courseType']):'',
			'courseList'=>isset($arrParams['courseList']) ? strval($arrParams['courseList']):'',
			'createTime'=>time(),
			'updateTime'=>0,
			'status'    =>isset($arrParams['status']) ? intval($arrParams['status']) : self::STATUS_OFFLINE,
			'extData'   =>isset($arrParams['extData']) ? json_encode($arrParams['extData']):'[]',
            'id'        => (new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_OPER_ACTIVITY_ID))->getIdAlloc()
		);

		if(!$arrFields['id']){
            Bd_Log::warning("Error:[Hk_Service_IdAlloc->getIdAlloc失败],Detail:[id:$id]");
		}
		$ret = $this->_objDaoTehuike->insertRecords($arrFields);
		if($ret){ 
			$ret = $arrFields['id'];
	        $this->updateTehuikeRedis($ret);
		}
		return $ret;
	}
	/**
	 * 更新
	 *
	 * @param  int $id
	 * @param  array $arrParams
	 * @return bool true/false
	 */
	public function updateTehuikeById($id,$arrParams){
		if(intval($id) <= 0){
			Bd_Log::warning("Error:[param error],Detail:[id:$id]");
			return false;
		}
		$arrConds = [ 'id' => intval($id) ];
		$arrFields = [];
		$arrAllFields = explode(',',self::ALL_FILEDS);
		foreach($arrParams as $key => $value){
			if(!in_array($key,$arrAllFields)){
				continue;
			}
			$arrFields[$key] = $value;
		}
		if(isset($arrFields['extData']) && is_array($arrFields['extData']) ){
			$arrFields['extData'] = json_encode($arrFields['extData']);
		}
		$ret = $this->_objDaoTehuike->updateByConds($arrConds,$arrFields);
		if($ret){
			$this->updateTehuikeRedis($id);
		}
		return $ret;
	}
	/**
	 * 删除
	 *
	 * @param  int $id
	 * @return bool true/false
	 */
	public function deleteTehuikeById($id){
		if(intval($id) <= 0){
			Bd_Log::warning("Error:[param error],Detail:[id:$id]");
			return false;
		}
		$arrConds = [ 'id' => intval($id) ];
		$arrFields=[
			'status' => self::STATUS_DISABLE
		];
		$ret = $this->_objDaoTehuike->updateByConds($arrConds,$arrFields);
		if($ret){
			$this->updateTehuikeRedis($id);
		}
		return $ret;
	}
	/**
	 * 更新redis缓存
	 * 1、更新 特惠课string
	 * 2、更新 特惠课年级 set
	 * @param  [type] $id   [description]
	 * @return [type]       [description]
	 */
	public function updateTehuikeRedis($id){
		// 设置
		$tehuike = $this->_objDaoTehuike->getRecordByConds(['id' => $id], explode(',',static::ALL_FILEDS));
		if( $tehuike['status'] !== self::STATUS_ENABLE ){
			$tehuike = [
				'id' => $id,
				'extData' => [ 'grade' => [] ]
			];
			$result = $this->_objRedis->del( sprintf(Oplib_Const_Cache::TEHUIKE_ACT_INFO,$id) );
		}else{
			$result = $this->_objRedis->set( sprintf(Oplib_Const_Cache::TEHUIKE_ACT_INFO,$id),json_encode($tehuike) );
		}
		// 所有年级
		foreach ( Zb_Const_GradeSubject::$GRADE as $grade=>$gradeText) {
			// 不同班型[特惠课/试听课]
			foreach (self::$ALL_CLASS_TYPE as $classType) {
				$k = sprintf(Oplib_Const_Cache::TEHUIKE_GRADE_MAP,$grade,$classType);
				// 是否需要新增
				$needSet = false;
				if( $tehuike['status'] == self::STATUS_ENABLE && $tehuike['classType'] == $classType &&  in_array($grade, $tehuike['extData']['grade']) ){
					$needSet = true;
				}
				// 1、删除
				$this->_objRedis->zRem($k,$tehuike['id']);
				// 2、新增
				if($needSet){
					$this->_objRedis->zAdd($k,$tehuike['pageType'],$tehuike['id']);
				}
			}
		}
	}

}
