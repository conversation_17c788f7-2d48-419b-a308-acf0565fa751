<?php

/**
 * @file    ExamRelation.php
 * <AUTHOR>
 * @date    2018-05-30
 * @brief   测试系统 -- 试卷相关绑定
 */
class Hkzb_Ds_Inclass_ExamRelation {

    private $_objDaoExamRelation;

    public function __construct() {
        $this->_objDaoExamRelation = new Hkzb_Dao_Inclass_ExamRelation();
    }

    /**
     * 根据examId获取试卷绑定信息
     * @param $intExamId
     * @return array
     */
    public function getExamRelationByExamId( $intExamId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>
        $intExamId = intval($intExamId);

        if (0 >= $intExamId) {
            return false;
        }

//rpc        //查询条件
//rpc        $arrConds  = array(
//rpc            'examId' => $intExamId,
//rpc        );
//rpc        $arrFields = $this->_objDaoExamRelation->arrFields;
//rpc
//rpc        //绑定信息
//rpc        $arrExamRelation = $this->_objDaoExamRelation->getRecordByConds($arrConds, $arrFields);

        $examRpcObj = new Hkzb_Ds_Examrpc_ExamRpcApi();
        $arrExamRelation = $examRpcObj->getExamRelationInfoByExamId($intExamId,10);

        $arrExamRelation = !empty($arrExamRelation) ? $arrExamRelation[0]:[];

        if(!empty($arrExamRelation)){
            $examInfo = $examRpcObj->getExamInfoByRpc($intExamId,10);
            $arrExamRelation['grade'] = $examInfo['grade'];
            $arrExamRelation['subject'] = $examInfo['subject'];
            $arrExamRelation['bindId'] = 10;
        }

        return $arrExamRelation ? $arrExamRelation : array();
    }


    /**
     * 根据BindId和BindType获取绑定信息(一个章节只能绑定一种类型的试卷)
     * @param $intBindId
     * @param $intBindType
     * @return array|bool
     */
    public function getExamRelationByBindIdAndBindType( $intBindId, $intBindType ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intBindId   = intval($intBindId);
        $intBindType = intval($intBindType);

        if (0 >= $intBindId || 0 >= $intBindType) {
            return false;
        }

//        //查询条件
        $arrConds  = array(
            'bindId'   => $intBindId,
            'bindType' => $intBindType,
            'deleted'  => 0,
        );
        $arrFields = $this->_objDaoExamRelation->arrFields;

        //绑定信息
        $arrExamRelation = $this->_objDaoExamRelation->getRecordByConds($arrConds, $arrFields);

        return $arrExamRelation ? $arrExamRelation : array();
    }

    /**
     * 根据BindId和BindType获取绑定信息(一个章节只能绑定一种类型的试卷)
     * @param $intBindId
     * @param $intBindType
     * @return array|bool
     */
    public function getExamRelationByBindIdAndBindTypeByYiKe( $intBindId, $intBindType ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intBindId   = intval($intBindId);
        $intBindType = intval($intBindType);

        if (0 >= $intBindId || 0 >= $intBindType) {
            return false;
        }

        $examRpcObj = new Hkzb_Ds_Examrpc_ExamRpcApi();
        //绑定信息
        $arrExamRelation = $examRpcObj->getExamRelationByBindIdByRpc($intBindId, $intBindType);

        return $arrExamRelation ? $arrExamRelation : array();
    }

    public function getInclassExamInfoByLessonId( $intLessonId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 获取绑定到这个章节上的试卷信息
        $intBindId   = intval($intLessonId);
        $intBindType = 10;

        //旧版隐藏
        /*$arrConds  = array(
            'bindId'   => $intBindId,
            'bindType' => $intBindType,
            'deleted'  => 0,
        );
        $arrFields = $this->_objDaoExamRelation->arrFields;

        $arrExamRelation = $this->_objDaoExamRelation->getRecordByConds($arrConds, $arrFields);*/

        /**
         * 读取exam3.0rpc获取数据
         */
        $objHkzbDsExamrpcExamRpcApi = new Hkzb_Ds_Examrpc_ExamRpcApi();
        $arrExamRelation = $objHkzbDsExamrpcExamRpcApi->getExamRelationByRPC($intBindId, $intBindType);
        if (empty( $arrExamRelation )) {
            return array();
        }

        $intExamId = intval($arrExamRelation[ 'examId' ]);

        $intExamStatus = intval($arrExamRelation[ 'extData' ][ 'examStatus' ]);
        $intStartTime  = intval($arrExamRelation[ 'extData' ][ 'startTime' ]);

        $objInclassExam = new Hkzb_Ds_Inclass_Exam();

        // 方法已经改过RPC 直接更换调用
        $arrInclassExam = $objInclassExam->getInclassExamInfoByExamIdByYiKe($intExamId);
        if (empty( $arrInclassExam )) {
            return array();
        }

        $strExamText = "共" . count($arrInclassExam[ 'tidList' ]) . "道题/满分" . ( $arrInclassExam[ 'content' ][ 'tot' ] / 10 ) . "分";


        //获取考试信息
        $arrExamInfo = array(
            'examId'      => $intExamId,
            'startTime'   => $intStartTime,
            'status'      => $intExamStatus,
            'examText'    => $strExamText,
            'exerciseNum' => count($arrInclassExam[ 'tidList' ]),
        );

        return $arrExamInfo;
    }

    // 开始课中练习
    // 由于该接口只有主讲端使用，并发很小，暂时不考虑事务操作
    public function startInclassExam( $intExamId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intBindId = intval($intExamId);

        if (0 >= $intBindId) {
            return false;
        }

        // 查询条件
        $arrConds = array(
            'examId' => $intExamId,
        );

        $arrFields = $this->_objDaoExamRelation->arrFields;

        //绑定信息
        $arrExamRelation = $this->_objDaoExamRelation->getRecordByConds($arrConds, $arrFields);

        if (empty( $arrExamRelation )) {
            return false;
        }

        if (!is_array($arrExamRelation[ 'extData' ])) {
            $arrExtData = array();
        } else {
            $arrExtData = $arrExamRelation[ 'extData' ];
        }

        // 这里直接把 examStatus 置为1
        $arrExtData[ 'examStatus' ] = 1;
        $arrExtData[ 'startTime' ]  = time();

        $arrFields = array(
            'extData'    => json_encode($arrExtData),
            'updateTime' => time(),
        );
        $this->_objDaoExamRelation->updateByConds($arrConds, $arrFields);

        return true;
    }

    // 结束课中练习
    public function stopInclassExam( $intExamId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intBindId = intval($intExamId);

        if (0 >= $intBindId) {
            return false;
        }

        // 查询条件
        $arrConds = array(
            'examId' => $intExamId,
        );

        $arrFields = $this->_objDaoExamRelation->arrFields;

        //绑定信息
        $arrExamRelation = $this->_objDaoExamRelation->getRecordByConds($arrConds, $arrFields);

        if (empty( $arrExamRelation )) {
            return false;
        }

        if (!is_array($arrExamRelation[ 'extData' ])) {
            $arrExtData = array();
        } else {
            $arrExtData = $arrExamRelation[ 'extData' ];
        }

        // 这里直接把 examStatus 置为1
        $arrExtData[ 'examStatus' ] = 2;

        $arrFields = array(
            'extData'    => json_encode($arrExtData),
            'updateTime' => time(),
        );
        $this->_objDaoExamRelation->updateByConds($arrConds, $arrFields);

        return true;
    }


    // 给帆爷提供的统计数据
    public function getIsBindByLessonIdList( $arrLessonIdList ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if (empty( $arrLessonIdList )) {
            return array();
        }

        $arrConds = array(
            'bindType' => 10,
            'deleted'  => 0,
            'bind_id in (' . implode(',', $arrLessonIdList) . ')',
        );

        $arrFields = array(
            'bindId',
        );

        $arrBindList = $this->_objDaoExamRelation->getListByConds($arrConds, $arrFields);

        $temp = array();
        foreach ($arrBindList as $value) {
            $temp[ $value[ 'bindId' ] ] = 1;
        }

        $arrOutput = array();
        foreach ($arrLessonIdList as $lessonId) {
            $intIsBind = 0;
            if (isset( $temp[ $lessonId ] )) {
                $intIsBind = 1;
            }
            $arrOutput[ $lessonId ] = $intIsBind;
        }


        return $arrOutput;
    }

}