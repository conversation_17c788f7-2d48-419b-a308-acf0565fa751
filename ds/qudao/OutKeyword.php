<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 18:43
 */

class Qdlib_Ds_Qudao_OutKeyword extends Qdlib_Ds_Qudao_OutBase
{

    const ALL_FIELDS = 'id,channel,account,campaignId,adgroupId,adId,creativeId,keywordId,keyword,configuredStatus,systemStatus,reportStatus,keywordCreatedTime,createTime,updateTime,ext,deleted';

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutKeyword');
    }

    protected function getFormatDbData($arrParams)
    {
        $arrFields = array(
            'channel' => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account' => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'campaignId' => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'adgroupId' => isset($arrParams['adgroupId']) ? intval($arrParams['adgroupId']) : 0,
            'adId' => isset($arrParams['adId']) ? intval($arrParams['adId']) : 0,
            'creativeId' => isset($arrParams['creativeId']) ? intval($arrParams['creativeId']) : 0,
            'keywordId' => isset($arrParams['keywordId']) ? intval($arrParams['keywordId']) : 0,
            'keyword' => isset($arrParams['keyword']) ? strval($arrParams['keyword']) : '',

            'configuredStatus' => isset($arrParams['configuredStatus']) ? strval($arrParams['configuredStatus']) : '',
            'systemStatus' => isset($arrParams['systemStatus']) ? strval($arrParams['systemStatus']) : '',

            'keywordCreatedTime' => isset($arrParams['keywordCreatedTime']) ? intval($arrParams['keywordCreatedTime']) : 0,
            'ext' => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
        );
        return $arrFields;
    }
}