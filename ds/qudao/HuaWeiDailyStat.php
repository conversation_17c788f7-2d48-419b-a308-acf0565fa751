<?php


class Qdlib_Ds_Qudao_HuaWeiDailyStat extends Qdlib_Ds_Qudao_OutBase
{
    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_HuaWeiDailyStat');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'dt'                        => isset($arrParams['dt']) ? intval($arrParams['dt']) : 0,
            'dtHour'                    => isset($arrParams['dtHour']) ? intval($arrParams['dtHour']) : -2,
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'flag'                      => isset($arrParams['flag']) ? strval($arrParams['flag']) : '',
            'uniqueId'                  => isset($arrParams['uniqueId']) ? strval($arrParams['uniqueId']) : '',
            'taskId'                    => isset($arrParams['taskId']) ? intval($arrParams['taskId']) : 0,
            'cost'                      => isset($arrParams['cost']) ? intval($arrParams['cost']) : 0,
            'showPv'                    => isset($arrParams['showPv']) ? intval($arrParams['showPv']) : 0,
            'showUv'                    => isset($arrParams['showUv']) ? intval($arrParams['showUv']) : 0,
            'clickPv'                   => isset($arrParams['clickPv']) ? intval($arrParams['clickPv']) : 0,
            'clickUv'                   => isset($arrParams['clickUv']) ? intval($arrParams['clickUv']) : 0,
            'download'                  => isset($arrParams['download']) ? intval($arrParams['download']) : 0,
            'conversion'                => isset($arrParams['conversion']) ? intval($arrParams['conversion']) : 0,
            'activation'                => isset($arrParams['activation']) ? intval($arrParams['activation']) : 0,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
            'reportStatus'              => isset($arrParams['reportStatus']) ? intval($arrParams['reportStatus']):0,
            'createTime'                => isset($arrParams['createTime']) ? intval($arrParams['createTime']):time(),
            'updateTime'                => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']):time(),
        );
        return $arrFields;
    }

}