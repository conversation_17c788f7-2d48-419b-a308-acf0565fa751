<?php

/**
 * @file    GuoyuanChargeRefund.php
 * <AUTHOR>
 * @date    2017-12-21
 * @brief   果园退款
 *
 * */
class Hkzb_Ds_Gnmis_GuoyuanChargeRefund
{

    //是否需要退款状态
    const REFUND_STATUS_WAITSTART     = 0; //待启动
    const REFUND_STATUS_HASREDAY      = 1; //退款准备完毕
    const REFUND_STATUS_WAITREADY     = 2; //退款等待中
    const REFUND_STATUS_NOTNEEDREFUND = 3; //标记不需要退款
    const REFUND_STATUS_FINISH        = 4; //退款结束

    //工作流状态
    const STATUS_INIT            = 0;
    const STATUS_CHECKING        = 1;
    const STATUS_BACK            = 3;
    const STATUS_FAILED          = 126;
    const STATUS_SUCCESS         = 127;
    const STATUS_REFUNDFAIL      = 100;
    const STATUS_MANUAL_REFUND   = 101;
    const STATUS_ACCOUNTANT_PASS = 102;

    const MATERIAL_STATUS_NO       = 0;//无需教材
    const MATERIAL_STATUS_YES      = 1;//待寄回
    const MATERIAL_STATUS_BACK_NO  = 2;//未寄回
    const MATERIAL_STATUS_BAKC_YES = 3;//已寄回

    static $MaterialStatusMap = [
        self::MATERIAL_STATUS_NO       => "无需教材",
        self::MATERIAL_STATUS_YES      => "待寄回",
        self::MATERIAL_STATUS_BAKC_YES => "已寄回",
        self::MATERIAL_STATUS_BACK_NO  => "未寄回",
    ];
    
    //记录删除状态
    const DELETED_NO  = 0;
    const DELETED_YES = 1;

    //退款类型标识
    const SIGN_OLD         = Hkzb_Util_Fudao_Gift::SIGN_OLD; //老退款逻辑，包含课程（和教材）
    const SIGN_COURSE      = Hkzb_Util_Fudao_Gift::SIGN_COURSE; //课程退款（可能暂扣教材）
    const SIGN_MATERIAL    = Hkzb_Util_Fudao_Gift::SIGN_MATERIAL; //教材退款
    const SIGN_PENCIL_CASE = Hkzb_Util_Fudao_Gift::SIGN_PENCIL_CASE;//笔袋退款
    const SIGN_NOTEBOOK    = Hkzb_Util_Fudao_Gift::SIGN_NOTEBOOK;//笔记本退款
    const SIGN_SCHOOLBAG   = Hkzb_Util_Fudao_Gift::SIGN_SCHOOLBAG;//书包退款
    const SIGN_ENTITY      = Hkzb_Util_Fudao_Gift::SIGN_ENTITY;//实物退款

    //赠品ID => 退款类型

    static $giftRefundSignMap = [
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_PENCIL_CASE_ID => self::SIGN_PENCIL_CASE,
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_NOTEBOOK_ID    => self::SIGN_NOTEBOOK,
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_SCHOOL_BAG_ID  => self::SIGN_SCHOOLBAG,
    ];
    //赠品ID => 退款名称
    static $giftRefundSignTxt = [
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_PENCIL_CASE_ID => "笔袋",
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_NOTEBOOK_ID    => "笔记本",
        Hkzb_Util_Fudao_Gift::COURSE_GIFT_SCHOOL_BAG_ID  => "书包",
        Zb_Const_Sku::MATERIAL_PENBAG_BOX_XIAO           => "精品资料盒(小学)",
        Zb_Const_Sku::MATERIAL_PENBAG_BOX_CHU            => "精品资料盒(初中)",
        Zb_Const_Sku::MATERIAL_PENBAG_BOX_GAO            => "精品资料盒(高中)",
    ];
    //退款类型 => 退款名称
    static $RefundSignTxtName = [
        self::SIGN_MATERIAL    => "教材",
        self::SIGN_PENCIL_CASE => "笔袋",
        self::SIGN_NOTEBOOK    => "笔记本",
        self::SIGN_SCHOOLBAG   => "书包",
    ];

    private $objDaoGuoyuanChargeRefund;
    private $objDsGiveAwayExpress;
    private $objAfterSale;
    private $objAfterSaleOperation;

    public function __construct()
    {
        $this->objDaoGuoyuanChargeRefund = new Hkzb_Dao_Gnmis_GuoyuanChargeRefund();
        $this->objDsGiveAwayExpress      = new Zhibo_Ds_GiveAwayExpress();
        $this->objAfterSale              = new Hkzb_Ds_Gnmis_AfterSale(); // 客服售后
        $this->objAfterSaleOperation     = new Hkzb_Ds_Gnmis_AfterSaleOperation(); // 客服售后操作流
    }

    /**
     * @return Dao_ChargeRefund
     */
    public function getDao()
    {
        return $this->objDaoGuoyuanChargeRefund;
    }

    /**
     * @param: $array {$orderId}
     *
     * @return array|false
     * 退款物品数量查询
     */
    public function getGuoyuanRefundRecord($ArrOrderIds)
    {
        if (empty($ArrOrderIds)) {
            Bd_Log::warning("[error] [param empty] [detail:order_id not exist]");

            return false;
        }
        $refundArr = [];
        foreach ($ArrOrderIds as $orderId) {
            $arrConds  = [
                'orderId' => $orderId,
            ];
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;

            $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields);
            if ($ret) {
                $refundArr = array_merge($refundArr, $ret);
            }
        }
        $giftArr = [];
        foreach ($refundArr as $refundInfo) {
            if ($refundInfo['sign'] == 1) {
                continue;
            }
            if ($refundInfo['sign'] == 2) {
                $sign[] = [
                    'orderId'       => $refundInfo['orderId'],
                    'sign'          => $refundInfo['sign'],
                    'express'       => $refundInfo['express'],
                    'expressNumber' => $refundInfo['expressNumber'],
                ];
                continue;
            }
            if ($giftArr) {
                foreach ($giftArr as $giftRecord) {
                    if ($refundInfo['sign'] == $giftRecord['id'] && $refundInfo['orderDetail']['courseId'] == $giftRecord['courseId']) {
                        continue;
                    }
                }
            }
            $sign[]       = [
                'orderId'       => $refundInfo['orderId'],
                'sign'          => $refundInfo['sign'],
                'express'       => $refundInfo['express'],
                'expressNumber' => $refundInfo['expressNumber'],
            ];
            $tradeRecord  = new Hkzb_Ds_Fudao_TradeRecord();
            $tradeInfo    = $tradeRecord->getTradeRecord($refundInfo['orderId']);
            $parOrderInfo = $tradeRecord->getTradeRecord($tradeInfo['parentOrderId']);
            if (!$parOrderInfo) {
                Bd_Log::warning("[error] [db empty] [get parOrder data fail]");
                continue;
            }
            $gift = $parOrderInfo['extData']['gift'];
            foreach ($gift as $giftInfo) {
                if (in_array($refundInfo['orderDetail']['courseId'], $giftInfo['giftRelatedCourse'])) {
                    $giftId = self::$giftRefundSignMap[$giftInfo['giftList'][0]];
                    if (empty($giftId)) {
                        $giftId = Hkzb_Util_Fudao_Gift::SIGN_ENTITY;//实物退款
                    }
                    foreach ($giftInfo['giftRelatedCourse'] as $courseId) {
                        $giftArr[] = [
                            'id'       => $giftId,
                            'courseId' => $courseId,
                        ];
                    }
                }
            }
        }

        return $sign;
    }

    /**
     * ['orderId'=>1, 'sign'=>2'express'=>'顺丰','expressNumber'=>'111'],
     *
     * @param: $orderId,$sign,$express,$expressNumber
     *
     * @return bool true/false
     * @desc : 快递单号填写
     */
    public function writeExpressToGuoyuanRefund($ArrOrderIds)
    {
        if (empty($ArrOrderIds)) {
            Bd_Log::warning("[error] [param not exist] [detail:ArrOrderIds empty]");
            $arrOutput['addStatus'] = 0;
            $arrOutput['resMsg']    = '参数错误';

            return $arrOutput;
        }
        $return = self::setExpressToGuoyuanRefund($ArrOrderIds);
        if (!$return) {
            $arrOutput['addStatus'] = 0;
            $arrOutput['resMsg']    = '您提交的物品与库房记录不符,请重新选择';

            return $arrOutput;
        }

        $afterSalePids = []; //父售后单id数据

        foreach ($ArrOrderIds as $orderInfo) {
            $file              = [
                'extData',
                'orderUid',
                'orderDetail',
            ];
            $orderIds          = [];
            $arrConds          = [
                'orderId' => $orderInfo['orderId'],
                'sign'    => $orderInfo['sign'],
            ];
            $guoyuanRefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
            if (!$guoyuanRefundList) {
                Bd_Log::warning("[error] [db empty] [get guoyuanChargeRefund fail ,orderid:{$orderInfo['orderId']}]");
            }
            if ($guoyuanRefundList) {
                $ext                = $guoyuanRefundList[0]['extData'];
                $ext['expressTime'] = time();
            }
            $arrFields = [
                'express'        => $orderInfo['express'],
                'express_number' => $orderInfo['expressNumber'],
            ];
            if ($ext) {
                $arrFields['ext_data'] = json_encode($ext);
            }
            if ($orderInfo['sign'] == 2) {
                $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
                if (!$ret) {
                    Bd_Log::warning("[error] [db empty] [update guoyuanChargeRefund fail ,orderid:{$orderInfo['orderId']}]");
                }
                // 同步更新售后快递单号
                $updateAfterSale = $this->objAfterSale->updateAfterExpressInfoByConds($arrConds, $arrFields);
                if (!$updateAfterSale) {
                    Bd_Log::warning("[error] [db empty] [update AfterSale fail ,orderId:{$orderInfo['orderId']}]");
                }
                // 获取父售后id
                $afterSale = $this->objAfterSale->getAfterSaleInfoByConds($arrConds, ['aftersalePid']);
                if ($afterSale) {
                    $afterSalePid                 = $afterSale['aftersalePid'];
                    $afterSalePids[$afterSalePid] = 1;
                }

                //兼容新的预支付订单
                $tradeRecord          = new Hkzb_Ds_Fudao_TradeRecord();
                $tradeInfo            = $tradeRecord->getTradeRecord($orderInfo['orderId']);
                $depositTradeRecordId = $tradeInfo['extData']['expendDiscount']['preOrderTradeRecordId'];
                if ($depositTradeRecordId) {
                    $arrConds          = [
                        'orderId' => $depositTradeRecordId,
                        'sign'    => $orderInfo['sign'],
                    ];
                    $guoyuanRefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
                    if ($guoyuanRefundList) {
                        $ext                = $guoyuanRefundList[0]['extData'];
                        $ext['expressTime'] = time();
                        if ($ext) {
                            $arrFields['ext_data'] = json_encode($ext);
                        }
                        $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
                        if (!$ret) {
                            Bd_Log::warning("[error] [db empty] [update guoyuanChargeRefund fail ,orderid:{$orderInfo['orderId']}]");
                        }
                        // 同步更新售后快递单号
                        $updateAfterSale = $this->objAfterSale->updateAfterExpressInfoByConds($arrConds, $arrFields);
                        if (!$updateAfterSale) {
                            Bd_Log::warning("[error] [db empty] [update AfterSale fail ,orderId:{$orderInfo['orderId']}]");
                        }
                        // 获取父售后id
                        $afterSale = $this->objAfterSale->getAfterSaleInfoByConds($arrConds, ['aftersalePid']);
                        if ($afterSale) {
                            $afterSalePid                 = $afterSale['aftersalePid'];
                            $afterSalePids[$afterSalePid] = 1;
                        }
                    }
                }
                continue;
            }
            $tradeRecord  = new Hkzb_Ds_Fudao_TradeRecord();
            $tradeInfo    = $tradeRecord->getTradeRecord($orderInfo['orderId']);
            $parOrderInfo = $tradeRecord->getTradeRecord($tradeInfo['parentOrderId']);
            if (!$parOrderInfo) {
                Bd_Log::warning("[error] [db empty] [get parOrder data fail,orderId:{$orderInfo['orderId']}]");
            }
            $consistOf = $parOrderInfo['extData']['consistOf'];
            foreach ($consistOf as $orderRecord) {
                $orderList[] = $orderRecord;
            }
            $gift = $parOrderInfo['extData']['gift'];
            foreach ($gift as $giftInfo) {
                if (in_array($guoyuanRefundList[0]['orderDetail']['courseId'], $giftInfo['giftRelatedCourse'])) {
                    $giftId    = self::$giftRefundSignMap[$giftInfo['giftList'][0]];
                    $courseArr = $giftInfo['giftRelatedCourse'];
                }
            }
            $orderStr   = implode(",", $orderList);
            $orderStr   = rtrim($orderStr, ",");
            $Conds      = [
                'orderUid' => $guoyuanRefundList[0]['orderUid'],
                'sign'     => $giftId,
                "order_id in ({$orderStr})",
            ];
            $RefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($Conds, ['id', 'orderId', 'extData', 'orderDetail']);
            if (!$RefundList) {
                Bd_Log::warning("[error] [db empty] [get refundList fail,orderId:{$guoyuanRefundList[0]['orderUid']}]");
            }
            foreach ($RefundList as $refundInfo) {
                if (in_array($refundInfo['orderDetail']['courseId'], $courseArr)) {
                    $arrConds = [
                        'id' => $refundInfo['id'],
                    ];

                    $ext                = $refundInfo['extData'];
                    $ext['expressTime'] = time();
                    $arrFields          = [
                        'express'        => $orderInfo['express'],
                        'express_number' => $orderInfo['expressNumber'],
                    ];
                    if ($ext) {
                        $arrFields['ext_data'] = json_encode($ext);
                    }

                    $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
                    if (!$ret) {
                        Bd_Log::warning("[error] [db update error] [update guoyuanChargeRefund fail ,id:{$refundInfo['id']}]");
                    }
                    // 同步更新售后快递单号
                    $updateAfterSale = $this->objAfterSale->updateAfterExpressInfoByConds(['refundId' => $refundInfo['id']], $arrFields);
                    if (!$updateAfterSale) {
                        Bd_Log::warning("[error] [db empty] [update AfterSale fail ,orderId:{$orderInfo['orderId']}]");
                    }
                    // 获取父售后id
                    $afterSale = $this->objAfterSale->getAfterSaleInfoByConds(['refundId' => $refundInfo['id']], ['aftersalePid']);
                    if ($afterSale) {
                        $afterSalePid                 = $afterSale['aftersalePid'];
                        $afterSalePids[$afterSalePid] = 1;
                    }

                    //兼容新的预支付订单
                    $tradeRecord          = new Hkzb_Ds_Fudao_TradeRecord();
                    $tradeInfo            = $tradeRecord->getTradeRecord($refundInfo['orderId']);
                    $depositTradeRecordId = $tradeInfo['extData']['expendDiscount']['preOrderTradeRecordId'];
                    if ($depositTradeRecordId) {
                        $arrConds          = [
                            'orderId' => $depositTradeRecordId,
                            'sign'    => $orderInfo['sign'],
                        ];
                        $guoyuanRefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
                        if ($guoyuanRefundList) {
                            $ext                = $guoyuanRefundList[0]['extData'];
                            $ext['expressTime'] = time();
                            if ($ext) {
                                $arrFields['ext_data'] = json_encode($ext);
                            }
                            $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
                            if (!$ret) {
                                Bd_Log::warning("[error] [db empty] [update guoyuanChargeRefund fail ,orderid:{$orderInfo['orderId']}]");
                            }
                            // 同步更新售后快递单号
                            $updateAfterSale = $this->objAfterSale->updateAfterExpressInfoByConds($arrConds, $arrFields);
                            if (!$updateAfterSale) {
                                Bd_Log::warning("[error] [db empty] [update AfterSale fail ,orderId:{$orderInfo['orderId']}]");
                            }
                            // 获取父售后id
                            $afterSale = $this->objAfterSale->getAfterSaleInfoByConds($arrConds, ['aftersalePid']);
                            if ($afterSale) {
                                $afterSalePid                 = $afterSale['aftersalePid'];
                                $afterSalePids[$afterSalePid] = 1;
                            }
                        }
                    }
                }
            }
        }

        // 售后更新
        if ($afterSalePids) {
            foreach ($afterSalePids as $id) {
                $afterSalePidInfo = $this->objAfterSale->getAfterSaleInfoById($id, ['status', 'nextStep', 'flows', 'extData']);
                if ($afterSalePidInfo) {
                    $flows    = $afterSalePidInfo['flows'];
                    $nextStep = $afterSalePidInfo['nextStep'] + 1; //更新 - 下一步操作
                    $nextOpRole = $flows[$nextStep-1]['role'];
                    $nextOpName = $flows[$nextStep-1]['name']; // 下一步操作名称
                    if ($nextOpName === Hkzb_Ds_Gnmis_AfterSale::OPERATION_FINANCIAL_AUDIT) { //若下一步为财务审核 - 则表示所有快递已签收
                        continue;
                    }

                    $consistOf       = $afterSalePidInfo['extData']['consistOf']; //关联子售后单信息
                    $pidInfoNextStep = $childNextStep = $afterSalePidInfo['nextStep']; //父售后单下一步操作[当前]
                    if ($consistOf) {
                        $childAfterSaleInfo = $this->objAfterSale->getArrAfterSale($consistOf, false, ['id', 'nextStep', 'status']);
                        if (!$childAfterSaleInfo) {
                            continue;
                        }
                        $giftSum = count($childAfterSaleInfo); //赠品退款信息
                        $completeSum = 0;
                        // 判断父售后单是否可进行下一步操作
                        foreach ($childAfterSaleInfo as $sale) {
                            if ($sale['nextStep'] === $nextStep) {
                                $completeSum += 1;
                            }
                        }
                        if ($giftSum === $completeSum) {
                            // 进行下一步售后
                            $ret = $this->objAfterSale->updateAfterExpressInfoByConds(['id' => $id, 'deleted' => 0], ['nextStep' => $nextStep, 'nextOpRole' => $nextOpRole]);
                            if ($ret) {
                                // 更新最后一步操作流状态 & 增加下一步操作流
                                $this->objAfterSaleOperation->updateAndAddFlowByafterSaleId($id, $flows, $nextStep);
                            }
                        }
                    }
                }
            }
        }

        $arrOutput['addStatus'] = 1;
        $arrOutput['resMsg']    = '操作成功';

        return $arrOutput;
    }
    
    /**
     * ['orderIds'=>array(12345,55678,.....), 'express'=>'顺丰','expressNumber'=>'111'],
     * @param: $orderIds,$express,$expressNumber
     * @return bool true/false
     * @desc: 快递单号填写（新模式）
     * @date  2018-06-22
     */
    public function waitNewExpressToGuoyuanRefund($arrOrderIds){
        if (empty($arrOrderIds) || !is_array($arrOrderIds)){
            Bd_Log::warning("[error] [param error] [detail: the arrOrderIds is not exist,arrOrderIds: ".json_encode($arrOrderIds)."]");
            return false;
        }
        $orderIds = $arrOrderIds['orderIds'];
        foreach ($orderIds as $id){
            $arrConds = array(
                'orderId'   => $id,
                'expressNumber'     => '',
            );
            $arrFields = array(
                'express' => $arrOrderIds['express'],
                'express_number' => $arrOrderIds['expressNumber'],
            );
            $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
            if (!$ret){
                Bd_Log::warning("[error] [db empty] [update guoyuanChargeRefund fail ,orderid:{$id}]");
            }
            //兼容新的预支付订单
            $tradeRecord            = new Hkzb_Ds_Fudao_TradeRecord();
            $tradeInfo              = $tradeRecord->getTradeRecord($id);
            $depositTradeRecordId   = $tradeInfo['extData']['expendDiscount']['preOrderTradeRecordId'];
            if ($depositTradeRecordId){
                $arrConds = array(
                    'orderId'   => $depositTradeRecordId,
                    'expressNumber'     => '',
                );
                $arrFields = array(
                    'express' => $arrOrderIds['express'],
                    'express_number' => $arrOrderIds['expressNumber'],
                );
                $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
                if (!$ret){
                    Bd_Log::warning("[error] [db empty] [update guoyuanChargeRefund fail ,orderid:{$depositTradeRecordId}]");
                }
            }
        }
        
        $arrOutput['addStatus'] = 1;
        $arrOutput['resMsg']    = '操作成功';
        return $arrOutput;
    }

    /**
     * 有快递单号发起退款
     *
     * @param type $ArrOrderIds
     */
    public function setExpressToGuoyuanRefund($ArrOrderIds)
    {
        $sign = [];
        foreach ($ArrOrderIds as $k => $v) {
            $sign[$v['sign']][] = $v['orderId'];
            $express            = $v['express'];
            $expressNumber      = $v['expressNumber'];
        }
        //判断快递回来的物品数量和学生要退的数量是否相同
        $list = $this->objDsGiveAwayExpress->getGiveawayListByExpress($expressNumber);
        if (count($list) == 0) {
            return true;
        }
        foreach ($list as $k => $v) {
            if (count($sign[$v['type']]) > $v['typeNum']) {
                return false;
            }
        }
        $idArr = [];
        foreach ($ArrOrderIds as $orderInfo) {
            $tradeRecord       = new Hkzb_Ds_Fudao_TradeRecord();
            $tradeInfo         = $tradeRecord->getTradeRecord($orderInfo['orderId']);
            $file              = [
                'id',
            ];
            $arrConds          = [
                'orderId' => $orderInfo['orderId'],
                'sign'    => $orderInfo['sign'],
            ];
            $arrFile           = [
                'extData',
                'orderUid',
                'orderDetail',
            ];
            $guoyuanRefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFile);
            if ($orderInfo['sign'] == 5) {
                $tradeRecord  = new Hkzb_Ds_Fudao_TradeRecord();
                $tradeInfo    = $tradeRecord->getTradeRecord($orderInfo['orderId']);
                $parOrderInfo = $tradeRecord->getTradeRecord($tradeInfo['parentOrderId']);
                $consistOf    = $parOrderInfo['extData']['consistOf'];
                foreach ($consistOf as $orderRecord) {
                    $orderList[] = $orderRecord;
                }
                $gift = $parOrderInfo['extData']['gift'];
                foreach ($gift as $giftInfo) {
                    if (in_array($guoyuanRefundList[0]['orderDetail']['courseId'], $giftInfo['giftRelatedCourse'])) {
                        $giftId    = self::$giftRefundSignMap[$giftInfo['giftList'][0]];
                        $courseArr = $giftInfo['giftRelatedCourse'];
                    }
                }
                $orderStr   = implode(",", $orderList);
                $orderStr   = rtrim($orderStr, ",");
                $Conds      = [
                    'orderUid' => $guoyuanRefundList[0]['orderUid'],
                    'sign'     => $giftId,
                    "order_id in ({$orderStr})",
                ];
                $RefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($Conds, ['id', 'orderId', 'extData', 'orderDetail']);
                foreach ($RefundList as $refundInfo) {
                    if (in_array($refundInfo['orderDetail']['courseId'], $courseArr)) {
                        //兼容新的预支付订单
                        $tradeRecord          = new Hkzb_Ds_Fudao_TradeRecord();
                        $tradeInfo            = $tradeRecord->getTradeRecord($refundInfo['orderId']);
                        $depositTradeRecordId = $tradeInfo['extData']['expendDiscount']['preOrderTradeRecordId'];
                        if ($depositTradeRecordId) {
                            $arrConds             = [
                                'orderId' => $depositTradeRecordId,
                                'sign'    => $orderInfo['sign'],
                            ];
                            $depositGuoyuanRefund = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
                            if ($depositGuoyuanRefund) {
                                $idArr['list'][] = ['id' => $depositGuoyuanRefund[0]['id'], 'status' => 3];
                            }
                        }
                        $idArr['list'][] = ['id' => $refundInfo['id'], 'status' => 3];
                    }
                }
            } else {
                //相符发起退款操作
                $guoyuanRefundList = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
                //兼容新的预支付订单
                $tradeRecord          = new Hkzb_Ds_Fudao_TradeRecord();
                $tradeInfo            = $tradeRecord->getTradeRecord($orderInfo['orderId']);
                $depositTradeRecordId = $tradeInfo['extData']['expendDiscount']['preOrderTradeRecordId'];
                if ($depositTradeRecordId) {
                    $arrConds             = [
                        'orderId' => $depositTradeRecordId,
                        'sign'    => $orderInfo['sign'],
                    ];
                    $depositGuoyuanRefund = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $file);
                    if ($depositGuoyuanRefund) {
                        $guoyuanRefundList = array_merge($depositGuoyuanRefund, $guoyuanRefundList);
                    }
                }
                foreach ($guoyuanRefundList as $gk => $gv) {
                    $idArr['list'][] = ['id' => $gv['id'], 'status' => 3];
                }
            }

            //减掉快递数量
            $this->objDsGiveAwayExpress->reduceGiveAwayExpress(
                ['expressNumber' => $expressNumber, 'type' => intval($orderInfo['sign']), 'orderId' => $orderInfo['orderId'], 'typeNum' => 1]
            );
        }
        //发起退款
        $header    = [
            'pathinfo' => "/gnmis/operation/guoyuanmaterialedit",
            'cookie'   => $_COOKIE,
        ];
        $arrInput  = $idArr;
        $ret       = ral('gnmis', 'POST', $arrInput, 123, $header);
        $arrOutput = json_decode($ret, true);
        if (0 !== $arrOutput['errNo']) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, $ret['errStr']);
        }
        if (false === $ret) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service guoyuanmaterialedit connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");

            return false;
        }

        return true;
    }

    /**
     * 未发货退款操作执行
     *
     * @param $orderId 订单号
     *
     */
    public function setRefundStatusToReady($orderId) {
        if (empty($orderId)){
            Bd_Log::warning("[error] [param error] []");
        }
        $arrConds = array(
            'orderId'       => $orderId,
        );
        $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields);
        
        if (empty($ret)){
            return true;
        }
        $idArr = array();
        foreach ($ret as $val){
            if ($val['sign'] > self::SIGN_COURSE){
                $idArr[] = array(
                    'id'    =>  $val['id'],
                    'status'=>  3,
                );
            }
        }
        if (!empty($idArr)){
            //发起退款
            $header    = [
                'pathinfo' => "/gnmis/operation/guoyuanmaterialedit",
                'cookie'   => $_COOKIE,
            ];
            $arrParam = array(
                'list'    => json_encode($idArr),
            );
            $ret       = ral('gnmis', 'POST', $arrParam, 123, $header);
            $arrOutput = json_decode($ret, true);
            if (0 !== $arrOutput['errNo']) {
                Bd_Log::warning("Error:[service guoyuanmaterialedit connect error], Detail:[errno:$ret errmsg:]");
                return false;
            }
            if (false === $ret) {
                $errno           = ral_get_errno();
                $errmsg          = ral_get_error();
                $protocol_status = ral_get_protocol_code();
                Bd_Log::warning("Error:[service guoyuanmaterialedit connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
                return false;
            }
        }

        return true;
    }

    /**
     *
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     *
     * @return array|false
     */
    public function getRefundListByConds($arrConds, $arrFields = [], $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        }

        $arrAppends = [
            "order by status,create_time desc ",
            "limit $offset, $limit",
        ];

        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     *
     * @param       $arrConds
     * @param array $arrFields
     *
     * @return array|false
     */
    public function getRefundInfoByConds($arrConds, $arrFields = [])
    {
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        }

        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取总记录数
     *
     * @param       $arrConds
     *
     * @return array|false
     */
    public function getRefundCnt($arrConds)
    {

        $ret = $this->objDaoGuoyuanChargeRefund->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 获取赠品详情
     *
     * @param  int $uid
     * @param  int $orderId
     *
     * @return array
     */
    public function getGuoyuanRefundByUidOrderId($uid, $orderId)
    {
        if (intval($uid) <= 0 || intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid orderId:$orderId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        }

        $arrConds   = [
            'orderUid' => intval($uid),
            'orderId'  => intval($orderId),
            'ext_bit in (1,2,3)',
        ];
        $arrAppends = [
            'ORDER BY create_time desc ',
        ];

        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * 获取详情
     *
     * @param  string $id        id
     * @param  array  $arrFields 指定属性
     *
     * @return array
     */
    public function getGuoyuanRefundInfo($id, $arrFields = [])
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        }

        $arrConds = [
            'id' => strval($id),
        ];

        $ret = $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取详情
     *
     * @param  int $uid
     * @param  int $orderId
     *
     * @return array
     */
    public function getRefundByUidOrderId($uid, $orderId, $isMaster = false)
    {
        $uid     = intval($uid);
        $orderId = intval($orderId);
        if ($uid <= 0 || $orderId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid orderId:$orderId]");

            return [];
        }
        $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
        if ($isMaster) {
            $ret = $this->objDaoGuoyuanChargeRefund->startTransaction();
            if (false === $ret) {
                return false;
            }
        }
        $arrConds = [
            'orderUid' => $uid,
            'orderId'  => $orderId,
        ];
        $ret      = $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        if ($isMaster) {
            $this->objDaoGuoyuanChargeRefund->commit();
        }

        return $ret;
    }

    /**
     * 获取订单退款详细信息 - 根据orderId
     *
     * @param  int $orderId
     *
     * @return array
     */
    public function getRefundByOrderId($orderId)
    {
        $orderId = intval($orderId);
        if ($orderId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[orderId:$orderId]");

            return [];
        }
        $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;

        $arrConds = [
            'orderId' => $orderId,
            'deleted' => 0,
        ];
        $ret      = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields, null, null);
        if (false === $ret) {
            return false;
        }

        return $ret;
    }

    /**
     * @param $id int
     * @param $arrFields array
     * @param null $arrOptions
     * @param null $arrAppends
     * @return array|bool
     *
     * 通过id修改退款信息
     *
     */
    public function updateDataById($id,$arrFields, $arrOptions=NULL, $arrAppends=NULL)
    {
        $id = intval($id);
        if ($id <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return array();
        }

        $arrConds = array(
            'id'  => $id,
        );
        $ret      = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields, $arrOptions, $arrAppends);

        return $ret;
    }
    
    /**
     * 新增
     *
     * @param  array $arrParams 属性
     * @return bool true/false
     */
    public function addRecord($arrParams)
    {
        $uid         = intval($arrParams['uid']);
        $orderId     = intval($arrParams['orderId']);
        $orderUid    = intval($arrParams['orderUid']);
        $orderGrade  = isset($arrParams['orderGrade']) ? intval($arrParams['orderGrade']) : 0;
        $orderType   = isset($arrParams['orderType']) ? intval($arrParams['orderType']) : 0;
        $sign        = isset($arrParams['sign']) ? intval($arrParams['sign']) : 0;
        $requestNo   = isset($arrParams['requestNo']) ? intval($arrParams['requestNo']) : 0;
        $orderDetail = $arrParams['orderDetail'];
        $workflowId  = intval($arrParams['workflowId']);
        $refundStatus = isset($arrParams['refundStatus']) ? intval($arrParams['refundStatus']) : 0;
        $refundRecordId = isset($arrParams['refundRecordId']) ? intval($arrParams['refundRecordId']) : 0;
        if (!$uid || !$orderId || !$orderUid) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
    
            return false;
        }
        //插入数据
        $arrFields = array(
            'orderId'     => $orderId,
            'sign'        => $sign,
            'requestNo'   => $requestNo,
            'orderUid'    => $orderUid,
            'orderGrade'  => $orderGrade,
            'orderType'   => $orderType,
            'orderDetail' => json_encode($orderDetail),
            'workflowId'  => $workflowId,
            'createUser'  => $uid,
            'createTime'  => time(),
            'status'      => self::STATUS_INIT,
            'deleted'     => self::DELETED_NO,
            'abstract'    => strval($arrParams['abstract']),
            'extBit'      => intval($arrParams['extBit']),
            'extData'     => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
            'refundStatus'=> $refundStatus,
            'refundRecordId'=> $refundRecordId,
        );
        $ret       = $this->objDaoGuoyuanChargeRefund->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }
    
        return $this->objDaoGuoyuanChargeRefund->getInsertId();
    }
    
    /**
     * 审核操作,请在调用前check用户是否有当前的审核员权限
     * @param $id
     * @param $status
     * @param $nextOpRole
     * @param $workflowId
     * @return bool
     */
    public function updateStatus($id, $status, $nextOpRole, $workflowId = 0)
    {
        if (!$id) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id status:$status nextOpRole:$nextOpRole]");
    
            return false;
        }
    
        $arrConds  = array('id' => $id);
        $arrFields = array('status' => $status, 'nextOpRole' => $nextOpRole);
        if ($workflowId) {
            $arrFields['workflowId'] = $workflowId;
        }
        $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);
    
        return $ret !== false;
    }
    
    /**
     * 退款类型过渡标志，是否为一笔退款
     * @param  int $orderId 订单号
     * 
     * @return int: 1 普通退款  2 一笔退款
     */
    public function getGuoyuanRefundTypeByOrderId($orderId)
    {
        if (intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[orderId:$orderId]");
            return false;
        }
    
        $arrConds = array(
            'orderId'       => $orderId,
        );
    
        $arrFields = Hkzb_Dao_Gnmis_GuoyuanChargeRefund::$allFields;
    
        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields);
        if (empty($ret)){
            return 2;
        } else {
            foreach ($ret as $info){
                if (!empty($info['refundRecordId'])){
                    return 2;
                }
            }
        }
        
        return 1;
    }
}
