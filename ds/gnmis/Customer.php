<?php
/**
 * Customer.php
 * <EMAIL>
 * 2018-05-07
 * 绩效
 */
Class Hkzb_Ds_Gnmis_Customer{
    const STATUS_VALID   = 0; // 有效
    const STATUS_INVALID = 0; // 无效
    
    const ALL_FIELDS= 'id,introPhone,introUid,appUid,appFr,customerPhone,customerUid,introId,status,createTime,ext,bit';
    private $_customer;
    public function __construct() {
        $this->_customer = new Hkzb_Dao_Gnmis_IntroCustomer();    
    }
    
    public function addCustomer($arrParams) {
        $introPhone = intval($arrParams['introPhone']); // 介绍人号码
        $introUid   = intval($arrParams['introUid']);   // 介绍人uid
        $appUid     = intval($arrParams['appUid']);     // 业务uid 比如sc的uid
        $appFr      = strval($arrParams['appFr']);      // 来源 用来统计一个批次的效果
        $customerPhone = intval($arrParams['customerPhone']);
        $customerUid   = intval($arrParams['customerUid']);
        $introId       = intval($arrParams['introId']);
        
        if (0 >= $introPhone || 0 >= $introUid || 0 >= $appUid || empty($appFr) || 0 >= $customerPhone || 0 >= $introId ) {
            return false;
        }
        $arrFields = [
            'intro_phone' => $introPhone,
            'intro_uid'   => $introUid,
            'app_uid'     => $appUid,
            'app_fr'      => $appFr,
            'customer_phone' => $customerPhone,
            'customer_uid'   => $customerUid,
            'intro_id'       => $introId,
            'create_time'    => time(),
        ];
        if ($arrParams['ext']) {
            $arrFields['ext'] = json_encode($arrParams['ext']);
        }
        $ret = $this->_customer->insertRecords($arrFields);
        if (false === $ret) {
            $strFields = json_encode($arrFields);
            Bd_Log::warning("Error[dbError] Abstract[insert]  strFields[{$strFields}]");
            return false;
        }
        $customerId = $this->_customer->getInsertId();
        return $customerId;
    }
}
