<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file LiveMonitor.php
 * <AUTHOR>
 * @date 2016/12/01
 * @brief 直播监控用户配置
 *
 **/
class Hkzb_Ds_Fudao_LiveMonitor
{
	//权限递增，既教务包括研发、访客包括研发及教务，查询时可指定onlyFlag查询具体角色数据
	const UTYPE_ADMIN = 1; //管理员
	const UTYPE_RD = 2; //研发
	const UTYPE_JW = 3; //教务
	const UTYPE_GUEST = 4; //访客

	private $_objDaoPrivilegeMonitor = null;
	private $_objDaoRegisterUser = null;
	//监控用户配置，如果不希望收到短信邮件等，可以不配置具体字段
	//uid主要用于权限校验，手机号主要用于发送报警短信，邮箱地址主要用于发送报警邮件
	private $_arrMonitorUsers = array(//        array(
		//            'uname' => '祁长春',
		//            'uType' => self::UTYPE_RD,
		//            'uid'   => 2172518536,
		//            'phone' => 13821544546,
		//            'mail'  => '<EMAIL>',
		//        )
	);

	private static $_arrLessonAlarmMap;

	public function __construct()
	{
		$this->_objDaoPrivilegeMonitor = new Hkzb_Dao_Fudao_PrivilegeMonitor();
	}

	private function _initData()
	{
		$arrConds = array('1=1');
		$arrFields = array('*');
		$monitors = $this->_objDaoPrivilegeMonitor->getListByConds($arrConds, $arrFields);
		if (!is_array($monitors)) {
			$monitors = array();
		}
		foreach ($monitors as $mo) {
			$per = array();
			$per['id'] = $mo['id'];
			$per['uname'] = $mo['name'];
			$per['uType'] = $mo['monitor_type'];
			$per['uid'] = $mo['uid'];
			$per['phone'] = $mo['phone'];
			$per['mail'] = $mo['email'];
			$per['sendMail'] = $mo['send_mail'];
			$per['sendSms'] = $mo['send_sms'];
			$per['createTime'] = $mo['create_time'];
			$per['updateTime'] = $mo['update_time'];
			$per['jsonData'] = json_encode($mo);
			$this->_arrMonitorUsers[] = $per;
		}
	}

	public function getMonitorUidList($uType = 0, $onlyFlag = false)
	{
		if (intval($uType) <= 0) {
			return array();
		}
		$uType = intval($uType);
		$ret = array();
		$this->_initData();
		foreach ($this->_arrMonitorUsers as $userData) {
			if (intval($userData['uid']) <= 0 || in_array($userData['uid'], $ret)) {
				continue;
			}
			if ($onlyFlag === true && (intval($userData['uType']) === $uType || intval($userData['uType']) === self::UTYPE_ADMIN)) {
				$ret[] = intval($userData['uid']);
			} else if ($onlyFlag !== true && intval($userData['uType']) <= $uType) {
				$ret[] = intval($userData['uid']);
			}
		}
		return $ret;
	}

	public function getMonitorUserDataList($uType = 0, $onlyFlag = false)
	{
		if (intval($uType) <= 0) {
			return array();
		}
		$uType = intval($uType);
		$ret = array();
		$this->_initData();
		foreach ($this->_arrMonitorUsers as $userData) {
			if (intval($userData['uid']) <= 0 || in_array($userData['uid'], $ret)) {
				continue;
			}
			if ($onlyFlag === true && (intval($userData['uType']) === $uType || intval($userData['uType']) === self::UTYPE_ADMIN)) {
				$ret[] = $userData;
			} else if ($onlyFlag !== true && intval($userData['uType']) <= $uType) {
				$ret[] = $userData;
			}
		}
		return $ret;
	}

	public function getMonitorPhoneList($uType = 0, $onlyFlag = false)
	{
		if (intval($uType) <= 0) {
			return array();
		}
		$uType = intval($uType);
		$ret = array();
		$this->_initData();
		foreach ($this->_arrMonitorUsers as $userData) {
			if (intval($userData['phone']) <= 0 || intval($userData['sendSms']) == 0 || in_array($userData['phone'], $ret)) {
				continue;
			}
			if ($onlyFlag === true && (intval($userData['uType']) === $uType || intval($userData['uType']) === self::UTYPE_ADMIN) && $userData['sendSms'] == 1) {
				$ret[] = intval($userData['phone']);
			} else if ($onlyFlag !== true && intval($userData['uType']) <= $uType && $userData['sendSms'] == 1) {
				$ret[] = intval($userData['phone']);
			}
		}
		return $ret;
	}

	public function getMonitorMailList($uType = 0, $onlyFlag = false)
	{
		if (intval($uType) <= 0) {
			return array();
		}
		$uType = intval($uType);
		$ret = array();
		$this->_initData();
		foreach ($this->_arrMonitorUsers as $userData) {
			if (empty($userData['mail']) || in_array($userData['mail'], $ret)) {
				continue;
			}
			if ($onlyFlag === true && (intval($userData['uType']) === $uType || intval($userData['uType']) === self::UTYPE_ADMIN) && $userData['sendMail'] == 1) {
				$ret[] = strval($userData['mail']);
			} else if ($onlyFlag !== true && intval($userData['uType']) <= $uType && $userData['sendMail'] == 1) {
				$ret[] = strval($userData['mail']);
			}
		}
		return $ret;
	}

	public function monitorAdd($arrInput)
	{
		return $this->_objDaoPrivilegeMonitor->insertRecords($arrInput);
	}

	public function monitorEdit($arrInput)
	{
		$id = $arrInput['id'];
		unset($arrInput['id']);
		return $this->_objDaoPrivilegeMonitor->updateByConds(array("id" => $id), $arrInput);
	}

	public function monitorSearch($arrInput, $fields)
	{
		$uservice = new Hk_Service_Ucloud();
		return $uservice -> getUserUid($arrInput['monitorPhone']);
	}

	public function monitorFind($arrInput, $fields)
	{
		$this->_objDaoRegisterUser = new Hkzb_Dao_Fudao_UserRegister();
		return $this->_objDaoPrivilegeMonitor->getRecordByConds($arrInput, $fields);
	}

	public function monitorDelete($arrInput)
	{
		return $this->_objDaoPrivilegeMonitor->deleteByConds($arrInput);
	}

    /**
     * 手工屏蔽某个章节的报警（默认屏蔽1天）
     * @param integer $lessonId
     * @return number|boolean 屏蔽成功true， 屏蔽失败false，参数错误-1
     */
    public function shieldAlarm($lessonId) {
        $lessonId = intval($lessonId);
        if ($lessonId <= 0) {
            Bd_Log::warning("param error, lessonId[$lessonId]");
            return -1;
        }
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lesson = $objLesson->getLessonInfo($lessonId, array('lessonId'));
        if (false === $lesson) {
            Bd_Log::warning("db error, get lesson info fail, lessonId[$lessonId]");
            return false;
        }
        if (empty($lesson)) {
            Bd_Log::warning("lesson not exist, lessonId[$lessonId]");
            return true;
        }
        //$objCache  = Hk_Service_RedisClient::getInstance("zhibo");
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $key = 'livemonitor_alarm_'.$lessonId;
        $ret = $objStored->setex($key, 86400, 1);
        if (false === $ret) {
            Bd_Log::warning("set redis error, key[$key]");
            return false;
        }
        return true;
    }

    /**
     * 恢复某个章节的报警
     * @param integer $lessonId
     * @return number|boolean 屏蔽成功true， 屏蔽失败false，参数错误-1
     */
    public function resumeAlarm($lessonId) {
        $lessonId = intval($lessonId);
        if ($lessonId <= 0) {
            Bd_Log::warning("param error, lessonId[$lessonId]");
            return -1;
        }
        //$objCache  = Hk_Service_RedisClient::getInstance("zhibo");
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $key = 'livemonitor_alarm_'.$lessonId;
        $ret = $objStored->del($key);
        if (is_null($ret)) {
            Bd_Log::warning("del redis error, key[$key]");
            return false;
        }
        return true;
    }

    /**
     * 判断课程是否被手工屏蔽报警
     * @param integer $lessonId
     * @return boolean 被屏蔽true， 未屏蔽false
     */
    public function isShieldLesson($lessonId) {
        $lessonId = intval($lessonId);
        if ($lessonId <= 0) {
            Bd_Log::warning("param error, lessonId[$lessonId]");
            return false;
        }
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        //$objCache  = Hk_Service_RedisClient::getInstance("zhibo");
        $key = 'livemonitor_alarm_'.$lessonId;
        $ret = $objStored->get($key);
        if (false === $ret) {
            Bd_Log::warning("get redis error, key[$key]");
            return false;
        }
        if ($ret) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断此章节是否忽略报警
     * @param integer $lessonId
     * @return boolean 需要忽略true，不忽略false
     */
    public function needIgnoreAlarm($lessonId) {
        $lessonId = intval($lessonId);
        if (isset(self::$_arrLessonAlarmMap[$lessonId])) {
            return self::$_arrLessonAlarmMap[$lessonId];
        }
        if ($lessonId > 0) {
            $flag = $this->_needIgnoreAlarm($lessonId);
            self::$_arrLessonAlarmMap[$lessonId] = $flag;
            return $flag;
        } else {
            return false;
        }
    }

    /**
     * 判断此章节是否忽略报警
     * 忽略报警的情况：
     * 1. 当前是测试环境
     * 2. 被手工屏蔽
     * 3. 内部课
     * 4. 研发建的课
     * @param integer $lessonId
     * @return boolean 需要忽略true，不忽略false
     */
    private function _needIgnoreAlarm($lessonId) {
        if (ral_get_idc() == 'test') {
            return true;
        }
        $lessonId = intval($lessonId);
        if ($lessonId <= 0) {
            return false;
        }
        if ($this->isShieldLesson($lessonId)) {
            return true;
        }
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo= $objLesson->getLessonInfo($lessonId, array('courseId'));
        if (false === $lessonInfo) {
            Bd_Log::warning("db error, get lessoninfo fail, lessonId[$lessonId]");
            return false;
        }
        if (empty($lessonInfo)) { //章节不存在，忽略
            return true;
        }
        $courseId = $lessonInfo['courseId'];
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo = $objCourse->getCourseInfo($courseId, array('teacherName', '`inner`'));
        if (false === $courseInfo) {
            Bd_Log::warning("db error, get courseinfo fail, courseId[$courseId]");
            return false;
        }
        if (empty($courseInfo)) { //课程不存在，忽略
            return true;
        }
        if ($courseInfo['inner']) {
            return true;
        }
        if (preg_match("#内部|测试|研发|开发|QA|内测#u", $courseInfo['teacherName'], $m)) { //内部账号，忽略
            return true;
        }
        return false;
    }

    /**
     * 发送钉钉报警
     * @param integer $lessonId 章节id，如果不是章节级别的报警，置为0
     * @param string $alarmTitle 报警标题
     * @param array $arrAlarmBody 报警内容（函数内部会拼成markdown语法），例：
     * array(
     *     '章节id' => 12345,
     *     '错误内容' => '老师迟到',
     *     '应该是有问题啊！！',
     * )
     * @param array $arrAlarmLink 可点击链接，例:
     * array(
     *     '查看详情' => 'https://www.zybang.com',
     * )
     * @param integer $alarmType 报警类型，1直播 2回放
     * @param boolean $isForce 是否强制发送，true时表示不进行lessonId有效性和屏蔽性检查，false表示需要检查
     * @return boolean true发送成功，false失败
     */
    public function sendDingAlarm($lessonId, $alarmTitle, $arrAlarmBody, $arrAlarmLink=array(), $alarmType=1, $isForce=false) {
        if (!$isForce && $this->needIgnoreAlarm($lessonId)) {
            return true;
        }
        if ($alarmType == 2) { //回放
            $robotId = Hkzb_Service_DingRobot::ID_PLAYBACK_ALARM;
        } else {
            $robotId = Hkzb_Service_DingRobot::ID_ZHIBO_ALARM;
        }
        $text = '#### '.$alarmTitle."\n\n";
        foreach ($arrAlarmBody as $k => $v) {
            if (is_numeric($k)) {
                $text .= '>'.$v."\n\n";
            } else {
                $text .= '>'.$k.'：'.$v."\n\n";
            }
        }
        $clientIp = CLIENT_IP;
        $localIp = Bd_Ip::getLocalIp();
        if ($clientIp != '' && $localIp != '') {
            $text .= '>来自['.$clientIp.']的请求，由['.$localIp.']发送'."\n\n";
        }
        foreach ($arrAlarmLink as $k => $v) {
            if (is_numeric($k)) {
                $text .= '>[点我]('.$v.")\n\n";
            } else {
                $text .= '>['.$k.']('.$v.")\n\n";
            }
        }
        return Hkzb_Service_DingRobot::sendMarkDownMsg($robotId, $alarmTitle, $text);
    }

    /**
     * 发送短信报警
     * @param integer $lessonId 章节id，如果不是章节级别的报警，置为0
     * @param integer $utype 接收报警的用户级别
     * @param string $msg 报警内容
     * @param array $arrReceiver 报警接收人，如果不为空则忽略$utype参数，使用此参数作为接收者，例：
     * array(
     *    18310138058,
     *    18501359563,
     * )
     * @param boolean $isForce 是否强制发送，true时表示不进行lessonId有效性和屏蔽性检查，false表示需要检查
     * @return boolean true发送成功，false失败
     */
    public function sendSmsAlarm($lessonId, $utype, $msg, $arrReceiver=array(), $isForce=false) {
        if (!$isForce && $this->needIgnoreAlarm($lessonId)) {
            return true;
        }
        if (empty($arrReceiver)) {
            $arrReceiver = $this->getMonitorPhoneList($utype);
        }
        if (!empty($arrReceiver)) {
            return Hk_Service_SmsCommon::sendSmsByTemplateId(implode(',', $arrReceiver), array($msg), 137, 1, 'guodu');
        } else {
            return true;
        }
    }

    /**
     * 发送邮件报警
     * @param integer $lessonId 章节id，如果不是章节级别的报警，置为0
     * @param integer $utype 接收报警的用户级别
     * @param string $alarmTitle 报警标题
     * @param array $arrAlarmBody 报警内容（函数内部会拼成html语法），例：
     * array(
     *     '章节id' => 12345,
     *     '错误内容' => '老师迟到',
     *     '应该是有问题啊！！',
     * )
     * @param array $arrAlarmLink 可点击链接，例:
     * array(
     *     '查看详情' => 'https://www.zybang.com',
     * )
     * @param array $arrReceiver 报警接收人，如果不为空则忽略$utype参数，使用此参数作为接收者，例：
     * array(
     *     '<EMAIL>',
     *     '<EMAIL>',
     * )
     * @param boolean $isForce 是否强制发送，true时表示不进行lessonId有效性和屏蔽性检查，false表示需要检查
     * @return boolean true发送成功，false失败
     */
    public function sendMailAlarm($lessonId, $utype, $alarmTitle, $arrAlarmBody, $arrAlarmLink=array(), $arrReceiver=array(), $isForce=false) {
        if (!$isForce && self::needIgnoreAlarm($lessonId)) {
            return true;
        }
        if (empty($arrReceiver)) {
            $arrReceiver = $this->getMonitorMailList($utype);
        }
        if (empty($arrReceiver)) {
            return true;
        }
        $text = '';
        foreach ($arrAlarmBody as $k => $v) {
            if (is_numeric($k)) {
                $text .= '<div>'.$v.'</div><p></p>';
            } else {
                $text .= '<div>'.$k.'：'.$v.'</div><p></p>';
            }
        }
        foreach ($arrAlarmLink as $k => $v) {
            if (is_numeric($k)) {
                $text .= '<div><a href="'.$v.'">点我</a><p></p>';
            } else {
                $text .= '<div><a href="'.$v.'">'.$k.'</a><p></p>';
            }
        }
        $clientIp = CLIENT_IP;
        $localIp = Bd_Ip::getLocalIp();
        if ($clientIp != '' && $localIp != '') {
            $text .= '<div>来自['.$clientIp.']的请求，由['.$localIp.']发送.</div><p></p>';
        }
        $receiver = implode(',', $arrReceiver);
        exec("nohup curl http://proxy.zuoyebang.com:1925/api/mail -XPOST -d 'tos=$receiver&subject=$alarmTitle&content=$text&format=html'>/dev/null 2>&1 &");
        return true;
    }
}
