<?php
/**
 * File: SurveyQuestion.php
 * User: lih<PERSON><PERSON>e
 * Date: 2018/2/5
 * Time: 下午4:23
 * Desc: 课后评价问题
 */
class Hkzb_Ds_Fudao_SurveyQuestion
{
    const ALL_FIELDS = 'id,surveyId,type,rank,question,options,deleted,createTime,updateTime,extData';

    const TYPE_SINGLE = 1;
    const TYPE_MULTIPLE = 2;
    const TYPE_INPUT = 3;
    const TYPE_STAR = 4;
    public static $TYPE_MAP = array(
        self::TYPE_SINGLE   => '单选题',
        self::TYPE_MULTIPLE => '多选题',
        self::TYPE_INPUT    => '主观题',
        self::TYPE_STAR     => '评分题',
    );
    public static $REQUIRED_TYPE = array(
        self::TYPE_SINGLE,
        self::TYPE_MULTIPLE,
        self::TYPE_STAR,
    );

    const DELETED_NO = 0;
    const DELETED_YES = 1;

    private $_objDaoSurveyQuestion;
    public function __construct()
    {
        $this->_objDaoSurveyQuestion = new Hkzb_Dao_Fudao_SurveyQuestion();
    }

    /**
     * @param $arrParams
     * @return bool
     */
    public function addSurveyQuestion($arrParams)
    {
        if (empty($arrParams) || !is_array($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $data = array(
            'surveyId'      => isset($arrParams['surveyId']) ? intval($arrParams['surveyId']) : 0,
            'type'          => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'rank'          => isset($arrParams['rank']) ? intval($arrParams['rank']) : 0,
            'question'      => isset($arrParams['question']) ? strval($arrParams['question']) : '',
            'options'       => isset($arrParams['options']) && is_array($arrParams['options']) ? json_encode($arrParams['options']) : '[]',
            'deleted'       => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createTime'    => time(),
            'updateTime'    => time(),
            'extData'       => isset($arrParams['extData']) && is_array($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );

        $ret = $this->_objDaoSurveyQuestion->insertRecords($data);
        if ($ret === false) {
            Bd_Log::warning("Insert survey question DB error!");
        }

        return $ret;
    }

    public function updateSurveyQuestion($id, $arrParams)
    {
        $id = intval($id);
        if ($id <= 0 || empty($arrParams) || !is_array($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[id: $id  param: " . json_encode($arrParams) . "]");
        }

        $arrFields = explode(',', self::ALL_FIELDS);
        unset($arrFields['id']);
        $arrUpdateFields = array();
        foreach ($arrParams as $key => $value) {
            if (in_array($key, $arrFields)) {
                $arrUpdateFields[$key] = $value;
            }
        }
        $arrUpdateFields['updateTime'] = time();
        $ret = $this->_objDaoSurveyQuestion->updateByConds(array('id' => $id), $arrUpdateFields);
        return $ret;
    }

    /**
     * 查询问题列表，查全部deleted传null
     * @param $surveyId
     * @param array $arrFields
     * @param int $deleted
     * @return array|false
     */
    public function getSurveyQuestionList($surveyId, $arrFields = array(), $deleted = self::DELETED_NO)
    {
        $surveyId = intval($surveyId);
        $arrConds = array(
            'surveyId' => $surveyId,
        );
        if (isset($deleted)) {
            $arrConds['deleted'] = $deleted;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoSurveyQuestion->getListByConds($arrConds, $arrFields);
        return $ret;
    }

}