<?php
/**
 * @filename:      LmsChapter.php
 * @author:        <EMAIL>
 * @desc:          Lecture章节服务
 * @create:        2017-07-05 18:54:57
 * @last modified: 2017-07-05 18:54:57
 */
class Hkzb_Ds_Fudao_LmsChapter
{
    private $_objDaoLmsChapter;
    const ALL_FIELDS = 'id,chapterId,courseId,courseName,teacherUid,teacherName,lectureAddTime,video,syncStatus,deleted,grade,subject,source,semester,year,chapterName,lecturePdf,lecturePic,lecture,studentLecturePdf,studentLecturePic,studentLecture,status,page,startTime,stopTime,courseType,readNoteStatus,writeNoteStatus,idx,pdfToPicTime';

    public function __construct()
    {
        $this->_objDaoLmsChapter = new Hkzb_Dao_Fudao_LmsChapter();
    }

    public function insert($rows)
    {
        $ret = $this->_objDaoLmsChapter->insertRecords($rows);
        if(false !== $ret){
            $ret = $this->_objDaoLmsChapter->getInsertId();
        }
        return $ret;
    }

    public function update($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL)
    {
        if (empty($arrConds)) {
            $arrConds = NULL;
        }
        $ret = $this->_objDaoLmsChapter->updateByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
        return $ret;
    }

    public function getListByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL)
    {
        if (empty($arrConds)) {
            $arrConds = NULL;
        }
        $ret = $this->_objDaoLmsChapter->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
        return $ret;
    }

    public function getCntByConds($arrConds)
    {
        if (empty($arrConds)) {
            $arrConds = NULL;
        }
        $ret = $this->_objDaoLmsChapter->getCntByConds($arrConds);
        return $ret;
    }

    public function getRecordByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL)
    {
        if (empty($arrConds)) {
            $arrConds = NULL;
        }
        $ret = $this->_objDaoLmsChapter->getRecordByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
        return $ret;
    }

    public function getChapterById($chapterId)
    {
        $arrConds = array(
            "chapterId" => $chapterId,
        );
        $arrFields = array("*");
        $ret = $this->_objDaoLmsChapter->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    public function getChapterByCourseId($courseId)
    {
        $arrConds = array(
            "courseId" => $courseId,
        );
        $arrFields = array("*");
        $ret = $this->_objDaoLmsChapter->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * updateLecturePicByChapterId
     *
     * 更新教师讲义图片列表
     * @param int $id
     * @param array $lecturePic
     * @access public
     * @return int|bool
     */
    public function updateLecturePicByChapterId($id, $lecturePic)
    {
        if (!$id || empty($lecturePic)) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        if (!is_array($lecturePic)) {
            Bd_Log::warning("Error:[lecturePic error]");
            return false;
        }

        $arrConds = [
            'chapterId' => (int) $id,
        ];

        $arrFields = [
            'lecturePic' => json_encode($lecturePic, JSON_UNESCAPED_UNICODE)
        ];

        $ret = $this->_objDaoLmsChapter->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * updatestudentLecturePicByChapterId
     *
     * 更新随堂讲义图片
     * @param int $id
     * @param array $studentLecturePic
     * @access public
     * @return int|bool
     */
    public function updatestudentLecturePicByChapterId($id, $studentLecturePic)
    {
        if (!$id || empty($studentLecturePic)) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        if (!is_array($studentLecturePic)) {
            Bd_Log::warning("Error:[lecturePic error]");
            return false;
        }

        $arrConds = [
            'chapterId' => (int) $id,
        ];

        $arrFields = [
            'studentLecturePic' => json_encode($studentLecturePic, JSON_UNESCAPED_UNICODE)
        ];

        $ret = $this->_objDaoLmsChapter->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**根据章节id更新数据
     * @param $chapterId
     * @param $arrParams
     * @return bool
     */
    public function updateChapter($chapterId, $arrParams)
    {
        if (intval($chapterId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[chapterId:$chapterId]");
            return false;
        }

        $arrConds = array(
            'chapterId' => intval($chapterId),
            );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        $ret = $this->_objDaoLmsChapter->updateByConds($arrConds, $arrFields);

        return $ret;
    }
}

