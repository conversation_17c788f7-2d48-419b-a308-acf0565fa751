<?php

/**
 * @file    AssistantCourseStudent.php
 * <AUTHOR>
 * @date    2017-11-11
 * @brief   班主任课程学生表
 *
 **/
class Hkzb_Ds_Fudao_AssistantCourseStudent {

    //预续报标识
    const PRE_CONTINUE_NO       = 0; // 未联系
    const PRE_CONTINUE_B        = 1; // B类意愿
    const PRE_CONTINUE_C        = 2; // C类意愿
    const PRE_CONTINUE_D        = 3; // D类意愿
    public static $ARR_PRECONTINUE = array(
        self::PRE_CONTINUE_NO => '未联系',
        self::PRE_CONTINUE_B => 'B',
        self::PRE_CONTINUE_C => 'C',
        self::PRE_CONTINUE_D => 'D',
    );

    const ALL_FIELDS='id,studentUid,courseId,assistantUid,classId,new,weixin,interview,backInterview,preContinue,regTime,deleteTime,status,createTime,updateTime,extData';

    protected $_objDao;

    public function __construct() {
        $this->_objDao = new Hkzb_Dao_Fudao_AssistantCourseStudent();
    }

    public function getStudentListByCourseId($intCourseId,$arrFields = array()) {
        $intCourseId = intval($intCourseId);

        if ( $intCourseId <= 0) {
            return array();
        }

        $arrConds = array(
            'courseId' => $intCourseId,
            'status'=>0,
        );
        
        if (empty($arrFields)) {
            $arrFields = explode(',',self::ALL_FIELDS);
        }

        $intCnt = $this->_objDao->getCntByConds($arrConds);

        if ( intval($intCnt) <= 0) {
            return array();
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit 0, $intCnt",
        );
        
        $arrList = $this->_objDao->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $arrList;
    }


}