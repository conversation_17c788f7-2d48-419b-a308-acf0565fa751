<?php
/**
* @file: UserAddressUnusual.php
* @desc: 物流地址错误数据表
* @date: 2018年6月28日 下午5:09:02
* @author: l<PERSON><PERSON><PERSON><PERSON> <lian<PERSON><PERSON><PERSON>@zuoyebang.com>
*/
class Hkzb_Ds_Fudao_UserAddressUnusual
{
    private $_objUserAddressUnusual;
    
    public function __construct(){
        $this->_objUserAddressUnusual = new Hkzb_Dao_Fudao_UserAddressUnusual();
    }
    
    /**
    * @desc: 新增数据
    * @date: 2018年6月28日 下午5:11:21
    * @author: lian<PERSON><PERSON><PERSON> <liang<PERSON><EMAIL>>
    * @param: $arrParams array()
    * @return: bool true|false
    */
    public function AddNewRecord($arrParams)
    {
        if (empty($arrParams) || !is_array($arrParams)){
            Bd_Log::warning("[error] [param error] [the arrParam is empty]");
            return false;
        }
        
        $arrConds = array(
            'phone'              => isset($arrParams['phone']) ? strval($arrParams['phone']) : "",
            'studentUid'         => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'courseId'           => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'parentOrderId'      => isset($arrParams['parentOrderId']) ? intval($arrParams['parentOrderId']) : 0,
            'buyTime'            => isset($arrParams['buyTime']) ? intval($arrParams['buyTime']) : 0,
            'buyName'            => isset($arrParams['buyName']) ? strval($arrParams['buyName']) : "",
            'receivePhone'       => isset($arrParams['receivePhone']) ? strval($arrParams['receivePhone']) : "",
            'address'            => isset($arrParams['address']) ? strval($arrParams['address']) : "",
            'createTime'         => time(),
            'extData'            => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : "",
        );
        
        $ret = $this->_objUserAddressUnusual->insertRecords($arrConds);
        
        return $ret;
    }
    
    /**
    * @desc: 获取异常地址数据信息
    * @date: 2018年6月28日 下午6:01:52
    * @author: liangjian <<EMAIL>>
    * @param: $arrConds ,$arrFields
    * @return: array() |false
    */
    public function getUserAddressDataByConds($arrConds,$arrFields = array())
    {
        if (empty($arrFields)){
            $arrFields = Hkzb_Dao_Fudao_UserAddressUnusual::$allFields;
        }
        $ret = $this->_objUserAddressUnusual->getListByConds($arrConds, $arrFields);
        
        return $ret;
    }
}