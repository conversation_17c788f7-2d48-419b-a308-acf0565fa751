<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:StudentMentoring.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2017/1/14
 * @time: 10:00
 * @desc:
 */

class Hkzb_Ds_Fudao_StudentMentoring
{

    //问题解答状态 status
    const QUESTION_STATUS_DEFAULT = 0;//未解决
    const QUESTION_STATUS_SOLVED = 1;//已解决
    const QUESTION_STATUS_SHOW = 2;//可展现答案

    //问题审核状态 verifyStatus
    const QUESTION_VERIFY_STATUS_DEFAULT = 0;//未审核
    const QUESTION_VERIFY_STATUS_PASSED = 1;//审核通过
    const QUESTION_VERIFY_STATUS_FAILED = 2;//审核未通过
    const QUESTION_VERIFY_STATUS_BANNED = 3;//拉黑

    //问题的删除状态
    const QUESTION_DELETED_STATUS_OK      = 0; //未删除
    const QUESTION_DELETED_STATUS_DELETED = 1; //已删除

    //排序类型
    const ORDER_TYPE_CREATE_TIME = 1;
    static private $orderTypeMap = array(
        self::ORDER_TYPE_CREATE_TIME => 'create_time',
    );

    //课程列表排序顺序
    const ORDER_DESC = 1; //倒序
    const ORDER_ASC = 2; //顺序
    static private $orderMap = array(
        self::ORDER_DESC => 'desc',
        self::ORDER_ASC  => 'asc',
    );

    //答疑问题处理来源
    const QUESTION_OPERATE_FROM_WAIBAO = 1;  //问题被外包人员处理
    const QUESTION_OPERATE_FROM_SECONDOPERATE = 2;  //问题内部老师二次处理

    static $DAYI_NOT_PASS_REASON_ARRAY = array(
        array(
            'stateCode' => 1,
            'describe'  =>'其它',
            'sort'      => 10000,//排序在最后
        ),
        array(
            'stateCode' => 2,
            'describe'  => '一次只能问一题,不要贪心哦',
            'sort'      => 1,
        ),
        array(
            'stateCode' => 3,
            'describe'  => '是不是忘传图片啦',
            'sort'      => 2,
        ),
        array(
            'stateCode' => 4,
            'describe'  => '题干不完整,老师没法帮你解答哦',
            'sort'      => 3,
        ),
        array(
            'stateCode' => 5,
            'describe'  => '字迹\\图片不清晰,老师看不清楚呀',
            'sort'      => 4,
        ),
        array(
            'stateCode' => 6,
            'describe'  => '这个问题和学科无关哦',
            'sort'      => 5,
        ),
        array(
            'stateCode' => 7,
            'describe'  => '这不是个具体问题,老师不知道怎么回答呀',
            'sort'      => 6,
        ),
    );

    //可以进入答疑质检平台的账号
    static $DAYI_CON_TEACHER_ARRAY = array(2269912432);

    private $_objDao;

    public function __construct()
    {
        $this->_objDao = new Hkzb_Dao_Fudao_StudentMentoring();
    }

    /**
     * 添加问题
     * @param $arrFields
     * @return bool
     */
    public function addQuestion($arrFields)
    {
        if (empty($arrFields['content']) || empty($arrFields['studentUid']) || empty($arrFields['teacherUid']) || empty($arrFields['courseId'])) {
            Bd_Log::warning("param error, studentUid:".$arrFields['studentUid'].", content:" . $arrFields['content'] .", teacherUid:".
                    $arrFields['teacherUid'].", courseId:".$arrFields['courseId']);
            return false;
        }
        $arrInsert = array(
            'studentUid' => isset($arrFields['studentUid']) ? intval($arrFields['studentUid']) : 0,
            'teacherUid' => isset($arrFields['teacherUid']) ? intval($arrFields['teacherUid']) : 0,
            'courseId'   => isset($arrFields['courseId']) ? intval($arrFields['courseId']) : 0,
            'deleted'    => self::QUESTION_DELETED_STATUS_OK,
            'status'     => 0,
            'content'    => isset($arrFields['content']) ? $arrFields['content'] : 0,
            'createTime' => time(),
            'extData'    => isset($arrFields['extData']) && is_array($arrFields['extData']) ? json_encode($arrFields['extData']) : '[]',
        );
        $ret       = $this->_objDao->insertRecords($arrInsert);
        if ($ret === false) {
            Bd_Log::warning("insert error, arrFields:". json_encode($arrFields));
            return false;
        }
        $insertId = $this->_objDao->getInsertId();

        return $insertId;
    }

    /**
     * 根据问题id查询问题详情
     * @param $intQid
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getQuestionByQid($intQid, $arrFields = array())
    {
        if (!$intQid) {
            Bd_Log::warning("param error, intQid:".$intQid);
            return false;
        }
        $arrConds = array('qid' => $intQid);
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        $res = $this->_objDao->getRecordByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 根据问题id查询问题详情
     * @param $intQid
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getQuestionByQidForUpdate($intQid, $arrFields = array())
    {
        if (!$intQid) {
            Bd_Log::warning("param error, intQid:".$intQid);
            return false;
        }
        $arrConds = array('qid' => $intQid);
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        $arrAppends = array(
            'FOR UPDATE',
        );
        $res        = $this->_objDao->getRecordByConds($arrConds, $arrFields, null, $arrAppends);

        return $res;
    }

    /**
     * 更新问题信息
     * @param $intQid
     * @param bool $intStatus
     * @param array $extData
     * @return bool
     */
    public function updateQuestion($intQid, $intStatus = false, $extData = array())
    {
        if (!$intQid) {
            Bd_Log::warning("param error, intQid:".$intQid);
            return false;
        }
        $questionInfo = self::getQuestionByQid($intQid);
        if ($questionInfo === false) {
            Bd_Log::warning("get question info failed, qid:$intQid");

            return false;
        }
        if (empty($questionInfo)) {
            Bd_Log::warning("question info not found, qid:$intQid");

            return false;
        }
        $arrConds  = array('qid' => $intQid);
        $arrFields = array();
        if ($intStatus !== false) {
            $arrFields['status'] = $intStatus;
        }
        if (!empty($extData)) {
            $ext = $questionInfo['extData'];
            foreach ($extData as $key => $value) {
                $ext[$key] = $value;
            }
            $arrFields['extData'] = json_encode($ext);
        }
        if (empty($arrFields)) {
            Bd_Log::warning("param error, status:$intStatus, ext:" . json_encode($extData));

            return false;
        }
        $res = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 问题审核
     * @param int $qid
     * @param int $verifyStatus
     */
    public function auditQuestion($qid, $verifyStatus,$arrExt = array())
    {
        $qid          = intval($qid);
        $verifyStatus = intval($verifyStatus);
        if ($qid <= 0 || $verifyStatus <= 0) {
            Bd_Log::warning("param error, qid[$qid] verifyStatus[$verifyStatus]");

            return 0;
        }
        $arrConds  = array(
            'qid' => $qid,
        );

        $arrFields = array(
            'verifyStatus' => $verifyStatus,
            'updateTime'   => time(),
        );
        if(!empty($arrExt)){
            $arrFields['extData'] = json_encode($arrExt);
        }

        $ret       = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 根据条件获取到所有提问列表
     * liangshuguang (<EMAIL>)
     * @param array $arrWhere 也可以是string
     * @param array $arrSelect
     * @param array $arrOrder
     * @return array|false
     */
    public function getAllMentoringListByCondition($arrWhere, $arrSelect, $arrOrder) {
        return $this->_objDao->getListByConds($arrWhere, $arrSelect, null, $arrOrder);
    }

    /**
     * 根据课程获取全部问题列表
     * @param int $intCourseId
     * @param bool $intStatus
     * @param array $arrFields
     * @param int $intStartTime
     * @param int $intEndTime
     * @param int $intOrderType
     * @param int $intOrder
     * @param int $pn
     * @param int $rn
     * @return array|bool|false
     */
    public function getQuestionListByCourse($intCourseId = 0, $intTeacherUid = 0, $intStudentUid, $arrFields = array(), $intStartTime = 0, $intEndTime = 0, $intOrderType = self::ORDER_TYPE_CREATE_TIME, $intOrder = self::ORDER_DESC, $pn = 0, $rn = 0)
    {
        if (empty($intCourseId) && empty($intTeacherUid)) {
            Bd_Log::warning("param error, intCourseId:".$intCourseId.", intTeacherUid:".$intTeacherUid);
            return false;
        }
        $arrConds = array(
            "verify_status = 1 or (student_uid = $intStudentUid and verify_status in (0, 1))",
            'deleted' => self::QUESTION_DELETED_STATUS_OK,
        );
        if (intval($intCourseId) > 0) {
            $arrConds['courseId']   = $intCourseId;
        }
        if (intval($intTeacherUid) > 0) {
            $arrConds['teacherUid'] = $intTeacherUid;
        }
        if ($intStartTime && $intEndTime) {
            $arrConds['createTime'] = array($intStartTime, '>', $intEndTime, '<');
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        //排序、分页
        $strAppend = 'order by ' . self::$orderTypeMap[$intOrderType] . ' ' . self::$orderMap[$intOrder] . ' ';
        if ($rn > 0) {
            $strAppend = $strAppend . " limit $pn, $rn";
        }
        $arrAppend = array($strAppend);
        $res       = $this->_objDao->getListByConds($arrConds, $arrFields, null, $arrAppend);

        return $res;
    }

    /**
     * 查询学生问题列表
     * @param $intStudentUid
     * @param bool $intStatus
     * @param bool $intVerifyStatus
     * @param array $arrFields
     * @param int $intOrderType
     * @param int $intOrder
     * @param int $pn
     * @param int $rn
     * @return array|bool|false
     */
    public function getQuestionListByStudent($intStudentUid, $courseId = 0, $intStatus = false, $intVerifyStatus = false, $arrFields = array(), $intOrderType = self::ORDER_TYPE_CREATE_TIME, $intOrder = self::ORDER_DESC, $pn = 0, $rn = 0)
    {
        if (empty($intStudentUid)) {
            Bd_Log::warning("param error, intStudentUid:".$intStudentUid);
            return false;
        }
        $arrConds = array(
            'studentUid' => $intStudentUid,
            'deleted'    => self::QUESTION_DELETED_STATUS_OK,
        );
        if ($courseId > 0) {
            $arrConds['courseId'] = $courseId;
        }
        if ($intStatus !== false) {
            $arrConds['status'] = $intStatus;
        }
        if ($intVerifyStatus !== false) {
            $arrConds['verifyStatus'] = $intVerifyStatus;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        //排序、分页
        $strAppend = 'order by ' . self::$orderTypeMap[$intOrderType] . ' ' . self::$orderMap[$intOrder] . ' ';
        if ($rn > 0) {
            $strAppend = $strAppend . " limit $pn, $rn";
        }
        $arrAppend = array($strAppend);
        $res       = $this->_objDao->getListByConds($arrConds, $arrFields, null, $arrAppend);

        return $res;
    }
    /**
     * 查询某天所有的问题数量（不包含无效问题）
     * @return list
     */
    public function getAllQuestion($dateTime = 0,$arrFields = array())
    {
        if (intval($dateTime) <= 0) {
            Bd_Log::warning("param error, dateTime:".intval($dateTime));
            return false;
        }
        $arrConds = array(
            "verify_status in (" . self::QUESTION_VERIFY_STATUS_DEFAULT . "," . self::QUESTION_VERIFY_STATUS_PASSED . ")",
            'deleted' => self::QUESTION_DELETED_STATUS_OK,
        );
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        $arrConds['createTime'] = array(
            $dateTime,
            '>=',
            $dateTime + 86400,
            '<',
        );
        $res = $this->_objDao->getListByConds($arrConds,$arrFields);

        return $res;
    }


    /**
     * 查询某天某课某辅导老师的问题数量（不包含无效问题）
     * @param $intCourseId
     * @param $intAssistantUid
     * @return bool|false|int
     */
    public function getQuestionCntByAssistant($intCourseId = 0, $intAssistantUid = 0, $date = '', $status = -1)
    {
        if (empty($intAssistantUid) && empty($intCourseId)) {
            Bd_Log::warning("param error, intAssistantUid:".$intAssistantUid.", intCourseId:".$intCourseId);
            return false;
        }
        $arrConds = array(
            "verify_status in (" . self::QUESTION_VERIFY_STATUS_DEFAULT . "," . self::QUESTION_VERIFY_STATUS_PASSED . ")",
            'deleted' => self::QUESTION_DELETED_STATUS_OK,
        );
        if (intval($intCourseId) > 0) {
            $arrConds['courseId']   = $intCourseId;
        }
        if (intval($intAssistantUid) > 0) {
            $arrConds['teacherUid'] = $intAssistantUid;
        }
        if (!empty($date)) {
            $arrConds['createTime'] = array(
                strtotime($date),
                '>',
                strtotime($date) + 86400,
                '<',
            );
        }
        if ($status >= 0) {
            $arrConds['status'] = $status;
        }
        $cnt = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }

    /**
     * 查询某天某课某辅导老师的问题数量（不包含无效问题）
     * @param $intCourseId
     * @param $intAssistantUid
     * @return bool|false|int
     */
    public function getUnsolvedCntByAssistant($intCourseId = 0, $intAssistantUid = 0, $date = '', $status = -1)
    {
        if (empty($intAssistantUid) && empty($intCourseId)) {
            Bd_Log::warning("param error, intAssistantUid:".$intAssistantUid.", intCourseId:".$intCourseId);
            return false;
        }
        $arrConds = array(
            "verify_status in (" . self::QUESTION_VERIFY_STATUS_DEFAULT . "," . self::QUESTION_VERIFY_STATUS_PASSED . ")",
            'deleted' => self::QUESTION_DELETED_STATUS_OK,
        );
        if (intval($intCourseId) > 0) {
            $arrConds['courseId']   = $intCourseId;
        }
        if (intval($intAssistantUid) > 0) {
            $arrConds['teacherUid'] = $intAssistantUid;
        }
        if (!empty($date)) {
            $arrConds['createTime'] = array(
                strtotime($date)-86400*7,
                '>',
                strtotime($date) + 86400,
                '<',
            );
        }
        if ($status >= 0) {
            $arrConds['status'] = $status;
        }
        $cnt = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }

        /**
     * 查询某天某课某辅导老师的问题数量（不包含无效问题）
     * @param $intAssistantUid
     * @return $res
     */
    public function getQuestionListByAssistant($intAssistantUid, $arrFields = array())
    {
        if (empty($intAssistantUid)) {
            Bd_Log::warning("param error, intAssistantUid:".$intAssistantUid);
            return false;
        }
        $arrConds = array(
            'teacherUid' => $intAssistantUid,
            "verify_status in (" . self::QUESTION_VERIFY_STATUS_DEFAULT . "," . self::QUESTION_VERIFY_STATUS_PASSED . ")",
            'deleted' => self::QUESTION_DELETED_STATUS_OK,
        );
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        $res = $this->_objDao->getListByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 查询某同学某天某课提的问题数量
     * @param $intCourseId
     * @param $intAssistantUid
     * @return bool|false|int
     */
    public function getQuestionCntByStudent($intCourseId, $intStudentUid, $date = '')
    {
        if (empty($intCourseId) || empty($intStudentUid)) {
            Bd_Log::warning("param error, intCourseId:".$intCourseId.", intStudentUid:".$intStudentUid);
            return false;
        }
        $arrConds = array(
            'courseId'   => $intCourseId,
            'studentUid' => $intStudentUid,
            'deleted'    => self::QUESTION_DELETED_STATUS_OK,
        );
        if (!empty($date)) {
            $arrConds['createTime'] = array(
                strtotime($date),
                '>',
                strtotime($date) + 86400,
                '<',
            );
        }
        $cnt = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }

    /**
     * 根据老师id，课程id和问题类型获取问题列表
     * @param int $teacherUid
     * @param int $courseId
     * @param string $type ，取值（"todo", "done", "notpass"）
     */
    public function getQuestionListByTeacherUidCourseIdType($teacherUid, $courseId = 0, $type, $arrFields)
    {
        $teacherUid = intval($teacherUid);
        $type       = strval($type);
        if ($teacherUid <= 0 || !in_array($type, array("todo", "done", "notpass"))) {
            Bd_Log::warning("param error, teacherUid[$teacherUid] type[$type]");

            return array();
        }

        if ($type == 'todo') {
            $arrConds = array(
                'status' => self::QUESTION_STATUS_DEFAULT,
                'verify_status in (' . self::QUESTION_VERIFY_STATUS_DEFAULT . ',' . self::QUESTION_VERIFY_STATUS_PASSED . ')',
            );
        } elseif ($type == 'done') {
            $arrConds = array(
                'status in (' . self::QUESTION_STATUS_SOLVED . ',' . self::QUESTION_STATUS_SHOW . ')',
                'verifyStatus' => self::QUESTION_VERIFY_STATUS_PASSED,
            );
        } elseif ($type == 'notpass') {
            $arrConds = array(
                'verify_status in (' . self::QUESTION_VERIFY_STATUS_FAILED . ',' . self::QUESTION_VERIFY_STATUS_BANNED . ')',
            );
        }
        $arrConds['teacherUid'] = $teacherUid;
        $arrConds['deleted']    = self::QUESTION_DELETED_STATUS_OK;
        if (intval($courseId) > 0) {
            $arrConds['courseId']   = $courseId;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        $res = $this->_objDao->getListByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 获取用户某课程下的被回答的问题id列表
     * @param $intStudentUid
     * @param $intCourseId
     */
    public function getStudentQidList($intStudentUid, $intCourseId)
    {
        if (empty($intStudentUid) || empty($intCourseId)) {
            Bd_Log::warning("param error, intCourseId:".$intCourseId.", intStudentUid:".$intStudentUid);
            return false;
        }
        $arrConds = array(
            'studentUid' => $intStudentUid,
            'courseId'   => $intCourseId,
            'status'     => self::QUESTION_STATUS_SHOW,
            'deleted'    => self::QUESTION_DELETED_STATUS_OK,
        );
        $ret      = $this->_objDao->getListByConds($arrConds, array('qid'));
        if ($ret === false) {
            return false;
        }
        if (empty($ret)) {
            return array();
        }
        $arrData = array();
        foreach ($ret as $value) {
            $arrData[] = $value['qid'];
        }

        return $arrData;
    }

    /**
     * 根据条件
     * @param int applyCoruseId
     * @param int status
     * @return false|int
     */
    public function getQuestionCnt($arrConds) {
        $ret = $this->_objDao->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 获取用户某课程下的被回答的问题id列表
     * @param $intStudentUid
     * @param $intCourseId
     */
    public function getQuestionList($arrConds, $arrFields = array(), $offset = 0, $limit = 0)
    {
        if(empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        }
        //limit限制
        if($offset >= 0 && $limit > 0){
            $orderBy .= " limit $offset,$limit";
        }else if($limit > 0){
            $orderBy .= " limit $limit";
        }
        $arrAppends = ($orderBy != '') ? array($orderBy) : NULL;
        $res = $this->_objDao->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $res;
    }

    /**
     * 根据qid更新问题（用于更新一条记录的各个字段，但不包含扩展字段）
     *
     * @param  int   $qid       问题qid(主键)
     * @param  array $arrParams 更新字段
     * @return bool  true/false
     * @create_time  2017-07-27
     */
    public function updateQuestionFieldsByQid($qid, $arrParams){
        if (intval($qid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[qid:$qid]");
            return false;
        }

        $arrConds = array(
            'qid' => intval($qid),
        );

        $arrFields    = array();
        $arrAllFields = Hkzb_Dao_Fudao_StudentMentoring::$arrFields;
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $ret;
    }
}
