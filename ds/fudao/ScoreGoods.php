<?php

/**
 * Created by PhpStorm.
 * User: ya<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2017/5/30
 * Time: 18:18
 * desc:积分商品
 */
class Hkzb_Ds_Fudao_ScoreGoods
{
    const SCORE_GOODS_STATUS_NORMAL  = 0;//商品状态，0有效
    const SCORE_GOODS_STATUS_INVALID = 1;//商品状态1无效，无效的商品不展示

    //商品类型定义
    const SCORE_GOODS_TYPE_COUPON    = 1;//商品类型，1是优惠券
    const SCORE_GOODS_TYPE_PRIVILEGE = 2;//商品类型2是特权
    const SCORE_GOODS_TYPE_SPECIAL   = 3;//表情包 不在积分商城展示

    //类型定义
    static $type = array(
        self::SCORE_GOODS_TYPE_COUPON    => '优惠券',
        self::SCORE_GOODS_TYPE_PRIVILEGE => '特权',
        self::SCORE_GOODS_TYPE_SPECIAL   => '表情包',
    );
    //优惠券的定义
    const SCORE_GOODS_TYPE_COUPON_SUBTYPE_CLASS = 0;//班课现金券
    const SCORE_GOODS_TYPE_COUPON_SUBTYPE_THEMATIC_CLASS = 1;//专题课现金券
    const SCORE_GOODS_TYPE_COUPON_SUBTYPE_PLATFORM_SHOP = 2;//平台购物券
    //特权的定义
    const SCORE_GOODS_GET_MIKE_PRIVILEGE      = 1;//抢麦特权
    const SCORE_GOODS_COLORFUL_WORD_PRIVILEGE = 2;//课中聊天炫彩字体
    const SCORE_GOODS_AVATAR_ORNAMENT          = 3;//头像挂件
    const SCORE_GOODS_CHAT_BUBBLE               = 4;//聊天气泡
    const SCORE_GOODS_YIKE_PRIVATE               = 5;//一课专属

    //特权的定义 privilege_id=>特权名称
    static $privilege = array(
        self::SCORE_GOODS_GET_MIKE_PRIVILEGE      => '抢麦特权',
        self::SCORE_GOODS_COLORFUL_WORD_PRIVILEGE => '课中聊天炫彩字体',
        self::SCORE_GOODS_AVATAR_ORNAMENT          => '头像挂件',
        self::SCORE_GOODS_CHAT_BUBBLE               => '聊天气泡',
        self::SCORE_GOODS_YIKE_PRIVATE               => '一课专属',
    );

    //商品消费类型限制
    const SCORE_GOODS_CONSUMETYPE_TIME     = 1;//消费类型计时间
    const SCORE_GOODS_CONSUMETYPE_COUNT    = 2;//消费类型计次数
    const SCORE_GOODS_CONSUMETYPE_NEWTIME  = 3;//消费类型计时间 同类型覆盖 后期可能做筛选与计时间分开 暂时逻辑一致
    const SCORE_GOODS_CONSUMETYPE_NOTIME  = 4;//消费类型无有效期

    //所有字段
    const ALL_FIELDS = 'goodsId,goodsName,price,status,type,subtype,goodsCnt,leftGoodsCnt,createTime,updateTime,operatorUid,operator,extData';


    private $objDaoScoreGoods;

    public function __construct()
    {
        $this->objDaoScoreGoods = new Hkzb_Dao_Fudao_ScoreGoods();
    }

    /**
     * 获取有效商品列表 去除表情包
     * @param int $type 商品的类型
     * @param array $arrFields 查询字段
     * @param int $offset 偏移量
     * @param int $limit 限制条数
     * @return array|false
     */

    public function getScoreGoodsList($type = 0, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'status' => self::SCORE_GOODS_STATUS_NORMAL
        );

        $type = intval($type);
        if ($type > 0) {
            $arrConds['type'] = $type;
        } else {
            $arrConds[] = 'type in ('.self::SCORE_GOODS_TYPE_COUPON.','.self::SCORE_GOODS_TYPE_PRIVILEGE.')';
        }

        $arrAppends = array(
            "order by goods_id desc",
            "limit $offset, $limit",
        );
        $ret        = $this->objDaoScoreGoods->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        if (false === $ret) {
            Bd_Log::warning("Error:[getListByConds], Detail:" . json_encode($arrConds));

            return false;
        }

        return $ret;
    }

    /**
     * 查询商品的数量 去除表情包
     * @param int $type 商品类型
     * @return bool|false|int
     */

    public function getScoreGoodsCnt($type = 0)
    {
        $arrConds = array(
            'status' => self::SCORE_GOODS_STATUS_NORMAL
        );

        $type = intval($type);
        if ($type > 0) {
            $arrConds['type'] = $type;
        } else {
            $arrConds[] = 'type in ('.self::SCORE_GOODS_TYPE_COUPON.','.self::SCORE_GOODS_TYPE_PRIVILEGE.')';
        }
        $ret = $this->objDaoScoreGoods->getCntByConds($arrConds);
        if (false === $ret) {
            Bd_Log::warning("Error:[getListByConds], Detail:" . json_encode($arrConds));

            return false;
        }

        return $ret;
    }

    /**
     * 增加一个积分商品
     * @param $arrParams 商品参数
     * @return bool
     */
    public function addScoreGoods($arrParams)
    {
        $arrFields = array(
            'goodsName'    => isset($arrParams['goodsName']) ? strval($arrParams['goodsName']) : '',
            'price'        => isset($arrParams['price']) ? intval($arrParams['price']) : 0,
            'status'       => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'subtype'      => isset($arrParams['subtype']) ? intval($arrParams['subtype']) : 0,
            'goodsCnt'     => isset($arrParams['goodsCnt']) ? intval($arrParams['goodsCnt']) : -1,
            'leftGoodsCnt' => isset($arrParams['leftGoodsCnt']) ? intval($arrParams['leftGoodsCnt']) : -1,
            'createTime'   => time(),
            'updateTime'   => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'operatorUid'  => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'     => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        if (empty($arrParams['goodsName']) || empty($arrParams['type']) || empty($arrParams['extData'])) {
            Bd_Log::warning("Error:[addScoreGoods], Detail:" . json_encode($arrParams));

            return false;
        }
        $ret = $this->objDaoScoreGoods->insertRecords($arrFields);
        if (false === $ret) {
            Bd_Log::warning("Error:[addScoreGoods], Detail:" . json_encode($arrParams));

            return false;
        }

        return $ret;
    }

    /**
     * 更新商品
     * @param $goodsId  商品id
     * @param $arrParams  需要修改的商品参数
     * @return bool
     */
    public function updateScoreGoods($goodsId, $arrParams)
    {
        if (intval($goodsId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[goodsId:$goodsId]");

            return false;
        }

        $goodsId      = intval($goodsId);
        $arrConds     = array(
            'goodsId' => $goodsId
        );
        $arrAllFields = explode(',', self::ALL_FIELDS);
        $arrFields    = array();
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoScoreGoods->updateByConds($arrConds, $arrFields);
        if (false === $ret) {
            Bd_Log::warning("Error:[updateScoreGoods], Detail:" . json_encode($arrParams));

            return false;
        }

        return $ret;
    }

    /**
     * 根据商品id获取商品的信息
     * @param $goodsId   商品id
     * @param array $arrFields 获取的字段
     * @return array|bool|false
     */

    public function getScoreGoodsInfoByGoodsId($goodsId, $arrFields = array())
    {
        $goodsId = intval($goodsId);
        if ($goodsId <= 0) {
            Bd_Log::warning("Error:[Param error],detail:[goodsId:$goodsId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'goodsId' => $goodsId
        );

        $ret = $this->objDaoScoreGoods->getRecordByConds($arrConds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning("Error:[getRecordByConds],detail:" . json_encode($arrConds));

            return false;
        }

        return $ret;
    }

    /**
     * 更新商品剩余数量，不允许业务代码直接调用！！！
     * @param $intGoodsId
     * @param int $quantity
     * @return bool
     */
    public function reduceLeftGoodsCnt($intGoodsId, $quantity = 1)
    {
        if ($intGoodsId <= 0) {
            return false;
        }
        $arrConds  = array(
            'goodsId' => $intGoodsId,
        );
        $arrFields = array(
            "left_goods_cnt = left_goods_cnt - $quantity",
        );
        $ret       = $this->objDaoScoreGoods->updateByConds($arrConds, $arrFields);

        return $ret;
    }
}