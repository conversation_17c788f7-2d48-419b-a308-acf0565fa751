<?php
/**
 * Created by PhpStorm.
 * User: renka<PERSON><PERSON>@zuoyebang.com
 * Date: 2018/4/19
 * Time: 18:48
 */
class Hkzb_Ds_Fudao_TeacherSaysConf
{
    //评论展示方式
    const STATUS_FIRSTCHECK = 0;
    const STATUS_FIRSTSEND = 1;
    static $STATUS_ARRAY = array(
        self::STATUS_FIRSTCHECK => '先审后发',
        self::STATUS_FIRSTSEND  => '先发后审',
    );

    const STATUS_FLOW_PART = 0;
    const STATUS_FLOW_ALL  = 1;
    static $FLOW_ARRAY = array(
        self::STATUS_FLOW_PART => '小流量',
        self::STATUS_FLOW_ALL  => '全量',
    );

    const TYPE_CHECK = 1;                 //审核
    const TYPE_FLOWCONTROL_TEACHER = 2;   //教师端流量控制
    const TYPE_FLOWCONTROL_STUDENT = 3;   //学生端流量控制
    static $TYPE_MAP = array(
        self::TYPE_CHECK => 1,
        self::TYPE_FLOWCONTROL_TEACHER => 2,
        self::TYPE_FLOWCONTROL_STUDENT => 3,
    );

    const ALL_FIELDS = 'id,stats,type,createTime,updateTime';

    private $_objDaoTeacherSaysConf;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->_objDaoTeacherSaysConf = new Hkzb_Dao_Fudao_TeacherSaysConf();
    }

    /**
     * 新增配置类型
     *
     * @param  array $arrParams 评论类型属性
     * @return bool true/false
     */
    public function addTeacherSaysConf($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['stats']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'stats' => isset($arrParams['stats']) ? intval($arrParams['stats']) : 0,
            'type' => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
        );

        $ret = $this->_objDaoTeacherSaysConf->insertRecords($arrParams['id'],$arrFields);

        return $ret;
    }

    /**
     * 更新配置
     *
     * @param  int  $id  配置id
     * @param  array  $arrParams 配置状态
     * @return bool true/false
     */
    public function updateTeacherSaysConf($id, $arrParams) {
        if(intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoTeacherSaysConf->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取指定配置信息
     *
     * @param  int $id id    类型id
     * @param  array $arrFields 类型属性
     * @return array|false
     */
    public function getTeacherSaysConf($id, $arrFields = array())
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheKeyConf = 'teachersays_conf_' . $id;
        $jsonConfInfo = $objMemcached->get($cacheKeyConf);

        if (empty($jsonConfInfo)) {

            if (empty($arrFields)) {
                $arrFields = explode(',', self::ALL_FIELDS);
            }

            $arrConds = array(
                'id' => intval($id),
            );

            $arrConfInfo = $this->_objDaoTeacherSaysConf->getRecordByConds($arrConds, $arrFields);
            $jsonConfInfo = $arrConfInfo;
            $objMemcached->set($cacheKeyConf, $jsonConfInfo, 60);

        }
        $ret = is_array($jsonConfInfo) ? $jsonConfInfo : json_decode(utf8_encode($jsonConfInfo), true);

        return $ret;
    }

    /**
     * 删除配置类型
     *
     * @param  int  $id  类型id
     * @return bool true/false
     */
    public function deleteTeacherSaysConf($id)
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->_objDaoTeacherSaysConf->deleteByConds($arrConds);

        return $ret;
    }

    /**
     * 获取配置类型
     * @return array|false
     */
    public function getTeacherSaysConfList($arrFields = array()){
        $arrConds = array('id > 0');
        if (empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }
        $ret = $this->_objDaoTeacherSaysConf->getListByConds($arrConds,$arrFields,NULL,array('order by type asc'));
        return $ret;
    }
}
