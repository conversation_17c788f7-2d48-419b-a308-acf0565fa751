<?php
/***************************************************************************
 *
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Course.php
 * <AUTHOR>
 * @date 2016/9/18 14:13:18
 * @brief 课程
 *
 **/

class Hkzb_Ds_Fudao_Advanced_Course
{
    private static $teacherInfo = array();
    //打包课
    const PACK_NO   = 0;
    const PACK_YES  = 1;
    const PACK_YESD = 2;
    static $PACK_ARRAY = array(
        self::PACK_NO   => '非打包课',
        self::PACK_YES  => '打包课',
        self::PACK_YESD => '被打包课',
    );

    //课程类型
    const TYPE_PRIVATE       = 0;
    const TYPE_PUBLIC        = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    const TYPE_PRE_LONG      = 4;
    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE       => '专题课',
        self::TYPE_PUBLIC        => '公开课',
        self::TYPE_PRIVATE_LONG  => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
        self::TYPE_PRE_LONG      => '预备班',
    );

    //状态
    const STATUS_TOPREPARE = 0; //待备课
    const STATUS_TOAUDIT   = 1; //待审核
    const STATUS_TOONLINE  = 2; //待上线
    const STATUS_ONLINE    = 3; //已上线
    const STATUS_FINISHED  = 4; //已结束
    const STATUS_DELETED   = 6; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_TOPREPARE => '待备课',
        self::STATUS_TOAUDIT   => '待审核',
        self::STATUS_TOONLINE  => '待上线',
        self::STATUS_ONLINE    => '已上线',
        self::STATUS_FINISHED  => '已结束',
        self::STATUS_DELETED   => '已删除',
    );

    //所有属性
    const ALL_FIELDS = 'teacherUid,teacherName,courseId,courseName,grade,subject,type,degree,price,registerStartTime,registerStopTime,studentCnt,studentMaxCnt,status,createTime,updateTime,startTime,operatorUid,operator,extData,`inner`,learnSeason,onlineStart,onlineStop,currentLessonTime,isShow,pack,content,tags,moreGrade';

    //18项服务
    static $services = array(
        1  => '直播课',
        2  => '课程回放',
        3  => '周学习报告',
        4  => '学期学习报告',
        5  => '期中复习计划',
        6  => '期末复习计划',
        7  => '作业讲解',
        8  => '一对一批改',
        9  => '7×24答疑',
        10 => '错题本',
        11 => '个性化作业',
        12 => '天天练',
        13 => '试卷分析',
        14 => '短信提醒',
        15 => '电话提醒',
        16 => '家长课',
        17 => '升学指导',
        18 => '专属辅导老师',
        19 => '纸质教材',
        20 => '课程缓存',
        21 => '天天练',
        22 => '结课典礼',
        23 => '书包',
        24 => '笔记本',
        26 => '笔袋',
        27 => '精品资料盒（小学）',
        28 => '精品资料盒（初中）',
        29 => '精品资料盒（高中）',
        30 => '强制报前测',
        31 => '非强制报前测',
        32 => '报后测',
        33 => '课前阶段测',
        34 => '课后阶段测',
        35 => '堂堂测',
        36 => '互动题',
        37 => '课后作业',
        38 => '电子讲义',
    );
    //赠品
     static $attrServices =array(
        19 => 2,//纸质教材
        23 => 5,//书包
        24 => 4,//笔记本
        26 => 3,//笔袋
        27 => 6,//实物。精品资料盒（小学）
        28 => 6,//实物。精品资料盒（初中）
        29 => 6,//实物。精品资料盒（高中）
     );
     //订单赠品类型
       static $signServices =array(
        19 => 2,//纸质教材
        23 => 5,//书包
        24 => 4,//笔记本
        26 => 3,//笔袋
        27 => 6,//实物。精品资料盒（小学）
        28 => 6,//实物。精品资料盒（初中）
        29 => 6,//实物。精品资料盒（高中）
     );

    //课程列表排序类型
    const ORDER_TYPE_START_TIME          = 1;
    const ORDER_TYPE_PRICE               = 2;
    const ORDER_TYPE_STUDENT_CNT         = 3;
    const ORDER_TYPE_CURRENT_LESSON_TIME = 4;
    static private $orderTypeMap = array(
        self::ORDER_TYPE_START_TIME          => 'start_time',
        self::ORDER_TYPE_PRICE               => 'price',
        self::ORDER_TYPE_STUDENT_CNT         => 'student_cnt',
        self::ORDER_TYPE_CURRENT_LESSON_TIME => 'current_lesson_time',
    );

    static public $autoOrderTypeMap = array(
        self::ORDER_TYPE_START_TIME          => 'startTime',
        self::ORDER_TYPE_PRICE               => 'price',
        self::ORDER_TYPE_STUDENT_CNT         => 'studentCnt',
        self::ORDER_TYPE_CURRENT_LESSON_TIME => 'currentLessonTime',
    );

    //课程列表排序顺序
    const ORDER_DESC = 1; //倒序
    const ORDER_ASC  = 2; //顺序
    static private $orderMap = array(
        self::ORDER_DESC => 'desc',
        self::ORDER_ASC  => 'asc',
    );

    //小班课 打包课id
    static $LITTLE_CLASS_COURSEID = array(
        1,
//        38154, //1209内测
//        38370, //1209内测
//        38248, // 1212内测
//        38475, //1212内测
    );

    //秒杀课id
    static $FLASH_SALES_COURSEID = array(
        82624,82628,82626,82632,55992,70126,56020,56011,55566,70129,56373,56386,56360,56366,56419,
    );
    //kv接口扩展
    static private $optionsMap = array(
        'extTeacher'   => 'getExtTeacherInfo',
        'extAssistant' => 'getExtTeacherCourse',
        'extLesson'    => 'getExtLessonInfo',
    );

    //批量接口扩展
    static private $optionsMapArr = array(
        'extTeacher'   => 'getExtTeacherInfoBatch',
        'extAssistant' => 'getExtTeacherCourseBatch',
        'extLesson'    => 'getExtLessonInfoBatch',
    );
    const GIVE_COURSE_AWAY="Give_Course_Away_";
    const CONVENTIONSWITH = 1;//文明公告开关
    //
    //*********************************************kv接口集***************************************
    //

    /**
     * 获取课程详情
     *
     * @param  int   $courseId 打包课id或者非打包课id，不支持子课id
     * @param  array $options 扩展属性 array('extTeacher','extAssistant','extLesson')
     * @return array|false
     * @example
     * array(
     *   'courseId' => 3287,
     *   'courseName' => '秋季班-八年级物理同步1班 ',
     *   ...
     *   'extData' = array(
     *     'onlineTime' => '09月17日-11月05日',
     *     ...
     *   ),
     *   'extInfo' => array (
     *     'extTeacher' => array (
     *       2135043384 => array (
     *         'teacherUid' => 2135043384,
     *         'teacherName' => '李海涛',
     *         'teacherAvatar' => 'zyb_049fab5629f3e6189d3c0aab8d07bbeb',
     *         'teacherHalfavatar' => 'zyb_aba0737de848c5e084b586dad33a283f',
     *       ),
     *     ),
     *     'extAssistant' => array (
     *       'statinfo' => array (
     *         'studentLeftCnt' => 16,
     *         'studentRegisterCnt' => 784,
     *       ),
     *       'assistantList' => array(
     *          2135043383 => array(
     *              'assistantUid' => 2135043383,
     *              'assistantStudentCnt' => 92,
     *              'assistantLeftCnt' => 2,
     *              'assistantRegisterNum' => 102,
     *              ...
     *          ),
     *       ),
     *     ),
     *     'extLesson' => array (
     *       'lessonCnt' => 21,
     *       'curLesson' => array (
     *         'lessonId' => 5633,
     *         'lessonName' => '初二实验真人秀3',
     *         'startTime' => 1474075800,
     *         'stopTime' => 1474083000,
     *         'status' => 1,
     *       ),
     *       'allLesson' => array(
     *          5633 => array(
     *              'lessonId' => 5633,
     *              'lessonName' => '初二实验真人秀3',
     *              'startTime' => 1474075800,
     *              'stopTime' => 1474083000,
     *              'status' => 1,
     *          ),
     *          ...
     *       ),
     *       'subCourse' => array(
     *          0 => array(
     *              'label'  => '主体课',
     *              'weight' => 1,
     *              'lessonIdList' => array(
     *                  0 => 5633,
     *                  1 => 5634,
     *                  ...
     *              ),
     *          ),
     *          ...
     *       ),
     *     ),
     *   ),
     * )
     */
    /**
     * refactor by MaRongcai
     * @param $courseId
     * @param array $aOption
     * @param bool $isCache
     * @param bool $showOff   [是否显示下架信息]
     * @return array|bool|mixed
     */
    public function getCourseInfo($courseId, $aOption = array(), $isCache = false, $showOff = false)
    {
        $courseId       = intval($courseId);
        if ($courseId <= 0) {
            Bd_Log::warning('Error:[param error], Detail:[courseId:'.$courseId.']');
            return false;
        }

        $aCourse        = array();
        //添加商品id处理逻辑
        if($courseId > 1000000) {
            $oProduct   = new Hkzb_Ds_Fudao_Advanced_Product();
            $aCourse    = $oProduct->getProductInfo($courseId, $aOption, $isCache, $showOff);
            return $aCourse;
        }

        //读缓存
        $oMemcached         = $cacheKey = '';
        if ($isCache) {
            $oMemcached     = Hk_Service_Memcached::getInstance('zhiboke');
            $cacheKey       = 'zhiboke_hkzb_advanced_course_'.$courseId;
            $oResult        = $oMemcached->get($cacheKey);
            if (!empty($oResult)) {
                Hk_Util_Log::incrKey('zhiboke_hkzb_advanced_course_hit', 1);
                $aCourse    = json_decode(utf8_encode($oResult), true);
            }
            Hk_Util_Log::incrKey('zhiboke_hkzb_advanced_course__miss', 1);
        }

        if (empty($aCourse)) {
            //读数据库
            $oCourse        = new Hkzb_Ds_Fudao_Course();
            $aCourse        = $oCourse->getCourseInfo($courseId, array());
            if (false === $aCourse) {
                Bd_Log::warning("Error:[getRecordByConds], Detail:[courseId:$courseId]");

                return false;
            }

            if (empty($aCourse)) {
                return array();
            }

            //写缓存
            if ($isCache) {
                $oMemcached->set($cacheKey, json_encode($aCourse), 3600);
            }
        }

        //不支持子课
        if ($aCourse['pack'] == self::PACK_YESD) {
            Bd_Log::warning("Error:[subCourse invalid], Detail:[courseId:$courseId]");
            return false;
        }

        $aChildCourseList       = array(array('courseId' => $courseId, 'weight' => 1, 'label' => '主体课'));
        //打包课 避免重复课程id 出现
        if ($aCourse['pack'] == self::PACK_YES) {
            //获取所有子课
            $aCourseIdMap       = [];
            $aCoreCourseId      = isset($aCourse['extData']['coreCourseIds']) ? $aCourse['extData']['coreCourseIds'] : array();
            $aChildCourseList   = array();
            if (!empty($aCoreCourseId)) {
                foreach ($aCoreCourseId as $aItem) {
                    $coreCourseId   = intval($aItem['courseId']);
                    if(isset($aCourseIdMap[$coreCourseId])){
                        continue;
                    }
                    $aCourseIdMap[$coreCourseId] = 1;
                    $aTmpCourse     = array(
                        'courseId'  => $coreCourseId,
                        'weight'    => 1,
                        'label'     => '主体课',
                    );
                    $aChildCourseList[] = $aTmpCourse;
                }
            }

            $aOtherCourseId     = isset($aCourse['extData']['otherCourseIds']) ? $aCourse['extData']['otherCourseIds'] : array();
            if (!empty($aOtherCourseId) && is_array($aOtherCourseId)) {
                foreach ($aOtherCourseId as $aItem) {
                    $otherCourseId  = intval($aItem['courseId']);
                    if(isset($aCourseIdMap[$otherCourseId])){
                        continue;
                    }
                    $aCourseIdMap[$otherCourseId] = 1;

                    $aTmpCourse    = array(
                        'courseId' => $otherCourseId,
                        'weight'   => 2,
                        'label'    => '提升课',
                    );
                    $aChildCourseList[] = $aTmpCourse;
                }
            }
        }

        if (is_array($aOption) && !empty($aOption)) {
            //获取全部的信息，然后缓存；获取是按需返回
            foreach ($aOption as $option) {
                if (!isset(self::$optionsMap["$option"])) {
                    Bd_Log::warning("Error:[option invalid], Detail:[option:$option]");
                    continue;
                }

                $function       = self::$optionsMap["$option"];
                $aExtData       = $this->$function($courseId, $aChildCourseList, $isCache);
                if (false === $aExtData) {
                    Bd_Log::warning("Error:[$function error], Detail:[courseId:$courseId]");
                    return false;
                }

                $aCourse['extInfo']["$option"] = $aExtData;
            }
        }

        return $aCourse;
    }


    /**
     * 批量获取课程详情
     *
     * @param  array $aCourseId 打包课id或者非打包课id，不支持子课id
     * @param  array $aOption 扩展属性 eg:array('extTeacher','extAssistant','extLesson')
     * @param  bool $isCache 默认使用缓存
     * @return array
     * @example
     * array(
     *   3865 => array (
     *     'courseId' => 3865,
     *     'courseName' => '课程名称',
     *     ...
     *   ),
     *   ...
     * )
     */
    public function getCourseInfoArr($aCourseId, $aOption = array(), $isCache = false)
    {

        $aCourseId      = array_filter($aCourseId);
        if (empty($aCourseId)) {
            return array();
        }

        $aParameter     = $aCourseId;
        //区分商品id跟课程id 商品走张骁的接口 课程还是走老的接口 这里改成批量获取信息
        $aCourseId      = array();
        $aSkuId         = array();

        foreach ($aParameter as $courseId){
            $courseId   = intval($courseId);
            if($courseId < 1000000){
                $aCourseId[]    = $courseId;
            } else {
                $aSkuId[]       = $courseId;
            }
        }

        $aCourseList    = array();
        //批量获取课程id的信息
        /*
         * params $courseIdArr = array(
         *    '38890','37897',
         * )
         * 这里返回的数据格式
         *  $res = array(
         *           '35889' => array('courseId'=>,'extInfo'=>),
         *           '35889' => array(),
         *           'relationShip' => array(
         *                 '38890' => array(
         *                   'courseId' => 38991,
                             'weight' => 1,
                             'label' => '主体课'
         *                 ),
         *                   '39008'  => array(
         *                  'courseId' => 39901,
                            'weight' => 2,
                            'label' => '提升课'
         *                      )
         *            );
         *         );
         * */
        if(!empty($aCourseId)){
            $aCourseList    = $this->getCourseInfoArray($aCourseId, $aOption, $isCache);
        }

        //批量获取商品id的信息
        /*
         * 这里返回的数据格式
         *  $res = array(
         *           '2000089' => array('courseId'=>,'extInfo'=>),
         *           '2000099' => array(),
         *           'relationShip' => array(
         *                 '38890' => array(
         *                   'courseId' => 38991,
                             'weight' => 1,
                             'label' => '主体课'
         *                 ),
         *                   '39008'  => array(
         *                  'courseId' => 39901,
                            'weight' => 2,
                            'label' => '提升课'
         *                      )
         *            ),
         *           'skurelationShip' => array(
         *              '2000089' => array(38890,39008);
         *              '2000099' => array(38891,39012);
         *                ),
         *         );
         * */
        if(!empty($aSkuId)){
            $aSkuList       = $this->getSkuInfoArray($aSkuId, $aOption, $isCache);
        }
        //合并课程跟商品信息
        if(!empty($aSkuList)){
            foreach ($aSkuList as $skuId => $aSku){
                $aCourseList[$skuId] = $aSku;
            }
        }


        return $aCourseList;
    }

    //根据课程id获取课程基本信息
    private function getCourseInfoArray($aCourseId,$aOption = array(), $isCache = true)
    {
        $aCourseId      = array_filter($aCourseId);
        if (empty($aCourseId)) {
            return array();
        }

        $aNotCacheCourseId      = array();
        $aFormatCourseList      = array();
        $aCourseLists           = array();
        if ($isCache) {
            $oMemcached         = Hk_Service_Memcached::getInstance('zhiboke');
            foreach ($aCourseId as $courseId) {
                $cacheKey       = 'zhiboke_hkzb_advanced_course_'.$courseId;
                $oResult        = $oMemcached->get($cacheKey);
                if (!empty($oResult)) {
                    Hk_Util_Log::incrKey('zhiboke_hkzb_advanced_course_hit', 1);
                    $aCourse    = json_decode(utf8_encode($oResult), true);
                    $aCourseLists[$courseId]    = $aCourse;
                } else {
                    $aNotCacheCourseId[]        = $courseId;
                }
            }
            if (!empty($aNotCacheCourseId)) {
                $oCourse        = new Hkzb_Ds_Fudao_Course();

                $aCourseId      = array_unique($aNotCacheCourseId);
                //根据courseId 批量获取course信息
                $aCourseList    = $oCourse->getCourseListByCourseIdList($aCourseId,array());
                if (empty($aCourseList)) {
                    return array();
                }

                //保存到缓存
                foreach ($aCourseList as $aCourse) {
                    $cacheKey   = 'zhiboke_hkzb_advanced_course_'.$aCourse['courseId'];
                    $oMemcached->set($cacheKey,json_encode($aCourse),3600);
                }

                foreach ($aCourseList as $aCourse) {
                    $courseId   = intval($aCourse['courseId']);
                    $aCourseLists[$courseId]    = $aCourse;
                }
            }
        } else {
            $oCourse            = new Hkzb_Ds_Fudao_Course();
            $aCourseId          = array_unique($aCourseId);
            $aCourseList        = $oCourse->getCourseListByCourseIdList($aCourseId,array());
            if (empty($aCourseList)) {
                return array();
            }

            foreach ($aCourseList as $aCourse) {
                $courseId       = intval($aCourse['courseId']);
                $aCourseLists[$courseId]   = $aCourse;
            }
        }

        //根据打包课id获取子课id关系
        if (!empty($aOption) && is_array($aOption)) {
            $aCourseLists           = $this->formatCourseExtData($aCourseLists,$aOption);
        }

        foreach ($aCourseLists as $aCourse) {
            $courseId           = intval($aCourse['courseId']);
            $aFormatCourseList[$courseId]   = $aCourse;
        }

        return $aFormatCourseList;
    }

    /**
     * 格式化处理course的扩展信息；包括extTeacher、extLesson、extAssiatant
     * @param $aCourseList
     * @param $aOption
     * @return array
     */
    private function formatCourseExtData($aCourseList,$aOption)
    {
        if (empty($aCourseList)) {
            return array();
        }

        if (empty($aOption) || !is_array($aOption)) {
            return array();
        }

        //格式化课程信息关系
        $aRelationList          = $this->getFormatCourseInfo($aCourseList);
        //获取拓展信息
        $aOptionExtData         = array();
        foreach ($aOption as $option) {
            if (!isset(self::$optionsMapArr["$option"])) {
                Bd_Log::warning("Error:[optionArr invalid], Detail:[option:$option]");
                continue;
            }

            $function           = self::$optionsMapArr["$option"];
            $aExtData           = $this->$function($aRelationList, false);
            if (false === $aExtData) {
                Bd_Log::warning("Error:[$function error], Detail:[courseList:" . json_encode($aRelationList) . "]");
                continue;
            }

            $aOptionExtData["$option"]  = $aExtData;
        }

        $aFormatCourseList      = array();
        //按照打包课维度拼接数据返回
        foreach ($aCourseList as $aCourse) {
            $courseId       = intval($aCourse['courseId']);
            $aExtTeacher    = isset($aOptionExtData['extTeacher']) ? $aOptionExtData['extTeacher'] : array();
            $aExtAssistant  = isset($aOptionExtData['extAssistant']) ? $aOptionExtData['extAssistant'] : array();
            $aExtLesson     = isset($aOptionExtData['extLesson']) ? $aOptionExtData['extLesson'] : array();
            $aTmpTeacher    = isset($aExtTeacher[$courseId]) ? $aExtTeacher[$courseId] : array();
            $aTmpAssistant  = isset($aExtAssistant[$courseId]) ? $aExtAssistant[$courseId] : array();
            $aTmpLesson     = isset($aExtLesson[$courseId]) ? $aExtLesson[$courseId] : array();

            if ($aTmpTeacher) {
                $aCourse['extInfo']['extTeacher']   = $aTmpTeacher;
            }

            if ($aTmpAssistant) {
                $aCourse['extInfo']['extAssistant'] = $aTmpAssistant;
            }

            if ($aTmpLesson) {
                $aCourse['extInfo']['extLesson']    = $aTmpLesson;
            }

            $aFormatCourseList[$courseId]   = $aCourse;
        }

        return $aFormatCourseList;
    }
    //根据商品id获取课程基本信息
    private function getSkuInfoArray($skuIdList, $options = array('extTeacher', 'extAssistant', 'extLesson'), $cacheSwith = false)
    {
        if (empty($skuIdList)) {
            return array();
        }

        $arrOutput       = array();
        $skuRelationShip = array();//sku 与 courseId映射关系
        $packCourseIds   = array();//courseId列表
        //临时兼容骁哥那边的分批阶段的逻辑
        $offset              = 20;
        $count               = count($skuIdList);
        $page                = ceil($count / $offset);
        $zbAdvancedInterface = new Zb_Advanced_Interface();
        for ($i = 0; $i < $page; $i++) {
            $start          = $i * $offset;
            $limitSkuIdList = array_slice($skuIdList, $start, $offset);
            if (!empty($limitSkuIdList)) {
                $productDetailList = $zbAdvancedInterface->getAdapterSkuInfoBatch($limitSkuIdList, true);
                //商品信息格式化成课程形式
                if (!empty($productDetailList)) {
                    foreach ($productDetailList as $skuId => $productDetailInfo) {
                        if (empty($productDetailInfo) || $productDetailInfo['status'] != Hkzb_Ds_Fudao_Advanced_Course::STATUS_ONLINE) {
                            continue;
                        }
                        //按照商品维度聚合下面的课程id
                        $skuRelationShip[$skuId] = $productDetailInfo['courseIdList'];
                        $packCourseIds           = array_merge($productDetailInfo['courseIdList'], $packCourseIds);  //获取所有的商品的打包课程
                        $arrOutput[$skuId]       = $productDetailInfo;
                    }
                }
            }
        }

        /* 打包课id
         * $courseIdList = array(35665,36786,38990);
        */
        $objDsCourse = new Hkzb_Ds_Fudao_Course();
        //批量获取课程信息
        $courseInfoArr = $objDsCourse->getCourseInfoArray($packCourseIds);
        //根据打包课id获取子课id关系
        if (empty($courseInfoArr)) {
            return array();
        }
        //获取课程id与课程信息的映射关系
        $tmpCid2Cinfo = array();
        foreach ($courseInfoArr as $tmpCinfo) {
            $tmpCid2Cinfo[$tmpCinfo['courseId']] = $tmpCinfo;
        }
        //这里处理商品信息同步成课程信息一致
        foreach ($skuRelationShip as $skuId => $courseIdList) {
            $courseId          = $courseIdList[0];
            $courseInfo        = $tmpCid2Cinfo[$courseId];
            $teacherUid        = $courseInfo['teacherUid'];
            $teacherName       = $courseInfo['teacherName'];
            $grade             = $courseInfo['grade'];
            $subject           = $courseInfo['subject'];
            $pack              = $courseInfo['pack'];
            $tags              = $courseInfo['tags'];
            $moreGrade         = $courseInfo['moreGrade'];
            $degree            = $courseInfo['degree'];
            $currentLessonTime = $courseInfo['currentLessonTime'];

            //获取对应的商品信息
            $productInfo               = $arrOutput[$skuId];
            $arrOutput[$skuId]['pack'] = $pack;
            $arrOutput[$skuId]['type'] = $productInfo['productType'];
            if ($productInfo['skuType'] == Hkzb_Ds_Fudao_Product::SKU_TYPE_COMPOSITE_PRODUCT) {
                $arrOutput[$skuId]['courseId']   = $skuId;
                $arrOutput[$skuId]['courseName'] = $productInfo['productName'];
                $arrOutput[$skuId]['grade']      = $productInfo['grade'][0];
                $arrOutput[$skuId]['subject']    = $productInfo['subject'][0];
                if (!isset($productInfo['tags'])) {
                    $arrOutput[$skuId]['tags'] = $tags;
                }
                if (!isset($productInfo['moreGrade'])) {
                    $arrOutput[$skuId]['moreGrade'] = $moreGrade;
                }
                foreach ($courseIdList as $cid) {
                    $cInfo             = $tmpCid2Cinfo[$cid];
                    $currentLessonTime = $cInfo['currentLessonTime'];
                    if (!isset($productInfo['currentLessonTime']) || $currentLessonTime < $productInfo['currentLessonTime']) {
                        $arrOutput[$skuId]['currentLessonTime'] = $currentLessonTime;
                    }
                }
            } else {
                $arrOutput[$skuId]['courseId']          = $courseId;
                $arrOutput[$skuId]['courseName']        = $arrOutput[$skuId]['productName'];
                $arrOutput[$skuId]['teacherUid']        = $teacherUid;
                $arrOutput[$skuId]['teacherName']       = $teacherName;
                $arrOutput[$skuId]['grade']             = $grade;
                $arrOutput[$skuId]['subject']           = $subject;
                $arrOutput[$skuId]['pack']              = $pack;
                $arrOutput[$skuId]['tags']              = $tags;
                $arrOutput[$skuId]['moreGrade']         = $moreGrade;
                $arrOutput[$skuId]['degree']            = $degree;
                $arrOutput[$skuId]['currentLessonTime'] = $currentLessonTime;
            }
        }
        //格式化课程信息关系
        $relationShip = $this->getFormatCourseInfo($courseInfoArr);

        //获取拓展信息
        $options = array('extTeacher', 'extAssistant', 'extLesson');
        if (!empty($options)) {
            foreach ($options as $option) {
                if (!isset(self::$optionsMapArr["$option"])) {
                    Bd_Log::warning("Error:[optionArr invalid], Detail:[option:$option]");
                    continue;
                }

                $function = self::$optionsMapArr["$option"];
                $info     = $this->$function($relationShip, $cacheSwith);
                if (false === $info) {
                    Bd_Log::warning("Error:[$function error], Detail:[courseId:$relationShip]");
                    continue;
                }

                //按照打包课维度拼接数据返回
                foreach ($arrOutput as $skuId => $skuInfo) {
                    $courseIdList = $skuRelationShip[$skuId];
                    if (empty($courseIdList) || !isset($skuInfo['pack'])) {
                        //置空商品信息
                        $arrOutput[$skuId] = array();
                        continue;
                    }
                    //区分组合商品跟非组合商品
                    if ($skuInfo['skuType'] == Hkzb_Ds_Fudao_Product::SKU_TYPE_COMPOSITE_PRODUCT) {
                        foreach ($courseIdList as $courseId) {
                            $extInfo                                          = $info[$courseId];
                            $arrOutput[$skuId]['extInfo'][$courseId]["$option"] = $extInfo;
                        }
                    } else {
                        $courseId                              = $courseIdList[0];
                        $extInfo                               = $info[$courseId];
                        $arrOutput[$skuId]['extInfo']["$option"] = $extInfo;
                    }
                }
            }
        }

        return $arrOutput;
    }

    //格式化课程信息维护课程关系
    /*
     * 返回格式
     * $res = array(
     *    '38890' => array(
     *              'courseId' => 38991,
                    'weight' => 1,
                    'label' => '主体课'
     *                 ),
     *    '39008'  => array(
     *              'courseId' => 39901,
                    'weight' => 2,
                    'label' => '提升课'
     *                      )
     * )
     * */
    private function getFormatCourseInfo($courseInfoArr){

        if(empty($courseInfoArr)){
            return array();
        }
        foreach ($courseInfoArr as $courseInfo){
            if(empty($courseInfo)) {
                //为空的话直接跳出
                continue;
            }
            //提取课程信息里面需要的信息
            $courseId       = $courseInfo['courseId'];    //打包课id
            $coreCourseIds  = $courseInfo['extData']['coreCourseIds'];
            $otherCourseIds = $courseInfo['extData']['otherCourseIds'];

            //不支持子课
            if ($courseInfo['pack'] == self::PACK_YESD) {
                Bd_Log::warning("Error:[subCourse invalid], Detail:[courseId:$courseId]");
                //子课直接跳出
                continue;
            }

            //按照打包课的课程维度聚合所有的子课 并且赋予权重 没有打包课本身就是子课属于主体课
            $subCourseIdShip[$courseId] = array(
                array(
                    'courseId' => $courseId,
                    'weight' => 1,
                    'label' => '主体课'
                )); //所有子课

            //打包课 避免重复课程id 出现
            if ($courseInfo['pack'] == self::PACK_YES) {
                //获取所有子课
                $subCourseIdShip[$courseId]  = [];
                $issetSubIds                 = [];

                //核心子课
                foreach ($coreCourseIds as $coreCourseId) {
                    if(isset($issetSubIds[intval($coreCourseId['courseId'])])){
                        continue;
                    } else {
                        $issetSubIds[intval($coreCourseId['courseId'])] = 1;
                    }
                    $subCourseIdInfo = array(
                        'courseId' => intval($coreCourseId['courseId']),
                        'weight'   => 1,
                        'label'    => '主体课',
                    );

                    $subCourseIdShip[$courseId][$coreCourseId['courseId']] = $subCourseIdInfo;
                }

                //非核心子课
                foreach ($otherCourseIds as $otherCourseId) {
                    if(isset($issetSubIds[intval($otherCourseId['courseId'])])){
                        continue;
                    } else {
                        $issetSubIds[intval($otherCourseId['courseId'])] = 1;
                    }
                    $subCourseIdInfo    = array(
                        'courseId' => intval($otherCourseId['courseId']),
                        'weight'   => 2,
                        'label'    => '提升课',
                    );
                    $subCourseIdShip[$courseId][$otherCourseId['courseId']] = $subCourseIdInfo;
                }
            }
        }
        return $subCourseIdShip;
    }

    //获取主讲老师信息
    private function getExtTeacherInfo($topCourseId, $aChildCourseList, $isCache)
    {
        $oCourse        = new Hkzb_Ds_Fudao_Course();
        $oTeacher       = new Hkzb_Ds_Fudao_Teacher();


        //获取全部的courseId
        $aCourseId      = array();
        foreach ($aChildCourseList as $aCourse) {
            $courseId       = intval($aCourse['courseId']);
            if (empty($courseId)) {
                continue;
            }
            $aCourseId[]    = $courseId;
            $aChildCourseList[$courseId]    = $aCourse;
        }

        $aCourseId      = array_unique($aCourseId);

        //根据courseId 批量获取course信息
        $aCourseList    = $oCourse->getCourseListByCourseIdList($aCourseId,array('courseId','teacherUid','extData'),$isCache);
        if (empty($aCourseList)) {
            return array();
        }

        //找出所有的主讲ID
        $aTeacherId     = array();
        foreach ($aCourseList as $key => $aCourse) {
            $courseId       = intval($aCourse['courseId']);
            $teacherId      = intval($aCourse['teacherUid']);
            $aTeacherId[]   = $teacherId;
            $aCourseList[$key]['weight']    = $aChildCourseList[$courseId]['weight'];
        }
        unset($aChildCourseList);

        if (empty($aTeacherId)) {
            return array();
        }

        //根据主讲批量获取主讲信息
        $aTeacherList       = $oTeacher->getTeacherInfoArr($aTeacherId,array('teacherName','teacherAvatar','extData'),$isCache);
        $aFormatTeacherList = array();
        foreach ($aCourseList as $aCourse) {
            $teacherId      = intval($aCourse['teacherUid']);
            $aTeacher       = $aTeacherList[$teacherId];
            $aFormatTeacherList[$teacherId] = array(
                'teacherUid'        => $teacherId,
                'teacherName'       => $aTeacher['teacherName'],
                'teacherAvatar'     => $aTeacher['teacherAvatar'],
                'teacherHalfavatar' => strval($aTeacher['extData']['teacherHalfavatar']),
                'weight'            => $aCourse['weight'],
                'groupId'           => intval($aCourse['extData']['groupId']),
            );
        }
        unset($aTeacherList);

        return $aFormatTeacherList;
    }

    /*批量获取主讲老师的信息
     *$subCourseIdsArr = array( ('packecourseId'=> array('corecourseId1','corecourseId2')));
    */
    private function getExtTeacherInfoBatch($subCourseIdList, $cacheSwith)
    {
        $arrOutput      = array();
        $objCourse      = new Hkzb_Ds_Fudao_Course();
        $objTeacher     = new Hkzb_Ds_Fudao_Teacher();
        $subIdList      = array();
        if(empty($subCourseIdList)){
            return array();
        }
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo){
            foreach ($subCourseIdInfo as $subCourseId => $subInfo){
                $subIdList[] = $subInfo['courseId'];
            }
        }
        //获取所有的子课的对应老师uid信息
        if(empty($subIdList)){
            return array();
        }

        $courseInfoList =  $objCourse->getCourseListByCourseIdList($subIdList, array('courseId','teacherUid', 'extData'),$cacheSwith);

        if(empty($courseInfoList)){
            return array();
        }
        foreach ($courseInfoList as $courseInfo){
            $cId                   = $courseInfo['courseId'];  //子课id
            $teacherUidArr[]       = $courseInfo['teacherUid'];
            $courseIdTeacher[$cId] = $courseInfo;
        }

        //获取所有的子课老师的信息 应该是按照teacherUid为数组下标格式组织的
        $teacherInfoList = $objTeacher->getTeacherInfoArr($teacherUidArr, array(), $cacheSwith);
        if(empty($teacherInfoList)){
            return array();
        }

        //按照打包课的维度重新组织对应的子课老师的信息
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo){
            foreach ($subCourseIdInfo as $subCourseId => $subInfo){
                $cId = $subInfo['courseId'];
                if(empty($courseIdTeacher[$cId]['teacherUid'])){
                    continue;
                }
                $teacherUid         = intval($courseIdTeacher[$cId]['teacherUid']);
                $groupId            = $courseIdTeacher[$cId]['extData']['groupId'];
                $teacherHalfavatar  = $teacherInfoList[$teacherUid]['extData']['teacherHalfavatar'];
                $teacherName        = $teacherInfoList[$teacherUid]['teacherName'];
                $teacherAvatar      = $teacherInfoList[$teacherUid]['teacherAvatar'];
                $weight             = isset($subInfo['weight']) ? $subInfo['weight'] : 2;

                $arrOutput[$courseId][$teacherUid] = array(
                    'teacherUid'        => intval($teacherUid),
                    'teacherName'       => $teacherName,
                    'teacherAvatar'     => $teacherAvatar,
                    'teacherHalfavatar' => strval($teacherHalfavatar),
                    'weight'            => $weight,
                    'groupId'           => intval($groupId ),
                );
            }
        }

        return $arrOutput;
    }
    //获取辅导老师信息
    private function getExtTeacherCourse($courseId, $subCourseIds, $cacheSwith)
    {
        $coreSubCourseId  = intval($subCourseIds[0]['courseId']);
        $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $teacherCourseCnt = $objTeacherCourse->getTeacherCnt($coreSubCourseId);
        if (false === $teacherCourseCnt) {
            Bd_Log::warning("Error:[getTeacherCnt error], Detail:[courseId:$coreSubCourseId]");

            return false;
        }

        $teacherCourseList = $objTeacherCourse->getTeacherList($coreSubCourseId, array(), 0, $teacherCourseCnt);
        if (false === $teacherCourseList) {
            Bd_Log::warning("Error:[getTeacherList error], Detail:[courseId:$coreSubCourseId]");

            return false;
        }

        $sumStudentLeftCnt     = 0; //剩余名额
        $sumStudentRegisterCnt = 0;
        $sumStudentMaxCnt      = 0;
        foreach ($teacherCourseList as $teacherCourse) {
            if ($teacherCourse['duty'] !== Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT) {
                continue;
            }
            $studentCnt            = intval($teacherCourse['studentCnt']);//实际报名人数
            $studentMaxCnt         = intval($teacherCourse['extData']['studentMaxCnt']); //最大报名人数
            $sumStudentMaxCnt      += $studentMaxCnt;
            $sumStudentLeftCnt     += $studentMaxCnt - $studentCnt;
            $sumStudentRegisterCnt += $studentCnt;
        }

        //辅导老师信息
        $objTeacher    = new Hkzb_Ds_Fudao_Teacher();
        $assistantList = array();
        foreach ($teacherCourseList as $teacherCourse) {
            if ($teacherCourse['duty'] !== Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT) {
                continue;
            }

            $studentCnt         = intval($teacherCourse['studentCnt']);//实际报名人数
            $studentMaxCnt      = intval($teacherCourse['extData']['studentMaxCnt']); //最大报名人数
            $studentLeftCnt     = $studentMaxCnt - $studentCnt;
            $studentRegisterCnt = $studentCnt;

            $teacherUid = intval($teacherCourse['teacherUid']);
            if (isset(self::$teacherInfo[$teacherUid])) {
                $teacherInfo = self::$teacherInfo[$teacherUid];
            } else {
                $teacherInfo = $objTeacher->getTeacherInfo($teacherUid, array(), $cacheSwith);
                if ($teacherInfo === false) {
                    Bd_Log::warning("Error:[getTeacherInfo error], Detail:[teacherUid:$teacherUid]");

                    return false;
                }
                self::$teacherInfo[$teacherUid] = $teacherInfo;
            }

            $assistantList[$teacherUid][] = array(
                'assistantUid'         => $teacherUid,
                'assistantName'        => $teacherInfo['teacherName'],
                'assistantStudentCnt'  => $studentCnt,
                'assistantLeftCnt'     => $studentLeftCnt,
                'assistantRegisterNum' => $studentRegisterCnt,
            );
        }

        $arrOutput = array(
            'statinfo'      => array(
                'studentLeftCnt'     => $sumStudentLeftCnt,
                'studentRegisterCnt' => $sumStudentRegisterCnt,
                'studentMaxCnt'      => $sumStudentMaxCnt,
            ),
            'assistantList' => $assistantList,
        );

        return $arrOutput;
    }

    //批量获取辅导老师信息
    private function getExtTeacherCourseBatch($subCourseIdList, $cacheSwith)
    {
        $arrOutput         = array();
        $objTeacherCourse  = new Hkzb_Ds_Fudao_TeacherCourse();
        $objTeacher        = new Hkzb_Ds_Fudao_Teacher();
        if(empty($subCourseIdList)){
            return array();
        }
        //这里只需要获取第一门核心课
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo){
            $firstSubCourseIds[] = array_shift($subCourseIdInfo)['courseId']; //维持之前的逻辑获取第一门课
        }
        if(empty($firstSubCourseIds)){
            return $arrOutput;
        }
        $teacherCourseList = $objTeacherCourse->getTeacherListArr($firstSubCourseIds);
        if (false === $teacherCourseList) {
            $strCoreSubcourseIds = json_encode($firstSubCourseIds);
            Bd_Log::warning("Error:[getTeacherList error], Detail:[firstSubcourseIdList:$strCoreSubcourseIds]");
            return false;
        }

        $sumStudentLeftCnt     = array(); //剩余名额
        $sumStudentRegisterCnt = array();
        $sumStudentMaxCnt      = array();
        foreach ($teacherCourseList as $teacherCourse) {
            if ($teacherCourse['duty'] !== Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT) {
                continue;
            }
            //聚合所有的辅导的老师并且去重
            $assistantUid   = $teacherCourse['teacherUid'];
            if(empty($assistantArrUid) || !in_array($assistantUid, $assistantArrUid) ){
                $assistantArrUid[]        = $assistantUid;
            }
            //按照核心子课维度收集班主任信息
            $cId                          = $teacherCourse['courseId'];
            $courseIdAssistantInfo[$cId]  = $teacherCourse;
            $studentCnt                   = intval($teacherCourse['studentCnt']);               //实际报名人数
            $studentMaxCnt                = intval($teacherCourse['extData']['studentMaxCnt']); //最大报名人数
            $sumStudentMaxCnt[$cId ]      += $studentMaxCnt;
            $sumStudentLeftCnt[$cId ]     += $studentMaxCnt - $studentCnt;
            $sumStudentRegisterCnt[$cId ] += $studentCnt;
        }

        //批量获取辅导老师信息
        if(empty($assistantArrUid)){
            return $arrOutput;
        }
        $assistantList = $objTeacher->getTeacherInfoArr($assistantArrUid, array(), $cacheSwith);

        if(empty($assistantList)){
            return $arrOutput;
        }

        //按照子课维度收敛班主任信息
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo){
            //获取第一门子课
            $firstCId      = array_shift($subCourseIdInfo)['courseId'];
            $assistantInfo = $courseIdAssistantInfo[$firstCId];
            $assistantItem = array();
            if(empty($assistantInfo)){
                continue;
            }
            $assistantUid       = $assistantInfo['teacherUid'];
            $studentCnt         = intval($assistantInfo['studentCnt']);               //实际报名人数
            $studentMaxCnt      = intval($assistantInfo['extData']['studentMaxCnt']); //最大报名人数
            $studentLeftCnt     = $studentMaxCnt - $studentCnt;
            $studentRegisterCnt = $studentCnt;
            $teacherName        = strval($assistantList[$assistantUid]['teacherName']);

            $assistantItem[$assistantUid][]  = array(
                'assistantUid'         => $assistantUid,
                'assistantName'        => $teacherName,
                'assistantStudentCnt'  => $studentCnt,
                'assistantLeftCnt'     => $studentLeftCnt,
                'assistantRegisterNum' => $studentRegisterCnt,
            );
            $arrOutput[$courseId]['statinfo'] = array(
                'studentLeftCnt'       => $sumStudentLeftCnt[$firstCId] ? $sumStudentLeftCnt[$firstCId] : 0,
                'studentRegisterCnt'   => $sumStudentRegisterCnt[$firstCId] ? $sumStudentRegisterCnt[$firstCId] : 0,
                'studentMaxCnt'        => $sumStudentMaxCnt[$firstCId] ? $sumStudentMaxCnt[$firstCId] : 0,
            );
            $arrOutput[$courseId]['assistantList'] = $assistantItem;
        }

        return $arrOutput;
    }

    //获取章节信息
    private function getExtLessonInfo($courseId, $aChildCourseList, $isCache)
    {
        $topCourseId    = $courseId;
        $lessonCnt      = 0;
        $aLessonList    = array();
        $aSubCourse     = array();
        $aCurrentLesson = array();
        $aRank          = array();
        $aFormatLessonList  = array();
        $oCourse        = new Hkzb_Ds_Fudao_Course();
        //声明返回值
        $aResult    = array(
            'lessonCnt'     => $lessonCnt,
            'curLesson'     => $aCurrentLesson,
            'allLesson'     => $aFormatLessonList,
            'subCourse'     => $aSubCourse
        );

        //获取全部的courseId
        $aCourseId      = array();
        foreach ($aChildCourseList as $aCourse) {
            $courseId       = intval($aCourse['courseId']);
            $aCourseId[]    = $courseId;
            $aChildCourseList[$courseId]    = $aCourse;
        }

        $aCourseList    = $oCourse->getSubCourseInfoArray($aCourseId,$isCache);
        if ($aCourseList === false) {
            Bd_Log::warning('getSubCourseInfoArray return error. [courseId]' . var_export($aCourseId, true));
            return $aResult;
        }

        foreach ($aCourseList as $aCourse) {

            $courseId   = intval($aCourse['courseId']);
            $aItem      = $aChildCourseList[$courseId];

            //计算章节数
            $lessonCnt += $aCourse['lessonCnt'];

            //计算所有章节
            $aLessonId          = array();
            $aCourseLesson      = $aCourse['allLesson'];
            if(!empty($aCourseLesson)){
                foreach ($aCourseLesson as $aLesson) {
                    $lessonId               = $aLesson['lessonId'];
                    $aLessonId[]            = $lessonId;
                    $aLessonList[$lessonId] = $aLesson;
                    $aRank[]                = $aLesson['startTime'];
                }
            }
            $aSubCourse[]       = array(
                'label'         => $aItem['label'],
                'weight'        => $aItem['weight'],
                'teacherUid'    => $aCourse['courseInfo']['teacherUid'],
                'startTime'     => $aCourse['courseInfo']['onlineStart'],
                'stopTime'      => $aCourse['courseInfo']['onlineStop'],
                'lessonIdList'  => $aLessonId,
            );

            //计算即将要上的章节
            $aTmpLesson         = $aCourse['curLesson'];
            if (!empty($aTmpLesson)) {
                if (empty($aCurrentLesson) || $aCurrentLesson['startTime'] > $aTmpLesson['startTime']) {
                    $aCurrentLesson     = $aTmpLesson;
                }
            }
        }

        //章节按时间排序
        array_multisort($aRank, SORT_ASC, $aLessonList);
        foreach ($aLessonList as $aLesson) {
            $lessonId                       = intval($aLesson['lessonId']);
            $aFormatLessonList[$lessonId]   = $aLesson;
        }

        $aResult    = array(
            'lessonCnt'     => $lessonCnt,
            'curLesson'     => $aCurrentLesson,
            'allLesson'     => $aFormatLessonList,
            'subCourse'     => $aSubCourse
        );

        return $aResult;
    }

    /*批量获取章节信息
    */
    private function getExtLessonInfoBatch($subCourseIdList, $cacheSwith)
    {
        $arrOutput     = array();
        $subIdList     = array();
        $objCourse     = new Hkzb_Ds_Fudao_Course();
        if(empty($subCourseIdList)){
            return array();
        }
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo) {
            foreach ($subCourseIdInfo as $subCourseId => $subInfo){
                $subIdList[] = $subInfo['courseId'];
            }
        }
        if(empty($subIdList)){
            return $arrOutput;
        }

        //获取子课章节信息
        $subCourseLessonInfoArr = $objCourse->getSubCourseInfoArray($subIdList, $cacheSwith);

        if(empty($subCourseLessonInfoArr)){
            return $arrOutput;
        }

        //按照子课维度聚合章节信息
        foreach ($subCourseIdList as $courseId => $subCourseIdInfo) {
            $lessonCnt     = 0;
            $allLesson     = array();
            $arrRank       = array();
            $allSortLesson = array();
            $subCourse     = array();
            $curLessonInfo = array();
            foreach ($subCourseIdInfo as $subCourseId => $subInfo){
                $cId                 = $subInfo['courseId'];
                $subCourseLessonInfo = $subCourseLessonInfoArr[$cId];
                if(empty($subCourseLessonInfo)){
                    continue;
                }
                //计算章节数
                $lessonCnt   += $subCourseLessonInfo['lessonCnt'];

                //计算所有章节
                $subAllLesson = $subCourseLessonInfo['allLesson'];
                $subLessonIds = array();
                if(!empty($subAllLesson)){
                    foreach ($subAllLesson as $subLesson) {
                        $lessonId             = $subLesson['lessonId'];
                        $subLessonIds[]       = $lessonId;
                        $allLesson[$lessonId] = $subLesson;
                        $arrRank[]            = $subLesson['startTime'];
                    }
                }
                $subCourse[] = array(
                    'label'        => $subInfo['label'],
                    'weight'       => $subInfo['weight'],
                    'teacherUid'   => $subCourseLessonInfo['courseInfo']['teacherUid'],
                    'startTime'    => $subCourseLessonInfo['courseInfo']['onlineStart'],
                    'stopTime'     => $subCourseLessonInfo['courseInfo']['onlineStop'],
                    'lessonIdList' => $subLessonIds,
                );

                //计算即将要上的章节
                $curLesson = $subCourseLessonInfo['curLesson'];
                if (!empty($curLesson)) {
                    if (empty($curLessonInfo) || $curLessonInfo['startTime'] > $curLesson['startTime']) {
                        $curLessonInfo = $curLesson;
                    }
                }
            }

            //章节按时间排序
            array_multisort($arrRank, SORT_ASC, $allLesson);;
            foreach ($allLesson as $lesson) {
                $lessonId                 = intval($lesson['lessonId']);
                $allSortLesson[$lessonId] = $lesson;
            }

            $arrOutput[$courseId] = array(
                'lessonCnt' => $lessonCnt,
                'curLesson' => $curLessonInfo,
                'allLesson' => $allSortLesson,
                'subCourse' => $subCourse,
            );
        }

        return $arrOutput;
    }

    //
    //*********************************************list接口集***************************************
    //

    /**
     * 按年级学科获取专题课在售课程列表(可指定排序规则)
     *
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @param  int $orderType 排序规则类型 eg:Hkzb_Ds_Fudao_Advanced_Course::ORDER_TYPE_START_TIME
     * @param  int $order 排序顺序 eg:Hkzb_Ds_Fudao_Advanced_Course::ORDER_DESC
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getZCourseListWithOrder($grade, $subject, $orderType = 0, $order = 0, $offset = 0, $limit = 0, $seasonIds = [])
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        //设置缓存key
        $seasonParams = json_encode($seasonIds);
        $strParam     = "$grade $subject $orderType $order $offset $limit $isInner $seasonParams";
        $cacheKey     = 'zhiboke_ds_advanced_courselist_getZCourseListWithOrder_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }

        //基本条件，预备班暂时和专题课放一起
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            //'type' => self::TYPE_PRIVATE,
            'type in (' . self::TYPE_PRIVATE . ',' . self::TYPE_PRE_LONG . ')',
            'isShow' => 1,
            'pack in (0,1)',
        );

        //指定年级
        $oriGrade = $grade;
        $grade    = isset(Hk_Util_Category::$GRADEMAP[$grade]) ? Hk_Util_Category::$GRADEMAP[$grade] : [];
        if (!empty($grade)) {
            $grade = implode(',', $grade);
            if (Hkzb_Util_FuDao::$MORE_GRADE === 1) {
                //多年级筛选
                //$gradeName = Hk_Util_Category::$GRADE[$oriGrade];
                //$gradeName = $this->formatTagName($gradeName);
                $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$oriGrade];
                $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
            } else {
                $arrConds[] = "grade in ($grade)";
            }
        }

        //指定学科
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }
        //指定学期
        if (!empty($seasonIds)) {
            //获取学期检索条件
            $seasonConf = Hk_Util_Category::$SEASONCONDS;

            $seasonList = [];
            foreach ($seasonIds as $seasonId) {
                $seasonList[] = $seasonConf[$seasonId];
            }
            $seasonConds = join(',', $seasonList);
            $arrConds[]  = 'learn_season in (' . $seasonConds . ')';
        }

        //报名开始时间
        $arrConds['registerStartTime'] = array(time(), '<');

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //排序规则
        if (!isset(self::$orderTypeMap[$orderType]) || !isset(self::$orderMap[$order])) {
            Bd_Log::warning("Error:[param error], Detail:[orderType:$orderType]");

            return false;
        }

        $orderType    = self::$orderTypeMap[$orderType];
        $order        = self::$orderMap[$order];
        $arrAppends[] = "order by $orderType $order";

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);

        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[grade:$grade subject:$subject]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }

    /**
     * 按标签获取专题课在售课程列表(可指定排序规则)
     *
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @param  int $orderType 排序规则类型 eg:Hkzb_Ds_Fudao_Advanced_Course::ORDER_TYPE_START_TIME
     * @param  int $order 排序顺序 eg:Hkzb_Ds_Fudao_Advanced_Course::ORDER_DESC
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getTagCourseListWithOrder($grade, $subject, $tagName, $orderType = 1, $order = 1, $offset = 0, $limit = 0)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
        $tagConf = array(
            '长期班',
            '专题课',
            '周末课程',
        );
        if (!in_array($tagName, $tagConf)) {
            return false;
        }
        //设置缓存key
        $strParam = "$grade $subject $tagName $orderType $order $offset $limit $isInner";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getTagCourseListWithOrder_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }

        //基本条件
        if ($tagName == '长期班') {
            /*$arrConds                                        = array(
                'status' => self::STATUS_ONLINE,
                'type'   => self::TYPE_PRIVATE_LONG,
                'isShow' => 1,
            );
            self::$orderTypeMap[self::ORDER_TYPE_START_TIME] = 'online_start';*/

            //长期班走商品课程列表接口 从产品功能上不支持筛选功能田思确认
            $zbAdvancedInterface = new Zb_Core_Ds_Dak_Interface();
            $courseSkuIdInfo     = $zbAdvancedInterface->getBSubjectSkuIdList($grade, $subject, $offset, $limit, true, $isInner);
            if (false === $courseSkuIdInfo) {
                Bd_Log::warning("Error:[courseSkuIdList], Detail:[grade:$grade subject:$subject]");

                return false;
            }

            $courseList          = array();
            foreach ($courseSkuIdInfo as $k => $courseIdList) {
                if($k == 'skuIdList' && !empty($courseIdList)){
                    $courseList  = $courseIdList;
                }
            }

            //写缓存
            $cacheValue = json_encode($courseList);
            $objMemcached->set($cacheKey, $cacheValue, 60);

            return $courseList;
        } else {
            $arrConds = array(
                'status' => self::STATUS_ONLINE,
                //'type' => self::TYPE_PRIVATE,
                'type in (' . self::TYPE_PRIVATE . ',' . self::TYPE_PRE_LONG . ')',
                'isShow' => 1,
            );
        }
        if ($tagName != '长期班' && $tagName != '专题课') {

            $tagName    = $this->formatTagName($tagName);
            $arrConds[] = "tags like '%{$tagName}%'";

        }
        $arrConds[] = 'pack in (0,1)';
        //指定年级
        $oriGrade = $grade;
        $grade    = Hk_Util_Category::$GRADEMAP[$grade];
        if (!empty($grade)) {
            $grade = implode(',', $grade);
            if (Hkzb_Util_FuDao::$MORE_GRADE === 1) {
                //多年级筛选
                //$gradeName = Hk_Util_Category::$GRADE[$oriGrade];
                //$gradeName = $this->formatTagName($gradeName);
                $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$oriGrade];
                $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
            } else {
                $arrConds[] = "grade in ($grade)";
            }
        }

        //指定学科
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }

        //报名开始时间
        //$arrConds['registerStartTime'] = array(time(), '<');

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //排序规则
        if (!isset(self::$orderTypeMap[$orderType]) || !isset(self::$orderMap[$order])) {
            Bd_Log::warning("Error:[param error], Detail:[orderType:$orderType]");

            return false;
        }

        $orderType    = self::$orderTypeMap[$orderType];
        $order        = self::$orderMap[$order];
        $arrAppends[] = "order by $orderType $order";

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[grade:$grade subject:$subject]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }

    /**
     * 按年级学科获取班课在售课程列表
     *
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @param  int $canPreSale 是否支持预售
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getBCourseList($grade, $subject, $canPreSale)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        $cacheVersion = 1514519176; // 缓存版本号  用于更新缓存
        //设置缓存key
        $strParam = "$grade $subject $canPreSale $isInner {$cacheVersion}";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getBCourseList_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $productList = json_decode(utf8_encode($cacheValue), true);

            return $productList;
        }
        $zbDakInterface = new Zb_Core_Ds_Dak_Interface();
        $productInfo    = $zbDakInterface->getBSubjectSkuIdList($grade, $subject, 0, 0, true, $isInner);
        if ($productInfo === false) {
            Bd_Log::warning("Error:[getBSubjectSkuIdList], Detail:[grade:$grade subject:$subject]");

            return false;
        }
        $productList = $productInfo['skuIdList'];
        //写缓存
        $cacheValue = json_encode($productList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $productList;
    }

    /**
     * 按主讲老师集获取在售课程数量 不用了
     *
     * @param  array $teacherUids 主讲老师uid
     * @param  int   $canPreSale 是否支持预售
     * @return int
     */
    public function getCourseCntByTeacherUids($teacherUids, $canPreSale)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        //设置缓存key
        $cacheKeys = array();
        foreach ($teacherUids as $teacherUid) {
            $strParam    = "$teacherUid $canPreSale $isInner";
            $cacheKey    = 'zhiboke_ds_advanced_courselist_getCourseCntByTeacherUid_' . md5($strParam);
            $cacheKeys[] = $cacheKey;
        }

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValues  = $objMemcached->mget($cacheKeys);

        $sumCourseCnt = 0;
        foreach ($teacherUids as $teacherUid) {
            //读缓存
            $strParam = "$teacherUid $canPreSale $isInner";
            $cacheKey = 'zhiboke_ds_advanced_courselist_getCourseCntByTeacherUid_' . md5($strParam);
            if (!empty($cacheValues[$cacheKey])) {
                $courseCnt    = json_decode(utf8_encode($cacheValues[$cacheKey]), true);
                $courseCnt    = intval($courseCnt['courseCnt']);
                $sumCourseCnt += $courseCnt;
                continue;
            }

            //读数据库
            $courseCnt = $this->getCourseCntByTeacherUid($teacherUid, $canPreSale);
            if (false === $courseCnt) {
                Bd_Log::warning("Error:[getCourseCntByTeacherUid], Detail:[teacherUid:$teacherUid]");
                continue;
            }

            $sumCourseCnt += $courseCnt;

            //写缓存
            $cacheValue = json_encode(array('courseCnt' => $courseCnt));
            $objMemcached->set($cacheKey, $cacheValue, 60);
        }

        return $sumCourseCnt;
    }

    /**
     * 按主讲老师获取在售课程数量 不用了
     *
     * @param  mix      $teacherUid 主讲老师uid
     * @param  int      $canPreSale 是否支持预售
     * @param  bool|int $distinctCourseType 是否区分课程类型，默认不区分，专题课0，班课2
     * @param  int      $status 是否区分课程状态，默认已上线状态，在售false  全部（在售、已结束）true
     * @return int
     */
    public function getCourseCntByTeacherUid($teacherUid, $canPreSale, $cacheSwitch = false, $distinctCourseType = false, $status = false)
    {
        if (intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");

            return false;
        }

        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        if ($cacheSwitch) {
            //设置缓存key
            $strParam = "$teacherUid $canPreSale $isInner $distinctCourseType $status";
            $cacheKey = 'zhiboke_ds_advanced_courselist_getCourseCntByTeacherUid_' . md5($strParam);

            //读缓存
            $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
            $cacheValue   = $objMemcached->get($cacheKey);
            if (!empty($cacheValue)) {
                $courseCnt = json_decode(utf8_encode($cacheValue), true);
                $courseCnt = intval($courseCnt['courseCnt']);

                return $courseCnt;
            }
        }

        //基本条件
        $arrConds = array(
            //'teacherUid' => $teacherUid,
            'isShow' => 1,
        );
        if ($status) {
            $arrConds[] = "status in (" . self::STATUS_ONLINE . "," . self::STATUS_FINISHED . ")";
        } else {
            $arrConds['status'] = self::STATUS_ONLINE;
        }
        //是否区分课程类型
        if ($distinctCourseType !== false) {
            if (in_array($distinctCourseType, array(Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE, Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG))) {
                $arrConds['type'] = $distinctCourseType;
            }
        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }
        //报名截止时间
        if (!$status) {//取在售课程
            $arrConds['registerStopTime'] = array(time(), '>');
        }
        //$arrConds[] = '(type = 2 and learn_season not in ("春_1","春_2","春_3","春_4") ) or type = 0';
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //获取该老师名下所有的打包课、以及被打包课对应的主课程id列表
        $courseList = $this->getCourseListByTeacherUids(array($teacherUid), $canPreSale);
        $arrConds[] = 'course_id in (' . implode(',', $courseList) . ')';

        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseCnt    = $objDaoCourse->getCntByConds($arrConds);
        if (false === $courseCnt) {
            Bd_Log::warning("Error:[getCntByConds], Detail:[teacherUid:$teacherUid]");

            return false;
        }

        if ($cacheSwitch) {
            //写缓存
            $cacheValue = json_encode(array('courseCnt' => $courseCnt));
            $objMemcached->set($cacheKey, $cacheValue, 60);
        }

        return $courseCnt;
    }

    /**
     * 按主讲老师集获取在售课程列表
     *
     * @param  array $teacherUids 主讲老师
     * @param  int   $canPreSale 是否支持预售
     * @param  int   $offset 偏移
     * @param  int   $limit 限制
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getCourseListByTeacherUids($teacherUids, $canPreSale, $offset = 0, $limit = 0)
    {
        $courseList = array();
        if (empty($teacherUids)) {
            return $courseList;
        }

//        是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        //设置缓存key
        sort($teacherUids);
        $strTeacherUids = json_encode($teacherUids);
        //$strParam       = "$strTeacherUids $canPreSale $offset $limit $isInner";
        $strParam       = "$strTeacherUids $isInner";
        $cacheKey       = 'zhiboke_ds_advanced_courselist_getCourseListByTeacherUids_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);
            return $courseList;
        }

        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'isShow' => 1,
        );

        //主讲老师
        $strTeacherUids = implode(',', $teacherUids);
        $arrConds[]     = "teacher_uid in ($strTeacherUids)";

        //班课走新逻辑
        $arrConds[] = 'type <> ' . self::TYPE_PRIVATE_LONG;

        $arrConds[] = '(type = 2 and learn_season not in ("春_1","春_2","春_3","春_4")) or (type = 0)';
       // 报名开始时间
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array($nowTime, '<');
        }

        //报名截止时间
        $arrConds['registerStopTime'] = array($nowTime, '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }
        //排序规则
        $arrAppends = array(
            'order by type desc, online_start desc, student_cnt desc',
        );

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId', 'extData', 'isShow'), null, $arrAppends);

        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[strTeacherUids:$strTeacherUids]");

            return false;
        }

        $arrCourseInfos = array();
        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            //区分子课跟打包课 如果是子课 则查询对应的打包课作为在售课程 packId存在的一定是子课
            if (0 == $courseInfo['isShow'] && isset($courseInfo['extData']['packId'])) {
                $courseList[$courseInfo['extData']['packId'][0]] = intval($courseInfo['extData']['packId'][0]);
            } else if (1 == $courseInfo['isShow']) {
                $courseList[$courseInfo['courseId']] = intval($courseInfo['courseId']);
            }
        }
        if ($courseList) {
            //过滤掉时间不在范围内的课程id
            unset($arrConds[0]);
            $arrConds[]     = 'course_id in (' . implode(',', $courseList) . ')';
            $arrCourseInfos = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
            if (false === $arrCourseInfos) {
                Bd_Log::warning("Error:[getListByConds], Detail:[courseList:$courseList]");

                return false;
            }
        }
        //重置课程列表
        $courseList = array();
        foreach ($arrCourseInfos as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }
        return $courseList;

        //限制规则 为了适应分页
        /*
        if ($limit > 0) {
            //移除第一次课程id条件
            unset($arrConds[1]);
            $arrAppends[]   = "limit $offset, $limit";
            $arrsConds[]    = 'course_id in (' . implode(',', $courseList) . ')';
            $arrsCourseInfo = $objDaoCourse->getListByConds($arrsConds, array('courseId'), null, $arrAppends);
            if (false === $arrsCourseInfo) {
                Bd_Log::warning("Error:[getListByConds], Detail:[courseList:$courseList]");

                return false;
            }
            //重置课程列表
            $courseList = array();
            foreach ($arrsCourseInfo as $courseInfo) {
                $courseList[] = intval($courseInfo['courseId']);
            }
        }
        */

        //新数据源
//        $skuList = $this->getSkuIdListByTeacherUids($teacherUids, $isInner);
//        $courseList = array_unique(array_merge($skuList, $courseList));

//        //写缓存
//        $cacheValue = json_encode($courseList);
//        $objMemcached->set($cacheKey, $cacheValue, 60);


    }

    /**
     * 按主讲老师获取课程列表（包含在售和已结束的课程）
     *
     * @param  int $teacherUid 主讲老师uid
     * @param  int $type 课程类型
     * @param  int $canPreSale 是否支持预售
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getAllCourseListByTeacherUid($teacherUid, $type, $canPreSale, $offset = 0, $limit = 0)
    {
        if (intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");

            return false;
        }

        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        //设置缓存key
        $strParam = "$teacherUid $type $canPreSale $offset $limit $isInner";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getAllCourseListByTeacherUid_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);
            return $courseList;
        }

        //new source start
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $ret = $this->getCourseListByTeacherUids(array($teacherUid), $canPreSale);
        $options = array('extTeacher', 'extAssistant', 'extLesson');
        if (in_array($teacherUid, array(2136864193, 2149830480, 2145426042))) {
            $ret[] = 5852;
        }
        $courseInfoArr = $this->getCourseInfoArr($ret, $options);
        $courseList = array();
        foreach ($courseInfoArr as $course) {
            if ($course['status'] != self::STATUS_ONLINE) {
                continue;
            }
            if ($course['isShow'] != 1) {
                continue;
            }
            if ($type == self::TYPE_PRIVATE && !in_array($course['type'], array(self::TYPE_PRIVATE, self::TYPE_PRE_LONG))) {
                continue;
            } elseif ($type == self::TYPE_PRIVATE_LONG && $type != $course['type']) {
                continue;
            }
            if ($canPreSale <= 0 && $course['registerStartTime'] >= $nowTime) {
                continue;
            }
            if ($course['registerStopTime'] <= $nowTime) {
                continue;
            }
            if ($isInner != 1 && $course['inner'] == 1) {
                continue;
            }
            $courseList[$course['courseId']] = $course['startTime'];
        }
        asort($courseList);
        $courseList = array_keys($courseList);
        if ($limit > 0) {
            $courseList = array_slice($courseList, $offset, $limit);
        }
        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);
        return $courseList;
        //new source end

        //基本条件
        if ($type == self::TYPE_PRIVATE) {
            $arrConds = array(
                //'teacherUid' => $teacherUid,
                'status' => self::STATUS_ONLINE,
                'type in (' . self::TYPE_PRIVATE . ',' . self::TYPE_PRE_LONG . ')',
                'isShow' => 1,
            );
        } else {
            $arrConds = array(
                //'teacherUid' => $teacherUid,
                'status' => self::STATUS_ONLINE,//array(self::STATUS_ONLINE, '>=', self::STATUS_FINISHED, '<='),
                'type'   => $type,
                'isShow' => 1,
            );
        }
//        if($type == 2) {
//            $arrConds[] = 'learn_season not in ("秋_2")';
//        }
        //特殊情况
        if (in_array($teacherUid, array(2136864193, 2149830480, 2145426042))) {
            unset($arrConds['teacherUid']);
            $arrConds[] = "teacher_uid = $teacherUid  or course_id = 5852";
        }

        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }

        //报名截止时间
        //if ($canPreSale <= 0) {
        $arrConds['registerStopTime'] = array(time(), '>');
        //}
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //获取该老师名下所有的打包课、以及被打包课对应的主课程id列表
        $courseList = $this->getCourseListByTeacherUids(array($teacherUid), $canPreSale);
        $arrConds[] = 'course_id in (' . implode(',', $courseList) . ')';

        //排序规则
        $arrAppends = array(
            'order by status, start_time',
        );

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[teacherUid:$teacherUid]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }

    /**
     * 获取老师的所有商品 目前支持班课
     * @param $teacherUids
     * @return array
     */
    public function getSkuIdListByTeacherUids($teacherUids, $isInner)
    {
        $skuIdList = array();
        $objDakApi = new Zb_Core_Ds_Dak_Interface();
        foreach ($teacherUids as $teacherUid) {
            $ret = $objDakApi->getTeacherBSkuIdList($teacherUid, 0, 0, true, $isInner);
            $skuIdList = array_merge($skuIdList, $ret['skuIdList']);
        }
        return $skuIdList;
    }

    /**
     * 按学季获取在售课程列表
     *
     * @param  array $teacherUids 主讲老师
     * @param  int   $type 课程类型
     * @param  int   $grade 年级
     * @param  int   $subject 学科
     * @param  int   $newSeason 区分学季规则
     * @param  int   $canPreSale 是否支持预售
     * @param  int   $offset 偏移
     * @param  int   $limit 限制
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getCourseListBySeasonId($seasonId, $type, $grade, $subject, $newSeason, $canPreSale, $offset = 0, $limit = 0)
    {
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        //班课走新接口
        if ($type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
            $strParam = "$grade $subject $seasonId $offset $limit ";
            $cacheKey = 'zhiboke_ds_advanced_courselist_getCourseListBySeasonId_' . md5($strParam);
            //读缓存
            $cacheValue = $objMemcached->get($cacheKey);
            if (!empty($cacheValue)) {
                $productIdList = json_decode(utf8_encode($cacheValue), true);

                return $productIdList;
            }
            $zbDakInterface = new Zb_Core_Ds_Dak_Interface();
            $skuIdList      = $zbDakInterface->getBSubjectSeasonSkuIdList($grade, $subject, $seasonId, $offset, $limit, true);
            if (empty($skuIdList['skuIdList'])) {
                return array();
            }
            $cacheValue = json_encode($skuIdList['skuIdList']);
            $objMemcached->set($cacheKey, $cacheValue, 60);

            return $skuIdList['skuIdList'];
        }

        $strParam = "$seasonId $type $grade $subject $newSeason $canPreSale $offset $limit";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getCourseListBySeasonId_' . md5($strParam);

        //读缓存
        $cacheValue = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }
        if ($newSeason == 1) {
            $seasonConds = Hkzb_Const_FudaoGradeMap::$seasonIdNameMap;;
        } else {
            $seasonConds = array(
                1  => '"春_1"',
                11 => '"春_2"',
                12 => '"春_3"',
                13 => '"春_4"',
                2  => '"暑_1","暑_2","暑_3","暑_4"',
                3  => '"秋_1"',
                31 => '"秋_2"',
                32 => '"秋_3"',
                33 => '"秋_4"',
                4  => '"寒_1","寒_2","寒_3","寒_4"',
            );
        }
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'type'   => $type,
            'isShow' => 1,
        );
        if ($seasonId > 0 && isset($seasonConds[$seasonId])) {
            $arrConds[] = 'learn_season in (' . $seasonConds[$seasonId] . ')';
        } else {
            //$arrConds[] = 'learn_season not in ("春_1","春_2","春_3","春_4")';
//            if(Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG == $type) {
//                $arrConds[] = 'learn_season != "秋_2"';
//            }
        }
        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
            //特殊处理3654,5852
            if (in_array($subject, array(1, 2, 3))) {
                unset($arrConds['subject']);
                $arrConds[] = 'subject =' . $subject . ' or course_id in (5852,10452,10453,10555)';
            }

        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程

        $arrConds['`inner`'] = 0;


        //排序规则
        $arrAppends = array(
            'order by online_start asc, student_cnt desc ',
        );

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[arrConds:" . json_encode($arrConds) . "]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }


    public function getCourseListBySeasonIds($seasonIds, $grade, $subject, $newSeason, $canPreSale, $offset = 0, $limit = 0, $type = 2, $isQiu2Old = 1, $fromPc = 0)
    {
        $courseList = array();
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
        $seasonIdsParam = json_encode($seasonIds);
        $strParam       = "$seasonIdsParam $type $grade $subject $newSeason $canPreSale $offset $limit $isInner $isQiu2Old";
        $cacheKey       = 'zhiboke_ds_advanced_courselist_getCourseListBySeasonIds_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }

        //如果是班课走新的接口逻辑
        $now      = Hkzb_Util_FuDao::getCurrentTimeStamp();
        if($type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG  && !empty($seasonIds)) {
            if ($grade == 12) {
                $showTime = Hkzb_Util_Fudao_Category::SPRING_COURSE_GRADE_TWO_SELL_START_TIME;
            } elseif ($grade == 13) {
                $showTime = Hkzb_Util_Fudao_Category::SPRING_COURSE_GRADE_THREE_SELL_START_TIME;
            } else {
                $showTime = Hkzb_Util_Fudao_Category::SPRING_COURSE_OTHER_GRADE_SELL_START_TIME;
            }
            $courseList = array();
            foreach ($seasonIds as $seasonId) {
                //春季展示时间
                if (in_array($seasonId, array(1, 11, 12, 13)) && $now < $showTime) {
                    continue;
                }
                //PC不展示春季课
                if (in_array($seasonId, array(1, 11, 12, 13)) && $fromPc == 1) {
                    continue;
                }
                //循环按照seasonId获取列表
                $arrCourseIds = $this->getCourseListBySeasonId($seasonId, $type, $grade, $subject, $newSeason, 1);
                if ($arrCourseIds === false) {
                    Bd_Log::warning("Error:[getCourseListBySeasonId], Detail:[arrConds:$seasonId]");
                    continue;
                }
                $courseList = array_merge($courseList, $arrCourseIds);
            }
            //写缓存
            $cacheValue = json_encode($courseList);
            $objMemcached->set($cacheKey, $cacheValue, 60);

            return $courseList;
        }

        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'type'   => $type,
            'isShow' => 1,
        );

        //获取学期检索条件
        if (!empty($seasonIds)) {
            $seasonConf = Hk_Util_Category::$SEASONCONDS;
            $seasonList = [];
            foreach ($seasonIds as $seasonId) {
                if ($now < Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_ONLINE_TIME && $type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
                    if ($seasonId == 31) {
                        continue;
                    }
                }
                if (in_array($seasonId, array(4, 41, 42, 43))) {
                    if (($isQiu2Old == 1 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_PRESELL_START_TIME) || ($isQiu2Old == 0 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_NEW_USER_SELL_START_TIME)) {
                        continue;
                    }
                }
                $seasonList[] = $seasonConf[$seasonId];
            }
            $seasonConds = join(',', $seasonList);
            $arrConds[]  = 'learn_season in (' . $seasonConds . ')';

        } else {
            if ($now < Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_ONLINE_TIME && $type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
                $arrConds[] = 'learn_season <> "秋_2"';
            }
            if (($isQiu2Old == 1 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_PRESELL_START_TIME) || ($isQiu2Old == 0 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_NEW_USER_SELL_START_TIME)) {
                $arrConds[] = 'learn_season not in ("寒_1", "寒_2", "寒_3", "寒_4")';
            }
        }
        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //排序规则
        $arrAppends = array(
            'order by online_start asc, student_cnt desc ',
        );

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();

        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[arrConds:" . json_encode($arrConds) . "]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }


    public function getCourseListByCourseArray($courseArray, $grade, $subject, $canPreSale, $offset = 0, $limit = 0, $showAll = 0, $isTrialCard = 0)
    {
        if (empty($courseArray)) {
            return array();
        }
        $courseList = array();
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
        sort($courseArray);
        $courseArr = json_encode($courseArray);
        $strParam  = "$courseArr $grade $subject $canPreSale $offset $limit $isInner $showAll $isTrialCard";
        $cacheKey  = 'zhiboke_ds_advanced_courselist_getCourseListBySeasonId_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }

        //new source start
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $options = array();
        $arrCourseInfo = $this->getCourseInfoArr($courseArray, $options);
        $courseList = array();
        $courseFull = []; // 课程是否满班映射
        if ($arrCourseInfo) {
            $onlineStartRank = array();
            $studentCntRank = array();
            foreach ($arrCourseInfo as $course) {
                if ($course['status'] != self::STATUS_ONLINE) {
                    continue;
                }
                if ($showAll == 0 && $course['isShow'] != 1) {
                    continue;
                }
                if ($grade > 0) {
                    $gradeInt = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
                    if ($course['moreGrade'] & $gradeInt != $gradeInt) {
                        continue;
                    }
                }
                if ($subject > 0 && $subject != $course['subject']) {
                    continue;
                }
                if ($canPreSale > 0 && $course['registerStartTime'] >= $nowTime) {
                    continue;
                }
                if ($course['registerStopTime'] <= $nowTime) {
                    continue;
                }
                if ($isInner <= 0 && $course['inner'] > 0) {
                    continue;
                }
                $onlineStartRank[] = $course['onlineStart'];
                $studentCntRank[]  = $course['studentCnt'];
                $courseList[]      = isset($course['productId']) ? $course['productId'] : $course['courseId'];
                // course表的体验课排序 是否满班
                $courseFull[$course['courseId']] = ( 0 >= $course['studentMaxCnt'] - $course['studentCnt']) ? 1 : 0; 
            }
            if ($courseList) {
                array_multisort($onlineStartRank, SORT_ASC, $studentCntRank, SORT_DESC, $courseList);
            }
        }
        // 体验课卡片特殊排序 满班的排在后面
        if ($isTrialCard && $courseFull) {
            $arrCanSale = [];
            $arrFull    = [];
            foreach ($courseList as $courseId) {
                if (1 == $courseFull[$courseId]) {
                    $arrFull[] = $courseId;
                } else {
                    $arrCanSale[] = $courseId;
                }
            }
            $courseList = array_merge($arrCanSale, $arrFull);
        }
        if ($limit > 0) {
            $courseList = array_slice($courseList, $offset, $limit);
        }
        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
        //new source end

        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
        );
        if ($showAll == 0) {
            //区分是否展现隐藏课
            $arrConds['isShow'] = 1;
        }

        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        if ($subject > 0) {
            $arrConds['subject'] = $subject;

        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }
        $arrConds[] = 'course_id in (' . join(',', $courseArray) . ')';
        //排序规则
        $arrAppends = array(
            'order by online_start asc, student_cnt desc ',
        );

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[arrConds:" . json_encode($arrConds) . "]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }
    //
    //*********************************************对外list接口集***************************************
    //

    /**
     * 按年级学科获取所有在售专题课(直播课策略方向专用)
     *
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @return mix
     */
    public function getZCourseListForStrategy($grade, $subject, $isNewUser = 0)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        //设置缓存key
        $strParam = "$grade $subject $isInner {$isNewUser}";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getZCourseListForStrategy_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }

        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            //'type' => self::TYPE_PRIVATE,
            'type in (' . self::TYPE_PRIVATE . ',' . self::TYPE_PRE_LONG . ')',
            'isShow' => 1,
            'pack in (0,1)'
        );

        //指定年级
        /*$grade = Hk_Util_Category::$GRADEMAP[$grade];
        if(!empty($grade)) {
            $grade = implode(',', $grade);
            $arrConds[] = "grade in ($grade)";
        }*/
        if ($grade > 0 && isset(Hkzb_Const_FudaoGradeMap::$GradeMap[$grade])) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        //指定学科
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }

        //报名开始时间
        $arrConds['registerStartTime'] = array(time(), '<');

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //报名人数不超过最大人数
        //$arrConds[] = "student_cnt < student_max_cnt";
        
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        //查询字段
        $arrFields = array('courseId', 'grade', 'subject', 'teacherUid', 'studentCnt', 'studentMaxCnt', 'registerStartTime', 'startTime', 'price', 'onlineStart', 'currentLessonTime', 'type');

        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseList   = $objDaoCourse->getListByConds($arrConds, $arrFields);
        if (false === $courseList) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");

            return false;
        }
        // 体验课满班过滤掉
        if (1 == $isNewUser) {
            $fullProLongCourse = [];
            foreach ($courseList as $idx => $row) {
                if ($row['type'] == self::TYPE_PRE_LONG && ($row['studentCnt'] >= $row['studentMaxCnt']) ) {
                    $fullProLongCourse[$row['courseId']] = 1;
                    unset($courseList[$idx]);
                }
            }
            Bd_Log::addNotice('fullProLongCourseSkip', json_encode($fullProLongCourse));
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 100);

        return $courseList;
    }
    
    /**
     * 按年级学科获取所有在售体验课(直播课策略方向专用)
     * 新用户首页专用
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @param  int $currentLessonTimeLimit 开课时间限制 比这个时间小
     * @param  int $isAllTeacher 是否取全部老师
     * @return mix
     */
    public function getPreLongCourseForStrategy($grade, $subject, $currentLessonTimeLimit = 0, $isAllTeacher = false) {
        // 必须制定年级
        $grade   = intval($grade);
        $subject = intval($subject);
        if (0 >= $grade) {
            return false;
        }
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
    
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'type'   => self::TYPE_PRE_LONG,
            'isShow' => 1,
            'pack in (0,1)'
        );
    
        if (isset(Hkzb_Const_FudaoGradeMap::$GradeMap[$grade])) {
            // 多年级
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        } else {
            // 单个年级
            $arrConds['grade'] = $grade;
        }
        //指定学科
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }
    
        //报名开始时间
        $arrConds['registerStartTime'] = array(time(), '<');
        
        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');
        
        //报名人数不超过最大人数
        $arrConds[] = "student_cnt < student_max_cnt";
    
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }
        
        // 开课时间限制
        if (0 < $currentLessonTimeLimit) {
            $arrConds['currentLessonTime'] = array($currentLessonTimeLimit, '<');
        }
        // 是否拿全部老师的课程
        if (!$isAllTeacher) {
            // 固定22个老师 （12个初中 10个小学）
            $arrConds[] = " teacher_uid in (2218104616,2201887497,2132947872,2234313374,2239457239,2164614872,2207199280,2218083514,2235863979,2208234650,2104000241,2257718631,2176857636,2140327332,2214212302,2180160942,2153868699,2113937413,2147426804,2180515695,2204532060,2135487734) ";
        }
        //查询字段
        $arrFields = array('courseId', 'grade', 'subject', 'teacherUid', 'studentCnt', 'registerStartTime', 'startTime', 'price', 'onlineStart', 'currentLessonTime', 'type');
        
        // 排序 现在基本都是按照开课时间进行排的 暂时写死
        $arrAppends = ['order by current_lesson_time asc'];
        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseList   = $objDaoCourse->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $courseList) {
            $strConds = json_encode($arrConds);
            Bd_Log::warning("Error:[getListByConds], Detail:[grade:{$grade},subject:{$subject}] strConds:[{$strConds}]");
            return false;
        }
    
        return $courseList;
    }
    
    /**
     * 按年级学科获取所有在售体验课(直播课策略方向专用)
     * 新用户首页专用
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @param  int $currentLessonTimeLimit 开课时间限制 比这个时间小
     * @return mix
     */
    public function getTeaPreLongCourseForStrategy($teacherUid, $currentLessonTimeLimit = 0) {
        // 必须制定年级
        $teacherUid   = intval($teacherUid);
        if (0 >= $teacherUid) {
            return false;
        }
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
    
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'type'   => self::TYPE_PRE_LONG,
            'isShow' => 1,
            'pack in (0,1)'
        );
        
        // 老师uid
        $arrConds['teacherUid'] = $teacherUid;
        //报名开始时间
        $arrConds['registerStartTime'] = array(time(), '<');
    
        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');
    
        //报名人数不超过最大人数
        $arrConds[] = "student_cnt < student_max_cnt";
    
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }
    
        // 开课时间限制
        if (0 < $currentLessonTimeLimit) {
            $arrConds['currentLessonTime'] = array($currentLessonTimeLimit, '<');
        }
    
        //查询字段
        $arrFields = array('courseId', 'grade', 'subject', 'teacherUid', 'studentCnt', 'registerStartTime', 'startTime', 'price', 'onlineStart', 'currentLessonTime', 'type');
    
        // 排序 现在基本都是按照开课时间进行排的 暂时写死
        $arrAppends = ['order by current_lesson_time asc'];
        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseList   = $objDaoCourse->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $courseList) {
            $strConds = json_encode($arrConds);
            Bd_Log::warning("Error:[getListByConds], Detail:[teacherUid:{$teacherUid}] strConds:[{$strConds}]");
            return false;
        }
    
        return $courseList;
    }

    /**
     * 获取在售课程年级学科类型
     * @return array|false
     */
    public function getCourseTypeOnlineNew()
    {
        $arrFields = array(
            "type",
            "grade",
            "subject",
            "count(*) as cnt",
        );

        $arrConds = array(
            'status' => self::STATUS_ONLINE,
        );
        if (!(Hk_Util_Ip::isInnerIp())) {
            $arrConds['`inner`'] = 0;
        }
        $arrAppends   = array(
            "group by type, grade, subject",
        );
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $ret          = $objDaoCourse->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    //获取mord_grade
    public function getCourseTypeOnlineForMoreGrade()
    {
        $arrFields = array(
            "type",
            "more_grade",
            "subject",
            "learn_season",
            "count(*) as cnt",
        );

        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            0        => 'pack in (0,1) ',
            1        => 'register_stop_time > ' . time(),
        );
        if (!(Hk_Util_Ip::isInnerIp())) {
            $arrConds['`inner`'] = 0;
        }
        $arrAppends   = array(
            "group by type, more_grade, subject, learn_season",
        );
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $ret          = $objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取课程数量，用户PC端分页
     * @param bool $type
     * @param int  $grade
     * @param int  $subject
     *
     * @return bool|false|int
     */
    public function getOnlineCourseCnt($type = false, $grade = 0, $subject = 0, $canPreSale = 0, $seasonIds = [], $isQiu2Old = 1, $fromPc = 0)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }
        //设置缓存key
        $seasonParams = json_encode($seasonIds);
        $strParam     = "$type $grade $subject $isInner $canPreSale $seasonParams $isQiu2Old";
        $cacheKey     = 'zhiboke_ds_advanced_elearninglist_getCourseCnt_' . md5($strParam);
        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $cacheValue = json_decode(utf8_encode($cacheValue), true);
            $courseCnt  = $cacheValue['cnt'];

            return $courseCnt;
        }
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'isShow' => 1,
        );
        if ($type !== false) {
            $arrConds['type'] = $type;
        }
        //指定年级
        if ($grade > 0 && isset(Hkzb_Const_FudaoGradeMap::$GradeMap[$grade])) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        //指定学科
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
        }
        $now = Hkzb_Util_FuDao::getCurrentTimeStamp();
        //指定学期
        if (!empty($seasonIds)) {
            //获取学期检索条件
            $seasonConf = Hk_Util_Category::$SEASONCONDS;
            $seasonList = [];
            foreach ($seasonIds as $seasonId) {
                if ($now < Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_ONLINE_TIME && $type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
                    if ($seasonId == 31) {
                        continue;
                    }
                }
                if (in_array($seasonId, array(4, 41, 42, 43))) {
                    if (($isQiu2Old == 1 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_PRESELL_START_TIME) || ($isQiu2Old == 0 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_NEW_USER_SELL_START_TIME)) {
                        continue;
                    }
                }
                $seasonList[] = $seasonConf[$seasonId];
            }
            $seasonConds = join(',', $seasonList);
            $arrConds[]  = 'learn_season in (' . $seasonConds . ')';
        } else {
            if ($now < Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_ONLINE_TIME && Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG == $type) {
                $arrConds[] = 'learn_season <> "秋_2"';
            }
            if (($isQiu2Old == 1 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_PRESELL_START_TIME) || ($isQiu2Old == 0 && $now < Hkzb_Util_Fudao_Category::WINTER_COURSE_NEW_USER_SELL_START_TIME)) {
                $arrConds[] = 'learn_season not in ("寒_1", "寒_2", "寒_3", "寒_4")';
            }
        }
        if ($fromPc) {
            $arrConds[] = 'learn_season not in ("春_1", "春_2", "春_3", "春_4")';
        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }
        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');
        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }
        //访问Dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseCnt    = $objDaoCourse->getCntByConds($arrConds);
        if (false === $courseCnt) {
            Bd_Log::warning("Error:[getCourseCnt], Detail:[]");

            return false;
        }
        //写缓存
        $cacheValue = json_encode(array('cnt' => $courseCnt));
        $objMemcached->set($cacheKey, $cacheValue, 100);

        return $courseCnt;
    }

    /**
     * 获取所有在售课程
     *
     * @return array|false
     * @example
     * array(7234,3457,9824)
     */
    public function getAllCourseIdList()
    {
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'isShow' => 1,
        );

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'));
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        return $courseList;
    }
    //获取一个打包课的所有子课 以及每个子课对应的所有打包课courseId

    /**
     * @param $courseId
     * @return array|bool
     * array(
     *  123=>array(
     *       1234,//打包课id
     *       2345,
     *  )
     * )
     */
    public function getSubCourseId($courseId)
    {
        //读数据库
        $arrFields    = explode(',', self::ALL_FIELDS);
        $arrConds     = array(
            'courseId' => intval($courseId),
        );
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseInfo   = $objDaoCourse->getRecordByConds($arrConds, $arrFields);
        if (false === $courseInfo) {
            Bd_Log::warning("Error:[getCourseInfo], Detail:[courseId:$courseId]");

            return false;
        }
        $allCourseIds = array();
        if ($courseInfo['pack'] == self::PACK_YES) {
            //获取所有子课
            $subCourseIds  = array();
            $coreCourseIds = $courseInfo['extData']['coreCourseIds'];
            $allCourseIds  = array();
            foreach ($coreCourseIds as $coreCourseId) {
                $subCourseId = intval($coreCourseId['courseId']);

                $allCourseIds[] = $coreCourseId['courseId'];
                if ($coreCourseId['type'] == 1) {
                    //获取主子课
                    array_unshift($subCourseIds, $subCourseId);
                } else {
                    $subCourseIds[] = $subCourseId;
                }
            }

            $otherCourseIds = $courseInfo['extData']['otherCourseIds'];
            foreach ($otherCourseIds as $otherCourseId) {
                $allCourseIds[] = $otherCourseId['courseId'];
                $subCourseId    = $otherCourseId['courseId'];
                $subCourseIds[] = $subCourseId;
            }

            //批量子课获取打包课信息
            $arrConds = array(
                0 => 'course_id in (' . join(',', $allCourseIds) . ')',
            );
            $subReg   = $objDaoCourse->getListByConds($arrConds, array('courseId', 'extData'));
            if (false === $subReg) {
                Bd_Log::warning("Error:[getSubCourseList], Detail:[courseId:$courseId]");

                return false;
            }
            $oriCourseIdList = array();
            foreach ($subReg as $info) {
                $oriCourseIdList[$info['courseId']] = $info['extData']['packId'];
            }
            $subCourseIdList = array();
            //重新排序保证主体课提升课顺序不变
            foreach ($subCourseIds as $courseId) {
                $subCourseIdList[$courseId] = $oriCourseIdList[$courseId];
            }

        } elseif ($courseInfo['pack'] == self::PACK_YESD) {
            return false;
        } else {
            $subCourseIdList = array(
                $courseId => array($courseId),
            );
        }

        return $subCourseIdList;
    }

    public function formatTagName($tagName)
    {
        $tagName = str_replace('"', '', json_encode($tagName));
        $tagName = str_replace("\\", '_', $tagName);

        return $tagName;
    }

    /**
     * 获取子课列表
     *
     * @param  int $courseId 课程id
     * @return array|false
     */
    public function getSubCourseIdList($courseId)
    {
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $courseInfo = $this->getCourseInfo($courseId);
        if (empty($courseInfo)) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");

            return false;
        }

        if (intval($courseInfo['pack']) === self::PACK_YESD) {
            Bd_Log::warning("Error:[subCourse invalid], Detail:[courseId:$courseId]");

            return false;
        }

        $subCourseIdList = array();

        //非打包课
        if (intval($courseInfo['pack']) === self::PACK_NO) {
            $subCourseIdList[] = $courseId;

            return $subCourseIdList;
        }

        //打包课
        $extData       = $courseInfo['extData'];
        $coreCourseIds = isset($extData['coreCourseIds']) ? $extData['coreCourseIds'] : array();
        foreach ($coreCourseIds as $coreCourseId) {
            $subCourseIdList[] = $coreCourseId['courseId'];
        }
        $otherCourseIds = isset($extData['otherCourseIds']) ? $extData['otherCourseIds'] : array();
        foreach ($otherCourseIds as $otherCourseId) {
            $subCourseIdList[] = $otherCourseId['courseId'];
        }

        return $subCourseIdList;
    }

    //给大搜推荐couseid
    public function getCourseCommandList()
    {
        //基本条件
        $arrConds                      = array(
            'status' => self::STATUS_ONLINE,
        );
        $arrConds['`inner`']           = 0;
        $arrConds['isShow']            = 1;
        $arrConds['registerStartTime'] = array(time(), '<');
        $arrConds['registerStopTime']  = array(time(), '>');
        //$arrConds[] = 'student_cnt < student_max_cnt';
        $arrConds[] = 'pack in (0, 1)';
        //$arrConds['type'] = self::TYPE_PRIVATE;
        $arrAppends = array(
            "order by create_time desc",
        );
        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), NULL, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");

            return false;
        }
        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        return $courseList;
    }

    //获取所有在线的课程
    public function getAllOnlineCourseList()
    {
        //基本条件
        $arrConds   = array(
            'status' => self::STATUS_ONLINE,
        );
        $arrConds[] = 'pack in (0, 1)';
        $arrAppends = array(
            "order by create_time desc",
        );
        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), NULL, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");

            return false;
        }
        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        return $courseList;
    }

    /**
     * 更新课程表中的currentLessonTime
     * @param $courseId
     * @return bool
     */

    public function updateCurrentLessonTimeByCourseId($courseId)
    {
        $objDscourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo  = $objDscourse->getCourseInfo($courseId);
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();

        $lessonField = array('startTime', 'lessonId', 'courseId', 'status');
        //打包课的处理
        if ($courseInfo['pack'] == self::PACK_YES) {
            //获取打包课的所有课程
            $courseIdArr = $this->getAllSubCourseIdsByCourseId($courseId);
            $allLesson   = $objDsLesson->getLessonListByCourseIds($courseIdArr, $lessonField);
            if (empty($allLesson)) {
                Bd_Log::warning("Error:[lesson info is empty]");

                return false;
            }

            $startTime = $this->getCourseCurrenStartTimeByLessonArr($allLesson);
        } else {
            $courseIdArr = array($courseId);
            $allLesson   = $objDsLesson->getLessonListByCourseIds($courseIdArr, $lessonField);

            if (empty($allLesson)) {
                Bd_Log::warning("Error:[lesson info is empty]");

                return false;
            }

            $startTime = $this->getCourseCurrenStartTimeByLessonArr($allLesson);
        }

        $fields = array(
            'currentLessonTime' => intval($startTime),
        );
        //更新时间
        $arrConds     = array(
            'courseId' => $courseId
        );
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $ret          = $objDaoCourse->updateByConds($arrConds, $fields);
        if ($ret === false) {
            Bd_Log::warning("Error:[db error] courseId-{$courseId}");
        }

        return true;
    }

    private function getCourseCurrenStartTimeByLessonArr($allLesson)
    {
        if (empty($allLesson)) {
            Bd_Log::warning("Error:[getCourseCurrenStartTimeByLessonArr] detail:alllesson is empty");

            return 0;
        }

        //按时间排序章节
        $sortArr = array();
        foreach ($allLesson as $lesson) {
            $sortArr[] = $lesson['startTime'];
        }

        array_multisort($sortArr, SORT_ASC, $allLesson);
        $startTime = 0;
        foreach ($allLesson as $lesson) {
            $startTime = $lesson['startTime'];
            if ($lesson['status'] < 2) {
                break;
            }
        }

        return $startTime;
    }


    public function setCurrentLessonTime($courseId)
    {
        if (empty($courseId)) {
            Bd_Log::warning("Error:[param courseId is empty]");

            return false;
        }
        $courseId    = intval($courseId);
        $objDscourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo  = $objDscourse->getCourseInfo($courseId);

        if ($courseInfo['pack'] == self::PACK_YESD) {
            $packCourseIdArr = isset($courseInfo['extData']['packId']) ? $courseInfo['extData']['packId'] : array();
            if (empty($packCourseIdArr)) {
                Bd_Log::warning("Error:packCourseId is empty");

                return false;
            }

            foreach ($packCourseIdArr as $packCourseId) {
                $this->updateCurrentLessonTimeByCourseId($packCourseId);
            }
        }

        $ret = $this->updateCurrentLessonTimeByCourseId($courseId);

        return $ret;
    }

    /**
     * 获取课程的所有子课
     * @param $courseId
     * @return array
     */

    private function getAllSubCourseIdsByCourseId($courseId)
    {
        $objCourse  = new Hkzb_Ds_Fudao_Course();
        $courseInfo = $objCourse->getCourseInfo($courseId);
        if ($courseInfo['pack'] != Hkzb_Ds_Fudao_Course::PACK_YES) {
            return array($courseId);
        }

        $subCourseIds   = array();
        $coreCourseIds  = $courseInfo['extData']['coreCourseIds'];
        $otherCourseIds = $courseInfo['extData']['otherCourseIds'];
        foreach ($coreCourseIds as $coreCourse) {
            $subCourseIds[] = $coreCourse['courseId'];
        }

        foreach ($otherCourseIds as $otherCourse) {
            $subCourseIds[] = $otherCourse['courseId'];
        }

        return $subCourseIds;
    }

    /**
     * 检查课程是否可以被分享（h5观看直播）
     * @param $courseId int
     * @return bool
     */
    public function checkCourseShare($courseId)
    {
        return Hkzb_Util_LaxinFreeCourse::checkCourseShare($courseId);
    }

    /**
     * 控制课前测试和课后测试的入口
     * testStatus:0，不需要测试，1，需要测试，2测试没有通过,3测试通过
     * @param     $studentUid
     * @param     $courseInfo
     * @param int $examType 3:课前测试，4课后测试
     * @return array
     */
    static function getCourseExamInfoByUidCourseId($studentUid, $courseInfo, $examType = 3)
    {
        //测试地址：
        $courseId = $courseInfo['courseId'];
        $examUrl  = "/course/speedexam/courseexampage?examType=3&courseId=" . $courseId;
        $arrRes   = array(
            'testStatus' => 0,
            'testRes'    => array(),
            'examUrl'    => $examUrl
        );
        if (empty($courseInfo)) {
            Bd_Log::warning("param is empty uid:{$studentUid},courseInfo:" . json_encode($courseInfo));

            return $arrRes;
        }

        //专题课不需要测试
        if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
            return $arrRes;
        }

        //不是秋二期的课，不用测试
        if ($courseInfo['learnSeason'] != '秋_2') {
            return $arrRes;
        }

        //预售阶段不能测试
        if ($examType == Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE) {
            $now = Hkzb_Util_FuDao::getCurrentTimeStamp();
            if ($now < $courseInfo['registerStartTime']) {
                return $arrRes;
            }
        }

        //获取课程的试卷信息
        $courseEaxmInfo = self::getExamInfoByCourseIdExamType($courseId, $examType);
        //试卷信息为空，那么就是没有测试，就不需要测试
        if (empty($courseEaxmInfo)) {
            return $arrRes;
        }

        //获取用户的状态和课程的状态
        $userCourseStatus = self::getUserCourseStatus($studentUid, $courseInfo);

        //课前课后测试，报名结束，结课，不需要测试
        $registerStop = $userCourseStatus['registerStop'];
        $isStop       = $userCourseStatus['isStop'];
        if ($registerStop || $isStop) {
            return $arrRes;
        }
        $isFull = $userCourseStatus['isFull'];
        $isReg  = $userCourseStatus['isReg'];
        //课前测试，已报名,已报满，不需要测试
        if ($examType == Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE && ($isReg || $isFull)) {
            return $arrRes;
        }

        //有试卷，没有登录需要测试
        if ($studentUid <= 0) {
            $arrRes['testStatus'] = 1;
            $arrRes['testRes']    = $courseEaxmInfo;

            return $arrRes;
        }

        //获取用户是否是该门课程的续报课程
        $courseSeriesMapList = Hkzb_Util_Fudao_Category::$COURSE_EXAM_RENEWAL_LIST;
        $courseList          = isset($courseSeriesMapList[$courseId]) ? $courseSeriesMapList[$courseId] : array();
        $studentCourseInfo   = array();
        if (!empty($courseList)) {
            $objDsStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
            $studentCourseInfo  = $objDsStudentCourse->getStudentCourseInfoArr($studentUid, $courseList,array(),false);
        }

        //是原班续报的，不需要参加测试
        if (!empty($studentCourseInfo)) {
            return $arrRes;
        }

        //获取用户的考试分数
        $userCourseExamInfo = self::getStudentCourseExamResult($studentUid, $courseId, $examType);

        if (empty($userCourseExamInfo)) { //没有作答的情况
            $arrRes['testStatus'] = 1;
            $arrRes['testRes']    = $courseEaxmInfo;

            return $arrRes;
        }

        $userTestMaxScore  = isset($userCourseExamInfo['userTestMaxScore']) ? $userCourseExamInfo['userTestMaxScore'] : 0;
        $examScoreLine     = isset($userCourseExamInfo['examScoreLine']) ? $userCourseExamInfo['examScoreLine'] : 0;
        $leftAccessExamNum = isset($userCourseExamInfo['testLeftCnt']) ? $userCourseExamInfo['testLeftCnt'] : 0;

        //是否通过
        $isAccessExam = 0;
        $testStatus   = 1;
        if ($userTestMaxScore >= $examScoreLine) {
            $isAccessExam = 1;//考试通过
            $testStatus   = 3;
        }

        //测试没有通过
        if ($isAccessExam == 0 && $leftAccessExamNum == 0) {
            $testStatus = 2;
        }

        $arrRes['testStatus'] = $testStatus;
        $arrRes['testRes']    = $userCourseExamInfo;

        return $arrRes;
    }

    static public function getUserCourseStatus($studentUid, $courseInfo)
    {
        $arrRes = array(
            'isReg'        => 0,
            'isFull'       => 0,
            'registerStop' => 0,
            'isStop'       => 0,
        );
        if (empty($courseInfo)) {
            return $arrRes;
        }

        $isCompositeProduct = 0;
        if(isset($courseInfo['skuType']) && $courseInfo['skuType'] == Hkzb_Ds_Fudao_Product::SKU_TYPE_COMPOSITE_PRODUCT) {
            $isCompositeProduct = 1;
        }

        if (!isset($courseInfo['isReg'])) {
            $isReg = 0;
            if ($studentUid > 0) {
                //计算是否已报名
                $courseIdList = $isCompositeProduct ? $courseInfo['courseIdList'] : array($courseInfo['courseId']);
                foreach ($courseIdList as $value) {
                    //是否已报名
                    $objStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
                    $studentCourse = $objStudentCourse->getNormalStudentCourseInfo($studentUid, $value);
                    if (!empty($studentCourse)) {
                        $isReg = 1;
                        break;
                    }
                }
            }
            $courseInfo['isReg'] = $isReg;
        }
        //计算报名是否结束
        if (!isset($courseInfo['registerStop'])) {
            $now = Hkzb_Util_FuDao::getCurrentTimeStamp();
            if ($now >= $courseInfo['registerStopTime'] || $courseInfo['status'] != Hkzb_Ds_Fudao_Course::STATUS_ONLINE) {
                $registerStop = 1;
            } else {
                $registerStop = 0;
            }
            $courseInfo['registerStop'] = $registerStop;
        }

        //计算是否已结课
        if (!isset($courseInfo['isStop'])) {
            $isStop = 0;
            if (Hkzb_Ds_Fudao_Course::STATUS_FINISHED === $courseInfo['status']) {
                $isStop = 1;
            }
            $courseInfo['isStop'] = $isStop;
        }
        //计算报名是否已满
        if (!isset($courseInfo['leftCnt'])) {
            $registerInfo          = Hkzb_Util_Fudao_Format::getCourseRegisterInfo($courseInfo);
            $courseInfo['leftCnt'] = $registerInfo['studentLeftCnt'];
        }

        //已报满课程不需要测试
        if (isset($courseInfo['isFull'])) {
            $isFull = $courseInfo['isFull'];
        } else {
            $isFull = $courseInfo['leftCnt'] > 0 ? 0 : 1;
        }

        $arrRes = array(
            'isReg'        => $courseInfo['isReg'],
            'isFull'       => $isFull,
            'registerStop' => $courseInfo['registerStop'],
            'isStop'       => $courseInfo['isStop'],
        );

        return $arrRes;
    }

    /**
     * 控制是否展示课前测试标签==== 展示逻辑，新用户并且是有试卷，就展示
     * @param     $studentUid
     * @param     $courseInfo
     * @param int $examType
     * @return int
     */
    static public function isShowExamTag($studentUid, $courseInfo, $examType = 3)
    {
        //列表页报前测逻辑判断都走新的逻辑
        $isShowExamTag = self::getShowExamTagStatus($studentUid, $courseInfo);
        return $isShowExamTag;
    }


    public static function getShowExamTagStatus($studentUid, $courseInfo)
    {

        $isShow = 0;

        $courseId = self::getCourseIdForExam($courseInfo);
        //检测课课程本省需不需要测试
        $objDsExamExamApi = new Hkzb_Ds_Fudao_Exam_ExamApi();
        $examRes          = $objDsExamExamApi->getBeforeRegExam($courseId, $studentUid);
        //没有试卷信息，不用测试
        if (empty($examRes)) {
            return $isShow;
        }
        $needExam      = isset($examRes['needExam']) ? $examRes['needExam'] : 0;
        $examType      = isset($examRes['examType']) ? $examRes['examType'] : 0;
        $beforeExmType = Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE;

        //不需要考试
        if (!$needExam) {
            return $isShow;
        }

        //不是报前测
        if ($examType != $beforeExmType) {
            return $isShow;
        }

        $isShow = 1;

        return $isShow;
    }

    public static function getExamInfoByCourseIdExamType($courseId, $examType)
    {
        if (empty($courseId)) {
            return array();
        }
        //获取课程的试卷信息
        $objDsRelation  = new Hkzb_Ds_Fudao_Exam_ExamPaperRelation();
        $courseEaxmInfo = $objDsRelation->getExamInfoByCourseIdExamType($courseId, $examType);
        $courseEaxmInfo = self::formatExamInfo($courseEaxmInfo);

        return $courseEaxmInfo;
    }

    /**
     * @param     $uid
     * @param     $courseId
     * @param int $examType
     * @return array|bool|false
     *
     *
     *
     */
    public static function getStudentCourseExamResult($uid, $courseId, $examType = 3)
    {
        if (empty($uid) || empty($courseId)) {
            return array();
        }

        //获取课程的试卷信息
        $objDsPaperStudent = new Hkzb_Ds_Fudao_Exam_ExamPaperStudent();
        $courseEaxmInfo    = $objDsPaperStudent->getStudentCourseExamResult($uid, $courseId, $examType);

        $courseEaxmInfo = self::formatResultExamInfo($courseEaxmInfo);

        return $courseEaxmInfo;
    }


    /**
     * 格式化试卷信息
     * @param $examInfo
     * @return array
     */
    static private function formatExamInfo($examInfo)
    {
        if (empty($examInfo)) {
            return array();
        }

        $arrRes = array(
            'testType'       => isset($examInfo['examType']) ? $examInfo['examType'] : 0,
            'examTitle'      => isset($examInfo['title']) ? $examInfo['title'] : '',
            'examId'         => isset($examInfo['examId']) ? $examInfo['examId'] : 0,
            'testLeftCnt'    => isset($examInfo['examInfo']['maxTryNum']) ? intval($examInfo['examInfo']['maxTryNum']) : 0,
            'userTestScore'  => isset($examInfo['userTestScore']) ? $examInfo['userTestScore'] : 0,
            'exerciseNum'    => isset($examInfo['sumnumber']) ? $examInfo['sumnumber'] : 0,
            'examAllScore'   => isset($examInfo['sumscore']) ? $examInfo['sumscore'] : 0,
            'finishExamTime' => isset($examInfo['finishExamTime']) ? $examInfo['finishExamTime'] : 0,
            'examScoreLine'  => isset($examInfo['examInfo']['passScore']) ? $examInfo['examInfo']['passScore'] : 0,
            'accessExamNum'  => isset($examInfo['examInfo']['maxTryNum']) ? $examInfo['examInfo']['maxTryNum'] : 0,
            'examTime'       => isset($examInfo['examInfo']['duration']) ? $examInfo['examInfo']['duration'] : 0, //考试时长
        );

        return $arrRes;
    }


    /**
     * 格式化用户结果
     * @param $examInfo
     * @return array
     */
    static private function formatResultExamInfo($examInfo)
    {
        if (empty($examInfo)) {
            return array();
        }

        $testType      = isset($examInfo['examInfo']['testType']) ? $examInfo['examInfo']['testType'] : 0;
        $examTitle     = isset($examInfo['examInfo']['title']) ? $examInfo['examInfo']['title'] : '';
        $examId        = isset($examInfo['examInfo']['examId']) ? $examInfo['examInfo']['examId'] : 0;
        $exerciseNum   = isset($examInfo['examInfo']['sumnumber']) ? $examInfo['examInfo']['sumnumber'] : 0;
        $examAllScore  = isset($examInfo['examInfo']['sumscore']) ? $examInfo['examInfo']['sumscore'] : 0;
        $examScoreLine = isset($examInfo['examInfo']['passScore']) ? $examInfo['examInfo']['passScore'] : 0;
        $accessExamNum = isset($examInfo['examInfo']['maxTryNum']) ? $examInfo['examInfo']['maxTryNum'] : 0;
        $examTime      = isset($examInfo['examInfo']['duration']) ? $examInfo['examInfo']['duration'] : 0; //考试时长

        //需要计算用考试的最高分数，当前这次考试的分数，当前这次考试的用时,剩余考试次数

        $finishExamTime   = 0; //当前这次考试的用时
        $testLeftCnt      = 0; //考试剩余次数
        $userTestMaxScore = 0;//用户测试最高分数
        $userTestScore    = 0;//当前这次考试的分数

        $answerInfo = isset($examInfo['answerInfo']) ? $examInfo['answerInfo'] : array();
        if (!empty($answerInfo)) {
            $hasTestCnt = 0;
            foreach ($answerInfo as $answer) {
                if ($answer['isFinish'] != 1) {
                    continue;
                }
                $nowScore         = $answer['score'];
                $userTestMaxScore = max($nowScore, $userTestMaxScore);
                //计算考试次数
                $hasTestCnt++;
            }

            //课后测试的特殊处理
            if ($testType == Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_AFTER) {
                $hasTestCnt = count($answerInfo);
            }

            $testLeftCnt = $accessExamNum - $hasTestCnt;
            if ($testLeftCnt <= 0) {
                $testLeftCnt = 0;
            }
            $nowExamInfo    = end($answerInfo);
            $userTestScore  = isset($nowExamInfo['score']) ? $nowExamInfo['score'] / 10 : 0;
            $finishExamTime = isset($nowExamInfo['duration']) ? $nowExamInfo['duration'] : 0;
        }
        $isAccessExam = 0;
        if ($userTestScore >= $examScoreLine) {
            $isAccessExam = 1;
        }
        $userTestMaxScore = $userTestMaxScore / 10;//考试分数除以10
        $arrRes           = array(
            'testType'         => $testType,
            'examTitle'        => $examTitle,
            'examId'           => $examId,
            'testLeftCnt'      => intval($testLeftCnt),
            'userTestScore'    => $userTestScore,
            'exerciseNum'      => $exerciseNum,
            'examAllScore'     => $examAllScore,
            'finishExamTime'   => $finishExamTime,
            'examScoreLine'    => $examScoreLine,
            'accessExamNum'    => $accessExamNum,
            'examTime'         => $examTime, //考试时长
            'userTestMaxScore' => $userTestMaxScore, //考试时长
            'isAccessExam'     => $isAccessExam, //当前这次是否通过考试
        );

        return $arrRes;
    }

    /**
     *
     * @param $courseInfo
     * @return bool
     * 判断是否展示寒春连报模块
     */
    public static function isShowWsSubjectRecommend($courseInfo, $studentUid = 0)
    {
        //不是寒季班的课程，不展示
        $learnSeason     = empty($courseInfo['learnSeason']) ? '' : $courseInfo['learnSeason'];//学季
        $learnSeasonlist = array('寒_1', '寒_2', '寒_3', '寒_4');
        if (!in_array($learnSeason, $learnSeasonlist)) {
            return false;
        }

        //没有绑定课程的不展示
        $courseId = $courseInfo['courseId'];
        //获取课程信息
        $boundCourseInfo = Hkzb_Util_Fudao_Category::getCourseListArrByEnterNum($courseId, $studentUid,1,false);
        if (empty($boundCourseInfo)) {
            return false;
        }
        //已报名，已报满，已结课等，不展示模块
        $userCourseStatus = self::getUserCourseStatus($studentUid, $courseInfo);
        $isReg            = $userCourseStatus['isReg'];
        $isFull           = $userCourseStatus['isFull'];
        $registerStop     = $userCourseStatus['registerStop'];
        $isStop           = $userCourseStatus['isStop'];

        if ($isReg || $isFull || $registerStop || $isStop) {
            return false;
        }

        $courseId = $courseInfo['courseId'];
        $examType = Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE;

        //如果当前课程没有通过测试，不展示模块
        $isPassExam = self::checkUserIsPassExam($studentUid, $courseId, $examType);
        if (!$isPassExam) {
            return false;
        }

        //====课程中的每个组合都有一个报满的，不展示模块,后期加上

        //获取课程信息
        $boundCourseInfo = Hkzb_Util_Fudao_Category::getCourseBoundList($courseId, $studentUid);
        //获取推荐课程信息
        $recommendCourseInfo = $boundCourseInfo['curSeason'];

        //计算是否每个组都有已报满,不展示
        $isAllFull = self::getCourseIsAllFullStatus($recommendCourseInfo, $studentUid);
        if ($isAllFull) {
            return false;
        }

        return true;

    }

    //检测用户这个课程是否通过测试
    static public function checkUserIsPassExam($studentUid, $courseId, $examType = 3)
    {
        //未登录视为考试通过
        if (empty($studentUid)) {
            $isPass = 1;

            return $isPass;
        }

        $isPass = 0;
        if (empty($courseId) || empty($examType)) {
            return $isPass;
        }

        //获取用户的试卷情况
        $objDsExamApi    = new Hkzb_Ds_Fudao_Exam_ExamApi();
        $studentExamInfo = $objDsExamApi->getBeforeRegExam($courseId, $studentUid);
        //如果是报后测试，考试通过
        $beforeExmType = Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE;
        if ($studentExamInfo['examType'] != $beforeExmType) {
            $isPass = 1;

            return $isPass;
        }
        $needExam = $studentExamInfo['needExam'];
        if (!$needExam) {//不需要考试
            $isPass = 1;

            return $isPass;
        }

        $isPass = $studentExamInfo['isPass'];

        return $isPass;
    }

    private static function getCourseIsAllFullStatus($recommendCourseInfo, $studentUid)
    {
        $isFullNum = 0;
        foreach ($recommendCourseInfo as $itemCourseList) {
            foreach ($itemCourseList as $courseInfo) {
                //获取课程的报名状态
                $userCourseStatus = Hkzb_Ds_Fudao_Advanced_Course::getUserCourseStatus($studentUid, $courseInfo);
                $isFull           = $userCourseStatus['isFull'];
                if ($isFull) {//已经报满的这个组无效
                    $isFullNum++;
                    break;
                }
            }
        }

        $courseGroupNum = count($recommendCourseInfo);
        if ($courseGroupNum == $isFullNum) {
            return true;
        }

        return false;
    }

    //获取课程分类板块的数据
    public function getCourseTypeBlockData($grade, $subject, $canPreSale, $haszhuanCourse = 1, $userType = 3)
    {

        $gradeBzInfo = $this->getBzCourseTypeInfo($grade);
        $bType       = Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG;
        $zType       = Hkzb_Ds_Fudao_Course::TYPE_PRIVATE;

        $banItem   = $gradeBzInfo[$bType];
        $zhuanItem = $gradeBzInfo[$zType];

        $hasbanCourse = 0;
        //获取班课列表
        $courseList = $this->getBCourseList($grade, $subject, $canPreSale);
        if (!empty($courseList)) {
            $hasbanCourse = 1;
        }

        //获取是否有自定义课程
        $customItem = $this->getCourseTypeCustomCourse($grade);
        unset($customItem['courseIdList']);
        $arrRes = array();
        if (!empty($hasbanCourse)) {
            $arrRes[] = $banItem;
        }
        if (!empty($haszhuanCourse)) {
            $arrRes[] = $zhuanItem;
        }
        if (!empty($customItem)) {
            //这里处理根据userType是否跳转情况
            if($userType == 0){
                $arrRes[] = $customItem;
            }else{
                $arrRes[] = $customItem;
            }
        }

        //格式化处理图片
        foreach ($arrRes as $key => $item) {
            $iconUrl = $item['iconUrl'];
            //$arrRes[$key]['iconUrl'] =  strval(Hkzb_Util_FuDao::getFileUrl($iconUrl));
            $arrRes[$key]['iconUrl'] = $iconUrl;
        }

        return $arrRes;
    }

    /**
     * 获取课程分类板块自定义的课程信息
     * @param int $grade
     * @return array
     */
    public function getCourseTypeCustomCourse($grade = 0)
    {
        //精品课
        $iconUrl1 = 'http://img.zuoyebang.cc/zyb_d29ce314b45465bbab088026d264b4a4.jpg';
        //自定义：
        $iconUrl2 = 'http://img.zuoyebang.cc/zyb_5c7dba7ef13cc77a187018b9c81dcee3.jpg';

        //自定义跳转标签
        $superUrl = '';
        //从数据库获取数据
        $objSubjectCard      = new Hkzb_Ds_Fudao_SubjectCard();
        $indexCustomCardInfo = $objSubjectCard->getIndexCustomCardInfo($grade, $arrFields = array());
        $customItem          = array();
        if (!empty($indexCustomCardInfo)) {
            $courseTypeTitle    = isset($indexCustomCardInfo['title']) ? $indexCustomCardInfo['title'] : '';
            $courseTypeSubTitle = isset($indexCustomCardInfo['extData']['cardSubTitle']) ? $indexCustomCardInfo['extData']['cardSubTitle'] : '';
            $courseTypeDesc     = isset($indexCustomCardInfo['extData']['cardDesc']) ? $indexCustomCardInfo['extData']['cardDesc'] : '';
            $courseIdList       = !empty($indexCustomCardInfo['courseIds']) ? $indexCustomCardInfo['courseIds'] : array();
            //To-do待实现
            $jumpUrl            = isset($indexCustomCardInfo['extData']['jumpUrl']) ? $indexCustomCardInfo['extData']['jumpUrl'] : '';

            $customItem = array(
                'iconUrl'            => $iconUrl1,
                'type'               => Hkzb_Ds_Fudao_SubjectCard::CARD_TYPE_INDEX_CUSTOM,
                'courseTypeTitle'    => $courseTypeTitle,
                'courseTypeSubTitle' => $courseTypeSubTitle,
                'courseTypeDesc'     => $courseTypeDesc,
                'courseIdList'       => $courseIdList,
                //自定义跳转url情况
                'superscriptUrl'     => $superUrl,
                'jumpUrl'            => $jumpUrl,
            );

        }

        return $customItem;
    }

    public function getBzCourseTypeInfo($grade = 0)
    {

        $gradeBzInfo = array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE      => array(
                'iconUrl'            => 'http://img.zuoyebang.cc/zyb_d52f68d8ca2df09d291b83196e4832fd.jpg',
                'type'               => Hkzb_Ds_Fudao_Course::TYPE_PRIVATE,
                'courseTypeTitle'    => '专题课',
                'courseTypeSubTitle' => '专项巩固提高',
                'courseTypeDesc'     => '专项知识学习，重点、难点、考点专项突破，查缺补漏',
            ),
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG => array(
                'iconUrl'            => 'http://img.zuoyebang.cc/zyb_0ff95927736a09d570167107e8bbd5b5.jpg',
                'type'               => Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
                'courseTypeTitle'    => '长期班',
                'courseTypeSubTitle' => '系统全面学习',
                'courseTypeDesc'     => '体系化教学，与学校进度同步免费赠送纸质教材，享多项专属课程服务',
            ),

        );

        return $gradeBzInfo;
    }

    public function getGradeAndSubjectByCourseIdsLearnSeason($courseIds = array(), $learnSeason = '', $grade = 0, $type = false , $subject = 0)
    {
        $resArr = array(
            'gradeList'        => array(),
            'gradeSubjectList' => array(),
            'learnSeasonList'  => array(),
            'classTypeList'  => array(),
        );

        $objDsCourse      = new Hkzb_Ds_Fudao_Course();
        $gradeSubjectList = $objDsCourse->getGradeAndMoreGradeByCourseIdsLearnSeason($courseIds, $learnSeason, $grade, $type);

        if (empty($gradeSubjectList)) {
            Bd_Log::warning('数据为空:$gradeSubjectList');

            return $resArr;
        }

        $res = $this->formatGradeSubject($gradeSubjectList,$subject);
        if (empty($res)) {
            Bd_Log::warning('结果为空：formatGradeSubject->$res');

            return $resArr;
        }

        $resArr = array(
            'learnSeasonList'  => $res['learnSeasonList'],
            'gradeSubjectList' => $res['gradeSubjectList'],
            'gradeList'        => $res['gradeList'],
            'classTypeList'    => $res['classTypeList'],
        );

        return $resArr;
    }

    /**
     * 根据课程courseIdArr获取获取学季和科目列表
     * @param array $courseIdArr
     * @return array
     */

    public function getLearnSeasonAndSubjectByCourseIdArr($courseIdArr = array(),$needCache=true)
    {
        $resArr = array();
        $gradeSubjectList = array();
        if(empty($courseIdArr)){
            return $resArr;
        }
        $isInner = Hk_Util_Ip::isInnerIp();

        //缓存键
        $paramStr = json_encode($courseIdArr);
        $cacheKey = md5($paramStr);

        //不是内网走缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        if($needCache){

            $cacheData = $objMemcached->get($cacheKey);
            if(!empty($cacheData)){
                $cacheData = json_decode(utf8_encode($cacheData),true);
            }

            if(!empty($cacheData)){
                return $cacheData;
            }
        }

        //走数据库
        $courseInfoArr = $this->getCourseInfoArr($courseIdArr,array(),true);

        if(empty($courseInfoArr)){
            return $resArr;
        }

        foreach ($courseInfoArr as $courseInfo){
            $subject = isset($courseInfo['subject']) ? $courseInfo['subject'] : 0;
            $learnSeason = isset($courseInfo['learnSeason']) ? $courseInfo['learnSeason'] : 0;
            $moreGrade = isset($courseInfo['moreGrade']) ? $courseInfo['moreGrade'] : 0;

            $itemRes = array(
                'subject'     => $subject,
                'learnSeason' => $learnSeason,
                'moreGrade'   => $moreGrade,
            );

            $gradeSubjectList[] = $itemRes;
        }


        $formatInfo = $this->formatGradeSubject($gradeSubjectList);
        if (empty($formatInfo)) {
            Bd_Log::warning('结果为空：formatGradeSubject->$res');

            return $resArr;
        }

        $learnSeasonList = isset($formatInfo['learnSeasonList']) ? $formatInfo['learnSeasonList'] : array();
        $gradeSubjectList = isset($formatInfo['gradeSubjectList']) ? $formatInfo['gradeSubjectList'] : array();
        $gradeList = isset($formatInfo['gradeList']) ? $formatInfo['gradeList'] : array();

        $resArr = array(
            'learnSeasonList'  => $learnSeasonList,
            'gradeSubjectList' => $gradeSubjectList,
            'gradeList'        => $gradeList
        );

        //设置缓存
        $cacheData = json_encode($resArr);
        $objMemcached->set($cacheKey,$cacheData,86400);

        return $resArr;
    }


    /**
     * 格式化科目和课程列表
     * @param $gradeSubjectList
     * @return array
     */
    private function formatGradeSubject($gradeSubjectList,$filterSubject = 0)
    {
        if (empty($gradeSubjectList)) {
            return array();
        }

        $gradeTextMap    = Hkzb_Const_FudaoGradeMap::$gradeTextMap;
        $subjectTextMap  = Hkzb_Const_FudaoGradeMap::$subjectTextMap;
        $gradeMap        = Hkzb_Const_FudaoGradeMap::$GradeMap;
        $seasonIdMap     = Hkzb_Const_FudaoGradeMap::$seasonIdMap;
        $seasonNameIdMap = array_flip($seasonIdMap);
        //过滤掉学部
        foreach ($gradeTextMap as $grade => $name) {
            if (in_array($grade, array(11, 50))) {
                unset($gradeTextMap[$grade]);
            }
        }

        $gradeKeys = array_keys($gradeTextMap);

        $gradeArr        = array();
        $gradeMapArr     = array();
        $gradeSubjectMap = array();
        $learnSeasonArr  = array();
        $seasonIdMapArr  = array();
        //班型列表
        $classTypeList = array();

        //获取班型配置文件
        $getClassTypeMap = $this->getClassTypeMap();

        foreach ($gradeSubjectList as $gsItem) {
            $subject   = $gsItem['subject'];

            //过滤掉不需要的科目班型
            if($filterSubject > 0 && $filterSubject != $subject ){
                continue;
            }

            $moreGrade = $gsItem['moreGrade'];
            //班型
            $classTypeId = isset($gsItem['classType']) ? intval($gsItem['classType']) : -1;

            $subjectName = isset($subjectTextMap[$subject]) ? $subjectTextMap[$subject] : '';
            $learnSeason = $gsItem['learnSeason'];
            $seasonId    = isset($seasonNameIdMap[$learnSeason]) ? $seasonNameIdMap[$learnSeason] : '';

            if (!isset($seasonIdMapArr[$seasonId])) {
                $seasonIdMapArr[$seasonId] = 1;
                if (!empty($seasonId)) {
                    $learnSeasonArr[$seasonId] = $learnSeason;
                }

            }

            foreach ($gradeKeys as $grade) {
                if (($moreGrade & $gradeMap[$grade]) == $gradeMap[$grade]) {
                    if (!isset($gradeMapArr[$grade])) {
                        $gradeMapArr[$grade] = 1;
                        $gradeName           = isset($gradeTextMap[$grade]) ? $gradeTextMap[$grade] : '';
                        if (!empty($gradeName)) {
                            $gradeArr[$grade] = $gradeName;
                        }
                    }
                }
            }

            //获取科目列表
            if(!isset($gradeSubjectMap[$subject])){
                $gradeSubjectMap[$subject] = $subjectName;
            }

            //获取班型列表
            if($classTypeId >=0 && !isset($classTypeList[$classTypeId]) && isset($getClassTypeMap[$classTypeId])){

                $classTypeList[$classTypeId] = $getClassTypeMap[$classTypeId];
            }

        }

        $arrRes = array(
            'gradeSubjectList' => $gradeSubjectMap,
            'learnSeasonList'  => $learnSeasonArr,
            'gradeList'        => $gradeArr,
            'classTypeList'    => $classTypeList,
        );

        return $arrRes;
    }

    private function getClassTypeMap()
    {
        $classTypeMap = array();
        //跨模块调用接口
        $arrHeader = array(
            'cookie' => $_COOKIE,
            'pathinfo' => '/miscourse/api/getclasstypenameapi',
            'referer' => ''
        );

        $arrParams['sid'] = 'ae7d4a6ed6a47114c7469f7a823401e1';

        $ret = ral('miscourse', 'POST', $arrParams, mt_rand(), $arrHeader);

        $res = json_decode($ret,true);
        if (empty($res) || $res['errNo'] > 0) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service XXX connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return $classTypeMap;
        }

        $data = isset($res['data']) ? $res['data'] : array();
        $classTypeMap = $data;

        return $classTypeMap;
    }

    /**
     * 获取首页课程分类课程列表
     * @param     $seasonId
     * @param     $type
     * @param     $grade
     * @param     $subject
     * @param     $newSeason
     * @param     $canPreSale
     * @param int $offset
     * @param int $limit
     * @param int $orderType
     * @param int $order
     * @return array|bool|mixed
     */
    public function getIndexTypeCourseList($seasonId, $courseIdArr = array(), $type, $grade, $subject, $newSeason, $canPreSale, $offset = 0, $limit = 0, $orderType = 0, $order = 0)
    {
        //是否内部ip
        $isInner = 0;
        if (Hk_Util_Ip::isInnerIp()) {
            $isInner = 1;
        }

        $strParam = "$seasonId $type $grade $subject $newSeason $canPreSale $offset $limit $isInner $orderType $order";
        $cacheKey = 'zhiboke_ds_advanced_courselist_getCourseListBySeasonId_' . md5($strParam);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue   = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $courseList = json_decode(utf8_encode($cacheValue), true);

            return $courseList;
        }
        //班课走新接口
        if($type == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
            $zbDakInterface = new Zb_Core_Ds_Dak_Interface();
            $productInfo    = $zbDakInterface->getBSubjectSeasonSkuIdList($grade, $subject, $seasonId, $offset, $limit, true, $isInner);
            if ($productInfo === false) {
                Bd_Log::warning("getBSubjectSeasonSkuIdList fail, grade:$grade, subject:$subject, seasonId:$seasonId, offset:$offset, limit:$limit");

                return false;
            }
            $productIdList = $productInfo['skuIdList'];
            $cacheValue    = json_encode($productIdList);
            $objMemcached->set($cacheKey, $cacheValue, 60);

            return $productIdList;
        }

        if ($newSeason == 1) {
            $seasonConds = Hkzb_Const_FudaoGradeMap::$seasonIdNameMap;
        }
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'isShow' => 1,
        );

        $validType = array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE,
            Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG,
        );

        if (in_array($type, $validType)) {
            $arrConds['type'] = $type;
        }

        if ($seasonId > 0 && isset($seasonConds[$seasonId])) {
            $arrConds[] = 'learn_season in (' . $seasonConds[$seasonId] . ')';
        }

        if (in_array($type, Hkzb_Ds_Fudao_SubjectCard::$SPECIALTYPEARR)) {
            $grade = 0;
        }

        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }
        if ($subject > 0) {
            $arrConds['subject'] = $subject;
            //特殊处理3654,5852
            if (in_array($subject, array(1, 2, 3))) {
                unset($arrConds['subject']);
                $arrConds[] = 'subject =' . $subject . ' or course_id in (5852,10452,10453,10555)';
            }

        }
        //报名开始时间
        if ($canPreSale <= 0) {
            $arrConds['registerStartTime'] = array(time(), '<');
        }

        //报名截止时间
        $arrConds['registerStopTime'] = array(time(), '>');

        //是否过滤内部课程
        if ($isInner <= 0) {
            $arrConds['`inner`'] = 0;
        }

        if (!empty($courseIdArr)) {
            $arrConds[] = 'course_id in (' . join(',', $courseIdArr) . ')';
        }

        //获取打包课和非打包课
        $arrConds[] = 'pack in (0,1) ';
        //排序规则
        if ($orderType > 0) {
            $orderType    = self::$orderTypeMap[$orderType];
            $order        = self::$orderMap[$order];
            $arrAppends[] = "order by $orderType $order";
        } else {
            $arrAppends = array(
                'order by current_lesson_time asc ',
            );
        }

        //限制规则
        if ($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'), null, $arrAppends);
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[arrConds:" . json_encode($arrConds) . "]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        //写缓存
        $cacheValue = json_encode($courseList);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $courseList;
    }

    /**
     * 报前测信息取开课早的courseId
     * @param $courseInfo
     * @return int
     */
    public static function getCourseIdForExam($courseInfo)
    {
        return $courseInfo['productId'] ? $courseInfo['productId'] : $courseInfo['courseId'];
        /*
        if (!isset($courseInfo['skuType']) || $courseInfo['skuType'] != Hkzb_Ds_Fudao_Product::SKU_TYPE_COMPOSITE_PRODUCT || !isset($courseInfo['extInfo'])) {
            return $courseInfo['courseId'];
        }
        $startTime = 0;
        $courseId = 0;
        foreach ($courseInfo['extInfo'] as $keyCourseId => $extInfo) {
            foreach ($extInfo['extLesson']['allLesson'] as $lesson) {
                if (!$startTime || $startTime > $lesson['startTime']) {
                    $startTime = $lesson['startTime'];
                    $courseId = $keyCourseId;
                }
            }
        }
        return intval($courseId);
        */
    }

    //寒季班开始，处理用户报前测试逻辑
    public static function getStudentCourseBeforeExamInfo($studentUid, $courseInfo)
    {
        $arrRes = array(
            'testStatus' => 0,
            'testRes'    => array(),
            'examUrl'    => ''
        );
        if (empty($courseInfo)) {
            Bd_Log::warning("param is empty uid:{$studentUid},courseInfo:" . json_encode($courseInfo));

            return $arrRes;
        }

        $courseId = self::getCourseIdForExam($courseInfo);
        //专题课不需要测试
        if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
            return $arrRes;
        }

        //预售阶段不能测试
        $now = Hkzb_Util_FuDao::getCurrentTimeStamp();
        if ($now < $courseInfo['registerStartTime']) {
            return $arrRes;
        }

        //获取用户的状态和课程的状态
        $userCourseStatus = self::getUserCourseStatus($studentUid, $courseInfo);

        //课前测试，报名结束，结课，不需要测试
        $registerStop = $userCourseStatus['registerStop'];
        $isStop       = $userCourseStatus['isStop'];
        $isFull       = $userCourseStatus['isFull'];
        $isReg        = $userCourseStatus['isReg'];

        if ($registerStop || $isStop || $isReg || $isFull) {
            return $arrRes;
        }

        //获取用户的试卷情况
        $objDsExamApi    = new Hkzb_Ds_Fudao_Exam_ExamApi();
        $studentExamInfo = $objDsExamApi->getBeforeRegExam($courseId, $studentUid);
        if(empty($studentExamInfo)){
            return $arrRes;
        }
        //如果是报后测试，考试通过
        $beforeExmType = Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE;
        if ($studentExamInfo['examType'] != $beforeExmType) {
            return $arrRes;
        }
        $needExam        = $studentExamInfo['needExam'];
        //不需要测试
        if (!$needExam) {
            return $arrRes;
        }
        //未登录，需要测试，
        if ($studentUid <= 0) {
            $arrRes['testStatus'] = 1;

            return $arrRes;
        }
        $leftExamNum = $studentExamInfo['leftExamNum'];
        $isPass      = $studentExamInfo['isPass'];
        if ($isPass) {
            //考试通过
            $arrRes['testStatus'] = 3;

            return $arrRes;
        }

        //考试没有通过
        if ($leftExamNum <= 0) {
            $arrRes['testStatus'] = 2;

            return $arrRes;
        }
        $examId               = isset($studentExamInfo['examId']) ? $studentExamInfo['examId'] : 0;
        $examUrl              = $studentExamInfo['url'];
        $testStatus           = 1;//需要考试
        $arrRes['testStatus'] = $testStatus;
        $arrRes['examUrl']    = $examUrl;
        $arrRes['testRes']    = array(
            'testLeftCnt' => $leftExamNum,
            'examId'      => $examId,
        );

        return $arrRes;
    }

    /**
     * 获取课程小班课状态
     * @param $courseId
     */
    public function getLittleClassStatus($courseId)
    {
        if (empty($courseId)) {
            Bd_Log::warning("Error:courseId is empty");
        }
        $objDscourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo  = $objDscourse->getCourseInfo($courseId);

        if ($courseInfo['pack'] == Hkzb_Ds_Fudao_Advanced_Course::PACK_YESD) {
            $packCourseIdArr = isset($courseInfo['extData']['packId']) ? $courseInfo['extData']['packId'] : array();
            if (empty($packCourseIdArr)) {
                Bd_Log::warning("Error:packCourseId is empty");

                return false;
            }

            foreach ($packCourseIdArr as $packCourseId) {
                $courseId = $packCourseId;
            }
        }
        $res = false;
        if (in_array($courseId, Hkzb_Ds_Fudao_Advanced_Course::$LITTLE_CLASS_COURSEID)) {
            $res = true;
        }

        return $res;

    }
    
    /**
     * 获取文明公告
     * @param int $uid
     * @return
     */
    public function getconvention($uid){
        if (intval($uid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid]");
            return false;
        }
        if (self::CONVENTIONSWITH == 1) {

//            $objRedis = Hk_Service_RedisClient::getInstance("zbcourse");
            $objRedis = Liveservice_Util_StoredService::getZhiboInstance();
            $conventionKey = 'COURSE_ONLINE_CONVENTION_CIVILIZATION_' . $uid;
            $convention = $objRedis->get($conventionKey);
            $conventionSwitch = 1;
            if (!empty($convention) && $convention == 1) {
                $conventionSwitch = 0;
            }
            $conventionTxt = self::get_conventionTxt();
            return array(
                'conventionAllSwitch' => self::CONVENTIONSWITH,
                'conventionSwitch' => $conventionSwitch,
                'conventionTxt' => $conventionTxt,
            );
        }
        
        return array(
            'conventionAllSwitch' => 0,
            'conventionSwitch' => 0,
            'conventionTxt' => '',
        );
    }
    
    public static function get_conventionTxt() {
        return "<p>各位同学，欢迎进入直播课堂。</p>
<p>为了更好的营造课堂环境，请所有的同学们遵守六项规定：</p>
<p>「第一」：禁止使用包含有国家和地区侮辱性质的字、词语及图片；</p>
<p>「第二」：禁止使用包含违反国家规定的、禁止使用的字、词语及图片；</p>
<p>「第三」：禁止使用包含辱骂、侮辱他人性质的字、词语及图片；</p>
<p>「第四」：禁止使用包含涉及黄、赌、毒的字、词语及图片；</p>
<p>「第五」：禁止使用包含网络广告性质的字、词语及图片；</p>
<p>「第六」：禁止不正当政治言论；</p>
<p> 同学们请注意（敲黑板）：</p>
<p><u>违反以上规范，一经查处，可能受到删除留言、禁言、扣罚学分、封停等相关操作。</u></p>
<p>最后，祝大家学习进步、生活愉快！感谢家长及同学长期以来的支持和理解。</p>";
    }
    /**
     * 获取标记开关
     * @param int $uid
     * @return
     */
    public function getSignSwitch($courseId,$uid = 0){
        $options    = array('extLesson');
        $courseInfo = $this->getCourseInfo($courseId, $options);
        if (false === $courseInfo) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            return 0;
        }
        /* $courseArr = array(40743,40744,40734,40733,39809,39808,38560,38370,38170,37373,35369,40596,43921,42220,35916,35876,43450,35806,35863);
        if ( !in_array($courseId, $courseArr) ) {
            return 0;
        } */
        $grade = $courseInfo['grade'];
        $gradeList = Hk_Util_Category::$GRADEMAPXB;
        if(in_array($gradeList[$grade], array(Hkzb_Const_FudaoGradeMap::ONEMOREGRADE, Hkzb_Const_FudaoGradeMap::FOURMOREGRADE))){ //小学 学前班
            return 0;
        }
        if ($uid <> 0) {
            //判断是否为复合商品
            if(isset($courseInfo['skuType']) && $courseInfo['skuType'] == Hkzb_Ds_Fudao_Product::SKU_TYPE_COMPOSITE_PRODUCT) {
                $isCompositeProduct = 1;
            }
            //获取课节列表
            if($isCompositeProduct ==1){
                $lessonList = array();
                foreach ($courseInfo['extInfo'] as $item) {
                    $lessonList = array_merge($lessonList, $item['extLesson']['allLesson']);
                }
            }else{
                $lessonList = $courseInfo['extInfo']['extLesson']['allLesson'];
            }
            
            $num = 0;
            $arr = array();
            $nowtime = time();
            $lessonstop = 0;
            if (!empty($lessonList)) {
                $_objStudentLesson = new Hkzb_Ds_Fudao_Advanced_StudentLesson();
                foreach ($lessonList as $k => $v) {
                    $num ++;
                    if ($v['stopTime'] > $nowtime) {
                        $lessonstop = 1;
                    };
                    $studentInfo = $_objStudentLesson->getStudentLesson($uid, $v['lessonId'], array('videoSignNote'));
                    $videoSignNote = ! empty($studentInfo['videoSignNote']['SignNote']) ? $studentInfo['videoSignNote']['SignNote'] : array();
                    if(empty($videoSignNote)){
                        unset($lessonList[$k]);
                        continue;
                    }
                    $tmp['lessonId'] = $v['lessonId'];
                    $tmp['lessonName'] = '第' . $num . '章';
                    $arr[] = $tmp;
                }
            }
            if($lessonstop == 1){
                $lessonstop = count($arr) - 1;
            }
            $lessonId = empty($arr[$lessonstop]['lessonId']) ? 0 : $arr[$lessonstop]['lessonId'];
            if( empty($lessonId) ){
                return false;
            }
            $arrOutput['lessonId'] = $lessonId;
            $arrOutput['lessonlist'] = $arr;
            return $arrOutput;
        } else {
            return 1;
        }
    }
    
    /**
     * 获取多人连麦弹框开关
     * @param int $uid
     * @return
     */
    public function getPlayerBombSwitch($courseId){
        return 0;
        $options    = array('extLesson');
        $courseInfo = $this->getCourseInfo($courseId, $options);
        if (false === $courseInfo) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            return 0;
        }
        $grade = $courseInfo['grade'];
        $playerBombSwitch = 1;
        $gradeList = Hk_Util_Category::$GRADEMAPXB;
        if($gradeList[$grade] == 1){ //小学
            $playerBombSwitch = 0;
        }
        return $playerBombSwitch;
    }
    
    
    public function getCourseInfoByDal($courseId){
        $arrCourseFields[] = "mainGradeId";
        $arrCourseFields[] = "mainSubjectId";
        $arrCourseFields[] = "courseType";
        $request = Zb_Service_Dal_Course::getKVByCourseId(array($courseId),$arrCourseFields,array());
        if (empty($request['data'])) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]".json_encode($request));
            return array();
        }
        $arr = array(
            'grade'=>$request['data'][$courseId]['mainGradeId'],
            'subject'=>$request['data'][$courseId]['mainSubjectId'],
            'type'=>$request['data'][$courseId]['courseType'],
        );
        return $arr;
    }
    /**
     * 小学数学判断是否启用新资源
     * @param int $uid
     * @return
     */
    public function studentLittleMathSwitch($courseId){
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            return 0;
        }
        
        $witch = Hkzb_Service_JmpRouter::checkCourse($courseId);
        if ($witch) {
            return 1;
        }
        return 0;
    }
    /**
     * 小学数学签到开关
     * @param int $uid
     * @return
     */
    public function studentCourseSignLittleMath($courseId,$os = 'stuwin'){
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            return 0;
        }
        
        $courseInfo = $this->getCourseInfoByDal($courseId);
        if(empty($courseInfo)){
            return 0;
        }
        $grade = $courseInfo['grade'];
        $subject = $courseInfo['subject'];
        $type = $courseInfo['type'];
        if ( $type != self::TYPE_PRIVATE_LONG ) {//非班课班课无签到
            return 0;
        }
        $witch = Hkzb_Service_JmpRouter::checkCourse($courseId);
        if ($witch) {
            return 1;
        }
        return 0;
    }
    
    
    /**
     * 小学数学判断有无签到
     * @param int $uid
     * @return
     */
    public function studentCourseSignLittleMathPack($courseId,$os = 'stuwin'){
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            return 0;
        }
        $courseInfo = $this->getCourseInfoByDal($courseId);
        if(empty($courseInfo)){
            return 0;
        }
        $type = $courseInfo['type'];
        $witch = Hkzb_Service_JmpRouter::checkCourse($courseId);
        if ($witch && $type != self::TYPE_PRIVATE_LONG) {
            return 0;
        }
        return 1;
    }
    
}
