<?php
/**
 * @file Trade.php
 * <AUTHOR>
 * @date 2016年9月19日 下午4:00:59
 * @version $Revision$
 * @brief 用户报名流程ds
 *
 **/
class Hkzb_Ds_Fudao_Advanced_Trade
{
    private $_objDsCourse;
    private $_objDsTradeRecord;
    private $_objZbCoreDsDakInterface;
    private $_objDsStudent;
    private $_objDsUcloud;
    private $_objDsStudentCourse;
    private $_objDsAdvancedStudentCourse;
    private $_objDsLesson;
    private $_objDsLessonDetail;
    private $_objDaoLessonDetail;
    private $_objDsLessonState;
    private $_objDaoLessonState;
    private $_objDsLessonAllState;
    private $_objDaoLessonAllState;
    private $_objDsGiveCourse;
    private $_objDsTeacherCourse;
    private $_objDsTeacher;
    private $_objDsExercise;
    private $_objDsExerciseDetail;
    private $_objDaoStudentCourse;
    private $_objDsAdvancedCourse;
    private $_objDsCourseAttribute;
    private $_objZbAdvancedInterface;

    private static $studentInfo = array();
    private static $userInfo    = array();

    public function __construct()
    {
        $this->_objDsCourse = new Hkzb_Ds_Fudao_Course();
        $this->_objDsTradeRecord = new Hkzb_Ds_Fudao_TradeRecord();
        $this->_objZbCoreDsDakInterface = new Zb_Core_Ds_Dak_Interface();
        $this->_objDsStudent = new Hkzb_Ds_Fudao_Student();
        $this->_objDsUcloud = new Hk_Ds_User_Ucloud();
        $this->_objDsStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
        $this->_objDsAdvancedStudentCourse = new Hkzb_Ds_Fudao_Advanced_StudentCourse();
        $this->_objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $this->_objDsLessonDetail = new Hkzb_Ds_Fudao_LessonDetail();
        $this->_objDaoLessonDetail = new Hkzb_Dao_Fudao_LessonDetail();
        $this->_objDsLessonState = new Hkzb_Ds_Fudao_LessonState();
        $this->_objDaoLessonState = new Hkzb_Dao_Fudao_LessonState();
        $this->_objDsLessonAllState = new Hkzb_Ds_Fudao_LessonAllState();
        $this->_objDaoLessonAllState = new Hkzb_Dao_Fudao_LessonAllState();
        $this->_objDsGiveCourse = new Hkzb_Ds_Fudao_GiveCourse();
        $this->_objDsTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $this->_objDsTeacher = new Hkzb_Ds_Fudao_Teacher();
        $this->_objDsExercise = new Hkzb_Ds_Fudao_Exercise();
        $this->_objDsExerciseDetail = new Hkzb_Ds_Fudao_ExerciseDetail();
        $this->_objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $this->_objDsShoppingCart = new Hkzb_Ds_Fudao_ShoppingCart();
        $this->_objDsAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        $this->_objDsCourseAttribute = new Hkzb_Ds_Fudao_CourseAttribute();
        $this->_objZbAdvancedInterface = new Zb_Advanced_Interface();
        $this->now = Hkzb_Util_FuDao::getCurrentTimeStamp();
    }

    /**
     * 立即加课
     *
     * @param array $arrParams
     *     <pre>
     *     array(
     *     'uid' => 123, //（必须）用户uid
     *     'courseId' => 234, //（必须）课程id
     *     'assistantUid' => 345, //（必须）辅导员uid
     *     'couponId' => 'abc', //优惠券id
     *     )
     *     </pre>
     */
    public function add($arrParams)
    {
        $arrParams['addFlag'] = true;
        $arrParams            = $this->_tradePreProc($arrParams);
        $arrParams            = $this->_tradeUseCoupon($arrParams);
        $arrParams            = $this->_tradeProcOrder($arrParams);
        $arrRes               = array(
            'data' => array(
                'orderId' => $arrParams['orderId'],
            ),
        );
        //$arrRes = $this->enter($arrParams['orderId']);
        //if ($arrRes['errNo'] != 0) {
        //    throw new Hk_Util_Exception($arrRes['errNo'], '', array('orderId' => $arrParams['orderId']));
        //}

        /**
         * 同步订单数据到新系统Trade模块
         **/
        $tradeParamInfo = array(
            //'payChannel'    => $arrParams['payChannel'],
            'uid'        => $arrParams['uid'],
            'jumpFrom'   => $arrParams['jumpFrom'],
            'vc'         => $arrParams['vc'],
            'vcname'     => $arrParams['vcname'],
            'plat'       => $arrParams['plat'],
            'lastfrom'     => $arrParams['lastfrom'],
            'orifrom'     => $arrParams['orifrom'],
            'logpath'     => $arrParams['logpath'],
            'cuid'       => $arrParams['cuid'],
            'appType'    => $arrParams['appType'],
            'phone'      => $arrParams['phone'],
            'ua'         => strval(substr($_SERVER['HTTP_USER_AGENT'], 0, 200)),
            //'appId'         => $arrParams['appId'],
            //'channel'       => $arrParams['channel'],
            //'os'            => $arrParams['os'],
            'courseList' => array(
                array(
                    'courseId' => $arrParams['courseId'],
                    'type'     => 1,
                    'couponId' => $arrParams['couponId'],
                ),
            ),
            //0元购课流水ID是0
            //'purchaseId'    => $arrParams['orderId'],
            'purchaseId' => 0,
            'tradeId'    => $arrParams['orderId'],
        );

        $tradeCourseInfo[$arrParams['courseId']] = array(
            'assistantUid' => $arrParams['assistantInfo']['assistantUid'],
            'teacherUid'   => $arrParams['courseInfo']['teacherUid'],
            'learnSeason'  => $arrParams['courseInfo']['learnSeason'],
            'grade'        => $arrParams['courseInfo']['grade'],
            'subject'      => $arrParams['courseInfo']['subject'],
            'orderId'      => $arrParams['orderId'],   //老接口,只有一条订单数据(流水Id=子订单Id,父订单Id=0)
        );

        $tradeParamInfo['tradeParam'] = $tradeCourseInfo;

        $this->_orderWriteToTrade($tradeParamInfo);

        return $arrRes;
    }

    /**
     * 立即报名
     *
     * @param array $arrParams ，
     *                         <pre>
     *                         array(
     *                         'uid' => 123, //（必须）用户uid
     *                         'courseId' => 234, //（必须）课程id
     *                         'assistantUid' => 345, //（必须）辅导员uid
     *                         )
     *                         </pre>
     */
    public function purchase($arrParams)
    {
        $arrParams = $this->_tradePreProc($arrParams);
        $arrParams['couponList'] = $this->_tradeGetMatchCouponList($arrParams);

        return $arrParams;
    }

    /**
     * @param $arrParams
     *
     * @desc 购物车立即报名
     * @return array|mixed
     */
    public function cartPurchase($arrParams)
    {
        $arrParams = $this->_cartTradePreProc($arrParams);
        $arrParams['courseList'] = self::getCouponStrategy($arrParams['courseList']);
        if (isset($arrParams['boundCidList'])) {
            $arrParams['courseList'] = $this->_tradeFormatBoundCourse($arrParams['courseList'], $arrParams['boundCidList'], $arrParams['uid']);
            unset($arrParams['boundCidList']);
        } elseif (isset($arrParams['summerMulSubCourseList'])) {
            $arrParams['courseList'] = $this->_tradeFormatBoundCourse($arrParams['courseList'], $arrParams['summerMulSubCourseList'], $arrParams['uid']);
            unset($arrParams['summerMulSubCourseList']);
            unset($arrParams['summerMulSubList']);
        } else {
            foreach ($arrParams['courseList'] as &$course) {
                if (isset($course['discountDesc']) && !empty($course['discountDesc'])) {
                    $course['boundCourseTag'] = '优惠';
                    $course['boundCourseDesc'] = $course['discountDesc'];
                }
            }
        }

        return $arrParams;
    }

    /**
     * @param $courseList
     *
     * @desc //获取最佳优惠券使用方式（从获得最大优惠金额角度考虑，不考虑同等优惠下时多种优惠券组合的情况）
     * @desc 获取最佳优惠券使用方式，穷举计算量过大，按课程价格由高到低依次取最佳优惠券，类似贪心算法
     * @return array
     */
    private function getCouponStrategy($courseList)
    {
        $tmp_price_sort = array();
        foreach ($courseList as $course) {
            $tmp_price_sort[] = $course['price'];
        }
        array_multisort($tmp_price_sort, SORT_DESC, SORT_NUMERIC, $courseList);
        $arrCouponUsed = array();
        foreach ($courseList as &$course) {
            unset($course['courseInfo']);
            if (!empty($course['couponList'])) {
                foreach ($course['couponList'] as $coupon) {
                    if (!in_array($coupon['couponId'], $arrCouponUsed)) {
                        $course['couponId'] = Hk_Util_IdCrypt::decodeQid($coupon['couponId']);
                        $course = $this->_tradeUseCoupon($course);
                        $course['couponId'] = $coupon['couponId'];
                        $arrCouponUsed[] = $coupon['couponId'];
                        break;
                    } else {
                        $course['couponId'] = '';
                    }
                }
            } else {
                $course['couponId'] = '';
            }
        }

        return $courseList;
    }

    /**
     * 立即支付
     *
     * @param array $arrParams
     *     <pre>
     *     array(
     *     'uid' => 123, //（必须）用户uid
     *     'courseId' => 234, //（必须）课程id
     *     'assistantUid' => 345, //（必须）辅导员uid
     *     'couponId' => 'abc', //优惠券id
     *     'payType' => 0, //0非扫码，1扫码
     *     )
     *     </pre>
     */
    public function pay($arrParams)
    {
        $arrParams = $this->_tradePreProc($arrParams);
        $arrParams = $this->_tradeUseCoupon($arrParams);
        $arrParams['couponList'] = $this->_tradeGetMatchCouponList($arrParams);
        $arrParams = $this->_tradeProcOrder($arrParams);

        /**
         * 同步订单数据到新系统Trade模块
         **/
        $payType        = intval($arrParams['payType']);
        $tradeParamInfo = array(
            //'payChannel'    => $arrParams['payChannel'],
            'uid'           => $arrParams['uid'],
            'phone'         => $arrParams['phone'],
            'jumpFrom'      => $arrParams['jumpFrom'],
            'vc'            => $arrParams['vc'],
            'vcname'        => $arrParams['vcname'],
            'cuid'          => $arrParams['cuid'],
            'appType'       => $arrParams['appType'],
            'ua'            => strval(substr($_SERVER['HTTP_USER_AGENT'], 0, 200)),
            //'appId'         => $arrParams['appId'],
            //'channel'       => $arrParams['channel'],
            //'os'            => $arrParams['os'],
            'courseList'    => array(
                array(
                    'courseId'   => $arrParams['courseId'],
                    'type'       => 1,
                    'couponId'   => $arrParams['couponId'],
                ),
            ),
            'purchaseId'    => $payType == 1 ? $arrParams['pcwxPayInfo']['orderId'] : $arrParams['payInfo']['orderId'],
            'plat' => $arrParams['plat'],
            'lastfrom' => $arrParams['lastfrom'],
            'orifrom' => $arrParams['orifrom'],
            'logpath' => $arrParams['logpath'],
        );

        $tradeCourseInfo[$arrParams['courseId']] = array(
            'assistantUid'      => $arrParams['assistantInfo']['assistantUid'],
            'teacherUid'        => $arrParams['courseInfo']['teacherUid'],
            'learnSeason'       => $arrParams['courseInfo']['learnSeason'],
            'grade'             => $arrParams['courseInfo']['grade'],
            'subject'           => $arrParams['courseInfo']['subject'],
            //老接口,只有一条订单数据(流水Id=子订单Id,父订单Id=0)
            'orderId'           => $payType == 1 ? $arrParams['pcwxPayInfo']['orderId'] : $arrParams['payInfo']['orderId'],
        );

        $tradeParamInfo['tradeParam'] = $tradeCourseInfo;

        $this->_orderWriteToTrade($tradeParamInfo);


        return $arrParams;
    }

    public function cartPay($arrParams, $arrJumpFrom)
    {
        //新系统参数
        $tradeParamInfo = array(
            'payChannel'    => $arrParams['payChannel'],
            'jumpFrom'      => $arrParams['jumpFrom'],
            'vc'            => $arrParams['vc'],
            'vcname'        => $arrParams['vcname'],
            'cuid'          => $arrParams['cuid'],
            'appType'       => $arrParams['appType'],
            'appId'         => $arrParams['appId'],
            'channel'       => $arrParams['channel'],
            'os'            => $arrParams['os'],
            'courseList'    => $arrParams['courseList'],
            'uid'           => $arrParams['uid'],
            'ua'            => strval(substr($_SERVER['HTTP_USER_AGENT'], 0, 200)),
            'phone'         => $arrParams['phone'],
        );
        if(isset($arrParams['plat'])){
            $tradeParamInfo['plat'] = $arrParams['plat'];
        }
        if(isset($arrParams['lastfrom'])){
            $tradeParamInfo['lastfrom'] = $arrParams['lastfrom'];
        }
        if(isset($arrParams['orifrom'])){
            $tradeParamInfo['orifrom'] = $arrParams['orifrom'];
        }
        if(isset($arrParams['logpath'])){
            $tradeParamInfo['logpath'] = $arrParams['logpath'];
        }

        $arrParams     = $this->_cartTradePreProc($arrParams);
        $arrUsedCoupon = array();
        foreach ($arrParams['courseList'] as &$course) {
            if (!in_array($course['couponId'], $arrUsedCoupon) || Hkzb_Util_FuDao::isSuperVipUser($arrParams['uid'])) {
                $course = $this->_tradeUseCoupon($course);
                if ($course['usedCoupon']) {
                    $arrUsedCoupon[] = $course['couponId'];
                }
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_COUPON_DUPLICATE);
            }
        }

        $arrParams = $this->_cartTradeProcOrder($arrParams, $arrJumpFrom);
        foreach ($arrParams['courseList'] as &$courseInfo) {
            $courseInfo['couponId'] = Hk_Util_IdCrypt::encodeQid($courseInfo['couponId']);
            unset($courseInfo['courseInfo']);
        }

        if (isset($arrParams['boundCidList'])) {
            $arrParams['courseList'] = $this->_tradeFormatBoundCourse($arrParams['courseList'], $arrParams['boundCidList'], $arrParams['uid']);
            unset($arrParams['boundCidList']);
        }

        /**
         * 同步订单数据到新系统Trade模块
         **/
        $tradeParamInfo['tradeParam'] = $arrParams['tradeParamInfo'];
        $tradeParamInfo['purchaseId'] = $arrParams['payInfo']['orderId'];
        $this->_orderWriteToTrade($tradeParamInfo);

        return $arrParams;
    }

    /**
     * @comment 数据同步到Trade模块 by ral
     * @param array     $arrParam
     *
     * @return bool
     **/
    public function _orderWriteToTrade($arrParams)
    {
        //moduleFrom=trade,即Trade同步调用course,不需要course再同步到Trade
        if ($arrParams['moduleFrom'] == 'trade') {
            return true;
        }

        $dataInfo               = array();
        $tradeParam             = $arrParams['tradeParam'];
        $reverseLearnSeason     = array_flip(Zb_Const_LearnSeason::$learnSeasonMap);

        if ($arrParams['dataType'] == 2) {
            //这里既有skuId又有CourseId
            $courseIdList   = array();
            $skuList        = array();
            $courseList     = array();
            foreach ($arrParams['skuList'] as $sku) {
                if ($sku['courseId'] > 1000000) {
                    $skuList[$sku['courseId']] = array(
                        'skuId'         => $sku['courseId'],
                        'couponId'      => $sku['couponId'],
                        'courseIdList'  => array(),
                    );
                } else {
                    $courseIdList[] = $sku['courseId'];
                    $courseList[$sku['courseId']] = $sku;
                }
            }

            //把courseId统一成skuId
            if (!empty($courseIdList)) {
                $objDsDakInterface = new Zb_Core_Ds_Dak_Interface();
                $courseSKUId = $objDsDakInterface->getSkuCourseIdListByCourseIds($courseIdList, false);
                if ($courseSKUId == false) {
                    Bd_Log::warning("trans_courseId_to_skuId_failed[". json_encode($courseIdList). "]");
                    return false;
                }

                foreach ($courseSKUId as $sku) {
                    $skuList[$sku['skuId']] = array(
                        'skuId'         => $sku['skuId'],
                        'couponId'      => $courseList[$sku['courseId']]['couponId'],
                        'courseIdList'  => array($sku['courseId']),
                    );
                }
            }

            foreach ($arrParams['courseList'] as $course) {
                if (!empty($course['skuId'])) {
                    $skuList[$course['skuId']]['courseIdList'][] = $course['courseId'];
                }
            }

            foreach ($skuList as $sku) {
                $tmp['skuId']       = $sku['skuId'];
                $tmp['couponId']    = Hk_Util_IdCrypt::encodeQid($sku['couponId']);
                $tmp['courseInfo']  = array();

                foreach ($sku['courseIdList'] as $courseId) {
                    $tmp['courseInfo'][] = array(
                        'courseId'      => $courseId,
                        'orderId'       => $tradeParam[$courseId]['orderId'],
                        'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                        'teacherUids'   => $tradeParam[$courseId]['teacherUid'],
                        'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['grade']],
                        'subject'       => $tradeParam[$courseId]['subject'],
                        'grade'         => $tradeParam[$courseId]['grade'],
                    );
                }

                $dataInfo[] = $tmp;
            }
        } else {
            foreach ($arrParams['courseList'] as $course) {
                if ($course['type'] == 2) {
                    foreach ($course['list'] as $item) {
                        $courseId   = $item['courseId'];
                        $dataInfo[] = array(
                            'courseId'      => $courseId,
                            'couponId'      => Hk_Util_IdCrypt::encodeQid($item['couponId']),
                            'orderId'       => $tradeParam[$courseId]['orderId'],
                            'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                            'teacherUids'   => strval($tradeParam[$courseId]['teacherUid']),
                            'subject'       => $tradeParam[$courseId]['subject'],
                            'grade'         => $tradeParam[$courseId]['grade'],
                            'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['grade']],
                        );
                    }
                } else {
                    $courseId   = $course['courseId'];
                    $dataInfo[] = array(
                        'courseId'      => $courseId,
                        'couponId'      => Hk_Util_IdCrypt::encodeQid($course['couponId']),
                        'orderId'       => $tradeParam[$courseId]['orderId'],
                        'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                        'teacherUids'   => strval($tradeParam[$courseId]['teacherUid']),
                        'subject'       => $tradeParam[$courseId]['subject'],
                        'grade'         => $tradeParam[$courseId]['grade'],
                        'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['learnSeason']],
                    );
                }
            }
        }

        $arrTradeParams = array(
            'groupKey'      => intval($arrParams['uid']),
            'payChannel'    => intval($arrParams['payChannel']),
            'jumpFrom'      => strval($arrParams['jumpFrom']),
            'os'            => strval($arrParams['os']),
            'moduleFrom'    => 'course',
            'dataInfo'      => json_encode($dataInfo),
            'dataType'      => empty($arrParams['dataType']) ? 1 : $arrParams['dataType'],
            'purchaseId'    => intval($arrParams['purchaseId']),
            'tradeId'       => empty($arrParams['tradeId']) ? intval($arrParams['purchaseId']) : intval($arrParams['tradeId']),
            'vc'            => strval($arrParams['vc']),
            'vcname'        => strval($arrParams['vcname']),
            'cuid'          => strval($arrParams['cuid']),
            'appChannel'    => strval($arrParams['channel']),
            'uid'           => intval($arrParams['uid']),
            'phone'         => strval($arrParams['phone']),
            'ua'            => strval($arrParams['ua']),
        );

        if ($arrParams['remark']) {
            $arrTradeParams['remark'] = strval($arrParams['remark']);
        }

        if ($arrParams['skipLaxin']) {
            $arrTradeParams['skipLaxin'] = intval($arrParams['skipLaxin']);
        }

        if ($arrTradeParams['os'] == 'pc') {
            $arrTradeParams['appChannel'] = strval($arrParams['jumpFrom']);
        }
        if ($arrParams['plat']){
            $arrTradeParams['plat'] = $arrParams['plat'];
        }
        if ($arrParams['lastfrom']){
            $arrTradeParams['lastfrom'] = $arrParams['lastfrom'];
        }
        if ($arrParams['orifrom']){
            $arrTradeParams['orifrom'] = $arrParams['orifrom'];
        }
        if ($arrParams['logpath']){
            $arrTradeParams['logpath'] = $arrParams['logpath'];
        }

        $ret = Zb_Service_Nmq::sendCommand(Zb_Const_Command::COMMAND_TRADE_170001, $arrTradeParams);

        if ($ret === false) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service trade_api_addorder connect error],Detail:[errno:$errno 
                             errmsg:$errmsg protocol_status:$protocol_status]");
            return false;
        }

        $errno = intval($ret['errno']);
        $errmsg = strval($ret['error_msg']);
        if ($errno > 0) {
            Bd_Log::warning("Error:[service trade_api_addorder process error],
                             Detail:[errno:$errno errmsg:$errmsg]");
            return false;
        }

        return true;
    }

    /**
     * @comment 获取course订单写入Trade的nmq参数(goods nmq通知trade写入数据库)
     * @param array     $arrParam
     *
     * @return bool
     **/
    public function getOrderWriteToTradeParam($arrParams)
    {

        $dataInfo               = array();
        $tradeParam             = $arrParams['tradeParam'];
        $reverseLearnSeason     = array_flip(Zb_Const_LearnSeason::$learnSeasonMap);

        if ($arrParams['dataType'] == 2) {
            //这里既有skuId又有CourseId
            $courseIdList   = array();
            $skuList        = array();
            $courseList     = array();
            foreach ($arrParams['skuList'] as $sku) {
                if ($sku['courseId'] > 1000000) {
                    $skuList[$sku['courseId']] = array(
                        'skuId'         => $sku['courseId'],
                        'couponId'      => $sku['couponId'],
                        'courseIdList'  => array(),
                    );
                } else {
                    $courseIdList[] = $sku['courseId'];
                    $courseList[$sku['courseId']] = $sku;
                }
            }

            //把courseId统一成skuId
            if (!empty($courseIdList)) {
                $objDsDakInterface = new Zb_Core_Ds_Dak_Interface();
                $courseSKUId = $objDsDakInterface->getSkuCourseIdListByCourseIds($courseIdList, false);
                if ($courseSKUId == false) {
                    Bd_Log::warning("trans_courseId_to_skuId_failed[". json_encode($courseIdList). "]");
                    return false;
                }

                foreach ($courseSKUId as $sku) {
                    $skuList[$sku['skuId']] = array(
                        'skuId'         => $sku['skuId'],
                        'couponId'      => $courseList[$sku['courseId']]['couponId'],
                        'courseIdList'  => array($sku['courseId']),
                    );
                }
            }

            foreach ($arrParams['courseList'] as $course) {
                if (!empty($course['skuId'])) {
                    $skuList[$course['skuId']]['courseIdList'][] = $course['courseId'];
                }
            }

            foreach ($skuList as $sku) {
                $tmp['skuId']       = $sku['skuId'];
                $tmp['couponId']    = Hk_Util_IdCrypt::encodeQid($sku['couponId']);
                $tmp['courseInfo']  = array();

                foreach ($sku['courseIdList'] as $courseId) {
                    $tmp['courseInfo'][] = array(
                        'courseId'      => $courseId,
                        'orderId'       => $tradeParam[$courseId]['orderId'],
                        'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                        'teacherUids'   => $tradeParam[$courseId]['teacherUid'],
                        'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['grade']],
                        'subject'       => $tradeParam[$courseId]['subject'],
                        'grade'         => $tradeParam[$courseId]['grade'],
                    );
                }

                $dataInfo[] = $tmp;
            }
        } else {
            foreach ($arrParams['courseList'] as $course) {
                if ($course['type'] == 2) {
                    foreach ($course['list'] as $item) {
                        $courseId   = $item['courseId'];
                        $dataInfo[] = array(
                            'courseId'      => $courseId,
                            'couponId'      => Hk_Util_IdCrypt::encodeQid($item['couponId']),
                            'orderId'       => $tradeParam[$courseId]['orderId'],
                            'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                            'teacherUids'   => strval($tradeParam[$courseId]['teacherUid']),
                            'subject'       => $tradeParam[$courseId]['subject'],
                            'grade'         => $tradeParam[$courseId]['grade'],
                            'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['grade']],
                        );
                    }
                } else {
                    $courseId   = $course['courseId'];
                    $dataInfo[] = array(
                        'courseId'      => $courseId,
                        'couponId'      => Hk_Util_IdCrypt::encodeQid($course['couponId']),
                        'orderId'       => $tradeParam[$courseId]['orderId'],
                        'assistantUid'  => $tradeParam[$courseId]['assistantUid'],
                        'teacherUids'   => strval($tradeParam[$courseId]['teacherUid']),
                        'subject'       => $tradeParam[$courseId]['subject'],
                        'grade'         => $tradeParam[$courseId]['grade'],
                        'learnSeason'   => $reverseLearnSeason[$tradeParam[$courseId]['learnSeason']],
                    );
                }
            }
        }

        $arrTradeParams = array(
            'groupKey'      => intval($arrParams['uid']),
            'payChannel'    => intval($arrParams['payChannel']),
            'jumpFrom'      => strval($arrParams['jumpFrom']),
            'plat'         => $arrParams['plat'],
            'lastfrom'    => $arrParams['lastfrom'],
            'orifrom'     => $arrParams['orifrom'],
            'logpath'     => $arrParams['logpath'],
            'os'            => strval($arrParams['os']),
            'dataInfo'      => json_encode($dataInfo),
            'dataType'      => empty($arrParams['dataType']) ? 1 : $arrParams['dataType'],
            'purchaseId'    => intval($arrParams['purchaseId']),
            'tradeId'       => empty($arrParams['tradeId']) ? intval($arrParams['purchaseId']) : intval($arrParams['tradeId']),
            'vc'            => strval($arrParams['vc']),
            'vcname'        => strval($arrParams['vcname']),
            'cuid'          => strval($arrParams['cuid']),
            'appChannel'    => strval($arrParams['channel']),
            'uid'           => intval($arrParams['uid']),
            'phone'         => strval($arrParams['phone']),
            'ua'            => strval($arrParams['ua']),
            'bizInfo'       => json_encode($arrParams['originalBizInfo']),
            'useCouponInfo' => json_encode($arrParams['useCouponInfo']),
            'addressInfo'   => json_encode($arrParams['addressInfo']),
            'giftCourseTradeRecord' => json_encode($arrParams['giftCourseTradeRecord']),
        );

        if ($arrParams['remark']) {
            $arrTradeParams['remark'] = strval($arrParams['remark']);
        }

        if ($arrParams['skipLaxin']) {
            $arrTradeParams['skipLaxin'] = intval($arrParams['skipLaxin']);
        }

        if ($arrTradeParams['os'] == 'pc') {
            $arrTradeParams['appChannel'] = strval($arrParams['jumpFrom']);
        }

        return $arrTradeParams;
    }

    /**
     * 购买流程数据预处理和相关检查
     *
     * @param array $arrParams
     */
    private function _tradePreProc($arrParams)
    {
        //数据获取并做相应检查，检查数据有依赖（$arrParams），检查顺序请不要随意调
        $uid = $arrParams['uid'];
        $courseId = $arrParams['courseId'];

        //检查用户是不是老师，老师不能购买课程
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($uid);
        if (false === $teacherInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        if (!empty($teacherInfo) && $teacherInfo['deleted'] != Hkzb_Ds_Fudao_Teacher::STATUS_DELETED) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_IS_TEACHER, '', array('uid' => $uid));
        }

        //获取课程信息
        $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
        if (false === $courseInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
        }
        $arrParams['courseInfo'] = $courseInfo;
        $arrParams['price'] = $courseInfo['price']; //支付页展示价格
        $arrParams['orderPrice'] = $courseInfo['price']; //实际订单需支付价格
        $arrParams['originalPrice'] = $courseInfo['price'];//购买时的原价，用于退款计算

        $allCourseInfo = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extLesson', 'extTeacher', 'extAssistant'), true);
        $arrParams['allCourseInfo'] = $allCourseInfo;
        //检查课程有效性
        $this->_tradeCheckCourseValidity($arrParams);
        //获取用户信息
        $studentInfo = $this->_objDsStudent->getStudentInfo($uid);
        if (false === $studentInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        $arrParams['studentInfo'] = $studentInfo;
        //检查学生能够购买此课程
        $this->_tradeCheckStudentCanBuy($arrParams);
        //检查辅导员信息有效性（专题课可能会自动分配辅导员）
        $arrParams['assistantInfo'] = $this->_tradeGetAssistantInfo($arrParams);

        //价格策略
        $arrParams = $this->_tradeDiscountStrategy($arrParams);
        $tradeInfo = array(
            'price' => $arrParams['price'],
            'type' => ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG ? $courseInfo['type'] : Hkzb_Ds_Fudao_Course::TYPE_PRIVATE),
            'season' => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
            'seasonId' => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
            'subject' => $courseInfo['subject'],
            'xuebu' => intval(Hk_Util_Category::$GRADEMAPXB[$courseInfo['grade']]),
            'grade' => intval($courseInfo['grade']),
            'moreGrade' => intval($courseInfo['moreGrade']),
            'courseId' => intval($courseId),
            'learnSeason' => strval($courseInfo['learnSeason']),
        );
        $arrParams['tradeInfo'] = $tradeInfo;

        return $arrParams;
    }

    /**
     * @param $arrParams
     *
     * @return mixed
     * @throws Hk_Util_Exception
     */
    private function _cartTradePreProc($arrParams)
    {
        $uid = $arrParams['uid'];
        $tradeCourseList = $arrParams['courseList'];
        $zbkvc = $arrParams['zbkvc'];
        $appType = $arrParams['appType'];
        //检查用户是不是老师，老师不能购买课程
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($uid);
        if (false === $teacherInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        if (!empty($teacherInfo) && $teacherInfo['deleted'] != Hkzb_Ds_Fudao_Teacher::STATUS_DELETED) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_IS_TEACHER, '', array('uid' => $uid));
        }
        //获取用户信息
        $studentInfo = $this->_objDsStudent->getStudentInfo($uid);
        if (false === $studentInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        $checkList = array();//课程列表
        $arrCourseIdList = array();//课程id列表
        $boundList = array();//关联课程组，做关联关系检查，目前有连报、联报优惠两类
        $boundCidList = array();//有绑定关系的课程id列表
        foreach ($tradeCourseList as $item) {
            //type=2，标识有关联关系的课程，目前组合课程（联报、连报）标为2，单门课程标识为1或无标识
            if (isset($item['type']) && $item['type'] == 2) {
                if (count($item['list']) < 2) {//关联课程至少应该有两门
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'invalid_bound_info');
                }
                foreach ($item['list'] as $c) {
                    $checkList[] = $c;//课程信息列表
                    $boundCidList[] = $c['courseId'];//关联课程id和角色映射
                    $arrCourseIdList[] = $c['courseId'];//课程id列表
                }
                $boundList[] = $item['list'];//关联课程组，做关联关系检查
            } else {
                $checkList[] = $item;
                $arrCourseIdList[] = $item['courseId'];
            }
        }
        //{{{判断是否有多科联报策略。目前课程组合售卖策略有多种，这里是确定命中哪种策略
        $sumMulSubDiscountInfo = Hkzb_Util_Fudao_Category::subjectRecommendDiscountInfo($arrCourseIdList, $uid);
        //低版本不支持多科优惠策略
        if ($appType == 'android') {
            if ($zbkvc < 10) {
                $sumMulSubDiscountInfo = false;
            }
        } elseif ($appType == 'ios') {
            if ($zbkvc <= 0) {
                $sumMulSubDiscountInfo = false;
            }
        }
        //如果确定有多科联报策略，走多课程联报检查逻辑，否者走绑定课检查逻辑；支付接口尚不同时支持（目前这类组合售卖的课程是直接跳支付页支付，暂时不会同时出现有多种组合策略的情况）多科联报策略和绑定策略，是可以支持的
        if ($sumMulSubDiscountInfo) {
            //课程时间是否冲突信息
            $summerCourseTimeValid = $sumMulSubDiscountInfo['summerCourseTimeValid'];
            //后端确认的有多科联报策略的课程信息及折扣信息
            $arrSumMulSubDiscount = $sumMulSubDiscountInfo['courseData'];
            //后端确认的有多科策略的课程id
            $arrSumCid = array_keys($arrSumMulSubDiscount);
            //获取参数中给出的有关联关系的课程列表
            $arrBoundCid = array();
            if (!empty($boundList)) {
                foreach ($boundList as $k => $v) {
                    foreach ($v as $i) {
                        $arrBoundCid[] = $i['courseId'];
                    }
                }
                //这里是做检验，判定参数中给出的关联课程信息是否是多科组合课程；
                //由于历史原因，boundList用于标识绑定课程组合，如果确定是多科组合，清空boundList
                if (!array_diff($arrSumCid, $arrBoundCid)) {
                    $boundList = array();
                    $boundCidList = array();
                } else {
                    //如果参数和后端检查关系不一致，抛异常
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR);
                    //当不一致时，如果这里对boundList分类，过滤掉有多科联报策略的课程，就可以得到有绑定策略的课程，就可以同时支持绑定课策略和多科联报策略了
                }
            }
            //确定课程组合为多科组合后，有时会有一些特殊的多科组合策略，这里做特殊处理
            if (count($arrSumCid) == 3) {
                $flag = 0;
                foreach (Hkzb_Util_Fudao_Category::$AUTUMN_TWO_FOR_ONE_COURSE_LIST as $item) {
                    foreach ($arrSumCid as $sumCid) {
                        if (in_array($sumCid, $item)) {
                            ++$flag;
                            continue 2;
                        }
                    }
                }
                if ($flag == 3) {
                    foreach ($arrSumCid as $sumCid) {
                        $arrSumMulSubDiscount[$sumCid]['hasSummerMulSubDiscount'] = 1;
                        $arrSumMulSubDiscount[$sumCid]['mulDiscountRuleSortTag'] = 201;
                        $arrSumMulSubDiscount[$sumCid]['summerMulSubDiscountRule'] = array(
                            "discountType" => Hkzb_Util_Fudao_Category::COURSE_SAIL_CATEGORY_DISCOUNT_TYPE_DISCOUNT,
                            "discountDesc" => '买二赠一，三门仅需399元',
                            "discount" => 67,
                        );
                    }
                }
            }
        } else {
            //如果不是多科组合策略，说明是绑定策略，走绑定组合策略
            $arrBoundInfo = array();
            $arrBoundAssistantInfo = array();
            if (!empty($boundList)) {
                $arrBoundCid = array();
                foreach ($boundList as $k => $v) {
                    foreach ($v as $i) {
                        $arrBoundCid[] = $i['courseId'];
                    }
                }
                $boundCourseDiscountInfo = Hkzb_Util_Fudao_Category::getBoundCourseDiscountInfo($arrBoundCid, $uid);
                if (!empty($boundCourseDiscountInfo)) {
                    $lastSeasonInfo = $boundCourseDiscountInfo['lastSeason'];
                    $curSeasonInfo = $boundCourseDiscountInfo['curSeason'];
                    //$arrBoundInfo['courseList'][intval($lastSeasonInfo['courseId'])] = $boundCourseDiscountInfo['lastSeason'];
                    //$arrBoundAssistantInfo[intval($lastSeasonInfo['courseId'])]      = $lastSeasonInfo['assistantInfo']['assistantUid'];
                    foreach ($lastSeasonInfo as $value) {
                        $arrBoundInfo['courseList'][intval($value['courseId'])] = $value;
                        $arrBoundAssistantInfo[intval($value['courseId'])] = $value['assistantInfo']['assistatnUid'];
                    }
                    foreach ($curSeasonInfo as $value) {
                        $arrBoundInfo['courseList'][intval($value['courseId'])] = $value;
                        $arrBoundAssistantInfo[intval($value['courseId'])] = $value['assistantInfo']['assistantUid'];
                    }
                    $boundGift = $boundCourseDiscountInfo['gift'];
                    if (!empty($boundGift)) {
                        $arrBoundInfo['gift'] = array(
                            'giftList' => $boundCourseDiscountInfo['gift'],
                            'giftRelatedCourse' => $arrBoundCid,
                        );
                    }
                }
            }
        }
        //}}}
        $courseList = array();//普通课程list
        $needAddress = 0;
        $addressInfo = array();
        $giftInfo = array();
        if (isset($arrBoundInfo) && !empty($arrBoundInfo['gift'])) {
            $giftInfo[] = $arrBoundInfo['gift'];
        }
        foreach ($checkList as $course) {
            $arrData = array();
            $courseId = $course['courseId'];
            if ($courseId > 1000000 || empty($courseId)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', $arrParams);
            }
            $arrData['courseId'] = $courseId;
            $arrData['assistantUid'] = isset($course['assistantUid']) ? intval($course['assistantUid']) : 0;
            if ($arrData['assistantUid'] <= 0) {
                if (isset($arrBoundAssistantInfo) && isset($arrBoundAssistantInfo[$courseId])) {
                    $arrData['assistantUid'] = $arrBoundAssistantInfo[$courseId];
                    Hk_Util_Log::setLog('old_version_patch_assistantUid_courseId_' . $courseId, 'assistantUid_' . $arrData['assistantUid']);
                }
            }
            $arrData['uid'] = $uid;
            $arrData['couponId'] = $course['couponId'];
            $arrData['role'] = 0;
            //如果课程有多课程优惠策略，写入优惠信息、规则、类别信息
            if (isset($arrSumMulSubDiscount) && isset($arrSumMulSubDiscount[$courseId])) {
                $arrData['hasSummerMulSubDiscount'] = $arrSumMulSubDiscount[$courseId]['hasSummerMulSubDiscount'];
                $arrData['summerMulSubDiscountRule'] = $arrSumMulSubDiscount[$courseId]['summerMulSubDiscountRule'];
                $arrData['mulDiscountRuleSortTag'] = $arrSumMulSubDiscount[$courseId]['mulDiscountRuleSortTag'];
            }
            //如果课程有连报优惠策略，写入优惠信息、规则
            if (isset($arrBoundInfo) && isset($arrBoundInfo['courseList'][$courseId])) {
                $arrData['boundDiscountStrategy'] = $arrBoundInfo['courseList'][$courseId]['boundDiscountStrategy'];
            }
            //获取课程信息
            $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
            if (false === $courseInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
            }
            //是否需要邮寄地址
            $needAddress = empty($needAddress) ? 0 : $needAddress;
            $addressInfo = empty($addressInfo) ? array() : $addressInfo;
            $courseAttributeInfo = $this->_objDsCourseAttribute->getCourseAttributeInfo($courseId, array('teachMaterial'));
            if ($courseAttributeInfo === false) {
                Bd_Log::warning("get courseattribute info fail, courseId:$courseId");
            } elseif (isset($courseAttributeInfo['teachMaterial']) && $courseAttributeInfo['teachMaterial']) {
                $needAddress = 1;
                if (!empty($studentInfo) && !empty($studentInfo['extData']['addressInfo'])) {
                    $addressInfo = $studentInfo['extData']['addressInfo'];
                }
            }

            $arrData['courseInfo']    = $courseInfo;
            $allCourseInfo            = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extLesson', 'extTeacher', 'extAssistant'), true);
            $arrData['allCourseInfo'] = $allCourseInfo;
            //检查课程有效性
            $this->_tradeCheckCourseValidity($arrData);
            $arrData['studentInfo'] = $studentInfo;
            //检查学生能够购买此课程
            $this->_tradeCheckStudentCanBuy($arrData);
            //课程信息
            $arrData['courseName'] = $courseInfo['courseName'];
            $arrData['onlineTime'] = Hkzb_Util_Fudao_Format::formatOnlineTime($allCourseInfo);
            $arrData['price'] = $courseInfo['price'];
            $arrData['orderPrice'] = $courseInfo['price'];
            $arrData['originalPrice'] = $courseInfo['price'];
            $arrData['service'] = '';//专属服务
            $arrData['grade'] = $courseInfo['grade'];
            //教师信息
            $teacherInfoList = $allCourseInfo['extInfo']['extTeacher'];
            $teacherNameStr = '';
            $teacherCnt = 0;
            /**
             * @des 统一处理线上报警 start
             * <AUTHOR>
             * @time 2018/04/09
             */
            if(is_array($teacherInfoList) && !empty($teacherInfoList)){
                foreach ($teacherInfoList as $teacherInfo) {
                    if ($teacherCnt < 2) {
                        $teacherNameStr .= $teacherInfo['teacherName'] . '    ';
                        ++$teacherCnt;
                    } else {
                        break;
                    }
                }
            }
            /**
             * end
             */
            $arrData['teacherName'] = trim($teacherNameStr);
            //购物车方式购买时自动分配班主任
            $arrData['assistantInfo'] = $this->_tradeGetAssistantInfo($arrData);
            //普通课程或有多课程优惠策略的课程价格计算逻辑
            $arrData = $this->_tradeDiscountStrategy($arrData);
            if (isset($arrData['gift']) && !empty($arrData['gift'])) {
                $giftInfo[] = array(
                    'giftList' => $arrData['gift'],
                    'giftRelatedCourse' => array($courseId),
                );
            }
            //有赠品时需要地址信息
            if (!empty($giftInfo)) {
                if (!$needAddress) {
                    $needAddress = 1;
                    if (!empty($studentInfo) && !empty($studentInfo['extData']['addressInfo'])) {
                        $addressInfo = $studentInfo['extData']['addressInfo'];
                    }
                }
            }
            $arrData['type'] = 1;//1普通课,2有关联关系的课（影响NA端展现策略，这里默认写1，后面有格式化逻辑，将有关联关系的课程类型设置为2）
            //获取优惠券信息
            $tradeInfo = array(
                'price' => $arrData['price'],
                'type' => ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG ? $courseInfo['type'] : Hkzb_Ds_Fudao_Course::TYPE_PRIVATE),
                'season' => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
                'seasonId' => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
                'subject' => $courseInfo['subject'],
                'xuebu' => intval(Hk_Util_Category::$GRADEMAPXB[$courseInfo['grade']]),
                'grade' => intval($courseInfo['grade']),
                'moreGrade' => intval($courseInfo['moreGrade']),
                'courseId' => intval($courseInfo['courseId']),
                'learnSeason' => strval($courseInfo['learnSeason']),
            );
            $arrData['tradeInfo'] = $tradeInfo;
            $arrData['changeZbk'] = intval($arrParams['changeZbk']);
            $arrData['couponList'] = $this->_tradeGetMatchCouponList($arrData);

            $courseList[] = $arrData;
        }
        $arrParams['courseList'] = $courseList;
        $arrParams['gift'] = $giftInfo;
        if (!empty($boundCidList)) {
            //绑定课信息
            $arrParams['boundCidList'] = $boundCidList;
            $arrParams['boundList'] = $boundList;
            $arrParams['boundDesc'] = '连报课程优惠';
        } elseif (!empty($arrSumMulSubDiscount)) {
            //多科联报信息
            $arrParams['summerMulSubList'] = array();
            $arrParams['summerMulSubCourseList'] = array();
            foreach ($arrSumMulSubDiscount as $cid => $value) {
                $arrParams['summerMulSubList'][$value['mulDiscountRuleSortTag']][] = $cid;
                $arrParams['summerMulSubCourseList'][] = $cid;
            }
            $arrParams['summerMulSubDesc'] = '联报课程优惠';
        }
        $arrParams['summerCourseTimeValid'] = isset($summerCourseTimeValid) ? $summerCourseTimeValid : 1;//课程时间是否冲突
        $arrParams['needAddress'] = $needAddress;//收否需要邮寄地址
        $arrParams['addressInfo'] = $addressInfo;//地址信息

        return $arrParams;
    }

    private function _tradeFormatBoundCourse($courseList, $boundCidList, $studentUid)
    {
        if (empty($boundCidList)) {
            return $courseList;
        }
        $boundCourseList = array();
        foreach ($courseList as $key => $course) {
            $courseId = $course['courseId'];
            $mulDiscountRuleSortTag = isset($course['mulDiscountRuleSortTag']) ? $course['mulDiscountRuleSortTag'] : 0;
            if (in_array($courseId, $boundCidList)) {
                $boundCourseList[$mulDiscountRuleSortTag][] = $course;
                unset($courseList[$key]);
            }
        }
        if (empty($boundCourseList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundCourseList);
        }
        $userInfo = Hkzb_Ds_Fudao_StudentInfo::getUserInfo($studentUid);
        $isLearnSeasonOld = isset($userInfo['isLearnSeasonOld']) ? $userInfo['isLearnSeasonOld'] : 0;
        $arrData = array();
        $tmpTagInfo = array();
        foreach ($boundCourseList as $sortTag => $courseInfoList) {
            foreach ($courseInfoList as $k => $courseInfo) {
                if (!in_array($sortTag, $tmpTagInfo)) {
                    $tmpTagInfo[] = $sortTag;
                    $arrData[$courseInfo['courseId']]['type'] = 2;
                    if (isset($courseInfo['hasSummerMulSubDiscount']) && $courseInfo['hasSummerMulSubDiscount']) {
                        $arrData[$courseInfo['courseId']]['sortTag'] = $sortTag;
                        $arrData[$courseInfo['courseId']]['boundCourseTag'] = '优惠';
                        $arrData[$courseInfo['courseId']]['boundCourseDesc'] = $courseInfo['summerMulSubDiscountRule']['discountDesc'];
                    } else {
                        $arrData[$courseInfo['courseId']]['boundCourseTag'] = '优惠';
                        $arrData[$courseInfo['courseId']]['boundCourseDesc'] = $courseInfo['boundDiscountStrategy']['discountDesc'];
                        if (empty($arrData[$courseInfo['courseId']]['boundCourseDesc'])) {
                            $arrData[$courseInfo['courseId']]['boundCourseTag'] = '';
                        }
                    }
                    $arrData[$courseInfo['courseId']]['tradeBoundCourseList'][] = $courseInfo;
                    unset($boundCourseList[$sortTag][$k]);
                }
            }
        }
        if (empty($arrData)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundCourseList);
        }
        foreach ($boundCourseList as $sortTag => $courseInfoList) {
            foreach ($courseInfoList as $k => $courseInfo) {
                foreach ($arrData as $cid => &$arrBoundCourse) {
                    if ($sortTag == $arrBoundCourse['sortTag']) {
                        $arrBoundCourse['tradeBoundCourseList'][] = $courseInfo;
                        unset($boundCourseList[$sortTag][$k]);
                    }
                }
            }
        }
        foreach ($boundCourseList as $sortTag => $v) {
            if (empty($v)) {
                unset($boundCourseList[$sortTag]);
            }
        }
        if (!empty($boundCourseList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundCourseList);
        }
        $courseList = array_merge(array_values($arrData), $courseList);

        return $courseList;
    }

    private function _tradeBoundDiscountStrategy($arrData)
    {
        $courseInfo = $arrData['allCourseInfo'];
        $studentUid = $arrData['uid'];
        $courseId = $courseInfo['courseId'];
        if ($courseInfo['type'] != Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CHECK_ERROR, 'invalid bound course type', array('courseId' => $courseId));
        }
        $originPrice = $arrData['price'];
        $learnSeason = $courseInfo['learnSeason'];
        $grade = $courseInfo['grade'];
        $discountInfo = Hkzb_Util_Fudao_Category::getCourseInfo($courseInfo, $studentUid);
        if ($discountInfo === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CHECK_ERROR, 'get discount info error', array('courseId' => $courseId, 'studentUid' => $studentUid));
        }
        $price = $discountInfo['price'];
        $arrData['price'] = $price;
        $arrData['orderPrice'] = $price;
        if ($learnSeason == '秋_2') {
            $arrData['boundDiscount'] = array(
                'discount' => $originPrice - $price,
                'zhe' => '秋1秋2连报，秋2期课程' . $discountInfo['discount'] * 10 . '折',
            );
        } else {
//            if ($grade == 13) {
//                $price                    = intval(round($originPrice * 0.5 / 100) * 100);
//                $arrData['price']         = $price;
//                $arrData['orderPrice']    = $price;
//                $discountInfo['discount'] = 0.5;
//            }
            $arrData['boundDiscount'] = array(
                'discount' => $originPrice - $price,
                'zhe' => '秋1秋2连报，秋1课程' . $discountInfo['discount'] * 10 . '折',
            );
            if ($originPrice == $price) {
                $arrData['boundDiscount']['zhe'] = '';
            }
        }

        return $arrData;
    }

    /**
     * 检查绑定售卖课程信息
     * @param $boundList
     *
     * @throws Hk_Util_Exception
     */
    private function _tradeCheckBoundCourseInfo($boundList)
    {
        //秋二期上线后无秋季连报
        if ($this->now > Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_ONLINE_TIME) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundList);
        }
        foreach ($boundList as $boundInfo) {
            if (count($boundInfo) !== 2) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundList);
            }
            $masterCid = 0;
            $subCid = 0;
            foreach ($boundInfo as $course) {
                if (isset(Hkzb_Util_Fudao_Category::$COURSE_BOUND_LIST[$course['courseId']])) {
                    $masterCid = $course['courseId'];
                } else {
                    $subCid = $course['courseId'];
                }
            }
            if (!$masterCid) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundList);
            }
            if ($subCid != Hkzb_Util_Fudao_Category::$COURSE_BOUND_LIST[$masterCid]) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_check_failed', $boundList);
            }
        }
    }

    /**
     * 用户报名成功处理
     *
     * @param int $orderId 订单号
     * @param bool $needTrans 是否需要事务
     *
     * @return array $arrRes 样例如下：
     * <pre>
     * array(
     *     'errNo' => 0, //处理成功为0，否则非0
     *     'errMsg' => '', //errNo非0时，errMsg表示处理失败原因
     *     'data' => array(...), //格式见_enterPreProc函数返回值
     * )
     * </pre>
     */
    public function enter($orderId, $needTrans = true, $ext = array())
    {
        $arrRes = array(
            'errNo' => 0,
            'errMsg' => '',
            'data' => array(),
        );
        $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
        if ($needTrans) {
            $db = Hk_Service_Db::getDB($dbName);
            if (empty($db)) {
                Bd_Log::warning("connect db fail, db[$dbName]");
                $arrRes['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = '获取fudao数据库连接失败';

                return $arrRes;
            }
            $res = $db->startTransaction();
            if (empty($res)) {
                Bd_Log::warning("start transaction fail, db[$dbName]");
                $arrRes['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库开启事务失败';

                return $arrRes;
            }
        }
        try {
            $arrOutput = $this->_enter($orderId, $ext);
        } catch (Hk_Util_Exception $e) {
            $arrRes['errNo'] = $e->getErrNo();
            $arrRes['errMsg'] = $e->getErrMsg();
            if ($needTrans) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                    $arrRes['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
                    $arrRes['errMsg'] = 'fudao数据库回滚失败';
                }
            }

            return $arrRes;
        }
        if ($needTrans) {
            $res = $db->commit();
            if (empty($res)) {
                Bd_Log::warning("commit fail, db[$dbName]");
                $arrRes['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库提交失败';

                return $arrRes;
            }
        }

        $arrRes['data'] = $arrOutput;

        return $arrRes;
    }

    /**
     * 用户加课流程
     * @param       $orderId
     * @param array $ext
     * @return array|bool
     */
    private function _enter($orderId, $ext = array())
    {
        //1. 获取公共数据并做相应数据检查
        $arrData = $this->_enterPreProc($orderId, $ext);

        //2. 特殊订单处理
        if ($arrData['hasPaid']) { //订单已支付过（含被冻结）则直接退出
            Bd_Log::warning("this order has paid, orderId[$orderId] ");

            return $arrData;
        }
        if ($arrData['needRefund']) { //之前有同样课程支付过，则当前订单进行退款
            Bd_Log::warning("there is same course has paid, this order need refund, orderId[$orderId]");
            $this->_enterTradeRecord($arrData);

            return $arrData;
        }

        //3. 进行fudao db写操作
        $this->_enterTradeRecord($arrData);
        //$this->_enterStudentInfo($arrData);


        Hk_Util_Log::setLog('nmqstudentcourse', 'singal');//打点跟踪
        /*
        $this->_enterStudentCourse($arrData);
        $this->_enterStudentLesson($arrData);
        $this->_enterExercise($arrData);
        */
        //{{{加课动作异步化
        $ext                = array(
            'orderId' => $orderId,
            'app'     => $arrData['app'],
            'skuId'   => isset($arrData['tradeRecord']['extData']['skuId']) ? $arrData['tradeRecord']['extData']['skuId'] : 0,
        );
        $arrAddCourseParams = array(
            'studentUid' => $arrData['uid'],
            'courseId'   => $arrData['courseId'],
            'isGift'     => 0,
            'ext'        => json_encode($ext),
        );
        $ret                = Zb_Service_Nmq::sendCommand(Zb_Const_Command::COMMAND_CORE_110017, $arrAddCourseParams);
        if ($ret === false) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service trade_api_addorder connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");

            return false;
        }
        $errno  = intval($ret['errno']);
        $errmsg = strval($ret['error_msg']);
        if ($errno > 0) {
            Bd_Log::warning("Error:[service trade_api_addorder process error], Detail:[errno:$errno errmsg:$errmsg]");

            return false;
        }
        //}}}
        $this->_enterTradeCnt($arrData);
        //$this->_enterShoppingCart($arrData);

        //4. 最后执行非fudao db的操作（因为无法回滚，最后操作，TODO改成异步）
        //$this->_enterAddGroup($arrData);异步化
        $this->_enterCoupon($arrData);
        $this->_enterUcloud($arrData);
        //$this->_enterMsgNotify($arrData);异步化
        //$this->_enterNotifyPreLongSold($arrData);

        //Hk_Util_Log::setLog('nmqAssistantMis', 'singal');//打点跟踪
        //$this->_enterAssistantMis($arrData);

        //同步售卖信息到 DAK 数据表中 @huwenhua
        $ret = $this->_enterDakSaleCnt($arrData);
        if (true === $ret) {
            Bd_Log::addNotice('syncDakSaleCntSuccess', $arrData['courseId']);
        } else {
            Bd_Log::addNotice('syncDakSaleCntFailure', $arrData['courseId']);
        }

        return $arrData;
    }

    /**
     * 加课流程预处理，包括基本检查，公共数据获取
     *
     * @param array $arrParams
     * @param array $arrOutput
     */
    private function _enterPreProc($orderId, $ext = array())
    {
        $orderId = intval($orderId);
        if ($orderId <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('orderId' => $orderId));
        }

        //获取订单信息
        $tradeRecord = $this->_objDsTradeRecord->getTradeRecord($orderId);
        if (false === $tradeRecord) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $orderId));
        }
        if (empty($tradeRecord)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ORDER_NOT_EXIST, '', array('orderId' => $orderId));
        }
        //如果是后台操作强制加课的
        $needForceAdd = 0;
        if ($tradeRecord['extData']['isChangeCourse']) {
            $needForceAdd = 1;
        }
        if (!$needForceAdd && $tradeRecord['status'] != Hkzb_Ds_Fudao_TradeRecord::STATUS_TOPAY) {
            $hasPaid = 1;
        } else {
            $hasPaid = 0;
        }
        $uid = intval($tradeRecord['studentUid']);
        $courseId = intval($tradeRecord['courseId']);
        $app = strval($tradeRecord['extData']['app']); //来源app：主app（homework或''），一课（airclass）
        if (!$needForceAdd && !empty($tradeRecord['extData']['couponInfo'])) {
            $couponInfo = $tradeRecord['extData']['couponInfo'];
        } else {
            $couponInfo = array();
        }
        if (isset($tradeRecord['extData']['payment'])) {
            $payment = intval($tradeRecord['extData']['payment']);
        } else {
            $payment = intval($tradeRecord['payment']); //用户实际支付价格
        }
        $teacherUid = intval($tradeRecord['teacherUid']);
        //查看之前是否有支付过相同的课
        $objUserTradeRecord = new Hkzb_Ds_Fudao_UserTradeRecord();
        if ($tradeRecord['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
            $orderListTmp = $objUserTradeRecord->getPrePayOrderIdListByStudentUidCourseId($uid, $courseId);
            if (false === $orderListTmp) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
            }
            $orderList = array();
            foreach ($orderListTmp as $order) { //排除当前课程
                if ($order != $orderId) {
                    $orderList[] = $order;
                }
            }
            if (count($orderList) > 0) { //之前支付过
                $needRefund = 1;
            } else {
                $needRefund = 0;
            }
        } else {
            $orderListTmp = $objUserTradeRecord->getOrderIdListByStudentUidCourseId($uid, $courseId);
            if (false === $orderListTmp) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
            }
            $orderList = array();
            foreach ($orderListTmp as $order) { //排除当前课程
                if ($order != $orderId) {
                    $orderList[] = $order;
                }
            }
            if (!$needForceAdd && count($orderList) > 0) { //之前支付过
                $needRefund = 1;
            } else {
                $needRefund = 0;
            }
        }
        //获取用户信息
        if(isset(self::$studentInfo[$uid])) {
            $studentInfo = self::$studentInfo[$uid];
        }else {
            $studentInfo = $this->_objDsStudent->getStudentInfo($uid);
            if (false === $studentInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
            }
            self::$studentInfo[$uid] = $studentInfo;
        }

        if(isset(self::$userInfo[$uid])){
            $userInfo = self::$userInfo[$uid];
        }else {
            $userInfo = $this->_objDsUcloud->getUserInfo($uid);
            if (empty($userInfo)) {
                //获取用户信息失败后不中断加课
                $userInfo = array();
                //throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::USERINFO_NOT_EXIST, '', array('uid' => $uid));
            } else {
                self::$userInfo[$uid] = $userInfo;
            }
        }

        //获取课程信息
        $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
        if (false === $courseInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
        }
        if (empty($courseInfo)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_EXIST, '', array('courseId' => $courseId));
        }
        $subCourseInfoList = $courseInfo['coreCourse'] + $courseInfo['otherCourse'];
        $subCourseIdList = array_keys($subCourseInfoList);
        $price = intval($courseInfo['price']);

        //获取所有课程对应的lesson信息
        $lessonInfoListRaw = $this->_objDsLesson->getLessonListByCourseArr($subCourseIdList, array(), 0, -1);
        if (false === $lessonInfoListRaw) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('subCourseIdList' => $subCourseIdList));
        }
        $lessonInfoListRaw2 = array();
        foreach ($lessonInfoListRaw as $lesson) {
            $lessonInfoListRaw2[$lesson['courseId']][] = $lesson;
        }
        $lessonInfoList = array();
        foreach ($subCourseIdList as $cid) {
            $lessonList = $lessonInfoListRaw2[$cid];
            if (empty($lessonList)) { //课程没有章节
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LESSON_NOT_EXIST, '', array('courseId' => $cid));
            }
            $lessonInfoList[$cid] = $this->_formatLessonList($lessonList);
        }

        //获取学生课程信息
        $stdCoInfoListRaw = $this->_objDsStudentCourse->getStudentCourseInfoArr($uid, $subCourseIdList);
        if (false === $stdCoInfoListRaw) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'subCourseIdList' => $subCourseIdList));
        }
        $stdCoInfoListRaw2 = array();
        foreach ($stdCoInfoListRaw as $studentCourse) {
            $stdCoInfoListRaw2[$studentCourse['courseId']][] = $studentCourse;
        }
        $studentCourseInfoList = array();
        $subCourseBuyStatusList = array();
        foreach ($stdCoInfoListRaw2 as $cid => $stdCoList) {
            $validList = array();
            $unvalidList = array();
            foreach ($stdCoList as $studentCourse) {
                if ($studentCourse['deleted'] == Hkzb_Ds_Fudao_StudentCourse::STATUS_OK && $studentCourse['status'] <= Hkzb_Ds_Fudao_StudentCourse::STATUS_FINISHED) {
                    $validList[] = $studentCourse;
                } else {
                    $unvalidList[] = $studentCourse;
                }
            }
            $studentCourseInfoList[$cid] = array_merge($validList, $unvalidList);
            if (!empty($validList)) { //购买过，当前有正常的课
                $subCourseBuyStatusList[$cid] = 1;
            } elseif (!empty($unvalidList)) { //购买过，当前没有正常的课
                $subCourseBuyStatusList[$cid] = 2;
            } else { //没有购买过
                $subCourseBuyStatusList[$cid] = 0;
            }
        }

        //获取赠送优惠券信息，暂停优惠券发送
        $giveCouponItemIdList = array();
        //邮寄地址获取
        $needAddress = 1;//目前实物类型较多，这里统一写入邮寄地址
        $addressInfo = isset($studentInfo['extData']['addressInfo']) ? $studentInfo['extData']['addressInfo'] : array();
//        $courseAttributeInfo = $this->_objDsCourseAttribute->getCourseAttributeInfo($courseId, array('teachMaterial'));
//        if ($courseAttributeInfo === false) {
//            Bd_Log::warning("get courseattribute info fail, courseId:$courseId");
//        } elseif (isset($courseAttributeInfo['teachMaterial']) && $courseAttributeInfo['teachMaterial']) {
//            $needAddress = 1;
//            if (!empty($studentInfo) && !empty($studentInfo['extData']['addressInfo'])) {
//                $addressInfo = $studentInfo['extData']['addressInfo'];
//            }
//        }
        //各子课辅导员信息获取
        $assistantUid = intval($tradeRecord['assistantUid']);
        $assistantInfoList = array();
//        if ($tradeRecord['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
//            //预约单不检查班主任信息
//        } else {
//            foreach ($subCourseInfoList as $course) {
//                $cid = $course['courseId'];
//                if ($course['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
//                    $params = array(
//                        'courseInfo' => $course,
//                        'lesson' => $lessonInfoList[$cid],
//                        'uid' => $uid,
//                    );
//                    $tid = $this->autoAllocAssistant($params);
//                    if (false === $tid) {
//                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $cid));
//                    }
//                } else {
//                    $tid = $assistantUid;
//                }
//                $assistantList = $this->_objDsTeacherCourse->getAssistantClassInfo($tid, $cid);
//                if (false === $assistantList) {
//                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $cid, 'teacherUid' => $tid));
//                }
//                $assistantInfo = array();
//                foreach ($assistantList as $item) {
//                    if (empty($assistantInfo)) {
//                        $assistantInfo = $item;
//                        if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
//                            break;
//                        }
//                    } else {
//                        if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
//                            $assistantInfo = $item;
//                            break;
//                        }
//                    }
//                }
//                if (empty($assistantInfo)) {
//                    //发送报警邮件
//                    $subject = '班主任不存在';
//                    $message = '课程：' . $course['courseName'] . '(courseId:' . $cid . ')没有该班主任(teacherUid:' . $tid . ')';
//                    $toMail = '<EMAIL>,<EMAIL>,<EMAIL>';
//                    exec("curl http://proxy.zuoyebang.com:1925/api/mail -XPOST -d 'tos=$toMail&subject=$subject&content=$message&format=html'");
//                   // throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_NOT_IN_COURSE, '', array('courseId' => $cid, 'teacherUid' => $tid));
//                }
//                $assistantInfoList[$course['courseId']] = $assistantInfo;
//            }
//        }

        //获取入门测试
        $preExerciseInfoList = array();
        /*
        foreach ($subCourseIdList as $cid) {
            $exerciseList = $this->_objDsExercise->getExerciseList($cid, 0, Hkzb_Ds_Fudao_Exercise::PURPOSE_PRECLASS, array('exerciseId'));
            if (false === $exerciseList) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $cid));
            }
            $preExerciseInfoList[$cid] = $exerciseList;
        }
        */

        //是否需要通知ucloud，进行经验值等添加
        if ($needForceAdd) {
            $needNotifyUcloud = 0;
        } else {
            $needNotifyUcloud = 1;
        }

        //是否需要发送报名成功提醒
        $needSendMsgNotify = true;
        if ($needForceAdd) {
            $needSendMsgNotify = false;
        }

        //获取群信息
        $groupId = Hkzb_Util_FuDao::getGroupIdByCourseId($courseId);

        //第三方支付优惠金额
        if (isset($ext['thirdPartyDiscount'])) {
            $thirdPartyDiscount = intval($ext['thirdPartyDiscount']);
        } else {
            $thirdPartyDiscount = 0;
        }

        //获取购买课程的skuId
        $skuId = isset($tradeRecord['extData']['skuId']) ? $tradeRecord['extData']['skuId'] : 0;

        $arrOutput = array(
            'orderId' => $orderId,
            'uid' => $uid,
            'courseId' => $courseId,
            'skuId' => $skuId,
            'needForceAdd' => $needForceAdd,
            'hasPaid' => $hasPaid,
            'needRefund' => $needRefund,
            'price' => $price,
            'payment' => $payment,
            'tradeRecord' => $tradeRecord,
            'couponInfo' => $couponInfo,
            'courseInfo' => $courseInfo,
            'subCourseInfoList' => $subCourseInfoList,
            'subCourseBuyStatusList' => $subCourseBuyStatusList,
            'giveCouponItemIdList' => $giveCouponItemIdList,
            'lessonInfoList' => $lessonInfoList,
            'studentCourseInfoList' => $studentCourseInfoList,
            'assistantUid' => $assistantUid,
            'assistantInfoList' => $assistantInfoList,
            'preExerciseInfoList' => $preExerciseInfoList,
            'studentInfo' => $studentInfo,
            'userInfo' => $userInfo,
            'app' => $app,
            'needAddress' => $needAddress,
            'addressInfo' => $addressInfo,
            'needNotifyUcloud' => $needNotifyUcloud,
            'needSendMsgNotify' => $needSendMsgNotify,
            'groupId' => $groupId,
            'thirdPartyDiscount' => $thirdPartyDiscount,
        );

        return $arrOutput;
    }

    private function _enterTradeRecord($arrParams)
    {
        $tradeRecord = $arrParams['tradeRecord'];
        $orderId = $arrParams['orderId'];
        $payment = $arrParams['payment'];
        $needAddress = $arrParams['needAddress'];
        $addressInfo = $arrParams['addressInfo'];
        $thirdPartyDiscount = intval($arrParams['thirdPartyDiscount']); //第三方支付优惠

        if ($tradeRecord['courseId'] > 0) {
            $realPayment = $payment - $thirdPartyDiscount;
        } else {
            $realPayment = $tradeRecord['extData']['payment'] - $thirdPartyDiscount;
        }
        if ($realPayment < 0) {
            $realPayment = 0;
        }

        if ($arrParams['needRefund']) { //之前购买过，现在这一单需要退款(后续统一由trade发命令通知)
            Bd_Log::warning("needRefund, orderId[$orderId] tradeRecord[". json_encode($tradeRecord) ."]");
            //if ($realPayment == 0) {
            //    //免费课程和新系统保持一致,统一改为退款状态
            //    $extData = $tradeRecord['extData'];
            //    $extData['refundTime'] = time();
            //    $extData['refundPayment'] = $realPayment;
            //    $extData['frozenSource'] = 'autorefund';
            //    if (empty($extData['refundRequestNo'])) {
            //        $requestNo = $orderId; //特殊情况，把orderId作为requestNo
            //        $extData['refundRequestNo'] = $requestNo;
            //    } else {
            //        $requestNo = intval($extData['refundRequestNo']);
            //    }
            //    if ($thirdPartyDiscount > 0) {
            //        $extData['thirdPartyDiscount'] = $thirdPartyDiscount;
            //    }
            //    $arrFields = array(
            //        'status' => Hkzb_Ds_Fudao_TradeRecord::STATUS_REFUND,
            //        'extData' => $extData,
            //    );
            //    if ($realPayment != $payment) {
            //        $arrFields['payment'] = $realPayment;
            //    }
            //    $res = $this->_objDsTradeRecord->updateTradeRecord($orderId, $arrFields, false);
            //    if (false === $res) {
            //        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $orderId));
            //    }
            //} else {
            //    $extData = $tradeRecord['extData'];
            //    $extData['refundTime'] = time();
            //    $extData['refundPayment'] = $realPayment;
            //    $extData['frozenSource'] = 'autorefund';
            //    if (empty($extData['refundRequestNo'])) {
            //        $requestNo = $orderId; //特殊情况，把orderId作为requestNo
            //        $extData['refundRequestNo'] = $requestNo;
            //    } else {
            //        $requestNo = intval($extData['refundRequestNo']);
            //    }
            //    if ($thirdPartyDiscount > 0) {
            //        $extData['thirdPartyDiscount'] = $thirdPartyDiscount;
            //    }
            //    $arrFields = array(
            //        'status' => Hkzb_Ds_Fudao_TradeRecord::STATUS_FORZEN,
            //        'extData' => $extData,
            //    );
            //    if ($realPayment != $payment) {
            //        $arrFields['payment'] = $realPayment;
            //    }
            //    $res = $this->_objDsTradeRecord->updateTradeRecord($orderId, $arrFields, false);
            //    if (false === $res) {
            //        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $orderId));
            //    }
            //    if (isset($extData['purchaseOrderId'])) {
            //        $purchaseOrderId = $extData['purchaseOrderId'];
            //    } else {
            //        $purchaseOrderId = $orderId;
            //    }
            //    $refundTransp = array(
            //        'orderId' => $orderId,
            //    );
            //    $arrRefundParams = array(
            //        'orderId' => $purchaseOrderId,
            //        'requestNo' => $requestNo,
            //        'refundPrice' => $realPayment,
            //        'refundFee' => $realPayment,
            //        'callbackUrl' => Zhibo_Static::REFUND_CALLBACK_URL,
            //        'source' => Zhibo_Static::PAY_SOURCE,
            //        'secret' => Zhibo_Static::PAY_SECRET,
            //        'refundTransp' => $refundTransp,
            //    );
            //    $res = Hk_Service_Pay::requestRefund($arrRefundParams);
            //    if (false === $res) {
            //        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PAY_REFUND_ERROR, '', array('orderId' => $orderId));
            //    }
            //}
        }

        $arrFields = array(
            'status' => Hkzb_Ds_Fudao_TradeRecord::STATUS_PAYED
        );
        $extData = $tradeRecord['extData'];
        $extData['tradeTime'] = time();
        if ($needAddress && !empty($addressInfo) && empty($extData['addressInfo'] )) {
            $extData['addressInfo'] = $addressInfo;
        }
        if ($tradeRecord['courseId'] > 0 && $realPayment != $payment) {
            $arrFields['payment'] = $realPayment;
        }
        if ($thirdPartyDiscount > 0) {
            $extData['thirdPartyDiscount'] = $thirdPartyDiscount;
        }
        $arrFields['extData'] = $extData;
        $res = $this->_objDsTradeRecord->updateTradeRecord($orderId, $arrFields,false);
        if (false === $res) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $orderId));
        }
        //如果有预约单，更新预约单状态
        if (isset($extData['expendDiscount'])) {
            $expendDiscount = $extData['expendDiscount'];
            $preOrderTradeRecordId = $expendDiscount['preOrderTradeRecordId'];
            $preOrderTradeRecordInfo = $this->_objDsTradeRecord->getTradeRecord($preOrderTradeRecordId);
            $preOrderExt = $preOrderTradeRecordInfo['extData'];
            $preOrderExt['consumeTime'] = time();
            $preOrderExt['consumeTradeRecordId'] = $orderId;
            $arrFields = array(
                'status' => Hkzb_Ds_Fudao_TradeRecord::STATUS_FINISH,
                'extData' => $preOrderExt,
            );
            $res = $this->_objDsTradeRecord->updateTradeRecord($preOrderTradeRecordId, $arrFields,false);
            if (false === $res) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $preOrderTradeRecordId));
            }
        }

        return true;
    }

    private function _enterStudentInfo($arrParams)
    {
        $uid = $arrParams['uid'];
        $studentInfo = $arrParams['studentInfo'];
        $userInfo = $arrParams['userInfo'];
        $courseInfo = $arrParams['courseInfo'];
        $oldestTime = isset($studentInfo['oldestTime']) ? $studentInfo['oldestTime'] : 0;//用户首次购买班课行为的时间，用于按照学季判断新老用户
        //是否标记为老用户
        $includeId = array(5119, 5071, 5067, 5066, 5026, 5020, 5019, 5018, 4998, 4913, 4912, 5061, 4905, 4900);
        if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG || in_array($courseInfo['courseId'], $includeId)) {
            $isOld = 1;
            if ($oldestTime == 0) {//首次购买班课时更新
                $oldestTime = time();
            }
        } else {
            $isOld = 0;
        }

        if (empty($studentInfo) || $studentInfo['deleted'] === Hkzb_Ds_Fudao_Student::STATUS_DELETED) {
            $arrFields = array(
                'studentUid' => $uid,
                'phone' => $userInfo['phone'],
                'studentName' => $userInfo['uname'],
                'oldestTime' => $oldestTime,
            );
            if ($isOld) {
                $arrFields['extData'] = array('isOld' => $isOld);
            }
            $res = $this->_objDsStudent->addStudent($arrFields);
            if (false === $res) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
            }
        } else {
            $arrFields = array(
                'oldestTime' => $oldestTime,
            );
            //已经存在判断老用户信息
            if ($studentInfo['extData']['isOld'] <= 0 && $isOld) {//置为老用户
                $extData = $studentInfo['extData'];
                $extData['isOld'] = $isOld;
                $arrFields['extData'] = $extData;
            }
            $ret = $this->_objDsStudent->updateStudentNoTrans($uid, $arrFields);
            if (false === $ret) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
            }
        }

        return true;
    }

    private function _enterStudentCourse($arrParams)
    {
        $uid = $arrParams['uid'];
        $orderId = $arrParams['orderId'];
        $app = $arrParams['app'];
        $assistantInfoList = $arrParams['assistantInfoList'];
        $subCourseInfoList = $arrParams['subCourseInfoList'];
        $lessonInfoList = $arrParams['lessonInfoList'];
        $studentCourseInfoList = $arrParams['studentCourseInfoList'];
        foreach ($subCourseInfoList as $courseId => $courseInfo) {
            $assistantInfo     = $assistantInfoList[$courseId];
            $lessonInfo        = $lessonInfoList[$courseId];
            $studentCourseInfo = $studentCourseInfoList[$courseId][0];
            if ($courseInfo['pack'] <> Hkzb_Ds_Fudao_Advanced_Course::PACK_YESD) {
                //是否需要购后测
                $allCourseInfo  = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extTeacher', 'extAssistant', 'extLesson'), true);
                $courseExamInfo = Hkzb_Ds_Fudao_Advanced_Course::getCourseExamInfoByUidCourseId($uid, $allCourseInfo, Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_AFTER);
                $needExam       = $courseExamInfo['testStatus'];
            }
            if (!empty($studentCourseInfo)) { //曾经买过的课程中包含这个子课
                $extData      = $studentCourseInfo['extData'];
                $payOrderList = $extData['payOrderList'];
                if (empty($payOrderList)) {
                    $payOrderList = array($orderId);
                } else {
                    $payOrderList[] = $orderId;
                }
                $extData['payOrderList']   = array_unique($payOrderList);
                $extData['orderId']        = $orderId;
                $extData['homeContactUid'] = $assistantInfo['teacherUid'];
                $extData['app']            = $app;
                if (isset($needExam)) {
                    $extData['needExam'] = $needExam;
                }
                $arrConds  = array(
                    'id' => $studentCourseInfo['id'],
                );
                $arrFields = array(
                    'pack'               => $courseInfo['pack'],
                    'type'               => $courseInfo['type'],
                    'teacherUid'         => $courseInfo['teacherUid'],
                    'assistantUid'       => $assistantInfo['teacherUid'],
                    'classId'            => $assistantInfo['classId'],
                    'className'          => strval($assistantInfo['classId']),
                    'deleted'            => 0, //覆盖原有状态
                    'status'             => 0,  //覆盖原有状态
                    'updateTime'         => time(),
                    'extData'            => json_encode($extData),
                    'lastLessonStopTime' => $lessonInfo['lastLessonStopTime'],
                );
                $res       = $this->_objDaoStudentCourse->updateByConds($uid, $arrConds, $arrFields);
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
                }
            } else { //第一次买这个子课
                $arrFields = array(
                    'studentUid'         => $uid,
                    'courseId'           => $courseId,
                    'pack'               => $courseInfo['pack'],
                    'type'               => $courseInfo['type'],
                    'grade'              => $courseInfo['grade'],
                    'subject'            => $courseInfo['subject'],
                    'teacherUid'         => $courseInfo['teacherUid'],
                    'assistantUid'       => $assistantInfo['teacherUid'],
                    'classId'            => $assistantInfo['classId'],
                    'className'          => strval($assistantInfo['classId']),
                    'extData'            => array(
                        'orderId'        => $orderId,
                        'homeContactUid' => $assistantInfo['teacherUid'],
                        'app'            => $app,
                        'payOrderList'   => array($orderId),
                    ),
                    'lastLessonStopTime' => $lessonInfo['lastLessonStopTime'],
                );
                if (isset($needExam)) {
                    $arrFields['extData']['needExam'] = $needExam;
                }
                $res = $this->_objDsStudentCourse->addStudentCourse($arrFields);
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
                }
            }
        }

        //打包课添加报名记录
        $courseInfo = $arrParams['courseInfo'];
        if ($courseInfo['pack'] == Hkzb_Ds_Fudao_Advanced_Course::PACK_YES) {
            $courseId          = intval($courseInfo['courseId']);
            $studentCourseInfo = $this->_objDsAdvancedStudentCourse->getStudentCourseInfo($uid, $courseId);
            $adCourseInfo      = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extTeacher', 'extAssistant', 'extLesson'), true);
            $adLessonInfo      = $this->_formatLessonList($adCourseInfo['extInfo']['extLesson']['allLesson']);
            //是否需要购后测
            $courseExamInfo = Hkzb_Ds_Fudao_Advanced_Course::getCourseExamInfoByUidCourseId($uid, $adCourseInfo, Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_AFTER);
            $needExam       = $courseExamInfo['testStatus'];
            //打包课班主任，取核心课的班主任。
            $defaultCoreCourseId = $arrParams['courseInfo']['defaultCoreCourseId'];
            $assistantInfo       = $assistantInfoList[$defaultCoreCourseId];
            if (!empty($studentCourseInfo)) {
                $extData      = $studentCourseInfo['extData'];
                $payOrderList = $extData['payOrderList'];
                if (empty($payOrderList)) {
                    $payOrderList = array($orderId);
                } else {
                    $payOrderList[] = $orderId;
                }
                $extData['payOrderList']   = array_unique($payOrderList);
                $extData['orderId']        = $orderId;
                $extData['homeContactUid'] = $assistantInfo['teacherUid'];
                $extData['app']            = $app;
                $extData['needExam']       = $needExam;
                $arrConds = array(
                    'courseId' => $courseId,
                    'studentUid' => $uid,
                );
                $arrFields = array(
                    'pack' => $courseInfo['pack'],
                    'type' => $courseInfo['type'],
                    'teacherUid' => $courseInfo['teacherUid'],
                    'assistantUid' => $assistantInfo['teacherUid'],
                    'deleted' => 0, //覆盖原有状态
                    'status' => 0,  //覆盖原有状态
                    'updateTime' => time(),
                    'extData' => json_encode($extData),
                    'lastLessonStopTime' => $adLessonInfo['lastLessonStopTime'],
                );
                $res = $this->_objDaoStudentCourse->updateByConds($uid, $arrConds, $arrFields);
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
                }
            } else {
                $arrFields = array(
                    'studentUid' => $uid,
                    'courseId' => $courseId,
                    'pack' => $courseInfo['pack'],
                    'type' => $courseInfo['type'],
                    'grade' => $courseInfo['grade'],
                    'subject' => $courseInfo['subject'],
                    'teacherUid' => $courseInfo['teacherUid'],
                    'assistantUid' => $assistantInfo['teacherUid'],
                    'lastLessonStopTime' => intval($adLessonInfo['lastLessonStopTime']),
                    'extData' => array(
                        'orderId' => $orderId,
                        'homeContactUid' => $assistantInfo['teacherUid'],
                        'app' => $app,
                        'payOrderList' => array($orderId),
                        'needExam' => $needExam,
                    ),
                );
                $res = $this->_objDsAdvancedStudentCourse->addStudentCourse($arrFields);
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
                }
            }
        }

        return true;
    }

    private function _enterStudentLesson($arrParams)
    {
        $uid                    = $arrParams['uid'];
        $orderId                = $arrParams['orderId'];
        $app                    = $arrParams['app'];
        $subCourseInfoList      = $arrParams['subCourseInfoList'];
        $lessonInfoList         = $arrParams['lessonInfoList'];
        $assistantInfoList      = $arrParams['assistantInfoList'];
        $subCourseBuyStatusList = $arrParams['subCourseBuyStatusList'];
        foreach ($subCourseInfoList as $courseId => $courseInfo) {
            $assistantInfo = $assistantInfoList[$courseId];
            $lessonList    = $lessonInfoList[$courseId]['allLessonList'];
            foreach ($lessonList as $lesson) {
                if ($subCourseBuyStatusList[$courseId] > 0) { //有则更新
                    $arrConds = array(
                        'studentUid' => $uid,
                        'lessonId' => $lesson['lessonId'],
                    );
                    $arrFields = array(
                        'teacherUid' => $courseInfo['teacherUid'],
                        'assistantUid' => $assistantInfo['teacherUid'],
                        'classId' => $assistantInfo['classId'],
                        'startTime' => $lesson['startTime'],
                        'stopTime' => $lesson['stopTime'],
                        'status' => $lesson['status'],
                        'updateTime' => time(),
                    );
                    $res = $this->_objDaoLessonDetail->updateByConds($uid, $arrConds, $arrFields);
                } else {
                    $arrFields = array(
                        'studentUid' => $uid,
                        'courseId' => $courseId,
                        'lessonId' => $lesson['lessonId'],
                        'teacherUid' => $courseInfo['teacherUid'],
                        'assistantUid' => $assistantInfo['teacherUid'],
                        'classId' => $assistantInfo['classId'],
                        'startTime' => $lesson['startTime'],
                        'stopTime' => $lesson['stopTime'],
                        'status' => $lesson['status'],
                        'extData' => array(
                            'app' => $app,
                        ),
                    );
                    $res = $this->_objDsLessonDetail->addLessonDetail($arrFields);
                }
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'lessonId' => $lesson['lessonId']));
                }
                if ($subCourseBuyStatusList[$courseId] > 0) { //有则更新
                    $arrConds = array(
                        'studentUid' => $uid,
                        'lessonId' => $lesson['lessonId'],
                    );
                    $arrFields = array(
                        'teacherUid' => $courseInfo['teacherUid'],
                        'assistantUid' => $assistantInfo['teacherUid'],
                        'classId' => $assistantInfo['classId'],
                        'updateTime' => time(),
                    );
                    $res = $this->_objDaoLessonState->updateByConds($arrConds, $arrFields);
                } else {
                    $arrFields = array(
                        'studentUid' => $uid,
                        'courseId' => $courseId,
                        'lessonId' => $lesson['lessonId'],
                        'teacherUid' => $courseInfo['teacherUid'],
                        'assistantUid' => $assistantInfo['teacherUid'],
                        'classId' => $assistantInfo['classId'],
                        'extData' => array(
                            'app' => $app,
                        ),
                    );
                    $res = $this->_objDsLessonState->addLessonState($arrFields);
                }
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'lessonId' => $lesson['lessonId']));
                }
            }
            if ($subCourseBuyStatusList[$courseId] > 0) { //有则更新
                $arrConds = array(
                    'studentUid' => $uid,
                    'courseId' => $courseId,
                );
                $arrFields = array(
                    'teacherUid' => $courseInfo['teacherUid'],
                    'assistantUid' => $assistantInfo['teacherUid'],
                    'classId' => $assistantInfo['classId'],
                    'updateTime' => time(),
                );
                $res = $this->_objDaoLessonAllState->updateByConds($arrConds, $arrFields);
            } else {
                $arrFields = array(
                    'studentUid' => $uid,
                    'courseId' => $courseId,
                    'teacherUid' => $courseInfo['teacherUid'],
                    'assistantUid' => $assistantInfo['teacherUid'],
                    'classId' => $assistantInfo['classId'],
                );
                $res = $this->_objDsLessonAllState->addLessonAllState($arrFields);
            }
            if (false === $res) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'courseId' => $courseId));
            }
        }

        return true;
    }

    private function _enterExercise($arrParams)
    {
        $uid = $arrParams['uid'];
        $subCourseInfoList = $arrParams['subCourseInfoList'];
        $lessonInfoList = $arrParams['lessonInfoList'];
        $preExerciseInfoList = $arrParams['preExerciseInfoList'];
        $subCourseBuyStatusList = $arrParams['subCourseBuyStatusList'];
        foreach ($subCourseInfoList as $courseId => $courseInfo) {
            //用户曾经购买过，不再添加exercise信息
            if ($subCourseBuyStatusList[$courseId] > 0) {
                continue;
            }
            $preExerciseList = $preExerciseInfoList[$courseId];
            foreach ($preExerciseList as $exercise) {
                $arrFields = array(
                    'exerciseId' => $exercise['exerciseId'],
                    'studentUid' => $uid,
                    'courseId' => $courseId,
                    'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_PRECLASS,
                );
                $res = $this->_objDsExerciseDetail->addExerciseDetail($arrFields);
                if (false === $res) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'exerciseId' => $exercise['exerciseId']));
                }
            }
            $overLessonList = $lessonInfoList[$courseId]['overLessonList'];
            foreach ($overLessonList as $lesson) {
                $lessonId = $lesson['lessonId'];
                $exerciseList = $this->_objDsExercise->getExerciseList($courseId, $lessonId, Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS, array('exerciseId', 'tid'));
                if (false === $exerciseList) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId, 'lessonId' => $lessonId));
                }
                foreach ($exerciseList as $exercise) {
                    $arrFields = array(
                        'exerciseId' => $exercise['tid'],
                        'studentUid' => $uid,
                        'courseId' => $courseId,
                        'lessonId' => $lessonId,
                        'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS,
                    );
                    $res = $this->_objDsExerciseDetail->addExerciseDetail($arrFields);
                    if (false === $res) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'exerciseId' => $exercise['exerciseId']));
                    }
                }
            }
        }

        return true;
    }

    private function _enterTradeCnt($arrParams)
    {
        $subCourseInfoList = $arrParams['subCourseInfoList'];
        //$assistantInfoList = $arrParams['assistantInfoList'];
        //$subCourseBuyStatusList = $arrParams['subCourseBuyStatusList'];
        foreach ($subCourseInfoList as $courseId => $courseInfo) {
            //用户如果有购买过此课程，且课程状态正常，不再更新报名数量
//            if ($subCourseBuyStatusList[$courseId] == 1) {
//                continue;
//            }
//            $assistantInfo = $assistantInfoList[$courseId];
//            $assistantUid = $assistantInfo['teacherUid'];
//            if (empty($assistantUid)) {
//                continue;
//            }
//            $classId = $assistantInfo['classId'];
//            $res = $this->_objDsTeacherCourse->updateTeacherStudentCnt($courseId, $assistantUid, $classId);
//            if (false === $res) {
//                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId, 'assistantUid' => $assistantUid, 'classId' => $classId));
//            }
            $res = $this->_objDsCourse->updateCourseStudentCnt($courseId);
            if (false === $res) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
            }
        }
        if ($arrParams['courseInfo']['pack'] == Hkzb_Ds_Fudao_Course::PACK_YES) {
            $courseId = $arrParams['courseId'];
            $res = $this->_objDsCourse->updateCourseStudentCnt($courseId);
            if (false === $res) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
            }
        }

        return true;
    }

    private function _enterAddGroup($arrParams)
    {
        $groupId = intval($arrParams['groupId']);
        $uid = $arrParams['uid'];
        $res = Hkzb_Service_IM::memberAdd($arrParams);
        if (false === $res) {
            Bd_Log::warning("add group fail, uid[$uid] groupId[$groupId]");
        }

        return true;
    }

    private function _enterCoupon($arrParams)
    {
        $courseInfo = $arrParams['courseInfo'];
        $couponInfo = $arrParams['couponInfo'];
        $couponId = intval($couponInfo['couponId']);
        $orderId = $arrParams['orderId'];
        $courseId = $arrParams['courseId'];
        $uid = $arrParams['uid'];
        $phone = $arrParams['userInfo']['phone'];
        if ($couponId > 0) { //标记购买中的优惠券为已使用
            $res = Hkzb_Service_Coupon::useCoupon($couponId, 'zyb_fudao', 'zybcouselesson', $orderId, $courseId, $courseInfo['type'], $courseInfo['courseName'], $courseInfo['price'], $uid);
            if (false === $res) { //只打warning不抛异常
                Bd_Log::warning("use coupon fail, uid[$uid] orderId[$orderId] couponId[$couponId]");
            }
        }

        $itemIdList = $arrParams['giveCouponItemIdList'];
        if (!empty($itemIdList)) { //需要赠送优惠券
            $itemIds = implode(',', $itemIdList);
            $res = Hkzb_Service_Coupon::addCouponByItemIds('zyb_fudao', 'zybcouselesson', $itemIds, $uid);
            if (false === $res) {
                Bd_Log::warning("give coupon fail, uid[$uid] itemIds[$itemIds]");
            } else {
                if ($phone > 0) {
                    Hk_Service_SmsCommon::sendSmsByTemplateId($phone, [], 197);
                }
            }
        }

        return true;
    }

    private function _enterUcloud($arrParams)
    {
        if ($arrParams['courseInfo']['`inner`']) {
            return true;
        }
        if ($arrParams['courseInfo']['isShow'] == 0) {
            return true;
        }
        if (!$arrParams['needNotifyUcloud']) {
            return true;
        }
        $studentUid = intval($arrParams['uid']);
        $courseId = intval($arrParams['courseId']);
        $courseInfo = $this->_objDsCourse->getCourseInfo($courseId);
        //将用户行为信息同步给平台
        //$onlineTime = strval($courseInfo['extData']['onlineTime']);//上课时间
        $onlineTime = Zhibo_Util::formatToday(strval($courseInfo['extData']['onlineTime']));//上课时间
        if (empty($courseInfo['extData']['onlineTime'])) {
            $bYear = date('Y', $courseInfo['onlineStart']);
            $eYear = date('Y', $courseInfo['onlineStop']);
            if ($eYear == $bYear) {
                $onlineTime = date('n月j日', $courseInfo['onlineStart']) . '-' . date('n月j日', $courseInfo['onlineStop']);
            } else {
                $onlineTime = date('Y年n月j日', $courseInfo['onlineStart']) . '-' . date('Y年n月j日', $courseInfo['onlineStop']);
            }
        }
        $lessonCnt = intval($courseInfo['extData']['lessonCnt']);
        if ($lessonCnt > 1) {
            $onlineList = explode(' ', $onlineTime);
            if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG) {
                $checkLessonCourseId = $courseId;
                if ($courseInfo['pack'] == 1) {
                    foreach ($courseInfo['extData']['coreCourseIds'] as $coreCourseIdArray) {
                        if (intval($coreCourseIdArray['type']) === 1) {
                            $checkLessonCourseId = $coreCourseIdArray['courseId'];
                            break;
                        }
                    }
                }
                $objLesson = new Hkzb_Ds_Fudao_Lesson();
                $firstLesson = $objLesson->getLessonInfoLast($checkLessonCourseId, array('startTime', 'stopTime'));
                $lessonStartTime = $firstLesson['startTime'];
                $lessonStopTime = $firstLesson['stopTime'];
                $weekConf = array(
                    0 => '周日',
                    1 => '周一',
                    2 => '周二',
                    3 => '周三',
                    4 => '周四',
                    5 => '周五',
                    6 => '周六',
                );
                $dataWeek = date('w', $lessonStartTime);
                $week = $weekConf[$dataWeek];
                $hourTime = date('H:i', $lessonStartTime) . '-' . date('H:i', $lessonStopTime);
                $onlineTime = $onlineList[0] . ' 每' . $week . ' ' . $hourTime . ' ' . $lessonCnt . '次课';
            } else {
                $onlineTime = $onlineList[0] . ' ' . $lessonCnt . '次课';
            }
        }

        //购买课程动态消息传递商品id
        $skuId = !empty($arrParams['skuId']) ? $arrParams['skuId'] : $courseId;

        $msgInfo = array(
            'uid' => $studentUid,//——用户id
            'act_time' => time(),//——发生时间
            'mid' => $skuId,//——直播课课程id
            'title' => $courseInfo['courseName'],//——课程标题
            'period' => $onlineTime,//——上课时间
            'price' => intval($courseInfo['price']),//——课程价格
            'subject' => isset(Hkzb_Util_FuDao::$COURSE_ONE[intval($courseInfo['subject'])]) ? strval(Hkzb_Util_FuDao::$COURSE_ONE[intval($courseInfo['subject'])]) : '',//学科
            'progress' => 1,
        );
        $res = Hk_Service_Ucloud::actionCallback(Hk_Const_Command::CMD_BUY_AIRCLASS, $msgInfo);
        if ($res === false) {
            Bd_Log::warning('action send to ucloud failed, command:' . Hk_Const_Command::CMD_BUY_AIRCLASS . ', params:' . json_encode($msgInfo));
        }
        if (false === $res) {
            Bd_Log::warning("notify ucloud fail, uid[$studentUid]");
        }
    }

    private function _enterMsgNotify($arrParams)
    {
        //暑二期修改发送短信发送逻辑
        $this->sendCourseMsg($arrParams);
        return true;

        //=========================暑秋过后以后逐步把下面的逻辑干掉===================
        $arrMessage = array();
        if (!$arrParams['needSendMsgNotify']) {
            return true;
        }
        $uid = $arrParams['uid'];
        $app = $arrParams['app'];
        if ($app == '') {
            $app = 'homework';
        }
        $phone = $arrParams['userInfo']['phone'];
        if (empty($phone)) { //没有手机号不发送
            return true;
        }
        $courseId = $arrParams['courseId'];
        $courseInfo = $arrParams['courseInfo'];
        $subCourseInfoList = $arrParams['subCourseInfoList'];
        $needAddress = $arrParams['needAddress'];

        $coreCourseId = $courseInfo['defaultCoreCourseId'];
        $courseName = $courseInfo['courseName'];
        $startTime = date('n月j日 H:i', $courseInfo['onlineStart']);

        if (in_array($courseInfo['type'], array(Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG, Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG))) {
            if ($app) {
                $app = 'homework';//暂时作业帮App、一课App使用同样的购买短信
            }
            $tpl = 'bk_buy_notify';
            if ($app == 'homework') {
                //$guideUrl   = Hk_Util_Host::getHost() . '/course/activity/userguide?courseId=' . $courseId . '&studentId=' . $uid;
                //$guideUrl   = Zhibo_Util::getShortUrl($guideUrl);
                $arrMessage = array($courseName, $startTime);
            } elseif ($app == 'airclass') {
                $arrMessage = array($courseName, $startTime);
            } else {
                //非法app标识
            }
        } else {
            $tpl = '';
        }
        //预备班报名成功，短信模板处理

        if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG) {
            //体验课关闭购课短信
            return true;
            $grade = array('15', '16');
            if (!in_array($courseInfo['grade'], $grade)) {
                $smsId = 777;
                $res = Hk_Service_SmsCommon::sendSmsByTemplateId($phone, $arrMessage, $smsId, 1);

            } else {
                $smsId = 811;
                $courseName = '班课体验:' . $courseName;
                $arr = array($courseName);
                $res = Hk_Service_SmsCommon::sendSmsByTemplateId($phone, $arr, $smsId, 1);
            }

            Bd_Log::addNotice('laxinBuySms', "{$phone}_{$smsId}_{$courseId}");

            if (false === $res) {
                Bd_Log::warning("send message error,  uid[$uid] courseId[$courseId] tmpid:$smsId");
            }

            return true;
        }

        $objSms = new Zhibo_Ds_Sms();

        if ($tpl) {
            //获取班主任信息
            $msgAssistantInfo = $this->getMsgAssistantInfo($arrParams['assistantUid'], $courseInfo);
            $isVirtual = $msgAssistantInfo['isVirtual'];//是否是虚拟账号，1是，0否
            $isVirtual = 1;    //打开添加班主任信息策略
            $smsId = 0;
            if ($needAddress) { //有教材的课程
                if ($subCourseInfoList[$coreCourseId]['extData']['lectureSendTime'] > 0) {
                    $smsId = 619;
                    if (time() > $subCourseInfoList[$coreCourseId]['extData']['lectureSendTime']) {
                        $sendDay = '开课前';
                        $cur = Hkzb_Util_FuDao::getCurrentTimeStamp();
                        if ($cur > 1518105600 && $cur < 1519228800) {
                            $sendDay = '春节后陆续';
                        }
                        $arrMessage = array($courseName, $startTime, $sendDay);
                    } else {
                        $sendDay = date('n月j号', $subCourseInfoList[$coreCourseId]['extData']['lectureSendTime']);
                        $cur = Hkzb_Util_FuDao::getCurrentTimeStamp();
                        if ($cur > 1518105600 && $cur < 1519228800) {
                            $sendDay = '春节';
                        }
                        $arrMessage = array($courseName, $startTime, $sendDay . '后陆续');
                    }

                    if (!$isVirtual) {
                        $smsId = 769;
                        $arrMessage[] = $msgAssistantInfo['teacherName'];
                        $arrMessage[] = $msgAssistantInfo['phone'];
                    }

                }

            }

            if ($smsId) {
                $re = Hk_Service_SmsCommon::sendSmsByTemplateId($phone, $arrMessage, $smsId, 1);
                if (false === $re) {
                    Bd_Log::warning("send message error,  uid[$uid] courseId[$courseId] tmpid:$smsId");
                }
            } else {//无教材
                if (!$isVirtual) {//非虚拟账号
                    $smsId = 770;
                    $arrMessage[] = $msgAssistantInfo['teacherName'];
                    $arrMessage[] = $msgAssistantInfo['phone'];
                    $res = Hk_Service_SmsCommon::sendSmsByTemplateId($phone, $arrMessage, $smsId, 1);
                } else {
                    $res = $objSms->sendMsg($tpl, $arrMessage, $uid, $app, $courseId);
                }

                if (false === $res) {
                    Bd_Log::warning("send message error, tpl[$tpl] uid[$uid] courseId[$courseId]");
                }
            }

        }

        return true;
    }

    private function sendCourseMsg($arrParams)
    {
        $phone = isset($arrParams['userInfo']['phone']) ? $arrParams['userInfo']['phone'] : 0;
        $needSendMsgNotify = isset($arrParams['needSendMsgNotify']) ? $arrParams['needSendMsgNotify'] : 0;
        $courseInfo = isset($arrParams['courseInfo']) ? $arrParams['courseInfo'] : array();

        //如果不需要发短信就不发
        if(!$needSendMsgNotify){
            return true;
        }

        //没有手机号不发送
        if (empty($phone)) {
            return true;
        }

        //不是班课，不发送短信
        $courseType = isset($courseInfo['type']) ? $courseInfo['type'] : 0;
        $validCourseType  = array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
        );

        if(!in_array($courseType,$validCourseType)){
            return true;
        }

        //确认是否需要教材
        $arrParams = $this->checkIsNeedAddress($arrParams);

        //计算模板变量
        $arrMessage = $this->getSmgMessageArr($arrParams);

        //根据业务逻辑获取模板id
        $smsId = $this->getSmgTplId($arrParams);

        if(empty($smsId)){
            return true;
        }

        $res = Hk_Service_SmsCommon::sendSmsByTemplateId($phone, $arrMessage, $smsId, 1);

        if(empty($res)){
            Bd_Log::warning("短信发送失败:".json_encode($arrParams));
        }

        return true;
    }

    private function checkIsNeedAddress($arrParams)
    {
        $arrParams['needAddress'] = 0;

        //确认是否需要教材
        $courseId = $arrParams['courseId'];
        $objCourseAttribute  = new Hkzb_Ds_Fudao_CourseAttribute();
        $courseAttributeInfo = $objCourseAttribute->getCourseAttributeInfo($courseId,array('teachMaterial'));
        $teachMaterial = isset($courseAttributeInfo['teachMaterial']) ?  $courseAttributeInfo['teachMaterial'] : 0;

        if(!empty($teachMaterial)){
            $arrParams['needAddress'] = 1;
        }

        return $arrParams;
    }

    private function getSmgMessageArr($arrParams)
    {
        $subCourseInfoList = $arrParams['subCourseInfoList'];
        $needAddress = $arrParams['needAddress'];
        $courseInfo = isset($arrParams['courseInfo']) ? $arrParams['courseInfo'] : array();

        $coreCourseId = $courseInfo['defaultCoreCourseId'];
        $courseName = $courseInfo['courseName'];
        $onlineStart = $courseInfo['onlineStart'];

        $lessonInfoList = isset($arrParams['lessonInfoList']) ? $arrParams['lessonInfoList'] : array();

        //计算第一个章节的开课时间
        if(!empty($lessonInfoList)){
            $curLessonInfo = current($lessonInfoList);
            $allLessonList = isset($curLessonInfo['allLessonList']) ? $curLessonInfo['allLessonList'] : array();
            if(!empty($allLessonList)){
                $firstLessonInfo = current($allLessonList);
                $firstStartTime = isset($firstLessonInfo['startTime']) ? $firstLessonInfo['startTime'] : 0;
                if(!empty($firstStartTime)){
                    $onlineStart = $firstStartTime;
                }
            }
        }

        $startTime = date('n月j日 H:i', $onlineStart);

        //短信模板参数
        $arrMessage = array($courseName, $startTime);

        //有教材,计算教材寄送时间
        if($needAddress){
            $lectureSendTime = isset($subCourseInfoList[$coreCourseId]['extData']['lectureSendTime']) ? $subCourseInfoList[$coreCourseId]['extData']['lectureSendTime'] : 0;
            if(empty($lectureSendTime)){
                $sendDay = '开课前';
            }else{
                $sendDay = date('n月j号', $lectureSendTime);
                $sendDay .= '后陆续';
            }

            $arrMessage[] = $sendDay;
        }

        return $arrMessage;
    }

    private function getSmgTplId($arrParams)
    {
        $msgId = 0;
        if(empty($arrParams)){
            return $msgId;
        }

        $studentUid = $arrParams['uid'];
        $needAddress = $arrParams['needAddress'];

        //检测用户是否是春_2用户
        $isSeasonUser = $this->checkUserIsSeasonUser($studentUid, array('春_2'));

        //检测课程是否有特惠班课
        $arrCourseIds = $this->getAllCourseIds($arrParams);
        $isSpecialCourse = $this->checkIsSpecialCourse($arrCourseIds);

        //如果有教材
        if($needAddress){
            $msgId = 619;
        }else{
            $msgId = 618;
        }

        //如果包含特惠课，并且是新生，那么就都是无教材的模板
        if($isSpecialCourse && !$isSeasonUser){
            $msgId = 618;
        }

        return $msgId;
    }

    private function getAllCourseIds($arrParams)
    {

        $courseId = isset($arrParams['courseId']) ? $arrParams['courseId'] : 0;
        $subCourseIds = array();

        if(!empty($arrParams['subCourseInfoList'])){
            $subCourseInfoList = $arrParams['subCourseInfoList'];
            $subCourseIds = array_keys($subCourseInfoList);
        }

        $arrCourseIds[] = $courseId;

        if(!empty($subCourseIds) && is_array($subCourseIds)){
            $arrCourseIds = array_merge($arrCourseIds,$subCourseIds);
        }

        //去重数组
        $arrCourseIds = array_unique($arrCourseIds);

        return $arrCourseIds;
    }

    /**
     * 检测用户是否是相关学季老用户
     * @param $studentUid
     * @param $seasonArr
     * @return int
     */
    private function checkUserIsSeasonUser($studentUid, $seasonArr)
    {
        $isValid = 0;
        if(empty($seasonArr) || empty($studentUid)){
            return $isValid;
        }

        $startTime = strtotime('2017-12-01');//购买课程的开始时间;
        $stopTime = strtotime('2018-08-01');//购买课程的结束时间;
        $learnSeasonArr = $seasonArr;

        $objStuCourse = new Hkzb_Ds_Fudao_StudentCourse();
        //获取用户某个学季的课程
        $stuCourseList = $objStuCourse->getStudentCourseInfoBySeasonArrAndCreateTime($studentUid,$startTime,$stopTime,$learnSeasonArr);

        if(!empty($stuCourseList)){
            $isValid = 1;
        }

        return $isValid;
    }

    //检测课程中是否有特惠班课
    private function checkIsSpecialCourse($arrCourseIds=array()){
        $isSpecialCourse = 0;
        if(empty($arrCourseIds)){
            return $isSpecialCourse;
        }
        $misCourseObj = new Hkzb_Ds_Fudao_Miscourse_Course();
        $resInfo =  $misCourseObj->isSpecialCourse($arrCourseIds);

        if(empty($resInfo)){
            return $isSpecialCourse;
        }

        foreach ($resInfo as $courseId=>$flag){
            if(!empty($flag)){
                $isSpecialCourse = 1;
                break;
            }
        }

        return $isSpecialCourse;
    }



    /**
     * 专题课／班课 自动获取辅导员
     *
     * @param array $courseInfo
     *
     * @return boolean
     */
    public function autoAllocAssistant($arrParams)
    {
        $uid           = $arrParams['uid'];
        $courseInfo    = $arrParams['courseInfo'];
        $courseId      = $courseInfo['courseId'];
        $type          = $courseInfo['type'];
        $allCourseInfo = isset($arrParams['allCourseInfo']) ? $arrParams['allCourseInfo'] : array();
        //班课、预备班支付前分配班主任，专题课支付后加课时分配班主任
        if (in_array($type, array(Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG, Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRE_LONG))) {
            $courseId      = $courseInfo['defaultCoreCourseId'];//获取核心课id
            $assistantInfo = Hkzb_Util_Fudao_Category::getPrivateLongAssistantInfo($uid, $allCourseInfo, 0, 0, false);

            //解决线上问题，分配班主任已经没有用了，直接返回来虚拟班主任即可
            //if (empty($assistantInfo) || $assistantInfo['assistantUid'] <= 0) {
//                Bd_Log::warning("db error, get teachercourse fail, courseId[$courseId]");
//
//                return false;
//            }

            $assistantUid = Hkzb_Util_Fudao_Category::VIRTUAL_ASSISTANT_UID;
            if(!empty($assistantInfo['assistantUid']) && $assistantInfo['assistantUid'] > 0){
                $assistantUid = $assistantInfo['assistantUid'];
            }

            return $assistantUid;

        } else {
            $assistantUid     = 0;
            $studentLeftCnt   = 0;
            $maxStudentCnt    = 0;
            $lastAssistantUid = 0;
            $assistantList    = $this->_objDsTeacherCourse->getTeacherInfoByCourseId($courseId, Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT);
            foreach ($assistantList as $item) {
                $maxStudentCnt += $item['extData']['studentMaxCnt'];
                $leftCnt       = $item['extData']['studentMaxCnt'] - $item['studentCnt'];
                if ($leftCnt > $studentLeftCnt) {
                    $studentLeftCnt = $leftCnt;
                    $assistantUid   = $item['teacherUid'];
                }
                $lastAssistantUid = $item['teacherUid'];
            }
        }
        //如果剩余人数最多的老师，剩余人数小于5，则自动再加一个老师
        if ($studentLeftCnt < 5) {
            if ($assistantUid > 0) {
                $lastAssistantUid = $assistantUid;
            }
            $assistantUid = -1;
            /**如果设定了最大人数，不再增加辅导员**/
            if (intval($courseInfo['extData']['regMaxCnt']) > 0 && $maxStudentCnt >= $courseInfo['extData']['regMaxCnt']) {
                $assistantUid = $lastAssistantUid;
            }
        }
        //此课程线上老师都被占满了，需要添加一条teacherCourse
        if ($assistantUid == -1) {
            $startTime              = $courseInfo['onlineStart'];
            $stopTime               = $courseInfo['onlineStop'];
            $courseConds            = array();
            $courseConds['type']    = $type;
            $courseConds['`inner`'] = 0;
            $overlapConds           = "(online_start >= $startTime and online_start < $stopTime) or ";
            $overlapConds           .= "(online_stop > $startTime and online_stop <= $stopTime) or ";
            $overlapConds           .= "(online_start <= $startTime and online_stop >= $stopTime)";
            $courseConds[]          = $overlapConds;
            //获取这个时段所有线上的课程
            $courseList = $this->_objDsCourse->getCourseListByConds($courseConds, array('courseId'), 0, 2000);
            if (false === $courseList) {
                Bd_Log::warning("db error, get courselist fail");

                return false;
            }
            $courseIds = array();
            foreach ($courseList as $course) {
                $courseIds[] = $course['courseId'];
            }
            if (!empty($courseIds)) {
                //获取线上这个时段所有可能的所有班主任
                $teacherCourseList = $this->_objDsTeacherCourse->getTeacherInfoListByCourseIds($courseIds, Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT, array('teacherUid'));
                if (false === $teacherCourseList) {
                    Bd_Log::warning("db error, get teachercourse fail");

                    return false;
                }
                $assistantUidList = array();
                foreach ($teacherCourseList as $teacherCourse) {
                    $assistantUidList[] = $teacherCourse['teacherUid'];
                }
            }
            $teacherConds = array();
            if (!empty($assistantUidList)) {
                $teacherConds[] = 'teacher_uid not in (' . implode(',', array_unique($assistantUidList)) . ')';
            }
            $teacherConds['type']    = $type;
            $teacherConds['duty']    = Hkzb_Ds_Fudao_Teacher::DUTY_ASSISTANT;
            $teacherConds['deleted'] = Hkzb_Ds_Fudao_Teacher::STATUS_OK;
            $teacherList             = $this->_objDsTeacher->getTeacherListByConds($teacherConds, array('teacherUid'), 0, 1);
            if (empty($teacherList)) {//如果暂无可用的则返回当前可满足的
                //发送报警邮件
                $subject = '专题课班主任不足预报';
                $message = '课程：' . $allCourseInfo['courseName'] . '(ID:' . $courseId . ')班主任不足';
                $toMail  = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
                exec("curl http://proxy.zuoyebang.com:1925/api/mail -XPOST -d 'tos=$toMail&subject=$subject&content=$message&format=html'");

                return false;
            }
            $assistant = $teacherList[0];
            $lesson    = $arrParams['lesson'];
            if (empty($lesson)) {
                $lessonList = $this->_objDsLesson->getLessonListByCourseId($courseId, array('lessonId', 'lessonName', 'status', 'startTime', 'stopTime'), 0, 500);
                if (false === $lessonList) {
                    Bd_Log::warning("db error, get lessonlist fail, courseId[$courseId]");

                    return false;
                }
                $lesson = $this->_formatLessonList($lessonList);
            }
            $objIdAlloc = new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_FUDAO_CLASS);
            $newClassId = $objIdAlloc->getIdAlloc();
            if ($newClassId <= 0) { //classid分配失败
                Bd_Log::warning("db error, alloc classid fail");

                return false;
            }
            $arrFields = array(
                'classId'            => $newClassId,
                'teacherUid'         => $assistant['teacherUid'],
                'courseId'           => $courseId,
                'duty'               => Hkzb_Ds_Fudao_Teacher::DUTY_ASSISTANT,
                'curLessonId'        => $lesson['curLessonId'],
                'curLessonName'      => $lesson['curLessonName'],
                'curLessonStartTime' => $lesson['curLessonStartTime'],
                'curLessonStopTime'  => $lesson['curLessonStopTime'],
                'startTime'          => $lesson['curLessonStartTime'],
                'operatorUid'        => $courseInfo['operatorUid'],
                'operator'           => $courseInfo['operator'],
                'extData'            => array(
                    'studentMaxCnt' => 2000,
                ),
            );
            $res       = $this->_objDsTeacherCourse->addAssistantClass($arrFields);
            if (false === $res) {
                Bd_Log::warning("db error, add teacher fail");

                return false;
            }
            $arrFields = array(
                'studentMaxCnt' => $courseInfo['studentMaxCnt'] + 2000,
            );
            $res       = $this->_objDsCourse->updateCourse($courseId, $arrFields);
            if (false === $res) {
                Bd_Log::warning("db error, update course fail, courseId[$courseId]");

                return false;
            }
            $assistantUid = $assistant['teacherUid'];
        } else {
            //成功分配了
        }

        return $assistantUid;
    }

    private function _formatLessonList($lessonList)
    {
        $curLessonId        = 0;
        $curLessonName      = '';
        $curLessonStartTime = 0;
        $curLessonStopTime  = 0;
        $lastLessonStopTime = 0;
        $overLessonList     = array();
        foreach ($lessonList as $lesson) {
            if ($lesson['stopTime'] < $this->now && $lesson['stopTime'] > $lastLessonStopTime) {
                $lastLessonStopTime = $lesson['stopTime'];
            }

            if ($lesson['stopTime'] > $this->now) {
                $curLessonId        = $lesson['lessonId'];
                $curLessonName      = $lesson['lessonName'];
                $curLessonStartTime = $lesson['startTime'];
                $curLessonStopTime  = $lesson['stopTime'];
                break;
            } else {
                $overLessonList[] = $lesson;
            }
        }
        $res = array(
            'curLessonId'        => $curLessonId,
            'curLessonName'      => $curLessonName,
            'curLessonStartTime' => $curLessonStartTime,
            'curLessonStopTime'  => $curLessonStopTime,
            'lastLessonStopTime' => $lastLessonStopTime,
            'overLessonList'     => $overLessonList,
            'allLessonList'      => $lessonList,
        );

        return $res;
    }

    private function _tradeDiscountStrategy($arrParams)
    {
        $courseInfo    = $arrParams['courseInfo'];
        $originalPrice = $arrParams['price'];
        $studentUid    = $arrParams['uid'];
        if (isset($arrParams['hasSummerMulSubDiscount']) && $arrParams['hasSummerMulSubDiscount']) {
            //多课程优惠策略
            $summerMulSubDiscountRule = $arrParams['summerMulSubDiscountRule'];
            $discountType             = $summerMulSubDiscountRule['discountType'];
            $discountDesc             = $summerMulSubDiscountRule['discountDesc'];
            $discount                 = $summerMulSubDiscountRule['discount'];
            if ($discountType == Hkzb_Util_Fudao_Category::COURSE_SAIL_CATEGORY_DISCOUNT_TYPE_DISCOUNT) {
                $price                    = intval(round($originalPrice * $discount / 100 / 100) * 100);
                $summerMulSubDiscount     = $originalPrice - $price;
                $arrSumMulSubDiscountInfo = array(
                    'discount' => $summerMulSubDiscount,
                    'desc'     => $discountDesc,
                );
            } elseif ($discountType == Hkzb_Util_Fudao_Category::COURSE_SAIL_CATEGORY_DISCOUNT_TYPE_FULLDOWN) {
                $price                    = max($originalPrice - $discount, 0);
                $summerMulSubDiscount     = $originalPrice - $price;
                $arrSumMulSubDiscountInfo = array(
                    'discount' => $summerMulSubDiscount,
                    'desc'     => $discountDesc,
                );
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'mul_course_discount_type_invalid');
            }
            $arrParams['price']                = $price;
            $arrParams['orderPrice']           = $price;
            $arrParams['summerMulSubDiscount'] = $arrSumMulSubDiscountInfo;
        } elseif (isset($arrParams['boundDiscountStrategy']) && !empty($arrParams['boundDiscountStrategy'])) {
            //连报课程优惠策略
            $boundDiscountStrategy = $arrParams['boundDiscountStrategy'];
            $discountType          = $boundDiscountStrategy['discountType'];
            $discountDesc          = $boundDiscountStrategy['discountDesc'];
            $discount              = $boundDiscountStrategy['discount'];
            if ($discountType == Hkzb_Util_Fudao_Category::COURSE_SAIL_CATEGORY_DISCOUNT_TYPE_DISCOUNT) {
                $price             = intval(round($originalPrice * $discount / 100) * 100);
                $boundDiscount     = $originalPrice - $price;
                $boundDiscountInfo = array(
                    'discount' => $boundDiscount,
                    'zhe'      => $discountDesc,
                );
            } elseif ($discountType == Hkzb_Util_Fudao_Category::COURSE_SAIL_CATEGORY_DISCOUNT_TYPE_FULLDOWN) {
                $price             = max($originalPrice - $discount, 0);
                $boundDiscount     = $originalPrice - $price;
                $boundDiscountInfo = array(
                    'discount' => $boundDiscount,
                    'zhe'      => $discountDesc,
                );
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'bound_course_discount_type_invalid');
            }
            $arrParams['price']         = $price;
            $arrParams['orderPrice']    = $price;
            $arrParams['boundDiscount'] = $boundDiscountInfo;
        } else {
            //单门课程优惠策略
            $discountPrice = Hkzb_Util_Fudao_Category::getCourseDiscountStrategy($courseInfo, $studentUid);
            if ($discountPrice !== false) {
                $price                     = $discountPrice['price'];
                $discountDesc              = $discountPrice['discountDesc'];
                $qiu2Discount              = $originalPrice - $price;
                $arrQiu2Discount           = array(
                    'discount' => $qiu2Discount,
                    'zhe'      => $discountDesc,
                );
                $arrParams['price']        = $price;
                $arrParams['orderPrice']   = $price;
                $arrParams['qiu2Discount'] = $arrQiu2Discount;
                $arrParams['discount']     = $discountPrice['discount'];
                $arrParams['discountDesc'] = $discountDesc;
                $arrParams['gift']         = $discountPrice['gift'];
            }
        }

        return $arrParams;
    }

    private function _tradeUseCoupon($arrParams)
    {
        $uid      = $arrParams['uid'];
        $couponId = $arrParams['couponId'];
        //超级vip用户，默认使用优惠券减免所有金额
        if (Hkzb_Util_FuDao::isSuperVipUser($uid)) {
            $arrParams['usedCoupon']     = true;
            $arrParams['orderPrice']     = 0;
            $arrParams['couponDiscount'] = $arrParams['tradeInfo']['price'];
            $arrParams['usedCouponInfo'] = array(
                'couponId'     => 0,
                'couponPrice'  => $arrParams['couponDiscount'],
                'discountText' => intval($arrParams['couponDiscount'] / 100),
                'unit'         => Zhibo_Ds_Coupon::$arrCouponItemConf['unit'][Zhibo_Ds_Coupon::COUPONITEM_UNIT_PRICE],
            );

            return $arrParams;
        }

        $arrParams['usedCoupon'] = false;
        if ($couponId <= 0) {
            return $arrParams;
        }
        $objDsCoupon = new Zhibo_Ds_Coupon();
        $coupon      = $objDsCoupon->getCouponById($couponId);
        if (false === $coupon) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('couponId' => $couponId));
        }
        if (empty($coupon)) {
            Bd_Log::warning("coupon is empty, couponId[$couponId]");

            return $arrParams;
        }
        if ($coupon['uid'] != $uid) {
            Bd_Log::warning("source is not own this user, couponId[$couponId] uid[{$coupon['uid']}]");

            return $arrParams;
        }
        if ($coupon['source'] != Zhibo_Static::COUPON_SOURCE) {
            Bd_Log::warning("source is not match, couponId[$couponId] source[{$coupon['source']}]");

            return $arrParams;
        }
        if (!Zhibo_Ds_Coupon::isValidCoupon($coupon)) {
            Bd_Log::warning("coupon is not valid, couponId[$couponId]");

            return $arrParams;
        }
        $tradeInfo = $arrParams['tradeInfo'];
        if (!$objDsCoupon->checkCouponRule($coupon['rule']['rule'], $tradeInfo)) {
            Bd_Log::warning("coupon rule is not match product, couponId[$couponId]");

            return $arrParams;
        }
        $arrParams['usedCoupon']     = true;
        $arrParams['orderPrice']     = $this->_getNeedPayment($tradeInfo['price'], $coupon);
        $arrParams['couponDiscount'] = $tradeInfo['price'] - $arrParams['orderPrice'];
        $arrParams['usedCouponInfo'] = array(
            'couponId'     => $couponId,
            'couponPrice'  => $arrParams['couponDiscount'],
            'discountText' => Zhibo_Ds_Coupon::formatDiscount($coupon['discount'], $coupon['unit']),
            'unit'         => Zhibo_Ds_Coupon::$arrCouponItemConf['unit'][$coupon['unit']],
        );

        return $arrParams;
    }

    private function _getNeedPayment($price, $coupon)
    {
        if ($coupon['unit'] == Zhibo_Ds_Coupon::COUPONITEM_UNIT_PRICE) {
            $discount = $coupon['discount'];
        } elseif ($coupon['unit'] == Zhibo_Ds_Coupon::COUPONITEM_UNIT_DISCOUNT) {
            $discount = $price * (100 - $coupon['discount']) / 100;
        } else {
            $discount = 0;
        }
        $needPayment = intval($price - $discount);
        //如果用完券为负值，则默认为0
        if ($needPayment < 0) {
            $needPayment = 0;
        }

        return $needPayment;
    }

    private function _tradeGetMatchCouponList($arrParams)
    {
        $uid         = $arrParams['uid'];
        $tradeInfo   = $arrParams['tradeInfo'];
        $objDsCoupon = new Zhibo_Ds_Coupon();
        $couponList  = $objDsCoupon->getMatchCouponList($uid, $tradeInfo);
        if (false === $couponList) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        if (!empty($couponList)) {
            $changeZbk  = isset($arrParams['changeZbk']) ? intval($arrParams['changeZbk']) : 1;
            $couponList = $objDsCoupon->newFormatCouponList($couponList, $changeZbk);
            foreach ($couponList as &$v) {
                $v['type'] = $v['unitType'];
                unset($v['unitType']);
            }
        } else {
            $couponList = array();
        }
        return $couponList;
    }

    /**
     * 获取用户该订单下不可用的有效优惠劵
     * @param $uid
     * @param array $matchCouponList
     * @return array
     * @throws Hk_Util_Exception
     */
    private function _tradeGetUnMatchCouponList($uid,$matchCouponList =array()){
        $matchCouponIds = array();
        foreach ($matchCouponList as $value) {
            $matchCouponIds[] = $value['couponId'];
        }
        $objDsCoupon = new Zhibo_Ds_Coupon();
        $allCouponList = $objDsCoupon->getValidCouponListByUid($uid);
        if ($allCouponList === false){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        $unCouponList = array();
        foreach ($allCouponList as $value) {
            $couponId = Hk_Util_IdCrypt::encodeQid($value['couponId']);
            if(in_array($couponId,$matchCouponIds)){
                continue;
            }
            $unCouponList[] = $value;
        }
        $unCouponList = $objDsCoupon->newFormatCouponList($unCouponList);
        if (!empty($unCouponList)){
            foreach ($unCouponList as &$v) {
                $v['type'] = $v['unitType'];
                unset($v['unitType']);
            }
        }else{
            $unCouponList = array();
        }


        return $unCouponList;

    }
    private function _tradeProcOrder($arrParams)
    {
        $uid          = $arrParams['uid'];
        $cuid         = $arrParams['cuid'];
        $courseId     = $arrParams['courseId'];
        $courseInfo   = $arrParams['courseInfo'];
        $courseName   = $courseInfo['courseName'];
        $orderPrice   = $arrParams['orderPrice'];
        $addFlag      = $arrParams['addFlag'];
        $isOldVersion = intval($arrParams['isOldVersion']);
        $payType      = intval($arrParams['payType']);
        if ($addFlag) {
            if ($orderPrice == 0) {
                $objIdAlloc = new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_ORDER);
                $orderId    = $objIdAlloc->getIdAlloc();
                if ($orderId <= 0) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
                }
                $arrParams['orderId']       = $orderId;
                $arrParams['paymentIsZero'] = 1;
                //$this->_tradeAddTradeRecord($orderId, $arrParams);
                $ret = $this->_sendOrderNMQ($arrParams);
                if ($ret === false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::CREATE_ORDER_FAILED);
                }
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR);
            }
        } elseif ($isOldVersion) {
            $arrPayParams = array(
                'payChannel'  => '1,2,3',
                'uid'         => $uid,
                'productId'   => $courseId,
                'productName' => $courseName,
                'price'       => $orderPrice,
                'cuid'        => $cuid,
                'callbackUrl' => Zhibo_Static::PAY_CALLBACK_URL,
                'source'      => Zhibo_Static::PAY_SOURCE,
                'secret'      => Zhibo_Static::PAY_SECRET,
                'app'         => isset($arrParams['appId']) ? strval($arrParams['appId']) : '',
            );
            $arrPayInfo   = Hk_Service_Pay::getPrePayInfo($arrPayParams);
            if (false === $arrPayInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PAY_PAY_ERROR, '', $arrPayParams);
            }
            $arrPayParams   = array(
                'payChannel'  => '4',
                'uid'         => $uid,
                'productId'   => $courseId,
                'productName' => $courseName,
                'price'       => $orderPrice,
                'cuid'        => $cuid,
                'callbackUrl' => Zhibo_Static::PAY_CALLBACK_URL,
                'source'      => Zhibo_Static::PAY_SOURCE,
                'secret'      => Zhibo_Static::PAY_SECRET,
                'app'         => isset($arrParams['appId']) ? strval($arrParams['appId']) : '',
            );
            $arrPcwxPayInfo = Hk_Service_Pay::getPrePayInfo($arrPayParams);
            if (false === $arrPcwxPayInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PAY_PAY_ERROR, '', $arrPayParams);
            }
            $arrParams['payInfo']     = $arrPayInfo;
            $arrParams['pcwxPayInfo'] = $arrPcwxPayInfo;
            $this->_tradeAddTradeRecord($arrPayInfo['orderId'], $arrParams);
            $this->_tradeAddTradeRecord($arrPcwxPayInfo['orderId'], $arrParams);
        } else {
            $arrPayParams = array(
                'payChannel'  => '1,2,3',
                'uid'         => $uid,
                'productId'   => $courseId,
                'productName' => $courseName,
                'price'       => $orderPrice,
                'cuid'        => $cuid,
                'callbackUrl' => Zhibo_Static::PAY_CALLBACK_URL,
                'source'      => Zhibo_Static::PAY_SOURCE,
                'secret'      => Zhibo_Static::PAY_SECRET,
                'app'         => isset($arrParams['appId']) ? strval($arrParams['appId']) : '',
            );
            if ($payType == 1) {
                $arrPayParams['payChannel'] = '4';
            }
            $arrPayInfo = Hk_Service_Pay::getPrePayInfo($arrPayParams);
            if (false === $arrPayInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PAY_PAY_ERROR, '', $arrPayParams);
            }
            if ($payType == 1) {
                $arrParams['pcwxPayInfo'] = $arrPayInfo;
            } else {
                $arrParams['payInfo'] = $arrPayInfo;
            }
            $this->_tradeAddTradeRecord($arrPayInfo['orderId'], $arrParams);
            if ($payType == 1 && $arrParams['usedCoupon']) {
                //暂时不锁优惠券
                //Hkzb_Service_Coupon::lockCoupon($arrParams['couponId'], Zhibo_Static::PAY_SOURCE, Zhibo_Static::PAY_SECRET, $arrPayInfo['orderId']);
            }
        }

        return $arrParams;
    }

    /**
     * @param $arrParams
     *
     * @desc 处理购物车订单
     * @return mixed
     * @throws Hk_Util_Exception
     */
    private function _cartTradeProcOrder($arrParams, $arrJumpFrom)
    {
        $uid                      = intval($arrParams['uid']);
        $payType                  = intval($arrParams['payType']);
        $payChannel               = intval($arrParams['payChannel']);
        $vcname                   = $arrParams['vcname'];
        $phone                    = $arrParams['phone'];
        $jumpFrom                 = $arrParams['jumpFrom'];
        $plat                     = $arrParams['plat'];
        $lastfrom                 = $arrParams['lastfrom'];
        $orifrom                  = $arrParams['orifrom'];
        $logpath                  = $arrParams['logpath'];
        $addressInfo              = $arrParams['addressInfo'];
        $vc                       = $arrParams['vc'];
        $cuid                     = $arrParams['cuid'];
        $appType                  = $arrParams['appType'];
        $appId                    = isset($arrParams['appId']) ? strval($arrParams['appId']) : '';
        $channel                  = strval($arrParams['channel']);
        $openId                   = strval($arrParams['openId']);
        $objIdAlloc               = new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_ORDER);
        $arrRecordList            = array();
        $payment                  = 0;
        $springCoursePayment      = 0;//标记订单中春季课金额，针对春季课送券特殊处理
        $boundList                = isset($arrParams['boundList']) ? $arrParams['boundList'] : array();//用于在支付订单里保存绑定销售课的关联关系
        $boundDesc                = isset($arrParams['boundDesc']) ? $arrParams['boundDesc'] : '';//多科连报文案
        $gift                     = isset($arrParams['gift']) ? $arrParams['gift'] : array();
        $summerMulSubList         = isset($arrParams['summerMulSubList']) ? $arrParams['summerMulSubList'] : array();
        $summerMulSubDesc         = isset($arrParams['summerMulSubDesc']) ? $arrParams['summerMulSubDesc'] : array();
        $summerMulSubOrderList    = array();
        $summerMulSubDiscountRule = array();
        $tradeParamInfo           = array();    //新系统参数
        $giftCourseTradeRecord    = array();    //赠课订单数据
        foreach ($arrParams['courseList'] as $course) {
            $mulDiscountRuleSortTag = isset($course['mulDiscountRuleSortTag']) ? $course['mulDiscountRuleSortTag'] : -1;//-1表示无折扣策略
            $arrRecordInfo          = array(
                'uid'                    => $uid,
                'courseId'               => $course['courseId'],
                'skuId'                  => isset($course['skuId']) ? $course['skuId'] : 0,
                'courseInfo'             => $course['courseInfo'],
                'orderPrice'             => $course['orderPrice'],
                'originalPrice'          => $course['originalPrice'],
                'assistantInfo'          => $course['assistantInfo'],
                'usedCouponInfo'         => $course['usedCouponInfo'],
                'usedCoupon'             => $course['usedCoupon'],
                'vcname'                 => $vcname,
                'phone'                  => $phone,
                'jumpFrom'               => isset($arrJumpFrom[$course['courseId']]) ? $arrJumpFrom[$course['courseId']] : $jumpFrom,
                'appId'                  => strval($appId),
                'channel'                => strval($channel),
                'vc'                     => $vc,
                'cuid'                   => $cuid,
                'appType'                => $appType,
                'type'                   => isset($course['saleType']) && $course['saleType'] == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE ? Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY : Hkzb_Ds_Fudao_TradeRecord::TYPE_SUB,
                'fromShoppingList'       => $jumpFrom == 'from_shopping_list' ? 1 : 0,
                'payChannel'             => $payChannel,
                'mulDiscountRuleSortTag' => $mulDiscountRuleSortTag,
                'plat'                    => $plat,
                'lastfrom'               => $lastfrom,
                'orifrom'                => $orifrom,
                'logpath'                => $logpath,
                'addressInfo'            => $addressInfo,
            );
            if(isset($course['isPresent']) && $course['isPresent']){
                $giftCourseTradeRecord[$course['courseId']] = 0;
            }
            if (isset($course['boundDiscount'])) {
                $arrRecordInfo['boundDiscount'] = $course['boundDiscount'];
            }
            if (isset($course['qiu2Discount'])) {
                $arrRecordInfo['qiu2Discount'] = $course['qiu2Discount'];
            }
            if (isset($course['expendDiscount'])) {
                $arrRecordInfo['expendDiscount'] = $course['expendDiscount'];
            }
            if (isset($course['summerMulSubDiscount'])) {
                $arrRecordInfo['summerMulSubDiscount'] = $course['summerMulSubDiscount'];
                $userInfo                              = Hkzb_Ds_Fudao_StudentInfo::getUserInfo($uid);
                $isOldUser                             = isset($userInfo['isLearnSeasonOld']) ? $userInfo['isLearnSeasonOld'] : 0;
                $learnSeason                           = $course['courseInfo']['learnSeason'];
                $grade                                 = $course['courseInfo']['grade'];
                if ($mulDiscountRuleSortTag == 201) {//特殊活动标记
                    $arrRecordInfo['summerMulSubDiscountRule'] = array(
                        1 => 100,
                        2 => 100,
                        3 => 67,
                    );
                } elseif ($learnSeason == '秋_2') {
                    $arrRecordInfo['summerMulSubDiscountRule'] = array(
                        1 => 100,
                        2 => 95,
                        3 => 90,
                    );
                }
                if (empty($summerMulSubDiscountRule)) {
                    $summerMulSubDiscountRule = $arrRecordInfo['summerMulSubDiscountRule'];
                }
            }
            if (isset($course['saleType']) && $course['saleType'] == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                $arrRecordInfo['expendPrice']    = $course['expendPrice'];
                $arrRecordInfo['skuExpendPrice'] = $course['skuExpendPrice'];
            }
            if (isset($course['needNotifyToAssistant']) && $course['needNotifyToAssistant']) {
                $arrRecordInfo['needNotifyToAssistant'] = $course['needNotifyToAssistant'];
            }

            //是否是特惠课的标识
            if (isset($course['specialSkuType']) && $course['specialSkuType']) {
                $arrRecordInfo['specialSkuType'] = $course['specialSkuType'];
            }

            $arrRecordList[] = $arrRecordInfo;
            $payment         += $course['orderPrice'];

            //新系统参数
            $tradeParamInfo[$course['courseId']] = array(
                'assistantUid' => $course['assistantInfo']['assistantUid'],
                'teacherUid'   => $course['courseInfo']['teacherUid'],
                'learnSeason'  => $course['courseInfo']['learnSeason'],
                'grade'        => $course['courseInfo']['grade'],
                'subject'      => $course['courseInfo']['subject'],
            );
        }
        //支付金额为0直接走加课逻辑
        if ($payment == 0) {
            $paymentIsZero = 1;
            $arrOrderList  = array();

            $zbIdAlloc = new Zb_Service_IdAlloc(Zb_Service_IdAlloc::NAME_ORDER);
            $len = sizeof($arrRecordList) + 1;
            $tradeRecordIds = $zbIdAlloc->getIdAllocBatch($len);
            if (empty($tradeRecordIds)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::IDALLOC_ERROR, 'get batch id error: 0 yuan');
            }
            $payOrderId = $tradeRecordIds[0];
            //$payOrderId    = $objIdAlloc->getIdAlloc();
            if ($payOrderId <= 0) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            foreach ($arrRecordList as $k=>&$record) {
                //$orderId = $objIdAlloc->getIdAlloc();
                $orderId = $tradeRecordIds[$k+1];
                if ($orderId <= 0) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
                }
                $record['paymentIsZero']   = $paymentIsZero;
                $record['orderId']         = $orderId;
                $record['purchaseOrderId'] = $payOrderId;
                $arrOrderList[]            = $orderId;
                $courseId                  = $record['courseId'];
                $mulDiscountRuleSortTag    = $record['mulDiscountRuleSortTag'];
                if (!empty($summerMulSubList) && isset($summerMulSubList[$mulDiscountRuleSortTag])) {
                    $summerMulSubOrderList[$mulDiscountRuleSortTag][$courseId] = $orderId;
                }

                //新体统参数
                $tradeParamInfo[$courseId]['orderId'] = $orderId;
                if(isset($giftCourseTradeRecord[$courseId])){
                    $giftCourseTradeRecord[$courseId] = $orderId;
                }
            }
            //这里添加关联信息，添加支付订单信息
            $payOrder = array(
                'uid'           => $uid,
                'orderId'       => $payOrderId,
                'orderPrice'    => 0,
                'courseId'      => 0,
                'courseInfo'    => array(
                    'teacherUid' => 0,
                    'subject'    => 0,
                    'grade'      => 0,
                    'onlineTime' => '',
                ),
                'assistantInfo' => array(
                    'assistantUid' => 0
                ),
                'vcname'        => $vcname,
                'phone'         => $phone,
                'jumpFrom'      => $jumpFrom,
                'appId'         => strval($appId),
                'channel'       => strval($channel),
                'vc'            => $vc,
                'cuid'          => $cuid,
                'appType'       => $appType,
                'consistOf'     => $arrOrderList,
                'payment'       => $payment,
                'type'          => Hkzb_Ds_Fudao_TradeRecord::TYPE_PACK,
                'payChannel'    => $payChannel,
                'plat'          => $plat,
                'lastfrom'      => $lastfrom,
                'orifrom'       => $orifrom,
                'logpath'       => $logpath,
                'paymentIsZero' => $paymentIsZero,
            );
            if (!empty($boundList)) {
                $payOrder['boundList'] = $boundList;
            }
            if (!empty($boundDesc)) {
                $payOrder['boundDesc'] = $boundDesc;
            }
            if (!empty($summerMulSubDesc)) {
                $payOrder['summerMulSubDesc'] = $summerMulSubDesc;
            }
            if (!empty($summerMulSubOrderList)) {
                $payOrder['summerMulSubOrderList'] = $summerMulSubOrderList;
            }
            if (!empty($gift)) {
                $payOrder['gift'] = $gift;
            }
            if (!empty($salesId)) {
                $payOrder['salesId'] = $salesId;
            }
            /*
            //开启事务
            $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
            $db     = Hk_Service_Db::getDB($dbName);
            if (empty($db)) {
                Bd_Log::warning("connect db fail, db[$dbName]");
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            $res = $db->startTransaction();
            if (empty($res)) {
                Bd_Log::warning("start transaction fail, db[$dbName]");
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }

            try {
                $this->_tradeAddTradeRecord($payOrderId, $payOrder, false);
            } catch (Hk_Util_Exception $e) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                }
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            */
            //下订单异步化  这里父订单入库
            $ret = $this->_sendOrderNMQ($payOrder);
            if ($ret == false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::CREATE_ORDER_FAILED, '', array('orderId' => $payOrder['orderId']));
            }

            //暑期班课自订单补绑定课程订单信息
            if (!empty($summerMulSubOrderList)) {
                foreach ($arrRecordList as &$tmpOrder) {
                    $mulDiscountRuleSortTag = $tmpOrder['mulDiscountRuleSortTag'];
                    if (isset($summerMulSubOrderList[$mulDiscountRuleSortTag])) {
                        $tmpOrder['summerMulSubOrderList'] = $summerMulSubOrderList[$mulDiscountRuleSortTag];
                    }
                }
            }
            /*
            $arrRes = $this->enterAddTradeRecord($arrRecordList, false);
            if ($arrRes['errNo'] != 0) {
                throw new Hk_Util_Exception($arrRes['errNo'], '', $arrRecordList);
            }
            */
            //下订单异步化 这里子订单入库
            foreach ($arrRecordList as $arrRecord) {
                $ret = $this->_sendOrderNMQ($arrRecord);
                if ($ret == false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::CREATE_ORDER_FAILED, '', array('orderId' => $arrRecord['orderId']));
                }
            }

            /*
            //0元课异步加课
            //$arrRes       = $this->_enterAddCourse($arrOrderList);
            $arrRes = $this->enterAddCourse($payOrderId, $arrOrderList, false);
            if ($arrRes === false) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                }
                throw new Hk_Util_Exception($arrRes['errNo'], '', $arrRecordList);
            }

            $res = $db->commit();
            if (empty($res)) {
                Bd_Log::warning("commit fail, db[$dbName]");
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            */
            $arrParams['payStatus'] = 1;
            $arrParams['payInfo']   = array('orderId' => $payOrderId);

            //tradeParamInfo 同步新trade系统需要
            $arrParams['tradeParamInfo'] = $tradeParamInfo;
            $arrParams['giftCourseTradeRecord'] = $giftCourseTradeRecord;
            return $arrParams;
        } else {
            if ($payChannel > 0) {
                $realPayChannel = strval($payChannel);
            } elseif ($payType == 0) {
                $realPayChannel = '1,2,3';
            } elseif ($payType == 1) {
                $realPayChannel = '4';
            } else {
                //目前不存这类情况
            }

            $arrPayParams = array(
                'payChannel'  => $realPayChannel,
                'uid'         => $uid,
                'productId'   => 1,
                'productName' => $arrRecordList[0]['courseInfo']['courseName'] . '等' . strval(count($arrRecordList)) . '门课',
                'price'       => $payment,
                'openId'      => $openId,
                'cuid'        => $cuid,
                'channel'     => $channel,
                'callbackUrl' => Zhibo_Static::PAY_CALLBACK_URL,
                'source'      => Zhibo_Static::PAY_SOURCE,
                'secret'      => Zhibo_Static::PAY_SECRET,
                'app'         => $appId,
                'appType'     => $appType,
                'os'          => isset($arrParams['os']) ? strval($arrParams['os']) : '',
                'plat'          => $plat,
                'lastfrom'      => $lastfrom,
                'orifrom'       => $orifrom,
                'logpath'       => $logpath,
            );
            if ($payType == 1) {
                $arrPayParams['payChannel'] = $realPayChannel;
            }
            if ($payChannel == 11) {
                $arrPayParams['returnUrl'] = Hk_Util_Host::getHost() . '/goods/na/payment/payresult?isShare=1';
            }
            $arrPayInfo = Hk_Service_Pay::getPrePayInfo($arrPayParams);
            if (false === $arrPayInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PAY_PAY_ERROR, '', $arrPayParams);
            }
            $arrOrderList = array();

            $zbIdAlloc = new Zb_Service_IdAlloc(Zb_Service_IdAlloc::NAME_ORDER);
            $len = sizeof($arrRecordList);
            $tradeRecordeIds = $zbIdAlloc->getIdAllocBatch($len);
            if (empty($tradeRecordeIds)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::IDALLOC_ERROR, 'get batch id error', $arrPayParams);
            }
            foreach ($arrRecordList as $k=>&$record) {
                $orderId = $tradeRecordeIds[$k];
                //$orderId = $objIdAlloc->getIdAlloc();
                if ($orderId <= 0) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
                }
                $record['orderId']         = $orderId;
                $record['purchaseOrderId'] = $arrPayInfo['orderId'];
                $arrOrderList[]            = $orderId;
                $courseId                  = $record['courseId'];
                $mulDiscountRuleSortTag    = $record['mulDiscountRuleSortTag'];
                if (!empty($summerMulSubList) && isset($summerMulSubList[$mulDiscountRuleSortTag])) {
                    $summerMulSubOrderList[$mulDiscountRuleSortTag][$courseId] = $orderId;
                }

                //新体统参数
                $tradeParamInfo[$courseId]['orderId'] = $orderId;
                if(isset($giftCourseTradeRecord[$courseId])){
                    $giftCourseTradeRecord[$courseId] = $orderId;
                }

            }

            //这里添加关联信息，添加支付订单信息
            $payOrder = array(
                'uid'                 => $uid,
                'orderId'             => $arrPayInfo['orderId'],
                'orderPrice'          => 0,
                'courseId'            => 0,
                'courseInfo'          => array(
                    'teacherUid' => 0,
                    'subject'    => 0,
                    'grade'      => 0,
                    'onlineTime' => '',
                ),
                'assistantInfo'       => array(
                    'teacherUid' => 0
                ),
                'vcname'              => $vcname,
                'phone'               => $phone,
                'jumpFrom'            => $jumpFrom,
                'appId'               => strval($appId),
                'channel'             => strval($channel),
                'vc'                  => $vc,
                'cuid'                => $cuid,
                'appType'             => $appType,
                'consistOf'           => $arrOrderList,
                'payment'             => $payment,
                'type'                => Hkzb_Ds_Fudao_TradeRecord::TYPE_PACK,
                'springCoursePayment' => $springCoursePayment,
                'payChannel'          => $payChannel,
                'plat'                 => $plat,
                'lastfrom'            => $lastfrom,
                'orifrom'             => $orifrom,
                'logpath'             => $logpath,
            );
            if (!empty($boundList)) {
                $payOrder['boundList'] = $boundList;
            }
            if (!empty($boundDesc)) {
                $payOrder['boundDesc'] = $boundDesc;
            }
            if (!empty($summerMulSubDesc)) {
                $payOrder['summerMulSubDesc'] = $summerMulSubDesc;
            }
            if (!empty($summerMulSubOrderList)) {
                $payOrder['summerMulSubOrderList'] = $summerMulSubOrderList;
            }
            if (!empty($gift)) {
                $payOrder['gift'] = $gift;
            }

            //$this->_tradeAddTradeRecord($arrPayInfo['orderId'], $payOrder, false);
            /*
             * 这里更新下单的异步化流程 支付父订单
             *
             * */
            $ret = $this->_sendOrderNMQ($payOrder);
            if ($ret == false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::CREATE_ORDER_FAILED, '', $payOrder);
            }

            //暑期班课自订单补绑定课程订单信息
            if (!empty($summerMulSubOrderList)) {
                foreach ($arrRecordList as &$tmpOrder) {
                    $mulDiscountRuleSortTag = $tmpOrder['mulDiscountRuleSortTag'];
                    if (isset($summerMulSubOrderList[$mulDiscountRuleSortTag])) {
                        $tmpOrder['summerMulSubOrderList'] = $summerMulSubOrderList[$mulDiscountRuleSortTag];
                    }
                }
            }

            //$arrRes = $this->enterAddTradeRecord($arrRecordList, false);

            /*
             * 这里更新下单的异步化流程  支付子订单
             *
             * */
            foreach ($arrRecordList as $arrRecord) {
                $ret = $this->_sendOrderNMQ($arrRecord);
                if ($ret == false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::CREATE_ORDER_FAILED, '', $arrRecord);
                }
            }

            /*
            foreach ($arrRecordList as $course) {
                if ($payType == 1 && $course['usedCoupon']) {
                    //暂时不锁优惠券
                    //Hkzb_Service_Coupon::lockCoupon($course['usedCouponInfo']['couponId'], Zhibo_Static::PAY_SOURCE, Zhibo_Static::PAY_SECRET, $arrPayInfo['orderId']);
                }
            }
            */
            $ret = array(
                'payInfo'        => $arrPayInfo,
                'courseList'     => $arrParams['courseList'],
                'boundCidList'   => $arrParams['boundCidList'],

                //tradeParamInfo 同步新trade系统需要
                'tradeParamInfo' => $tradeParamInfo,
                'giftCourseTradeRecord' => $giftCourseTradeRecord,
            );

            return $ret;
        }
    }

    private function _tradeCheckStudentCanBuy($arrParams)
    {
        $uid         = $arrParams['uid'];
        $courseInfo  = $arrParams['courseInfo'];
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($uid);
        if (false === $teacherInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        if (!empty($teacherInfo) && $teacherInfo['deleted'] != Hkzb_Ds_Fudao_Teacher::STATUS_DELETED) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_IS_TEACHER, '', array('uid' => $uid));
        }
        $objUserTradeRecord = new Hkzb_Ds_Fudao_UserTradeRecord();
        $res                = $objUserTradeRecord->getOrderIdListByStudentUidCourseId($uid, $courseInfo['courseId']);
        if (!empty($res)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_CHECK_ERROR, '', array('uid' => $uid, 'courseId' => $courseInfo['courseId']));
        }
        $specialCourseList = Hkzb_Util_Fudao_NewUserCourse::getAllCourse();
        if (in_array($courseInfo['courseId'], $specialCourseList) && !in_array($courseInfo['courseId'], array(18984, 18986))) {
            $arrRedisConf = Bd_Conf::getConf("/hk/redis/fudao");
            $objRedis     = new Hk_Service_Redis($arrRedisConf['service']);
            $cacheKey    = 'ZHIBOKE_STUDENT_' . $uid;
            $studentFlag = $objRedis->get($cacheKey);
            if ($studentFlag == 1) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_CHECK_ERROR, '新用户专享', array('uid' => $uid, 'courseId' => $courseInfo['courseId']));
            }
        }

        //=====需要测试的课程，如果没有测试通过，不允许报名 ,秋2开始
        if ($this->now > Hkzb_Util_Fudao_Category::AUTUMN_COURSE_SEASON_TWO_OLD_USER_SELL_START_TIME) {
            $allCourseInfo = isset($arrParams['allCourseInfo']) ? $arrParams['allCourseInfo'] : array();
            $examType      = Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_COURSE_BEFORE;
            $userExamInfo  = Hkzb_Ds_Fudao_Advanced_Course::getCourseExamInfoByUidCourseId($uid, $allCourseInfo, $examType);
            $tesStatus     = $userExamInfo['testStatus'];
            if ($tesStatus == 1 || $tesStatus == 2) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_CHECK_ERROR, '用户需要测试才能购买', array('uid' => $uid, 'courseId' => $courseInfo['courseId']));
            }

        }

        return true;
    }

    private function _tradeCheckCourseValidity($arrParams)
    {
        $allCourseInfo = $arrParams['allCourseInfo'];
        $studentUid    = $arrParams['uid'];
        $courseInfo    = Hkzb_Util_Fudao_Category::getCourseInfo($allCourseInfo, $studentUid);
        $courseId      = $allCourseInfo['courseId'];
        $now           = $this->now;

        if (empty($courseInfo)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_EXIST, '', array('courseId' => $courseId));
        }
        if ($courseInfo['status'] !== Hkzb_Ds_Fudao_Course::STATUS_ONLINE) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_ONLINE, '', array('courseId'));
        }
        if ($courseInfo['registerStartTime'] > $now) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_BEGIN, '', array('courseId' => $courseId));
        }
        if ($courseInfo['registerStopTime'] < $now) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_HAS_END, '', array('courseId' => $courseId));
        }
        if ($courseInfo['cannotBuyPreLongCourse']) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CAN_NOT_BUY, '', array('courseId' => $courseId));
        }

        return $courseInfo;
    }

    private function _tradeGetAssistantInfo($arrParams)
    {
        $courseInfo    = $arrParams['courseInfo'];
        $assistantUid  = $arrParams['assistantUid'];
        $allCourseInfo = $arrParams['allCourseInfo'];
        $isAutoAlloc   = 0;
        if ($assistantUid <= 0) {
            if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
                $assistantInfo = array(
                    'isShow'        => 0,
                    'assistantUid'  => -1,
                    'assistantName' => '',
                );

                return $assistantInfo;
            } elseif (in_array($courseInfo['type'], array(Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG, Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG))) {
                //班课自动分配辅导员逻辑
                $assistantUid = $this->autoAllocAssistant($arrParams);
                if (false === $assistantUid) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('assistantUid' => $assistantUid));
                }
                $isAutoAlloc = 1;
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('assistantUid' => $assistantUid));
            }
        }
        $assistantList = $this->_objDsTeacherCourse->getAssistantClassInfo($assistantUid, $courseInfo['defaultCoreCourseId'], 0, array('studentCnt', 'extData'));
//        if (false === $assistantList) {
//            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('assistantUid' => $assistantUid));
//        }
//        if (empty($assistantList)) {
//            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_NOT_EXIST, '', array('assistantUid' => $assistantUid));
//        }

        //解决线上问题，分配班主任之前不再支付前分配
        if(empty($assistantList)){
            $assistantInfo = array(
                'isShow'        => 0,
                'assistantUid'  => $assistantUid,
                'assistantName' => '',
            );
            return $assistantInfo;
        }

        $assistantInfo = array();
        foreach ($assistantList as $item) {
            if (empty($assistantInfo)) {
                $assistantInfo = $item;
                if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
                    break;
                }
            } else {
                if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
                    $assistantInfo = $item;
                    break;
                }
            }
        }
        if ($assistantInfo['studentCnt'] >= $assistantInfo['extData']['studentMaxCnt']) {
            //特权用户，在报满的情况可以继续报名，不抛异常
            $objSpecialClass = new Hkzb_Ds_Gnmis_SpecialClass();
            $res             = $objSpecialClass->getClassRecordByUids($arrParams['courseId'], $arrParams['uid']);
            if (empty($res) && $isAutoAlloc) {
                //throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_STUDENT_EXEED, '', array('assistantUid' => $assistantUid));
            }
        }
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($assistantUid, array('teacherName'), true);
        $data        = array(
            'assistantUid'         => $assistantUid,
            'assistantName'        => $teacherInfo['teacherName'],
            'assistantLeftCntDesc' => '',
        );
        if ($assistantUid == Hkzb_Util_Fudao_Category::VIRTUAL_ASSISTANT_UID) {
            $data['isShow'] = 0;
        } else {

            $data['isShow'] = Hkzb_Util_Fudao_Format::isShowSmallClass($allCourseInfo, $arrParams['uid']);
        }

        return $data;
    }

    public function tradeGetAssistantInfo($arrParams)
    {
        $courseInfo    = $arrParams['courseInfo'];
        $assistantUid  = $arrParams['assistantUid'];
        $allCourseInfo = $arrParams['allCourseInfo'];
        $isAutoAlloc   = 0;
        if ($assistantUid <= 0) {
            if ($courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
                $assistantInfo = array(
                    'isShow'        => 0,
                    'assistantUid'  => -1,
                    'assistantName' => '',
                );

                return $assistantInfo;
            } elseif (in_array($courseInfo['type'], array(Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG, Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG))) {
                //班课自动分配辅导员逻辑
                $assistantUid = $this->autoAllocAssistant($arrParams);
                if (false === $assistantUid) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('assistantUid' => $assistantUid));
                }
                $isAutoAlloc = 1;
            } else {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('assistantUid' => $assistantUid));
            }
        }
        $assistantList = $this->_objDsTeacherCourse->getAssistantClassInfo($assistantUid, $courseInfo['defaultCoreCourseId'], 0, array('studentCnt', 'extData'));
        if (false === $assistantList) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('assistantUid' => $assistantUid));
        }
        if (empty($assistantList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_NOT_EXIST, '', array('assistantUid' => $assistantUid));
        }
        $assistantInfo = array();
        foreach ($assistantList as $item) {
            if (empty($assistantInfo)) {
                $assistantInfo = $item;
                if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
                    break;
                }
            } else {
                if ($item['studentCnt'] < $item['extData']['studentMaxCnt']) {
                    $assistantInfo = $item;
                    break;
                }
            }
        }
        if ($assistantInfo['studentCnt'] >= $assistantInfo['extData']['studentMaxCnt']) {
            //特权用户，在报满的情况可以继续报名，不抛异常
            $objSpecialClass = new Hkzb_Ds_Gnmis_SpecialClass();
            $res             = $objSpecialClass->getClassRecordByUids($arrParams['courseId'], $arrParams['uid']);
            if (empty($res) && $isAutoAlloc) {
                //目前课程库存不再依赖班主任信息，这里不检查班主任名额，班主任分配由班主任团队处理
                //throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_STUDENT_EXEED, '', array('assistantUid' => $assistantUid));
            }
        }
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($assistantUid, array('teacherName'), true);
        $data        = array(
            'assistantUid'         => $assistantUid,
            'assistantName'        => $teacherInfo['teacherName'],
            'assistantLeftCntDesc' => '',
        );
        if ($assistantUid == Hkzb_Util_Fudao_Category::VIRTUAL_ASSISTANT_UID) {
            $data['isShow'] = 0;
        } else {

            $data['isShow'] = Hkzb_Util_Fudao_Format::isShowSmallClass($allCourseInfo, $arrParams['uid']);
        }

        return $data;
    }

    private function _tradeAddTradeRecord($orderId, $arrParams, $needTrans = true)
    {
        $studentUid   = $arrParams['uid'];
        $courseId     = $arrParams['courseId'];
        $assistantUid = $arrParams['assistantInfo']['assistantUid'];
        $courseInfo   = $arrParams['courseInfo'];
        $onlineTime   = $courseInfo['onlineTime'];
        $arrFields    = array(
            'orderId'      => $orderId,
            'studentUid'   => $studentUid,
            'courseId'     => $courseId,
            'grade'        => $courseInfo['grade'],
            'subject'      => $courseInfo['subject'],
            'payment'      => $arrParams['orderPrice'],
            'teacherUid'   => $courseInfo['teacherUid'],
            'assistantUid' => $assistantUid,
            'onlineTime'   => $onlineTime,
            'extData'      => array(
                'vcname'   => $arrParams['vcname'],
                'phone'    => $arrParams['phone'],
                'jumpFrom' => $arrParams['jumpFrom'],
                'vc'       => $arrParams['vc'],
                'cuid'     => $arrParams['cuid'],
                'appType'  => $arrParams['appType'],
                'ua'       => substr($_SERVER['HTTP_USER_AGENT'], 0, 200),
                'payment'  => $arrParams['orderPrice'],
                'plat'     => $arrParams['plat'],
                'lastfrom' => $arrParams['lastfrom'],
                'orifrom'  => $arrParams['orifrom'],
                'logpath'  => $arrParams['logpath'],
            ),
        );
        if (isset($arrParams['usedCouponInfo'])) {
            $arrFields['extData']['couponInfo'] = $arrParams['usedCouponInfo'];
        }
        if (isset($arrParams['qiu2Discount'])) {
            $arrFields['extData']['qiu2DiscountInfo'] = $arrParams['qiu2Discount'];
        }
        if (isset($arrParams['consistOf'])) {
            $arrFields['extData']['consistOf']     = $arrParams['consistOf'];
            $arrFields['userExtData']['consistOf'] = $arrParams['consistOf'];
        }
        if (isset($arrParams['payment'])) {
            $arrFields['extData']['payment'] = $arrParams['payment'];
        }
        if (isset($arrParams['purchaseOrderId'])) {
            $arrFields['extData']['purchaseOrderId'] = $arrParams['purchaseOrderId'];
        }
        if (isset($arrParams['boundDiscount'])) {
            $arrFields['extData']['boundDiscount'] = $arrParams['boundDiscount'];
        }
        if (isset($arrParams['boundList'])) {
            $arrFields['extData']['boundList']     = $arrParams['boundList'];
            $arrFields['userExtData']['boundList'] = $arrParams['boundList'];
        }
        if (isset($arrParams['boundDesc'])) {
            $arrFields['extData']['boundDesc']     = $arrParams['boundDesc'];
            $arrFields['userExtData']['boundDesc'] = $arrParams['boundDesc'];
        }
        if (isset($arrParams['originalPrice'])) {
            $arrFields['extData']['originalPrice'] = $arrParams['originalPrice'];
        }
        if (isset($arrParams['type'])) {
            $arrFields['type'] = intval($arrParams['type']);
        }
        if (isset($arrParams['fromShoppingList'])) {
            $arrFields['extData']['fromShoppingList'] = intval($arrParams['fromShoppingList']);
        }
        if (isset($arrParams['springCoursePayment'])) {
            $arrFields['extData']['springCoursePayment'] = intval($arrParams['springCoursePayment']);
        }
        if (isset($arrParams['summerMulSubDesc'])) {
            $arrFields['extData']['summerMulSubDesc'] = $arrParams['summerMulSubDesc'];
        }
        if (isset($arrParams['summerMulSubOrderList'])) {
            $arrFields['extData']['summerMulSubOrderList'] = $arrParams['summerMulSubOrderList'];
        }
        if (isset($arrParams['summerMulSubDiscountRule'])) {
            $arrFields['extData']['summerMulSubDiscountRule'] = $arrParams['summerMulSubDiscountRule'];
        }
        if ($arrParams['payChannel']) {
            $arrFields['extData']['payChannel'] = $arrParams['payChannel'];
        }
        if (isset($arrParams['appId']) && $arrParams['appId']) {
            $arrFields['extData']['app'] = $arrParams['appId'];
        }
        if (isset($arrParams['channel']) && $arrParams['channel']) {
            $arrFields['extData']['channel'] = $arrParams['channel'];
        }
        if (isset($arrParams['gift']) && !empty($arrParams['gift'])) {
            $arrFields['extData']['gift'] = $arrParams['gift'];
        }
        if (Hkzb_Util_FuDao::isSuperVipUser($studentUid)) {
            $arrFields['extData']['isSuperVipUser'] = 1;
        }
        if (isset($arrParams['skuId']) && !empty($arrParams['skuId'])) {
            $arrFields['extData']['skuId'] = $arrParams['skuId'];
        }
        if (isset($arrParams['expendPrice'])) {
            $arrFields['extData']['expendPrice'] = $arrParams['expendPrice'];
        }
        if (isset($arrParams['skuExpendPrice'])) {
            $arrFields['extData']['skuExpendPrice'] = $arrParams['skuExpendPrice'];
        }
        if (isset($arrParams['expendDiscount'])) {
            $arrFields['extData']['expendDiscount'] = $arrParams['expendDiscount'];
        }
        if (isset($arrParams['needNotifyToAssistant'])) {
            $arrFields['extData']['needNotifyToAssistant'] = $arrParams['needNotifyToAssistant'];
        }

        //是否是特惠课标识
        if (isset($arrParams['specialSkuType'])) {
            $arrFields['extData']['specialSkuType'] = $arrParams['specialSkuType'];
        }

        $ret = $this->_objDsTradeRecord->addTradeRecord($arrFields, $needTrans);
        if (false === $ret) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', $arrFields);
        }

        return $ret;
    }

    public function enterAddTradeRecord($arrRecordList, $needTrans = true)
    {
        $arrRes = array(
            'errNo'  => 0,
            'errMsg' => '',
            'data'   => array(),
        );
        $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
        if ($needTrans) {
            $db = Hk_Service_Db::getDB($dbName);
            if (empty($db)) {
                Bd_Log::warning("connect db fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = '获取fudao数据库连接失败';

                return $arrRes;
            }
            $res = $db->startTransaction();
            if (empty($res)) {
                Bd_Log::warning("start transaction fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库开启事务失败';

                return $arrRes;
            }
        }
        try {
            foreach ($arrRecordList as $record) {
                $orderId = $record['orderId'];
                $this->_tradeAddTradeRecord($orderId, $record, false);
                //发起nmq异步订单下单流程
            }
        } catch (Hk_Util_Exception $e) {
            $arrRes['errNo']  = $e->getErrNo();
            $arrRes['errMsg'] = $e->getErrMsg();
            if ($needTrans) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                    $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                    $arrRes['errMsg'] = 'fudao数据库回滚失败';
                }
            }

            return $arrRes;
        }
        if ($needTrans) {
            $res = $db->commit();
            if (empty($res)) {
                Bd_Log::warning("commit fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库提交失败';

                return $arrRes;
            }
        }

        return $arrRes;
    }

    public function enterCartCourse($payOrderId, $orderList, $ext = array())
    {
        $res = $this->enterAddCourse($payOrderId, $orderList, true, $ext);
        if ($res['errNo'] != 0) {
            throw new Hk_Util_Exception($res['errNo'], '', $orderList);
        }

        return $res;
    }

    public function enterAddCourse($payOrderId, $orderList, $needTrans = true, $ext = array())
    {
        $arrRes = array(
            'errNo'  => 0,
            'errMsg' => '',
            'data'   => array(),
        );
        $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
        if ($needTrans) {
            $db = Hk_Service_Db::getDB($dbName);
            if (empty($db)) {
                Bd_Log::warning("connect db fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = '获取fudao数据库连接失败';

                return $arrRes;
            }
            $res = $db->startTransaction();
            if (empty($res)) {
                Bd_Log::warning("start transaction fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库开启事务失败';

                return $arrRes;
            }
        }
        try {
            $this->_enterPayOrder($payOrderId, $ext);
            $arrOutput = $this->_enterAddCourse($orderList, $ext);
        } catch (Hk_Util_Exception $e) {
            $arrRes['errNo']  = $e->getErrNo();
            $arrRes['errMsg'] = $e->getErrMsg();
            if ($needTrans) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                    $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                    $arrRes['errMsg'] = 'fudao数据库回滚失败';
                }
            }

            return $arrRes;
        }
        if ($needTrans) {
            $res = $db->commit();
            if (empty($res)) {
                Bd_Log::warning("commit fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库提交失败';

                return $arrRes;
            }
        }
        $arrRes['data'] = $arrOutput;

        return $arrRes;
    }

    private function _enterAddCourse($orderList, $ext = array())
    {
        $thirdPartyDiscount = intval($ext['thirdPartyDiscount']);
        $sumPayment         = intval($ext['sumPayment']);
        $arrDataList        = array();
        $leftDiscount       = $thirdPartyDiscount;
        reset($orderList);
        sort($orderList);//按照订单id正序排列，保证分拆第三方优惠时算法稳定
        $lastOrderId        = end($orderList);
        foreach ($orderList as $orderId) {
            //1. 获取公共数据并做相应数据检查
            $arrData = $this->_enterPreProc($orderId);
            $payment = $arrData['payment'];
            if ($leftDiscount > 0 && $sumPayment > 0) {
                if ($lastOrderId == $orderId) {
                    $arrData['thirdPartyDiscount'] = $leftDiscount;
                } else {
                    $arrData['thirdPartyDiscount'] = min($leftDiscount, min(ceil($thirdPartyDiscount * $payment / $sumPayment / 10) * 10, $payment)); //精确到角
                    $leftDiscount                  -= $arrData['thirdPartyDiscount'];
                }
            } else {
                $arrData['thirdPartyDiscount'] = 0;
            }

            //2. 特殊订单处理
            if ($arrData['hasPaid']) { //订单已支付过（含被冻结）则直接退出
                Bd_Log::warning("this order has paid, orderId[$orderId] ");
                continue;
            }
            if ($arrData['needRefund']) { //之前有同样课程支付过，则当前订单进行退款
                Bd_Log::warning("there is same course has paid, this order need refund, orderId[$orderId]");
                $this->_enterTradeRecord($arrData);
                continue;
            }

            //3. 进行fudao db写操作
            if ($arrData['tradeRecord']['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                $this->_enterTradeRecord($arrData);
                $this->_enterTradeCnt($arrData);
                $this->_enterShoppingCart($arrData);
            } else {
                $this->_enterTradeRecord($arrData);
                //$this->_enterStudentInfo($arrData);

                Hk_Util_Log::setLog('nmqstudentcourse' . $arrData['courseId'], 'array');//打点跟踪
                //{{{加课动作异步化
                //$this->_enterStudentCourse($arrData);
                //$this->_enterStudentLesson($arrData);
                //$this->_enterExercise($arrData);
                $ext                = array(
                    'orderId'           => $orderId,
                    'app'               => $arrData['app'],
                    'needSendMsgNotify' => $arrData['needSendMsgNotify'],
                    'skuId'             => isset($arrData['tradeRecord']['extData']['skuId']) ? $arrData['tradeRecord']['extData']['skuId'] : 0,
                );
                $arrAddCourseParams = array(
                    'studentUid' => $arrData['uid'],
                    'courseId'   => $arrData['courseId'],
                    'isGift'     => 0,
                    'ext'        => json_encode($ext),
                );
                $ret                = Zb_Service_Nmq::sendCommand(Zb_Const_Command::COMMAND_CORE_110017, $arrAddCourseParams);
                if ($ret === false) {
                    $errno           = ral_get_errno();
                    $errmsg          = ral_get_error();
                    $protocol_status = ral_get_protocol_code();
                    Bd_Log::warning("Error:[service trade_api_addorder connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");

                    return false;
                }
                $errno  = intval($ret['errno']);
                $errmsg = strval($ret['error_msg']);
                if ($errno > 0) {
                    Bd_Log::warning("Error:[service trade_api_addorder process error], Detail:[errno:$errno errmsg:$errmsg]");

                    return false;
                }
                //}}}
                //$this->_enterShoppingCart($arrData);
                if (isset($arrData['tradeRecord']['extData']['expendDiscount'])) {
                    //尾款单不更新库存
                } else {
                    $this->_enterTradeCnt($arrData);
                }

            }
            $arrDataList[] = $arrData;
        }
        //4. 最后执行非fudao db的操作（因为无法回滚，最后操作，TODO改成异步）


        $dakSyncCourseIdListSuccess = array();
        $dakSyncCourseIdListFailure = array();
        foreach ($arrDataList as $arrData) {
            if ($arrData['tradeRecord']['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                $this->_enterCoupon($arrData);
            } else {
                //$this->_enterAddGroup($arrData);异步化
                $this->_enterCoupon($arrData);
                $this->_enterUcloud($arrData);
                //$this->_enterMsgNotify($arrData);异步化
                //$this->_enterNotifyPreLongSold($arrData);

                //数据有遗漏,打点跟踪
                //Hk_Util_Log::setLog('nmqAssistantMis' . $arrData['courseId'], 'array');//打点跟踪
                //$this->_enterAssistantMis($arrData);
            }
            if (isset($arrData['tradeRecord']['extData']['expendDiscount'])) {
                //尾款单不更新库存
            } else {
                //同步售卖信息到 DAK 数据表中 @huwenhua
                $ret = $this->_enterDakSaleCnt($arrData);
                if (true === $ret) {
                    array_push($dakSyncCourseIdListSuccess, $arrData['courseId']);
                } else {
                    array_push($dakSyncCourseIdListFailure, $arrData['courseId']);
                }
            }
            if (isset($arrData['tradeRecord']['extData']['needNotifyToAssistant']) && $arrData['tradeRecord']['extData']['needNotifyToAssistant']) {
                $obj = new Hkzb_Service_SpecialCourseSaleAssistantSupport();
                $ret = $obj->reduceQuota($arrData['courseId'], $arrData['uid']);
                if ($ret === false) {
                    Bd_Log::warning("reduce quota fail, courseId:" . $arrData['courseId'] . ", uid:" . $arrData['uid']);
                }
            }
        }
        Bd_Log::addNotice('syncDakSaleCntSuccess', json_encode($dakSyncCourseIdListSuccess));
        Bd_Log::addNotice('syncDakSaleCntFailure', json_encode($dakSyncCourseIdListFailure));

        return array();
    }

    private function _enterShoppingCart($arrData)
    {
        $uid         = $arrData['uid'];
        $tradeRecord = $arrData['tradeRecord'];
        $courseId    = isset($tradeRecord['extData']['skuId']) ? $tradeRecord['extData']['skuId'] : $arrData['courseId'];
        $cartInfo    = $this->_objDsShoppingCart->getShoppingCartCourse($uid, $courseId);
        if ($cartInfo === false) {
            Bd_Log::warning("Error:[get cart course error]", Hk_Util_ExceptionCodes::DB_ERROR, array('uid' => $uid, 'courseId' => $courseId));

            return;
        }
        if ($tradeRecord['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
            //预约订单支付完成后将商品加入选课单
            if (!empty($cartInfo)) {
                if ($cartInfo['deleted'] == Hkzb_Ds_Fudao_ShoppingCart::SHOPPING_CART_COURSE_NORMAL) {

                } else {
                    $arrFields = array('assistantUid' => 0, 'deleted' => Hkzb_Ds_Fudao_ShoppingCart::SHOPPING_CART_COURSE_NORMAL);
                    $res       = $this->_objDsShoppingCart->updateShoppingCart($uid, $courseId, $arrFields);
                    if (false === $res) {
                        Bd_Log::warning("Error:[update cart course error]", Hk_Util_ExceptionCodes::DB_ERROR, array('uid' => $uid, 'courseId' => $courseId, 'fields' => json_encode($arrFields)));

                        return;
                    }
                }
            } else {
                $arrFields = array(
                    'studentUid' => $uid,
                    'courseId'   => $courseId,
                );
                $res       = $this->_objDsShoppingCart->addCourseToCart($arrFields);
                if (false === $res) {
                    Bd_Log::warning("Error:[add cart course error]", Hk_Util_ExceptionCodes::DB_ERROR, array('uid' => $uid, 'courseId' => $courseId));

                    return;
                }
            }
        } else {
            if ($cartInfo['deleted'] == Hkzb_Ds_Fudao_ShoppingCart::SHOPPING_CART_COURSE_NORMAL) {
                $arrFields = array('assistantUid' => 0, 'deleted' => Hkzb_Ds_Fudao_ShoppingCart::SHOPPING_CART_COURSE_DELETED);
                $res       = $this->_objDsShoppingCart->updateShoppingCart($uid, $courseId, $arrFields);
                if (false === $res) {
                    Bd_Log::warning("Error:[update cart course error]", Hk_Util_ExceptionCodes::DB_ERROR, array('uid' => $uid, 'courseId' => $courseId, 'fields' => json_encode($arrFields)));

                    return;
                }
            }
        }
    }

    private function _enterPayOrder($orderId, $ext = array())
    {
        $orderId            = intval($orderId);
        $thirdPartyDiscount = intval($ext['thirdPartyDiscount']);
        if ($orderId <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('orderId' => $orderId));
        }
        //获取订单信息
        $tradeRecord = $this->_objDsTradeRecord->getTradeRecord($orderId);
        if (false === $tradeRecord) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('orderId' => $orderId));
        }
        if (empty($tradeRecord)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ORDER_NOT_EXIST, '', array('orderId' => $orderId));
        }
        $uid = $tradeRecord['studentUid'];
        //获取用户信息
        $studentInfo = $this->_objDsStudent->getStudentInfo($uid);
        if (false === $studentInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        $userInfo = $this->_objDsUcloud->getUserInfo($uid);
        if (empty($userInfo)) {
            //获取用户信息失败，不中断加课
            $userInfo = array();
            //throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::USERINFO_NOT_EXIST, '', array('uid' => $uid));
        }
        if ($tradeRecord['status'] != Hkzb_Ds_Fudao_TradeRecord::STATUS_TOPAY) {
            return array();
        }
        $arrData = array(
            'orderId'            => $orderId,
            'needRefund'         => false,
            'payment'            => $tradeRecord['payment'],
            'tradeRecord'        => $tradeRecord,
            'needAddress'        => false,
            'addressInfo'        => array(),
            'thirdPartyDiscount' => $thirdPartyDiscount,
        );
        $this->_enterTradeRecord($arrData);
        //特殊处理春季课赠券逻辑
        $springCoursePayment = isset($tradeRecord['extData']['springCoursePayment']) ? intval($tradeRecord['extData']['springCoursePayment']) : 0;
        if ($springCoursePayment >= 12000) {//春季课满120元赠20元优惠券
            $itemIds = 113;
            $res     = Hkzb_Service_Coupon::addCouponByItemIds('zyb_fudao', 'zybcouselesson', $itemIds, $uid);
            if (false === $res) {
                Bd_Log::warning("give coupon fail, uid[$uid] itemIds[$itemIds]");
            } else {
                $cmdNo   = Hk_Service_Message_Const::SYS_NOTICE;
                $message = array(
                    'title'   => '恭喜你获得春季班报名奖励',
                    'content' => '春季班奖励优惠券已到账，在卡券礼包查看',
                );
                if (isset($tradeRecord['extData']['appType']) && $tradeRecord['extData']['appType'] == 'android') {
                    $cmdNo          = Hk_Service_Message_Const::URL_PUSH;
                    $message['url'] = 'homework://www.zuoyebang.com/fudao/couponlist';
                }
                Hk_Service_Push::pushByUids($cmdNo, $message, array($uid));
            }
        }

        return array();
    }

    /**
     * 同步gnmis后台预备班售卖信息
     * @param $arrData
     * @return array
     */
    private function _enterNotifyPreLongSold($arrData)
    {
        $courseInfo = $arrData['courseInfo'];
        $type       = $courseInfo['type'];
        if ($type != Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRE_LONG && $type != Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE) {
            return array();
        }
        $studentUid         = intval($arrData['uid']);
        $userInfo           = Hkzb_Ds_Fudao_StudentInfo::getUserInfo($studentUid);
        $isSerialCourseUser = isset($userInfo['isSerialCourseUser']) ? $userInfo['isSerialCourseUser'] : 0;
        if ($isSerialCourseUser) {
            return array();
        }
        $header    = array(
            'pathinfo' => "/gnmis/api/salestudentbind",
            'cookie'   => $_COOKIE,
        );
        $arrParams = array(
            'studentUid' => $studentUid,
            'courseId'   => intval($courseInfo['courseId']),
            'gradeId'    => intval($courseInfo['grade']),
        );
        $arrParams = Dayi_Util_GnSign::getSign($arrParams);
        $ret       = ral('gnmis', 'POST', $arrParams, rand(), $header);
        Bd_Log::addNotice('ral_gnmis_user_buy_pre_long_course', $ret);
        if (false === $ret) {
            $errno           = ral_get_errno();
            $errMsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service gnmis connect error], Detail:[errno:$errno errMsg:$errMsg protocol_status:$protocol_status]");
        } else {
            $ret    = json_decode($ret, true);
            $errno  = intval($ret['errNo']);
            $errMsg = strval($ret['errstr']);
            if (!is_array($ret) || $errno > 0) {
                Bd_Log::warning("Error:[service gnmis process error], Detail:[errno:$errno errMsg:$errMsg]");
            }
        }

        return array();
    }

    /**
     * 报名成功通知班主任工作站
     * @param $arrData
     * @return true/false
     */
    private function _enterAssistantMis($arrData)
    {
        $defaultCoreCourseId  = $arrData['courseInfo']['defaultCoreCourseId'];
        $assistantInfo        = $arrData['assistantInfoList'][$defaultCoreCourseId];
        $data                 = array();
        $data['courseId']     = $arrData['courseId'];
        $data['studentUid']   = $arrData['uid'];
        $data['assistantUid'] = $assistantInfo['teacherUid'];
        $data['classId']      = $assistantInfo['classId'];
        $data['orderId']      = $arrData['orderId'];
        $data['time']         = time();
        $objNmq               = new Hk_Service_Nmq();

        //数据打点
        Hk_Util_Log::setLog('nmqData' . $data['courseId'], json_encode($data));

        $ret = $objNmq->talkToQcm(Hk_Const_Command::CMD_FUDAO_ADD_STUDENT_ASSISTANT, $data);
        if (false === $ret) {
            Bd_Log::warning("Error:[servies nmq error], Detail:[" . json_encode($data) . "]");
        }

        return $ret;
    }

    /**
     * 同步销量到 DAK(重构后的售卖系统数据模块)
     *
     * <AUTHOR>
     * @date    2017-12-28
     * @param   array $arrData
     * @return  bool
     */
    private function _enterDakSaleCnt($arrData)
    {
        $objDakCommit = new Zb_Core_Ds_Dak_Interface();
        $ret          = $objDakCommit->incrSkuSaleCnt($arrData['courseId']);
        if (false === $ret) {
            Bd_Log::warning("Error:[dak incr saleCnt error], Detail:[" . json_encode($arrData) . "]");
        }

        return $ret;
    }

    private function getMsgAssistantInfo($assistantUid, $courseInfo)
    {
        $res = array(
            'isVirtual'   => 0,
            'phone'       => 0,
            'teacherName' => '',
        );
        if (empty($assistantUid)) {
            return $res;
        }
        $virtualAssistantUid = Hkzb_Util_Fudao_Category::VIRTUAL_ASSISTANT_UID;
        //判断是否是虚拟账号
        if ($assistantUid == $virtualAssistantUid) {
            $res['isVirtual'] = 1;

            return $res;
        }
        //如果不是秋2的，做虚拟账号处理
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();
        if ($nowTime < strtotime('2017-12-30') && $courseInfo['learnSeason'] == '秋_1') {
            $res['isVirtual'] = 1;

            return $res;
        }

        $assistantInfo = $this->_objDsTeacher->getTeacherInfo($assistantUid, array('teacherUid', 'phone', 'duty', 'teacherName'));

        if (empty($assistantInfo)) {
            Bd_Log::warning("assistantInfo is empty assistantUid:{$assistantUid}");

            return $res;
        }

        $res = array(
            'isVirtual'   => 0,
            'phone'       => $assistantInfo['phone'],
            'teacherName' => $assistantInfo['teacherName'],
        );

        return $res;
    }

    public function goPcPrePay($arrParams)
    {
        $uid            = intval($arrParams['uid']);
        $courseInfoList = $arrParams['courseList'];
        $appId          = $arrParams['appId'];
        $appType        = $arrParams['appType'];
        //参数检查
        if ($uid <= 0 || empty($courseInfoList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'goPrePay', $arrParams);
        }
        //检查用户角色
        $this->checkUserValid($uid);
        //获取支付信息
        $payInfo = $this->getPayInfo($courseInfoList, $uid, $appId, $appType, 0);

        return $payInfo;
    }

    //新预支付接口
    public function goPrePay($arrParams)
    {
        $uid            = intval($arrParams['uid']);
        $courseInfoList = $arrParams['courseList'];
        $appId          = $arrParams['appId'];
        $appType        = $arrParams['appType'];
        $isJson         = $arrParams['isJson'];

        //参数检查
        if ($uid <= 0 || empty($courseInfoList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'goPrePay', $arrParams);
        }

        //检查用户角色
        $this->checkUserValid($uid);

        //获取支付信息
        $payInfo = $this->getPayInfo($courseInfoList, $uid, $appId, $appType, $isJson);

        //格式化展现
        $productInfoList = $this->formatCoursePayInfo($payInfo);

        //处理邮寄地址
        if ($productInfoList['needAddress']) {
            $studentInfo = $this->_objDsStudent->getStudentInfo($uid);
            if ($studentInfo === false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
            }
            $productInfoList['addressInfo'] = isset($studentInfo['extData']['addressInfo']) ? $studentInfo['extData']['addressInfo'] : array();
        } else {
            $productInfoList['addressInfo'] = array();
        }

        return $productInfoList;
    }

    /**
     * 检查用户角色是否合法
     * @param $intStudentUid
     * @throws Hk_Util_Exception
     */
    private function checkUserValid($intUid)
    {
        $uid = intval($intUid);
        if ($uid <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('uid' => $intUid));
        }
        //检查用户是不是老师，老师不能购买课程
        $teacherInfo = $this->_objDsTeacher->getTeacherInfo($uid);
        if (false === $teacherInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid));
        }
        if (!empty($teacherInfo) && $teacherInfo['deleted'] != Hkzb_Ds_Fudao_Teacher::STATUS_DELETED) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_IS_TEACHER, '', array('uid' => $uid));
        }
    }


    private function getPayInfo($arrCourseInfoList, $intUid, $strAppId, $strOS, $isJson, $needBizLog=0)
    {
        $uid = intval($intUid);
        //参数检查
        if ($uid <= 0 || empty($arrCourseInfoList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', array('uid' => $intUid, 'courseInfoList' => json_encode($arrCourseInfoList)));
        }
        //遍历课程信息获取商品（课程）id，预支付时不用处理优惠券信息
        $arrCourseId                = array();
        $arrId2CouponIdAssistantUid = array();
        foreach ($arrCourseInfoList as $item) {
            if (isset($item['courseId']) && intval($item['courseId']) > 0) {
                $courseId                              = intval($item['courseId']);
                $couponId                              = isset($item['couponId']) ? intval($item['couponId']) : 0;
                $assistantUid                          = isset($item['assistantUid']) ? intval($item['assistantUid']) : 0;
                $arrCourseId[]                         = $courseId;
                $arrId2CouponIdAssistantUid[$courseId] = array(
                    'couponId'     => $couponId,
                    'assistantUid' => $assistantUid,
                );
            }
        }
        if (empty($arrCourseId)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '', $arrCourseInfoList);
        }
        if (count($arrCourseInfoList) != count($arrCourseId)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'arrCourseInfoList:' . json_encode($arrCourseInfoList) . ' arrCourseId:' . json_encode($arrCourseId));
        }
        //获取商品（课程信息）
        $courseDetailInfoList = $this->_objDsAdvancedCourse->getCourseInfoArr($arrCourseId, array('extLesson', 'extTeacher', 'extAssistant'), true);
        if ($courseDetailInfoList === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'arrCourseId:' . json_encode($arrCourseId));
        }
        if (empty($courseDetailInfoList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'arrCourseId:' . json_encode($arrCourseId));
        }
        //检查课程状态，同时区分商品和课程
        $arrSkuIdList       = array();//skuId
        $arrCIdList         = array();//courseId
        $now                = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $canBuyTrailCourse  = -1;
        $studentRoleInfo    = Hkzb_Ds_Fudao_StudentInfo::getUserInfo($uid);//用户角色信息
        $isSerialCourseUser = isset($studentRoleInfo['isSerialCourseUser']) ? $studentRoleInfo['isSerialCourseUser'] : 0;//是否班课用户
        foreach ($courseDetailInfoList as $courseDetail) {
            if ($courseDetail['status'] != Hkzb_Ds_Fudao_Advanced_Course::STATUS_ONLINE) {
                $courseId = isset($courseDetail['productId']) ? $courseDetail['productId'] : $courseDetail['courseId'];
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_ONLINE, '', array('courseId' => $courseId));
            }
            if ($courseDetail['type'] == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRE_LONG) {
                if ($canBuyTrailCourse == -1) {
                    if ($isSerialCourseUser || Hkzb_Ds_Fudao_StudentInfo::checkTrialFilter($uid)) {
                        $canBuyTrailCourse = 0;//班课用户或7天内买过预备班的用户无法重复购买预备班
                    } else {
                        $canBuyTrailCourse = 1;
                    }
                }
                if ($canBuyTrailCourse == 0) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CAN_NOT_BUY, '七天内不可重复购买试听课');
                }
            }
            $learnSeason = $courseDetail['learnSeason'];
            if (isset($courseDetail['skuType'])) {
                //这里因为没有暑秋数据，临时默认所有的商品走商品策略，上线前需调整回来
                if (in_array($learnSeason, array('暑_0', '暑_1', '暑_2', '暑_3', '暑_4', '暑_5', '暑_6', '秋_1', '秋_2')) || ($now > Hkzb_Util_Fudao_Category::SPRING_COURSE_LEARN_LESSON_TWO_START_TIME || $courseDetail['productId'] == 1028089)) {
                    $arrSkuIdList[] = $courseDetail['productId'];
                } else {
                    //暑秋前的商品走课程逻辑
                    $arrCIdList = array_merge($arrCIdList, $courseDetail['courseIdList']);
                }
            } else {
                /*为兼容老数据，3.28号暑秋售卖上线后，新接口仍需支持春季班课售卖策略*/
                $arrCIdList[] = $courseDetail['courseId'];
            }
        }
        $arrOutput       = array();
        $skuUsedCouponId = array();

        //不确定为什么取不出来相等数量的sku,先做个trap
        if(count($arrCourseInfoList) > count($arrSkuIdList)){
            Bd_Log::warning('$courseDetailInfoList num error', json_encode($courseDetailInfoList));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CHECK_ERROR, '课程检查失败');
        }

        //处理商品信息、策略信息、优惠券信息
        if (!empty($arrSkuIdList)) {
            $skuGroupInfo            = $this->_getSKUGroupBizInfo($arrSkuIdList, $uid, $arrId2CouponIdAssistantUid, $strAppId, $strOS, Zb_Const_Zbbiz::PAGE_TYPE_PAY, $isJson, $courseDetailInfoList, $needBizLog);
            $skuUsedCouponId         = $skuGroupInfo['skuUsedCouponId'];
            $arrOutput['skuPayInfo'] = $skuGroupInfo;
            $arrOutput['originalBizInfo'] = $skuGroupInfo['originalBizInfo'];
            $arrOutput['useCouponInfo']   = $skuGroupInfo['useCouponInfo'];
        }
        //处理课程信息、策略信息、优惠券信息
        if (!empty($arrCIdList)) {
            $courseInfoWithBiz          = $this->_getCourseInfoWitchBiz($arrCIdList, $uid, $arrId2CouponIdAssistantUid, $skuUsedCouponId, $isJson, $courseDetailInfoList);
            $arrOutput['coursePayInfo'] = $courseInfoWithBiz;
        }

        return $arrOutput;
    }

    private function _getSKUGroupBizInfo($arrSkuIdList, $uid, $arrSkuId2CouponIdAssistantUid, $strAppId, $strOS, $strPageType, $isJson, $courseDetailInfoList, $needBizLog)
    {
        //商品走新策略模块
        $arrBizInfo = Hkzb_Util_Fudao_Biz::getGroupSkuBiz($arrSkuIdList, $uid, $strAppId, $strOS, $strPageType, $needBizLog);
        if ($arrBizInfo === false || empty($arrBizInfo)) {
            //获取营销信息失败，抛异常
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::BIZ_GET_BIZ_INFO_ERROR, '', array('skuIdList' => json_encode($arrSkuIdList)));
        }
        //下面处理商品以及对应的策略信息
        $skuList             = $arrBizInfo['skuList'];
        $bizGroupInfo        = $arrBizInfo['groupInfo'];
        $bizGiftInfo         = isset($arrBizInfo['gitfBizList'])?$arrBizInfo['gitfBizList']:[];
        $skuGroupInfoWithBiz = array();
        $needAddress         = 0;
        $allSkuItemList      = array();
        $allCourseIdList     = array();
        foreach ($skuList as $item) {
            $skuId   = intval($item['skuId']);
            $skuInfo = $courseDetailInfoList[$skuId];
            if (empty($skuInfo)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SKU_NOT_EXIST, '', array('skuId' => $skuId));
            }
            $allCourseIdList = array_merge($allCourseIdList, $skuInfo['courseIdList']);
        }

        //检查是否存在预付和普通(尾款)混合购买
        $preSale    = array();
        $sale       = array();
        foreach ($skuList as $item) {
            if ($item['saleType'] == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                $preSale[] = $item;
            } else {
                $sale[] = $item;
            }
        }

        if (!empty($preSale) && !empty($sale)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_CAN_NOT_BUY, '预付普通不可混合购买', array('skuList' => $skuList));
        }
        //检查是否已经购买过课程
        $objUserTradeRecord = new Hkzb_Ds_Fudao_UserTradeRecord();
        $res                = $objUserTradeRecord->getOrderIdListByStudentUidCourseIds($uid, $allCourseIdList, Hkzb_Ds_Fudao_TradeRecord::TYPE_SUB, Hkzb_Ds_Fudao_TradeRecord::STATUS_PAYED);
        if (!empty($res)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::STUDENT_HAD_BUY_COURSE, '', array('uid' => $uid, 'courseIdList' => $allCourseIdList));
        }
        foreach ($skuList as $item) {
            //检查商品是否可购买
            $canBuy = intval($item['canBuy']);
            if ($canBuy === Zb_Const_Zbbiz::SKU_CANBUY_NO) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::BIZ_SKU_CAN_NOT_BUY, '', array('skuId' => $item['skuId']));
            }
            //获取商品信息
            $skuId          = intval($item['skuId']);
            $item['uid']    = $uid;
            $hasPayPreOrder = intval($item['hasPayPreOrder']);
            $skuInfo        = $courseDetailInfoList[$skuId];
            //检查库存
            $courseRegisterInfo = Hkzb_Util_Fudao_Format::getCourseRegisterInfo($skuInfo);
            if ($courseRegisterInfo['studentLeftCnt'] <= 0) {
                if ($hasPayPreOrder) {

                } else {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ASSISTANT_STUDENT_EXEED, "课程已报满");
                }
            }
            //获取用户是用的优惠券
            $item['couponId'] = isset($arrSkuId2CouponIdAssistantUid[$skuId]['couponId']) ? $arrSkuId2CouponIdAssistantUid[$skuId]['couponId'] : 0;

            //支付尾款时处理价格信息
            $saleType = intval($item['saleType']);
            if ($hasPayPreOrder && $saleType === Zb_Const_Zbbiz::SKU_SALE_TYPE_SALE) {
                $item['price']         = max(intval($item['price']) - intval($item['expendPrice']), 0);
                $item['discountPrice'] += intval($item['expendPrice']);
            }

            //检查是否已有预约单
            if ($saleType == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                $hasPreSale = Hkzb_Ds_Fudao_Advanced_TradeRecord::getUserPrePayOrder($uid, $skuId);
                if ($hasPreSale === false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('uid' => $uid, 'skuId' => $skuId));
                }
                if (!empty($hasPreSale)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SKU_ALREADY_DEPOSIT, '', array('uid' => $uid, 'skuId' => $skuId));
                }
            }

            $item['skuName']      = $skuInfo['productName'];
            $item['courseIdList'] = $skuInfo['courseIdList'];
            $item['onlineStart']  = $skuInfo['onlineStart'];
            $item['onlineStop']   = $skuInfo['onlineStop'];
            //获取教师信息
            if ($skuInfo['skuType'] == Zb_Const_Sku::SKU_TYPE_SKU_GROUP) {
                foreach ($skuInfo['extInfo'] as $skuCourseId => $courseExtInfo) {
                    $item['teacherInfo'][$skuCourseId] = $courseExtInfo['extTeacher'];
                }
            } else {
                $item['teacherInfo'][$skuId] = $skuInfo['extInfo']['extTeacher'];
            }
            //是否有教材
            $hasBK = intval($skuInfo['hasBK']);
            if ($hasBK && !$needAddress) {
                $needAddress = 1;
            }
            //这里使用orderPrice字端，表示使用优惠券前的价格，当使用优惠券时，会更新orderPrice，主要是为了复用现有代码
            $item['orderPrice'] = $item['price'];
            //获取优惠券信息
            //判断商品能否使用优惠券，预定单不可使用优惠券
            $canUseCoupon = intval($item['canuseCoupon']);
            if ($saleType === Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                $canUseCoupon = 0;
            }
            if ($canUseCoupon && $item['price'] > 0) {
                //获取优惠券信息
                $tradeInfo = array(
                    'price'       => $item['price'],//折后价格
                    'type'        => $skuInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG ? $skuInfo['type'] : Hkzb_Ds_Fudao_Course::TYPE_PRIVATE,
                    'season'      => isset(Zhibo_Ds_Course::$seasonConf[$skuInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$skuInfo['learnSeason']] : 0,
                    'seasonId'    => isset(Zhibo_Ds_Course::$seasonConf[$skuInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$skuInfo['learnSeason']] : 0,
                    'subject'     => $skuInfo['subject'],
                    'xuebu'       => intval(Hk_Util_Category::$GRADEMAPXB[$skuInfo['grade']]),
                    'grade'       => intval($skuInfo['grade']),
                    'moreGrade'   => intval($skuInfo['moreGrade']),
                    'courseId'    => $skuId,
                    'learnSeason' => strval($skuInfo['learnSeason']),
                );
                $arrData   = array(
                    'uid'       => $uid,
                    'tradeInfo' => $tradeInfo,
                );

                $matchCouponList           = $this->_tradeGetMatchCouponList($arrData);
                $item['tradeInfo']         = $tradeInfo;
                $item['couponList']        = $matchCouponList;
                $item['unMatchCouponList'] = $this->_tradeGetUnMatchCouponList($uid, $matchCouponList);
            } else {
                $item['couponList']        = array();
                $item['unMatchCouponList'] = $this->_tradeGetUnMatchCouponList($uid, array());

            }
            //将商品分组
            $groupId                                    = intval($item['groupId']);
            $skuGroupInfoWithBiz[$groupId]['skuList'][] = $item;
            if (!isset($skuGroupInfoWithBiz[$groupId]['bizList'])) {
                $skuGroupInfoWithBiz[$groupId]['bizList'] = array();
            }
            $allSkuItemList[] = $item;
        }
        //将商品、策略分组
        //groupId和bizGroupId是对应的（如果有bizGroupInfo的话），值相同
        if (!empty($bizGroupInfo)) {
            foreach ($bizGroupInfo as $bizGroupId => $bizDetail) {
                if (!empty($bizDetail)) {
                    $skuGroupInfoWithBiz[$bizGroupId]['bizList'] = $bizDetail;
                    //如果没有教材，这里需要跟进赠品信息判断是否需要邮寄地址
                    if (!$needAddress) {
                        foreach ($bizDetail as $item) {
                            $giftList = isset($item['giftList']) && !empty($item['giftList']) ? $item['giftList'] : array();
                            if (!empty($giftList)) {
                                foreach ($giftList as $giftItem) {
                                    if ($giftItem['type'] == Zb_Const_Zbbiz::GIFT_TYPE_SKU) {
                                        $giftId = $giftItem['giftId'];
                                        //获取赠品信息，赠品是特殊的商品
                                        $objZbDakInterface = new Zb_Core_Ds_Dak_Interface();
                                        $giftSkuInfo       = $objZbDakInterface->getSkuInfoMap($giftId, true);
                                        if (!empty($giftSkuInfo) && $giftSkuInfo['skuType'] == Zb_Const_Sku::SKU_TYPE_ENTITY) {
                                            $needAddress = 1;
                                            break 2;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //处理优惠券信息
        $allSkuItemListWithCouponDiscount = self::getSkuCouponStrategy($allSkuItemList, array(), $isJson);
        $skuId2SkuWithCouponDiscount      = array();
        $skuUsedCouponId                  = array();
        $useCouponInfo                    = array();
        foreach ($allSkuItemListWithCouponDiscount as $skuWithCouponDiscount) {
            $skuId2SkuWithCouponDiscount[$skuWithCouponDiscount['skuId']] = $skuWithCouponDiscount;
            if (!empty($skuWithCouponDiscount['couponId'])) {
                $skuUsedCouponId[] = $skuWithCouponDiscount['couponId'];
                $useCouponInfo[$skuWithCouponDiscount['couponId']] = array(
                    "couponDiscount"    => $skuWithCouponDiscount["couponDiscount"],
                    "couponId"          => $skuWithCouponDiscount["usedCouponInfo"]["couponId"],
                    "discountText"      => $skuWithCouponDiscount["usedCouponInfo"]["discountText"],
                    "unit"              => $skuWithCouponDiscount["usedCouponInfo"]["unit"],
                );
            }
        }
        foreach ($skuGroupInfoWithBiz as &$skuGroupBizItem) {
            foreach ($skuGroupBizItem['skuList'] as &$value) {
                $value = $skuId2SkuWithCouponDiscount[$value['skuId']];
                if ($value['usedCoupon']) {
                    $value['discountPrice'] += $value['couponDiscount'];
                }
            }
        }
        $arrOutput = array(
            'skuGroupInfoWithBiz' => $skuGroupInfoWithBiz,
            'needAddress'         => $needAddress,
            'skuUsedCouponId'     => $skuUsedCouponId,//返回这个数据是为了处理课程的优惠券信息，现阶段要支持商品、课程同时支付，恶心了点，呵呵哒
            'originalBizInfo'	  => $arrBizInfo,       //biz策略下传到Trade
            'useCouponInfo'       => $useCouponInfo,//优惠券信息下传到Trade
        );

        return $arrOutput;
    }

    private function _getCourseInfoWitchBiz($arrCourseIdList, $uid, $arrCourseId2CouponIdAssistantUid, $skuUsedCouponId = array(), $isJson, $courseDetailInfoList)
    {
        if (empty($arrCourseIdList) || $uid <= 0) {
            return false;
        }
        //优先获取组合策略信息
        $boundCourseDiscountInfo = Hkzb_Util_Fudao_Category::getBoundCourseDiscountInfo($arrCourseIdList, $uid);
        if (!empty($boundCourseDiscountInfo)) {
            $arrBoundInfo          = array();
            $arrBoundAssistantInfo = array();
            $lastSeasonInfo        = $boundCourseDiscountInfo['lastSeason'];
            $curSeasonInfo         = $boundCourseDiscountInfo['curSeason'];
            foreach ($lastSeasonInfo as $value) {
                $arrBoundInfo['courseList'][intval($value['courseId'])] = $value;
                $arrBoundAssistantInfo[intval($value['courseId'])]      = $value['assistantInfo']['assistantUid'];
            }
            foreach ($curSeasonInfo as $value) {
                $arrBoundInfo['courseList'][intval($value['courseId'])] = $value;
                $arrBoundAssistantInfo[intval($value['courseId'])]      = $value['assistantInfo']['assistantUid'];
            }
            $boundGift = $boundCourseDiscountInfo['gift'];
            if (!empty($boundGift)) {
                $arrBoundInfo['gift'] = array(
                    'giftList'          => $boundCourseDiscountInfo['gift'],
                    'giftRelatedCourse' => $arrCourseIdList,
                );
            }
        }
        //标记是否需要地址信息
        $needAddress = 0;
        //标记赠品信息
        $giftInfo = array();
        if (isset($arrBoundInfo) && !empty($arrBoundInfo['gift'])) {
            $giftInfo[] = $arrBoundInfo['gift'];
        }
        //遍历课程，处理课程信息
        $courseList = array();
        foreach ($arrCourseIdList as $courseId) {
            $arrData = array();
            if (empty($courseId)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR);
            }
            $arrData['uid']          = $uid;
            $arrData['courseId']     = $courseId;
            $arrData['assistantUid'] = isset($arrCourseId2CouponIdAssistantUid[$courseId]['assistantUid']) ? $arrCourseId2CouponIdAssistantUid[$courseId]['assistantUid'] : 0;
            $arrData['couponId']     = isset($arrCourseId2CouponIdAssistantUid[$courseId]['couponId']) ? $arrCourseId2CouponIdAssistantUid[$courseId]['couponId'] : 0;
            if ($arrData['assistantUid'] <= 0) {
                if (isset($arrBoundAssistantInfo) && isset($arrBoundAssistantInfo[$courseId])) {
                    $arrData['assistantUid'] = $arrBoundAssistantInfo[$courseId];
                    Hk_Util_Log::setLog('old_version_patch_assistantUid_courseId_' . $courseId, 'assistantUid_' . $arrData['assistantUid']);
                }
            }
            //如果课程有连报优惠策略，写入优惠信息、规则
            if (isset($arrBoundInfo) && isset($arrBoundInfo['courseList'][$courseId])) {
                $arrData['boundDiscountStrategy'] = $arrBoundInfo['courseList'][$courseId]['boundDiscountStrategy'];
            }
            //获取课程信息
            $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
            if (false === $courseInfo) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
            }
            $arrData['courseInfo']    = $courseInfo;
            $allCourseInfo            = $courseDetailInfoList[$courseId];
            $arrData['allCourseInfo'] = $allCourseInfo;
            //检查课程有效性
            $this->_tradeCheckCourseValidity($arrData);
            //检查学生能够购买此课程
            $this->_tradeCheckStudentCanBuy($arrData);
            //课程信息
            $arrData['courseName']    = $courseInfo['courseName'];
            $arrData['onlineTime']    = Hkzb_Util_Fudao_Format::formatOnlineTime($allCourseInfo);
            $arrData['price']         = $courseInfo['price'];
            $arrData['orderPrice']    = $courseInfo['price'];
            $arrData['originalPrice'] = $courseInfo['price'];
            $arrData['service']       = '';//专属服务
            $arrData['grade']         = $courseInfo['grade'];
            //教师信息
            $teacherInfoList = $allCourseInfo['extInfo']['extTeacher'];
            $teacherNameStr  = '';
            $teacherCnt      = 0;
            foreach ($teacherInfoList as $teacherInfo) {
                if ($teacherCnt < 2) {
                    $teacherNameStr .= $teacherInfo['teacherName'] . '    ';
                    ++$teacherCnt;
                } else {
                    break;
                }
            }
            $arrData['teacherName'] = trim($teacherNameStr);
            //购物车方式购买时自动分配班主任
            $arrData['assistantInfo'] = $this->_tradeGetAssistantInfo($arrData);
            //普通课程或有多课程优惠策略的课程价格计算逻辑
            $arrData = $this->_tradeDiscountStrategy($arrData);
            if (isset($arrData['gift']) && !empty($arrData['gift'])) {
                $giftInfo[] = array(
                    'giftList'          => $arrData['gift'],
                    'giftRelatedCourse' => array($courseId),
                );
            }
            //有教材时需要邮寄地址
            $courseAttributeInfo = $this->_objDsCourseAttribute->getCourseAttributeInfo($courseId, array('teachMaterial'));
            if ($courseAttributeInfo === false) {
                Bd_Log::warning("get courseattribute info fail, courseId:$courseId");
            } elseif (isset($courseAttributeInfo['teachMaterial']) && $courseAttributeInfo['teachMaterial']) {
                $needAddress = 1;
            }
            //有赠品时需要地址信息
            if (!empty($giftInfo)) {
                if (!$needAddress) {
                    $needAddress = 1;
                }
            }
            //获取优惠券信息
            $tradeInfo                    = array(
                'price'       => $arrData['price'],
                'type'        => $courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG ? $courseInfo['type'] : Hkzb_Ds_Fudao_Course::TYPE_PRIVATE,
                'season'      => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
                'seasonId'    => isset(Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']]) ? Zhibo_Ds_Course::$seasonConf[$courseInfo['learnSeason']] : 0,
                'subject'     => $courseInfo['subject'],
                'xuebu'       => intval(Hk_Util_Category::$GRADEMAPXB[$courseInfo['grade']]),
                'grade'       => intval($courseInfo['grade']),
                'moreGrade'   => intval($courseInfo['moreGrade']),
                'courseId'    => intval($courseInfo['courseId']),
                'learnSeason' => strval($courseInfo['learnSeason']),
            );
            $arrData['tradeInfo']         = $tradeInfo;
            $matchCouponList              = $this->_tradeGetMatchCouponList($arrData);
            $arrData['couponList']        = $matchCouponList;
            $arrData['unMatchCouponList'] = $this->_tradeGetUnMatchCouponList($uid, $matchCouponList);

            unset($arrData['courseInfo']['extData']);
            unset($arrData['courseInfo']['coreCourse']);
            unset($arrData['allCourseInfo']);
            $courseList[] = $arrData;
        }
        $courseList = $this->getSkuCouponStrategy($courseList, $skuUsedCouponId, $isJson);

        $arrOutput = array(
            'courseList'  => $courseList,
            'needAddress' => $needAddress,
        );

        return $arrOutput;
    }

    private function formatCoursePayInfo($coursePayInfo)
    {
        $arrOutput         = array();
        $skuPayInfo        = isset($coursePayInfo["skuPayInfo"]) ? $coursePayInfo['skuPayInfo'] : array();
        $coursePayInfo     = isset($coursePayInfo['coursePayInfo']) ? $coursePayInfo['coursePayInfo'] : array();
        $needAddress       = 0;//是否需要地址
        $isConfirm         = 0;//是否需要弹窗
        $confirmCourseList = array();//弹窗课程信息
        if (!empty($skuPayInfo)) {
            $needAddress         = $needAddress | $skuPayInfo['needAddress'];
            $skuGroupInfoWithBiz = $skuPayInfo['skuGroupInfoWithBiz'];
            foreach ($skuGroupInfoWithBiz as $skuGroup) {
                $groupItem      = array();
                $skuList        = $skuGroup['skuList'];
                $bizList        = $skuGroup['bizList'];
                $groupPromotion = array();
                $skuBizInfo     = array();
                foreach ($bizList as $bizItem) {
                    //biz模块保证主策略只会命中一个
                    $bizType = intval($bizItem['bizType']);
                    if ($bizType === Zb_Const_Zbbiz::BIZ_TYPE_MAIN) {
                        $bizTitle       = isset($bizItem['title']) ? $bizItem['title'] : '';
                        $bizDesc        = isset($bizItem['description']) ? $bizItem['description'] : '';
                        $groupPromotion = array(
                            'type'    => $bizTitle,
                            'content' => $bizDesc
                        );
                    }
                    $bizSkuIdList = $bizItem['skuIdList'];
                    foreach ($bizSkuIdList as $skuId) {
                        $skuBizInfo[$skuId][] = array(
                            'name'     => $bizItem['title'],
                            'discount' => $bizItem['discountPriceMap'][$skuId],
                        );
                    }
                }
                if (!empty($groupPromotion)) {
                    $groupItem['promotionListInfo'] = $groupPromotion;
                }
                foreach ($skuList as $skuItem) {
                    //一元特惠课需要弹窗提示
                    if ($skuItem['specialSkuType']) {
                        $isConfirm   = 1;
                        $confirmInfo = array(
                            "courseName" => $skuItem['skuName'],
                            "courseDate" => date('n月j日', $skuItem['onlineStart']) . '-' . date('n月j日', $skuItem['onlineStop']),
                            "courseTime" => ""
                        );
                        $productId   = $skuItem['skuId'];
                        $productInfo = $this->_objDsAdvancedCourse->getCourseInfo($productId, array('extLesson', 'extTeacher', 'extAssistant'), true);
                        $onlineTime  = Hkzb_Util_Fudao_Format::formatOnlineTime($productInfo);
                        $timeInfo    = explode(' ', $onlineTime);
                        if (!empty($timeInfo[2])) {
                            $confirmInfo['courseTime'] = $timeInfo[1] . ' ' . $timeInfo[2];
                        }
                        $confirmCourseList[] = $confirmInfo;
                    }
                    //获取优惠信息
                    $skuItem['promotionInfo'] = isset($skuBizInfo[$skuItem['skuId']]) ? $skuBizInfo[$skuItem['skuId']] : array();
                    //临时过滤折扣金额为0的优惠信息，策略方的坑
                    if (!empty($skuItem['promotionInfo'])) {
                        foreach ($skuItem['promotionInfo'] as $key => $tmp) {
                            if ($tmp['discount'] == 0) {
                                unset($skuItem['promotionInfo'][$key]);
                            }
                        }
                    }
                    if ($skuItem['hasPayPreOrder']) {
                        $skuItem['promotionInfo'][] = array(
                            'name'     => '订金抵扣',
                            'discount' => $skuItem['expendPrice'],
                        );
                    }
                    //获取课程时间、教师信息
                    $ext             = date('n月j日', $skuItem['onlineStart']) . '-' . date('n月j日', $skuItem['onlineStop']) . ' |';
                    $teacherNameList = array();
                    foreach ($skuItem['teacherInfo'] as $teacherItem) {
                        foreach ($teacherItem as $teacherDetail) {
                            $teacherName = $teacherDetail['teacherName'];
                            if (!in_array($teacherName, $teacherNameList)) {
                                $teacherNameList[] = $teacherName;
                                $ext               .= ' ' . $teacherName;
                            }
                        }
                    }
                    //优惠券文案
                    $couponDiscountTxt = '';
                    if (!empty($skuItem['couponId'])) {
                        $usedCouponInfo = $skuItem['usedCouponInfo'];
                        if ($usedCouponInfo['unit'] == '元') {
                            $couponDiscountTxt = '- ¥' . $usedCouponInfo['discountText'];
                        } elseif ($usedCouponInfo['unit']) {
                            $couponDiscountTxt = $usedCouponInfo['discountText'] . $usedCouponInfo['unit'];
                        }
                    }
                    //按售卖类型格式化信息
                    if ($skuItem['saleType'] == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                        $courseItem           = array(
                            'type'               => 1,
                            'name'               => $skuItem['skuName'],
                            'ext'                => $ext,
                            'reservationDeposit' => $skuItem['orderPrice'],
                            'expendPrice'        => $skuItem['expendPrice'],
                        );
                        $groupItem['extDesc'] = date('n月j日H点', $skuItem['preSaleValidPeriodStart']) . '-' . date('n月j日H点', $skuItem['preSaleValidPeriodStop']) . '间可付尾款';
                    } else {
                        $courseItem = array(
                            'type'              => 0,
                            'courseId'          => $skuItem['skuId'],
                            'skuId'             => $skuItem['skuId'],
                            'name'              => $skuItem['skuName'],
                            'price'             => $skuItem['oriPrice'],
                            'couponId'          => $skuItem['couponId'],
                            'promotionList'     => $skuItem['promotionInfo'],
                            'couponList'        => $skuItem['couponList'],
                            'unMatchCouponList' => $skuItem['unMatchCouponList'],
                            'canUseCoupon'      => $skuItem['canuseCoupon'],
                            'ext'               => $ext,
                            'couponDiscountTxt' => $couponDiscountTxt,
                        );
                    }

                    $groupItem['courseList'][] = $courseItem;
                    $groupItem['price']        += $skuItem['orderPrice'];
                    $arrOutput['totalPrice']   += $skuItem['orderPrice'];
                    if ($skuItem['saleType'] == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                        $arrOutput['totalOriginalPrice'] += $skuItem['orderPrice'];
                    } else {
                        $arrOutput['totalOriginalPrice'] += $skuItem['oriPrice'];
                    }
                    $arrOutput['totalDiscountPrice'] += $skuItem['discountPrice'];
                }
                $arrOutput['productList'][] = $groupItem;
            }
        }
        if (!empty($coursePayInfo)) {
            $needAddress = $needAddress | $coursePayInfo['needAddress'];
            $courseList  = $coursePayInfo['courseList'];
            foreach ($courseList as $course) {
                //针对课程，不处理分组关系
                $groupItem       = array();
                $courseItem      = array(
                    'type'          => 0,
                    'courseId'      => $course['courseId'],
                    'name'          => $course['courseName'],
                    'price'         => $course['originalPrice'],
                    'couponId'      => $course['couponId'],
                    'promotionList' => array(),
                    'couponList'    => $course['couponList'],
                    'unMatchCouponList' => $course['unMatchCouponList'],
                    'canUseCoupon'  => 1,
                );
                $courseInfo      = $this->_objDsAdvancedCourse->getCourseInfo($course['courseId'], array('extLesson', 'extTeacher', 'extAssistant'), true);
                $ext             = date('n月j日', $courseInfo['onlineStart']) . '-' . date('n月j日', $courseInfo['onlineStop']) . ' |';
                $teacherNameList = array();
                foreach ($courseInfo['extInfo']['extTeacher'] as $teacherItem) {
                    $teacherName = $teacherItem['teacherName'];
                    if (!in_array($teacherName, $teacherNameList)) {
                        $teacherNameList[] = $teacherName;
                        $ext               .= ' ' . $teacherName;
                    }
                }
                $courseItem['ext'] = $ext;
                if (!empty($course['couponId'])) {
                    $usedCouponInfo    = $course['usedCouponInfo'];
                    $couponDiscountTxt = '';
                    if ($usedCouponInfo['unit'] == '元') {
                        $couponDiscountTxt = '- ¥' . $usedCouponInfo['discountText'];
                    } elseif ($usedCouponInfo['unit']) {
                        $couponDiscountTxt = $usedCouponInfo['discountText'] . $usedCouponInfo['unit'];
                    }
                    $courseItem['couponDiscountTxt'] = $couponDiscountTxt;
                } else {
                    $courseItem['couponDiscountTxt'] = '';
                }

                //获取课程的skuId
                $courseItem['skuId'] = 0;
                if ($course['courseId'] < 1000000) {
                    $aSkuType   = array(Zb_Const_Sku::SKU_TYPE_COURSE,Zb_Const_Sku::SKU_TYPE_COURSE_BK);
                    $courseItemSkuIdList   = $this->_objZbCoreDsDakInterface->getSkuIdListByCourseIdAndSkuType($course['courseId'],$aSkuType);
                    $courseItem['skuId'] = $courseItemSkuIdList[0];
                    if( $courseItem['skuId'] === false) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'can not get course info by SKU:getPaymentSKUList', array('courseId' => $courseId));
                    }
                }
                $groupItem['courseList'][]       = $courseItem;
                $groupItem['price']              += $course['orderPrice'];
                $arrOutput['totalPrice']         += $course['orderPrice'];
                $arrOutput['totalOriginalPrice'] += $course['originalPrice'];
                $arrOutput['totalDiscountPrice'] += $course['originalPrice'] - $course['orderPrice'];
                $arrOutput['productList'][]      = $groupItem;
            }
        }

        $arrOutput['needAddress']       = $needAddress;
        $arrOutput['isConfirm']         = $isConfirm;
        $arrOutput['confirmCourseList'] = $confirmCourseList;

        return $arrOutput;
    }

    /**
     * @param $skuList
     * @param $skuUsedCouponId //指定已使用过的（不可使用的）优惠券。当同时支付商品、课程时，使用此参数便于分组处理商品、课程，避免优惠券重复使用
     * @param $isJson
     *
     * @desc //获取最佳优惠券使用方式（从获得最大优惠金额角度考虑，不考虑同等优惠下时多种优惠券组合的情况）
     * @desc 获取最佳优惠券使用方式，穷举计算量过大，按课程价格由高到低依次取最佳优惠券，类似贪心算法
     * @return array
     */
    private function getSkuCouponStrategy($skuList, $skuUsedCouponId = array(), $isJson = 0)
    {
        $tmp_price_sort = array();
        foreach ($skuList as $sku) {
            $tmp_price_sort[] = $sku['price'];
        }
        array_multisort($tmp_price_sort, SORT_DESC, SORT_NUMERIC, $skuList);
        $arrCouponUsed = $skuUsedCouponId;
        //优先处理指定优惠券的商品
        foreach ($skuList as &$sku233) {
            unset($sku233['courseInfo']);
            $couponId = $sku233['couponId'];
            if (empty($couponId)) {
                continue;
            }
            if (empty($sku233['couponList'])) {
                $sku233['couponId'] = '';
                continue;
            }
            $couponId = Hk_Util_IdCrypt::encodeQid($couponId);
            if (in_array($couponId, $arrCouponUsed)) {
                $sku233['couponId'] = '';
                continue;
            }
            foreach ($sku233['couponList'] as $coupon) {
                if ($coupon['couponId'] == $couponId) {
                    $sku233['couponId'] = Hk_Util_IdCrypt::decodeQid($coupon['couponId']);
                    $sku233             = $this->_tradeUseCoupon($sku233);
                    $sku233['couponId'] = $coupon['couponId'];
                    $arrCouponUsed[]    = $coupon['couponId'];
                    continue 2;
                }
            }
            $sku233['couponId'] = '';
        }
        //异步接口不主动帮用户选择优惠券
        if (!$isJson) {
            foreach ($skuList as &$sku) {
                if (!empty($sku['couponId'])) {
                    continue;
                }
                if (!empty($sku['couponList'])) {
                    foreach ($sku['couponList'] as $coupon) {
                        if (!in_array($coupon['couponId'], $arrCouponUsed)) {
                            $sku['couponId'] = Hk_Util_IdCrypt::decodeQid($coupon['couponId']);
                            $sku             = $this->_tradeUseCoupon($sku);
                            $sku['couponId'] = $coupon['couponId'];
                            $arrCouponUsed[] = $coupon['couponId'];
                            break;
                        } else {
                            $sku['couponId'] = '';
                        }
                    }
                } else {
                    $sku['couponId'] = '';
                }
            }
        }

        return $skuList;
    }

    public function submitOrder($arrParams)
    {
        $uid            = intval($arrParams['uid']);
        $courseInfoList = $arrParams['courseList'];
        $payChannel     = intval($arrParams['payChannel']);
        $appId          = $arrParams['appId'];
        $appType        = $arrParams['appType'];
        $arrJumpFrom    = $arrParams['arrJumpFrom'];
        $vcname         = $arrParams['vcname'];
        $vc             = $arrParams['vc'];
        $cuid           = $arrParams['cuid'];
        $isPcPay        = $arrParams['isPcPay'];
        $isJson         = 1;
        $addressInfo    = $arrParams['addressInfo'];

        //参数检查
        if ($uid <= 0 || empty($courseInfoList)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'goPrePay', $arrParams);
        }

        //订单同步到Trade参数
        $tradeParamInfo = array(
            'payChannel'    => $arrParams['payChannel'],
            'jumpFrom'      => $arrParams['jumpFrom'],
            'plat'          => $arrParams['plat'],
            'lastfrom'      => $arrParams['lastfrom'],
            'orifrom'       => $arrParams['orifrom'],
            'logpath'       => $arrParams['logpath'],
            'vc'            => $arrParams['vc'],
            'vcname'        => $arrParams['vcname'],
            'cuid'          => $arrParams['cuid'],
            'appType'       => $arrParams['appType'],
            'appId'         => $arrParams['appId'],
            'channel'       => $arrParams['channel'],
            'os'            => $arrParams['os'],
            'skuList'       => $arrParams['courseList'],
            'uid'           => $arrParams['uid'],
            'phone'         => $arrParams['phone'],
            'ua'            => strval(substr($_SERVER['HTTP_USER_AGENT'], 0, 200)),
            'dataType'      => 2,
            'moduleFrom'    => $arrParams['moduleFrom'],
            'addressInfo'   => $addressInfo,
        );
        ///////////////////////

        //检查用户角色
        $this->checkUserValid($uid);

        //获取支付信息
        $payInfo = $this->getPayInfo($courseInfoList, $uid, $appId, $appType, $isJson, 1);

        //处理商品信息，这里因为时间关系，为了复用现有的下单代码，将数据格式化为现有下单流程的格式
        $courseList    = array();
        $boundList     = array();
        $orderGiftInfo = array();
        $skuPayInfo    = isset($payInfo['skuPayInfo']) ? $payInfo['skuPayInfo'] : array();
        $coursePayInfo = isset($payInfo['coursePayInfo']) ? $payInfo['coursePayInfo'] : array();
        $needAddress   = 0;
        $originalBizInfo    = $payInfo['originalBizInfo'];
        $useCouponInfo      = $payInfo['useCouponInfo'];

        //商品信息
        if (!empty($skuPayInfo)) {
            $needAddress         = $needAddress | $skuPayInfo['needAddress'];
            $skuGroupInfoWithBiz = $skuPayInfo['skuGroupInfoWithBiz'];
            if (!empty($skuGroupInfoWithBiz)) {
                foreach ($skuGroupInfoWithBiz as $groupItem) {
                    $id2Sku  = array();
                    $skuList = $groupItem['skuList'];
                    if (empty($skuList)) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::BIZ_GET_BIZ_INFO_ERROR);
                    }
                    foreach ($skuList as $sku) {
                        $id2Sku[$sku['skuId']] = $sku;
                    }
                    $bizList = $groupItem['bizList'];
                    foreach ($bizList as $bizItem) {
                        $bizItemSkuIdList = $bizItem['skuIdList'];
                        $giftList         = isset($bizItem['giftList']) ? $bizItem['giftList'] : array();
                        $bizType          = $bizItem['bizType'];
                        $subBizType       = $bizItem['subBizType'];
                        //处理实物赠品，2018暑秋连报只有实物赠品，这里不处理非实物赠品
                        if (!empty($giftList)) {
                            foreach ($giftList as $giftItem) {
                                $giftId = $giftItem['giftId'];
                                if (isset(Hkzb_Util_Fudao_Gift::$COURSE_GIFT_PRICE[$giftId])) {
                                    $giftItem = array(
                                        'giftList'          => array(
                                            $giftId,
                                        ),
                                        'giftRelatedCourse' => array(),
                                    );
                                } else {
                                    $objZbDakInterface = new Zb_Core_Ds_Dak_Interface();
                                    $giftSkuInfo       = $objZbDakInterface->getSkuInfoMap($giftId, true);
                                    if (empty($giftSkuInfo)) {
                                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::GIFT_NOT_EXIST);
                                    }
                                    if (!empty($giftSkuInfo) && $giftSkuInfo['skuType'] == Zb_Const_Sku::SKU_TYPE_ENTITY) {
                                        $giftItem = array(
                                            'giftList'          => array(
                                                $giftId,
                                            ),
                                            'giftRelatedCourse' => array(),
                                        );
                                    }
                                }
                                foreach ($bizItemSkuIdList as $bizItemSkuIdListSkuId) {
                                    $skuInfo                       = $id2Sku[$bizItemSkuIdListSkuId];
                                    $giftItem['giftRelatedCourse'] = array_merge($giftItem['giftRelatedCourse'], $skuInfo['courseIdList']);
                                }
                                $orderGiftInfo[] = $giftItem;
                            }
                        }
                        //按策略处理信息
                        if ($bizType == Zb_Const_Zbbiz::BIZ_TYPE_MAIN) {
                            //连报策略，按boundList格式处理
                            if (in_array($subBizType, array(Zb_Const_Zbbiz::SUB_BIZ_TYPE_COMB_APPOINT, Zb_Const_Zbbiz::SUB_BIZ_TYPE_COMB_SELECT))) {
                                $boundListItem = array();
                                foreach ($bizItemSkuIdList as $bizItemSkuIdListSkuId) {
                                    $skuInfo                        = $id2Sku[$bizItemSkuIdListSkuId];
                                    $skuInfo['boundDiscount']       = array(
                                        'discount'    => $bizItem['discountPriceMap'][$bizItemSkuIdListSkuId],
                                        'title'       => $bizItem['title'],
                                        'description' => $bizItem['description'],
                                        'zhe'         => $bizItem['title'] . ' ' . $bizItem['description'],
                                    );
                                    $id2Sku[$bizItemSkuIdListSkuId] = $skuInfo;
                                    $boundListItem                  = array_merge($boundListItem, $skuInfo['courseIdList']);
                                }
                                $tmp = array();
                                foreach ($boundListItem as $boundItem) {
                                    $tmp[] = array('courseId' => $boundItem);
                                }
                                $boundList[] = $tmp;
                            }
                            //单门折扣
                            if ($subBizType == Zb_Const_Zbbiz::SUB_BIZ_TYPE_SELECT_APPOINT) {
                                foreach ($bizItemSkuIdList as $bizItemSkuIdListSkuId) {
                                    $skuInfo                        = $id2Sku[$bizItemSkuIdListSkuId];
                                    $skuInfo['qiu2Discount']        = array(
                                        'discount'    => $bizItem['discountPriceMap'][$bizItemSkuIdListSkuId],
                                        'title'       => $bizItem['title'],
                                        'description' => $bizItem['description'],
                                        'zhe'         => $bizItem['title'] . ' ' . $bizItem['description'],
                                    );
                                    $id2Sku[$bizItemSkuIdListSkuId] = $skuInfo;
                                }
                            }
                        }
                        //首单立减
                        if ($bizType == Zb_Const_Zbbiz::BIZ_TYPE_FIRSTTRADE) {
                            foreach ($bizItemSkuIdList as $bizItemSkuIdListSkuId) {
                                $skuInfo                        = $id2Sku[$bizItemSkuIdListSkuId];
                                $skuInfo['firstTradeDiscount']  = array(
                                    'discount'    => $bizItem['discountPriceMap'][$bizItemSkuIdListSkuId],
                                    'title'       => $bizItem['title'],
                                    'description' => $bizItem['description'],
                                );
                                $id2Sku[$bizItemSkuIdListSkuId] = $skuInfo;
                            }
                        }
                    }
                    foreach ($id2Sku as $id2Sku_SkuInfo) {
                        $skuCourseIdList = $id2Sku_SkuInfo['courseIdList'];
                        $saleType        = $id2Sku_SkuInfo['saleType'];
                        if (count($skuCourseIdList) == 1) {
                            //获取课程信息
                            $courseId   = $skuCourseIdList[0];
                            $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
                            if (false === $courseInfo) {
                                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
                            }
                            $courseItem = array(
                                'skuId'                 => $id2Sku_SkuInfo['skuId'],
                                'saleType'              => $id2Sku_SkuInfo['saleType'],
                                'hasPayPreOrder'        => $id2Sku_SkuInfo['hasPayPreOrder'],
                                'courseId'              => $skuCourseIdList[0],
                                'courseInfo'            => $courseInfo,
                                'orderPrice'            => $id2Sku_SkuInfo['orderPrice'],
                                'originalPrice'         => $id2Sku_SkuInfo['oriPrice'],
                                'assistantInfo'         => array(),
                                'usedCouponInfo'        => $id2Sku_SkuInfo['usedCouponInfo'],
                                'usedCoupon'            => $id2Sku_SkuInfo['usedCoupon'] ? $id2Sku_SkuInfo['usedCoupon'] : false,
                                'needNotifyToAssistant' => $id2Sku_SkuInfo['needNotifyToAssistant'],
                                'specialSkuType'        => $id2Sku_SkuInfo['specialSkuType'],
                            );
                            if ($saleType == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                                $courseItem['expendPrice']    = $id2Sku_SkuInfo['expendPrice'];
                                $courseItem['skuExpendPrice'] = $id2Sku_SkuInfo['expendPrice'];
                            } else {
                                $allCourseInfo               = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extLesson', 'extTeacher', 'extAssistant'), true);
                                $courseItem['assistantInfo'] = $this->_tradeGetAssistantInfo(array('uid' => $uid, 'courseId' => $courseId, 'courseInfo' => $courseInfo, 'allCourseInfo' => $allCourseInfo));
                                if (isset($id2Sku_SkuInfo['boundDiscount'])) {
                                    $courseItem['boundDiscount'] = $id2Sku_SkuInfo['boundDiscount'];
                                }
                                if (isset($id2Sku_SkuInfo['qiu2Discount'])) {
                                    $courseItem['qiu2Discount'] = $id2Sku_SkuInfo['qiu2Discount'];
                                }
                                if (isset($id2Sku_SkuInfo['firstTradeDiscount'])) {
                                    $courseItem['firstTradeDiscount'] = $id2Sku_SkuInfo['firstTradeDiscount'];
                                }
                                if ($id2Sku_SkuInfo['hasPayPreOrder']) {
                                    $payPreOrderInfo = Hkzb_Ds_Fudao_Advanced_TradeRecord::getUserPrePayOrder($uid, $id2Sku_SkuInfo['skuId']);
                                    if (empty($payPreOrderInfo)) {
                                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'preorderinfo invalid', array('uid' => $uid, 'skuId' => $id2Sku_SkuInfo['skuId']));
                                    }
                                    if ($payPreOrderInfo === false) {
                                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'getUserPrePayOrder fail ', array('uid' => $uid, 'skuId' => $id2Sku_SkuInfo['skuId']));
                                    }
                                    $courseItem['expendDiscount'] = array(
                                        'description'           => '订金抵扣',
                                        'skuId'                 => $id2Sku_SkuInfo['skuId'],
                                        'payment'               => $payPreOrderInfo['prePayOrderInfo'][$courseId]['payment'],
                                        'expendPrice'           => $payPreOrderInfo['prePayOrderInfo'][$courseId]['expendPrice'],
                                        'preOrderTradeRecordId' => $payPreOrderInfo['prePayOrderInfo'][$courseId]['tradeRecordId'],
                                    );
                                }
                            }
                            $courseList[] = $courseItem;
                        } else {
                            //组合商品，需要将支付金额、优惠券、折扣金额拆分到课程维度
                            $courseCnt               = count($skuCourseIdList);
                            $skuOrderPrice           = $id2Sku_SkuInfo['orderPrice'];
                            $skuOriPrice             = $id2Sku_SkuInfo['oriPrice'];
                            $skuUsedCoupon           = $id2Sku_SkuInfo['usedCoupon'] ? $id2Sku_SkuInfo['usedCoupon'] : false;
                            $skuUsedCouponInfo       = $id2Sku_SkuInfo['usedCouponInfo'];
                            $cntFlag                 = 1;
                            $orderPriceDistribute    = 0;
                            $couponPriceDistribute   = 0;
                            $boundDiscountDistribute = 0;
                            $qiu2DiscountDistribute  = 0;
                            $firstTradeDistribute    = 0;
                            $expendPriceDistribute   = 0;
                            $allCourseInfoList       = $this->_objDsAdvancedCourse->getCourseInfoArr($skuCourseIdList, array('extLesson', 'extTeacher', 'extAssistant'), true);
                            if(empty($allCourseInfoList)) {
                                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::COURSE_NOT_EXIST, '', array('courseIdList' => json_encode($skuCourseIdList)));
                            }
                            $courseTotalPrice = 0;
                            foreach($allCourseInfoList as $tmpCourseItem) {
                                $courseTotalPrice += $tmpCourseItem['price'];
                            }
                            foreach ($skuCourseIdList as $courseId) {
                                //获取课程信息
                                $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
                                if (false === $courseInfo) {
                                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
                                }
                                $courseOriPrice       = $courseInfo['price'];
                                $courseUsedCouponInfo = array();
                                if ($cntFlag == $courseCnt) {
                                    $courseOrderPrice = $skuOrderPrice - $orderPriceDistribute;
                                    if ($skuUsedCoupon) {
                                        $courseUsedCouponInfo                = $skuUsedCouponInfo;
                                        $courseUsedCouponInfo['couponPrice'] = $skuUsedCouponInfo['couponPrice'] - $couponPriceDistribute;
                                    }
                                } else {
                                    //为了保证学币退款单位问题，这里末位置为0
                                    $courseOrderPrice     = intval($skuOrderPrice * ($courseOriPrice / $courseTotalPrice)) - intval($skuOrderPrice * ($courseOriPrice / $courseTotalPrice)) % 10;
                                    $orderPriceDistribute += $courseOrderPrice;
                                    if ($skuUsedCoupon) {
                                        $courseUsedCouponInfo                = $skuUsedCouponInfo;
                                        $courseUsedCouponInfo['couponPrice'] = intval($skuUsedCouponInfo['couponPrice'] * ($courseOriPrice / $courseTotalPrice)) - intval($skuUsedCouponInfo['couponPrice'] * ($courseOriPrice / $courseTotalPrice)) % 10;
                                        $couponPriceDistribute               += $courseUsedCouponInfo['couponPrice'];
                                    }
                                }
                                $courseItem = array(
                                    'skuId'                 => $id2Sku_SkuInfo['skuId'],
                                    'saleType'              => $id2Sku_SkuInfo['saleType'],
                                    'hasPayPreOrder'        => $id2Sku_SkuInfo['hasPayPreOrder'],
                                    'courseId'              => $courseId,
                                    'courseInfo'            => $courseInfo,
                                    'assistantInfo'         => array(),
                                    'orderPrice'            => $courseOrderPrice,
                                    'originalPrice'         => $courseOriPrice,
                                    'usedCoupon'            => $skuUsedCoupon,
                                    'usedCouponInfo'        => $courseUsedCouponInfo,
                                    'needNotifyToAssistant' => $id2Sku_SkuInfo['needNotifyToAssistant'],
                                    'specialSkuType'        => $id2Sku_SkuInfo['specialSkuType'],
                                );
                                if ($saleType == Zb_Const_Zbbiz::SKU_SALE_TYPE_PRESALE) {
                                    $courseItem['skuExpendPrice'] = $id2Sku_SkuInfo['expendPrice'];
                                    if ($cntFlag == $courseCnt) {
                                        $courseItem['expendPrice'] = $id2Sku_SkuInfo['expendPrice'] - $expendPriceDistribute;
                                    } else {
                                        $courseItem['expendPrice'] = intval($id2Sku_SkuInfo['expendPrice'] * ($courseOriPrice / $courseTotalPrice)) - intval($id2Sku_SkuInfo['expendPrice'] * ($courseOriPrice / $courseTotalPrice)) % 10;
                                        $expendPriceDistribute     += $courseItem['expendPrice'];
                                    }
                                } else {
                                    $allCourseInfo               = $this->_objDsAdvancedCourse->getCourseInfo($courseId, array('extLesson', 'extTeacher', 'extAssistant'), true);
                                    $courseItem['assistantInfo'] = $this->_tradeGetAssistantInfo(array('uid' => $uid, 'courseId' => $courseId, 'courseInfo' => $courseInfo, 'allCourseInfo' => $allCourseInfo));
                                    if (isset($id2Sku_SkuInfo['boundDiscount'])) {
                                        $courseItem['boundDiscount'] = $id2Sku_SkuInfo['boundDiscount'];
                                        if ($cntFlag == $courseCnt) {
                                            $courseItem['boundDiscount']['discount'] = $id2Sku_SkuInfo['boundDiscount']['discount'] - $boundDiscountDistribute;
                                        } else {
                                            $courseItem['boundDiscount']['discount'] = intval($id2Sku_SkuInfo['boundDiscount']['discount'] * ($courseOriPrice / $courseTotalPrice)) - intval($id2Sku_SkuInfo['boundDiscount']['discount'] * ($courseOriPrice / $courseTotalPrice)) % 10;
                                            $boundDiscountDistribute                 += $courseItem['boundDiscount']['discount'];
                                        }
                                    }
                                    if (isset($id2Sku_SkuInfo['qiu2Discount'])) {
                                        $courseItem['qiu2Discount'] = $id2Sku_SkuInfo['qiu2Discount'];
                                        if ($cntFlag == $courseCnt) {
                                            $courseItem['qiu2Discount']['discount'] = $id2Sku_SkuInfo['qiu2Discount']['discount'] - $qiu2DiscountDistribute;
                                        } else {
                                            $courseItem['qiu2Discount']['discount'] = intval($id2Sku_SkuInfo['qiu2Discount']['discount'] * ($courseOriPrice / $courseTotalPrice)) - intval($id2Sku_SkuInfo['qiu2Discount']['discount'] * ($courseOriPrice / $courseTotalPrice)) % 10;
                                            $boundDiscountDistribute                += $courseItem['qiu2Discount']['discount'];
                                        }
                                    }
                                    if (isset($id2Sku_SkuInfo['firstTradeDiscount'])) {
                                        $courseItem['firstTradeDiscount'] = $id2Sku_SkuInfo['firstTradeDiscount'];
                                        if ($cntFlag == $courseCnt) {
                                            $courseItem['firstTradeDiscount']['discount'] = $id2Sku_SkuInfo['firstTradeDiscount']['discount'] - $firstTradeDistribute;
                                        } else {
                                            $courseItem['firstTradeDiscount']['discount'] = intval($id2Sku_SkuInfo['firstTradeDiscount']['discount'] * ($courseOriPrice / $courseTotalPrice)) - intval($id2Sku_SkuInfo['firstTradeDiscount']['discount'] * ($courseOriPrice / $courseTotalPrice)) % 10;
                                            $boundDiscountDistribute                      += $courseItem['firstTradeDiscount']['discount'];
                                        }
                                    }
                                    if ($id2Sku_SkuInfo['hasPayPreOrder']) {
                                        $payPreOrderInfo = Hkzb_Ds_Fudao_Advanced_TradeRecord::getUserPrePayOrder($uid, $id2Sku_SkuInfo['skuId']);
                                        if ($payPreOrderInfo === false) {
                                            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'getUserPrePayOrder fail ', array('uid' => $uid, 'skuId' => $id2Sku_SkuInfo['skuId']));
                                        }
                                        if ($cntFlag == $courseCnt) {
                                            $courseItem['expendDiscount'] = array(
                                                'description'           => '订金抵扣',
                                                'skuId'                 => $id2Sku_SkuInfo['skuId'],
                                                'payment'               => $payPreOrderInfo['prePayOrderInfo'][$courseId]['payment'],
                                                'expendPrice'           => $payPreOrderInfo['prePayOrderInfo'][$courseId]['expendPrice'],
                                                'preOrderTradeRecordId' => $payPreOrderInfo['prePayOrderInfo'][$courseId]['tradeRecordId'],
                                            );
                                        } else {
                                            $courseItem['expendDiscount'] = array(
                                                'description'           => '订金抵扣',
                                                'skuId'                 => $id2Sku_SkuInfo['skuId'],
                                                'payment'               => $payPreOrderInfo['prePayOrderInfo'][$courseId]['payment'],
                                                'expendPrice'           => $payPreOrderInfo['prePayOrderInfo'][$courseId]['expendPrice'],
                                                'preOrderTradeRecordId' => $payPreOrderInfo['prePayOrderInfo'][$courseId]['tradeRecordId'],
                                            );
                                            $expendPriceDistribute        += $courseItem['expendDiscount']['discount'];
                                        }
                                    }
                                }
                                $courseList[] = $courseItem;
                                ++$cntFlag;
                            }
                        }
                    }
                }
            }
        }
        //课程信息
        if (!empty($coursePayInfo)) {
            $needAddress       = $needAddress | $coursePayInfo['needAddress'];
            $coursePayItemList = $coursePayInfo['courseList'];
            foreach ($coursePayItemList as $item) {
                $courseId   = $item['courseId'];
                $courseInfo = $this->_objDsCourse->getPackCourseInfo($courseId);
                if (false === $courseInfo) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('courseId' => $courseId));
                }
                $courseItem   = array(
                    'courseId'       => $courseId,
                    'courseInfo'     => $courseInfo,
                    'assistantInfo'  => $item['assistantInfo'],
                    'orderPrice'     => $item['orderPrice'],
                    'originalPrice'  => $item['originalPrice'],
                    'usedCoupon'     => isset($item['usedCoupon']) ? $item['usedCoupon'] : false,
                    'usedCouponInfo' => isset($item['usedCouponInfo']) ? $item['usedCouponInfo'] : array(),
                );
                $courseList[] = $courseItem;
            }
        }
        //追加赠送课程写入
        $giftSkuList = $this->getGiftList($originalBizInfo)['skuGiftList'];
        $giftSkuId = $this->ids($giftSkuList, 'giftId');
        if ($giftSkuId) {
            //赠送课程
            $giftCourseIds = [];
            $giftCourseInfo = $this->_objZbAdvancedInterface->getSkuInfoBatch($giftSkuId);
            if ($giftCourseInfo == false) {
                Bd_Log::warning("getSkuInfoBatch empty[". json_encode($giftSkuId). "]");
            }
            $giftCourseList = [];
            foreach($giftCourseInfo as $k => $v){
                foreach($v['courseIds'] as $vv){
                    if(!in_array($vv, $giftCourseIds)){
                        $giftCourseIds[] = $vv;
                    }
                }
                foreach($v['courseInfoList'] as $k => $v){
                    $giftCourseList[$k] = $v;
                }
            }
            $ret = Zb_Service_Dar_SubTrade::getStatusKVByCourseIds($uid, $giftCourseIds);
            if(false === $ret){
                Bd_Log::warning('Zb_Service_Dar_SubTrade::getStatusKVByCourseIds error[uid:'.$uid.' giftCourseIds:'. Trade_Util::arr2str($giftCourseIds).']');
            }
            $hasBuyList = isset($ret['data'])?$ret['data']:[];
            if($hasBuyList){
                $toFilterCourseIds = [];
                foreach($hasBuyList as $hasBuyCourseId => $hasBuy){
                    if($hasBuy){
                        $toFilterCourseIds[] = $hasBuyCourseId;
                    }
                }
                if($toFilterCourseIds){
                    $giftCourseIds = array_diff($giftCourseIds, $toFilterCourseIds);
                }
            }
            foreach($giftCourseIds as $cid){
                $courseItem   = array(
                    'courseId'       => $cid,
                    'courseInfo'     => $giftCourseList[$cid],
                    'orderPrice'     => 0,
                    'originalPrice'  => 0,
                    'usedCoupon'     => false,
                    'usedCouponInfo' => array(),
                    'isPresent'          => 1,
                );
                $courseItem['assistantInfo'] = $this->_tradeGetAssistantInfo(array('uid' => $uid, 'courseId' => $cid, 'courseInfo' => $giftCourseList[$cid], 'allCourseInfo' => $giftCourseList));

                $courseList[] = $courseItem;
            }
        }

        $arrParams['courseList'] = $courseList;
        $arrParams['gift']       = $orderGiftInfo;
        if (!empty($boundList)) {
            //绑定课信息
            $arrParams['boundList'] = $boundList;
            $arrParams['boundDesc'] = '连报课程优惠';
        }
        $arrParams['summerCourseTimeValid'] = isset($summerCourseTimeValid) ? $summerCourseTimeValid : 1;//课程时间是否冲突
        $arrParams['needAddress']           = $needAddress;//收否需要邮寄地址

        if(!isset($arrParams['addressInfo'] ) || empty($arrParams['addressInfo'] )){
            $studentInfo                        = $this->_objDsStudent->getStudentInfo($uid, array(), true);
            $arrParams['addressInfo']           = isset($studentInfo['extData']['addressInfo']) ? $studentInfo['extData']['addressInfo'] : array();//地址信息
            $tradeParamInfo['addressInfo'] =  $arrParams['addressInfo'];
        }
        $arrParams                          = $this->_cartTradeProcOrder($arrParams, $arrJumpFrom);
        $payInfo                            = $arrParams['payInfo'];
        $payInfo['payStatus']               = $arrParams['payStatus'];

        //订单同步到Trade
        $tradeParamInfo['purchaseId']		= $arrParams['payInfo']['orderId'];
        $tradeParamInfo['tradeParam']		= $arrParams['tradeParamInfo'];
        $tradeParamInfo['courseList']		= $arrParams['courseList'];
        $tradeParamInfo['giftCourseTradeRecord'] = $arrParams['giftCourseTradeRecord'];
        $tradeParamInfo['originalBizInfo']	= $originalBizInfo;
        $tradeParamInfo['useCouponInfo']    = $useCouponInfo;

        $nmqToTradeData = $this->getOrderWriteToTradeParam($tradeParamInfo);
        if ($tradeParamInfo['moduleFrom'] == 'goods') {
            $arrParams['nmqToTradeData'] = $nmqToTradeData;
            $payInfo['nmqToTradeData']   = $nmqToTradeData;
        }
        ///////////////

        return (isset($isPcPay) && ($isPcPay === 1)) ? $arrParams : $payInfo;
    }

    //格式化订单入库前的数据格式
    private function _formatOrderData($orderId, $arrParams)
    {
        $studentUid   = $arrParams['uid'];
        $courseId     = $arrParams['courseId'];
        $assistantUid = $arrParams['assistantInfo']['assistantUid'];
        $courseInfo   = $arrParams['courseInfo'];
        $arrFields    = array(
            'orderId'      => $orderId,
            'studentUid'   => $studentUid,
            'courseId'     => $courseId,
            'grade'        => $courseInfo['grade'],
            'subject'      => $courseInfo['subject'],
            'payment'      => $arrParams['orderPrice'],   //To-do这个是不是应该是payment取值？
            'teacherUid'   => $courseInfo['teacherUid'],
            'assistantUid' => $assistantUid,
            'extData'      => array(
                'vcname'   => $arrParams['vcname'],
                'phone'    => $arrParams['phone'],
                'jumpFrom' => $arrParams['jumpFrom'],
                'vc'       => $arrParams['vc'],
                'cuid'     => $arrParams['cuid'],
                'appType'  => $arrParams['appType'],
                'ua'       => substr($_SERVER['HTTP_USER_AGENT'], 0, 200),
                'payment'  => $arrParams['orderPrice'],
            ),
        );

        //订单支付类型 支付父订单、子订单、正常单、预约单
        if (isset($arrParams['type'])) {
            $arrFields['type'] = intval($arrParams['type']);
        }else {
            $arrFields['type'] = Hkzb_Ds_Fudao_TradeRecord::TYPE_NORMAL;
        }

        if (isset($arrParams['usedCouponInfo'])) {
            $arrFields['extData']['couponInfo'] = $arrParams['usedCouponInfo'];
        }

        //同时涉及到userExtData的信息结构
        if (isset($arrParams['consistOf'])) {
            $arrFields['extData']['consistOf']     = $arrParams['consistOf'];
            $arrFields['userExtData']['consistOf'] = $arrParams['consistOf'];
        }
        if (isset($arrParams['boundList'])) {
            $arrFields['extData']['boundList']     = $arrParams['boundList'];
            $arrFields['userExtData']['boundList'] = $arrParams['boundList'];
        }
        if (isset($arrParams['boundDesc'])) {
            $arrFields['extData']['boundDesc']     = $arrParams['boundDesc'];
            $arrFields['userExtData']['boundDesc'] = $arrParams['boundDesc'];
        }

        if (isset($arrParams['payment'])) {
            $arrFields['extData']['payment'] = $arrParams['payment'];
        }
        if (isset($arrParams['purchaseOrderId'])) {
            $arrFields['extData']['purchaseOrderId'] = $arrParams['purchaseOrderId'];
        }
        if (isset($arrParams['boundDiscount'])) {
            $arrFields['extData']['boundDiscount'] = $arrParams['boundDiscount'];
        }

        if (isset($arrParams['originalPrice'])) {
            $arrFields['extData']['originalPrice'] = $arrParams['originalPrice'];
        }

        if (isset($arrParams['fromShoppingList'])) {
            $arrFields['extData']['fromShoppingList'] = intval($arrParams['fromShoppingList']);
        }

        if ($arrParams['payChannel']) {
            $arrFields['extData']['payChannel'] = $arrParams['payChannel'];
        }
        if (isset($arrParams['appId']) && $arrParams['appId']) {
            $arrFields['extData']['app'] = $arrParams['appId'];
        }
        if (isset($arrParams['channel']) && $arrParams['channel']) {
            $arrFields['extData']['channel'] = $arrParams['channel'];
        }
        if (isset($arrParams['gift']) && !empty($arrParams['gift'])) {
            $arrFields['extData']['gift'] = $arrParams['gift'];
        }
        if (Hkzb_Util_FuDao::isSuperVipUser($studentUid)) {
            $arrFields['extData']['isSuperVipUser'] = 1;
        }
        if (isset($arrParams['skuId']) && !empty($arrParams['skuId'])) {
            $arrFields['extData']['skuId'] = $arrParams['skuId'];
        }

        //预约单的拓展字段
        if (isset($arrParams['expendPrice'])) {
            $arrFields['extData']['expendPrice'] = $arrParams['expendPrice'];
        }
        if (isset($arrParams['skuExpendPrice'])) {
            $arrFields['extData']['skuExpendPrice'] = $arrParams['skuExpendPrice'];
        }
        if (isset($arrParams['expendDiscount'])) {
            $arrFields['extData']['expendDiscount'] = $arrParams['expendDiscount'];
        }

        //临时需求字段通知家访
        if (isset($arrParams['needNotifyToAssistant'])) {
            $arrFields['extData']['needNotifyToAssistant'] = $arrParams['needNotifyToAssistant'];
        }
        //是否是特惠课标识
        if (isset($arrParams['specialSkuType'])) {
            $arrFields['extData']['specialSkuType'] = $arrParams['specialSkuType'];
        }
        if(isset($arrParams['plat'])){
            $arrFields['extData']['plat'] = $arrParams['plat'];
        }
        if(isset($arrParams['lastfrom'])){
            $arrFields['extData']['lastfrom'] = $arrParams['lastfrom'];
        }
        if(isset($arrParams['orifrom'])){
            $arrFields['extData']['orifrom'] = $arrParams['orifrom'];
        }
        if(isset($arrParams['logpath'])){
            $arrFields['extData']['logpath'] = $arrParams['logpath'];
        }

        //To-do可以尝试废弃的拓展字段
        if (isset($arrParams['qiu2Discount'])) {
            $arrFields['extData']['qiu2DiscountInfo'] = $arrParams['qiu2Discount'];
        }
        if (isset($arrParams['summerMulSubDesc'])) {
            $arrFields['extData']['summerMulSubDesc'] = $arrParams['summerMulSubDesc'];
        }
        if (isset($arrParams['summerMulSubOrderList'])) {
            $arrFields['extData']['summerMulSubOrderList'] = $arrParams['summerMulSubOrderList'];
        }
        if (isset($arrParams['summerMulSubDiscountRule'])) {
            $arrFields['extData']['summerMulSubDiscountRule'] = $arrParams['summerMulSubDiscountRule'];
        }
        if (isset($arrParams['springCoursePayment'])) {
            $arrFields['extData']['springCoursePayment'] = intval($arrParams['springCoursePayment']);
        }
        if (isset($arrParams['paymentIsZero'])) {
            $arrFields['extData']['paymentIsZero'] = $arrParams['paymentIsZero'];
        }

        if (isset($arrParams['addressInfo'])) {
            $arrFields['extData']['addressInfo'] = $arrParams['addressInfo'];
        }

        return $arrFields;
    }

    //发起nmq的异步下单命令
    private function _sendOrderNMQ($arrParams)
    {
        $orderId           = $arrParams['orderId'];
        $formatParams      = $this->_formatOrderData($orderId, $arrParams);
        $arrAddOrderParams = array(
            'studentUid'   => $formatParams['studentUid'],
            'orderId'      => $formatParams['orderId'],
            'type'         => $formatParams['type'],
            'payment'      => $formatParams['payment'],
            'courseId'     => $formatParams['courseId'],
            'grade'        => $formatParams['grade'],
            'subject'      => $formatParams['subject'],
            'teacherUid'   => $formatParams['teacherUid'],
            'assistantUid' => $formatParams['assistantUid'],
            'ext'          => json_encode($formatParams['extData']),
            'userExt'      => isset($formatParams['userExtData']) ? json_encode($formatParams['userExtData']) : '[]',
        );

        //发起订单请求
        $ret = Zb_Service_Nmq::sendCommand(Zb_Const_Command::COMMAND_TRADE_170003, $arrAddOrderParams);
        if ($ret === false) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service trade_api_addorder connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");

            return false;
        }
        $errno  = intval($ret['errno']);
        $errmsg = strval($ret['error_msg']);
        if ($errno > 0) {
            Bd_Log::warning("Error:[service trade_api_addorder process error], Detail:[errno:$errno errmsg:$errmsg]");

            return false;
        }

        return true;
    }

    private function getGiftList($arrBizInfo)
    {
        $arrOutGiftList = array();
        if (empty($arrBizInfo)) {
            return $arrOutGiftList;
        }

        return $this->getGiftListWithBizInfo($arrBizInfo['groupInfo'], $arrBizInfo['giftBizList']);
    }

    private function getGiftListWithBizInfo($bizGroupInfo, $arrGiftBizInfo)
    {

        $arrOutGiftList = array();
        $totalGiftList = array();

        //寒春新增字段,则赠品全部从这个数组里取出
        if (isset($arrGiftBizInfo) && !empty($arrGiftBizInfo)) {
            foreach ($arrGiftBizInfo as $giftBizItem) {
                if (!empty($giftBizItem)) {
                    if (isset($giftBizItem['giftList']) && !empty($giftBizItem['giftList'])) {
                        $totalGiftList = array_merge($totalGiftList, $giftBizItem['giftList']);
                    }
                }
            }
        } else {
            //如果不存在寒春新增字段,则从老字段giftBizList里取出
            if (!empty($bizGroupInfo)) {
                foreach ($bizGroupInfo as $bizGroupId => $bizDetail) {
                    if (!empty($bizDetail)) {
                        $skuGroupInfoWithBiz[$bizGroupId]['bizList'] = $bizDetail;
                        foreach ($bizDetail as $item) {
                            $giftList = isset($item['giftList']) && !empty($item['giftList']) ? $item['giftList'] : array();
                            if (!empty($giftList)) {
                                $totalGiftList = array_merge($totalGiftList, $giftList);
                            }
                        }
                    }
                }
            }
        }
        $skuGiftList = array();
        $couponGiftList = array();
        //如果不空,则进行分类处理
        if (!empty($totalGiftList)) {
            foreach ($totalGiftList as $giftItem) {
                if ('coupon' === $giftItem['type']) {
                    $couponGiftList[] = $giftItem;
                } else if ('sku' === $giftItem['type']) {
                    $skuGiftList[] = $giftItem;
                } else {
                    //有不识别的赠品类型
                    return false;
                }
            }
        }
        $arrOutGiftList['skuGiftList'] = $skuGiftList;
        $arrOutGiftList['couponGiftList'] = $couponGiftList;

        return $arrOutGiftList;
    }
    private function ids($data, $key='id'){
        $ids = [];
        foreach($data as $k => $v){
            if(!in_array($v[$key], $ids)){
                $ids[] = $v[$key];
            }
        }
        return $ids;
    }

}
