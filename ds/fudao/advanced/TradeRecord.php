<?php
/**
 * @file TradeRecord.php
 * <AUTHOR>
 * @date 2016年11月30日 下午5:53:15
 * @version $Revision$ 
 * @brief 订单记录
 *  
 **/
class Hkzb_Ds_Fudao_Advanced_TradeRecord
{
    //状态
    const STATUS_TOPAY  = 0;
    const STATUS_PAYED  = 1;
    const STATUS_REFUND = 2;
    const STATUS_FORZEN = 3;
    const STATUS_FINISH = 4;

    //实物退款类型
    const GIFT_PRICE_ZERO   = 1; //实物0押金（不论是否寄回）
    const GIFT_PRICE_NORMAL = 2; //实物正常抵押（一经发出概不退回）

    /**
     * 获取订单信息
     * @param integer $orderId
     * @param array   $options 取值：refundDetail，consumeDetail
     * <pre>
     * array(
     *     'purcharseOrderId' => 12345,
     *     'type' => 1,
     *     'createTime' => 111111,
     *     'subOrderList' => array(
     *         '334' => array(
     *             'orderId' => 334,
     *             'courseId' => 554,
     *             'teacherUid' => 123456,
     *             'assistantUid' => 33445,
     *             'status' => 2,
     *             'sendStatus' => 1,
     *             'originalPrice' => 1,
     *             'payment' => 222,
     *             'extData' => array(),
     *             'address' => array(),
     *             'express' => array(),
     *             'course' => array(...),
     *             'refundDetail' => array(...), //已退费的订单有用
     *             'consumeDetail' => array(....), //消耗详情，退款使用
     *         ),
     *         ...
     *     ),
     *     'boundList' => array(
     *         334 => array(234, 445), //orderId对应关系
     *         442 => array(123),
     *     ),
     * )
     * </pre>
     */
    public static function getTradeRecord($orderId, $options = array(), $needTrans = false)
    {
        if ($needTrans) {
            $_db = Hk_Service_Db::getDB('fudao/zyb_fudao');
            $_db->startTransaction();
        }
        $objTradeRecord = new Hkzb_Ds_Fudao_TradeRecord();
        $tradeRecord    = $objTradeRecord->getTradeRecord($orderId);
        if (empty($tradeRecord)) {
            if ($needTrans) {
                $_db->rollback();
            }

            return $tradeRecord;
        }
        $purchaseTradeRecord = array();
        if (isset($tradeRecord['extData']['purchaseOrderId'])) {
            //$type                = Hkzb_Ds_Fudao_TradeRecord::TYPE_SUB;
            $type                = $tradeRecord['type'];
            $purchaseOrderId     = $tradeRecord['extData']['purchaseOrderId'];
            $purchaseTradeRecord = $objTradeRecord->getTradeRecord($purchaseOrderId);
            if (empty($purchaseTradeRecord)) {
                if ($needTrans) {
                    $_db->rollback();
                }

                return false;
            }
            if ($type == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                //因组合商品会拆分成多个子订单存储，当查询到的是预约订单时，这里遍历返回当前组合商品的全部预约订单
                $skuId        = $tradeRecord['extData']['skuId'];
                $subOrderInfo = array();
                if (isset($purchaseTradeRecord['extData']['consistOf'])) {
                    $subList = $objTradeRecord->getTradeRecordArr($purchaseTradeRecord['extData']['consistOf']);
                    if (empty($subList)) {
                        if ($needTrans) {
                            $_db->rollback();
                        }

                        return false;
                    }
                    foreach ($subList as $subItem) {
                        if ($subItem['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                            if ($skuId == $subItem['extData']['skuId']) {
                                $subOrderInfo[] = $subItem;
                            }
                        }
                    }
                }
            } else {
                $subOrderInfo = array($tradeRecord);
            }
        } elseif (isset($tradeRecord['extData']['consistOf'])) {
            $purchaseTradeRecord = $tradeRecord;
            $type                = $tradeRecord['type'];
            //$type                = Hkzb_Ds_Fudao_TradeRecord::TYPE_PACK;
            $purchaseOrderId = $orderId;
            $subOrderInfo    = $objTradeRecord->getTradeRecordArr($tradeRecord['extData']['consistOf']);
            if (empty($subOrderInfo)) {
                if ($needTrans) {
                    $_db->rollback();
                }

                return false;
            }
        } else {
            $purchaseTradeRecord = $tradeRecord;
            $type                = Hkzb_Ds_Fudao_TradeRecord::TYPE_NORMAL;
            $purchaseOrderId     = $orderId;
            $subOrderInfo        = array($tradeRecord);
        }

        $arrCourse = array();
        $courseIds = array();
        foreach ($subOrderInfo as $v) {
            $courseId             = $v['courseId'];
            $orderId              = $v['orderId'];
            $arrCourse[$courseId] = $orderId;
            $courseIds[]          = $v['courseId'];
        }
        $boundListRaw = $tradeRecord['extData']['boundList'];
        if (empty($boundListRaw)) {
            $boundList = array();
        } else {
            $boundList = array();
            foreach ($boundListRaw as $bound) {
                $boundOrder      = array_shift($bound);
                $boundOrderId    = $arrCourse[$boundOrder['courseId']];
                $boundedOrderIds = array();
                foreach ($bound as $b) {
                    if ($arrCourse[$b['courseId']]) {
                        $boundedOrderIds[] = $arrCourse[$b['courseId']];
                    }
                }
                if (!empty($boundedOrderIds)) {
                    $boundList[$boundOrderId] = $boundedOrderIds;
                }
            }
        }
        //获取本次交易赠品信息
        $giftRefundType = self::getGiftRefundType($courseIds);
        $purchaseGiftInfo = isset($purchaseTradeRecord['extData']['gift']) ? $purchaseTradeRecord['extData']['gift'] : array();
        $tradeGiftInfo    = array();
        if (!empty($purchaseGiftInfo)) {
            foreach ($purchaseGiftInfo as $giftItem) {
                $giftList                    = $giftItem['giftList'];
                $giftRelatedCourse           = $giftItem['giftRelatedCourse'];
                $giftRelatedCourseSumPayment = 0;
                $giftRelatedCoursePayment    = array();
                foreach ($giftRelatedCourse as $eachGiftRelatedCourse) {
                    foreach ($subOrderInfo as $subOrder) {
                        if ($eachGiftRelatedCourse == $subOrder['courseId']) {
                            $giftRelatedCourseSumPayment                      += $subOrder['payment'];
                            $giftRelatedCoursePayment[$eachGiftRelatedCourse] = $subOrder['payment'];
                        }
                    }
                }
                foreach ($giftList as $gift) {
                    if (isset(Hkzb_Util_Fudao_Gift::$COURSE_GIFT_PRICE[$gift])) {
                        $giftCost = Hkzb_Util_Fudao_Gift::$COURSE_GIFT_PRICE[$gift];
                        $giftName = Hkzb_Util_Fudao_Gift::$COURSE_GIFT_NAME[$gift];
                    } else {
                        $objZbDakInterface = new Zb_Core_Ds_Dak_Interface();
                        $giftSkuInfo       = $objZbDakInterface->getSkuInfoMap($gift, true);
                        if (empty($giftSkuInfo)) {
                            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::GIFT_NOT_EXIST);
                        }
                        $giftCost = $giftSkuInfo['costPrice'];
                        $giftName = $gift;
                    }
                    if ($giftRefundType == self::GIFT_PRICE_ZERO)
                    {
                        $giftCost = 0;
                    }
                    $relatedCourseCnt            = count($giftRelatedCourse);
                    $giftContributionCostRemains = $giftCost;
                    $tmpCnt                      = 0;
                    sort($giftRelatedCourse);//按课程id正序处理分拆逻辑，保证算法稳定性
                    foreach ($giftRelatedCourse as $eachGiftRelatedCourse) {
                        if ($relatedCourseCnt == 1) {
                            $giftContributionCost = $giftCost;
                        } else {
                            ++$tmpCnt;
                            if ($tmpCnt < $relatedCourseCnt) {
                                if ($giftRelatedCourseSumPayment > 0) {
                                    $giftContributionCost = intval($giftCost * $giftRelatedCoursePayment[$eachGiftRelatedCourse] / $giftRelatedCourseSumPayment);
                                } else {
                                    $giftContributionCost = intval($giftCost / $relatedCourseCnt);
                                }
                                $giftContributionCostRemains -= $giftContributionCost;
                            } else {
                                $giftContributionCost = $giftContributionCostRemains;
                            }
                        }
                        $tradeGiftInfo[$eachGiftRelatedCourse][] = array(
                            'giftId'               => $gift,
                            'giftName'             => $giftName,
                            'giftContributionCost' => $giftContributionCost,
                            'giftSendStatus'       => $purchaseTradeRecord['sendStatus'],
                        );
                    }
                }
            }
        }
        $objCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        if ($needTrans) {
            $canSwitch = false;
        } else {
            $canSwitch = true;
        }
        $courseInfoList = $objCourse->getCourseInfoArr($courseIds, array('extTeacher', 'extAssistant', 'extLesson'), $canSwitch);
        if (empty($courseInfoList)) {
            if ($needTrans) {
                $_db->rollback();
            }

            return false;
        }
        $objCourseAttribute  = new Hkzb_Ds_Fudao_CourseAttribute();
        $courseAttributeList = $objCourseAttribute->getCourseAttributeInfoArr($courseIds, array('teachMaterial'));
        if (false === $courseAttributeList) {
            if ($needTrans) {
                $_db->rollback();
            }

            return false;
        }
        if ($needTrans) {
            $_db->commit();
        }
        $objUcloud    = new Hk_Ds_User_Ucloud();
        $userData     = $objUcloud->getUserInfo($tradeRecord['studentUid']);
        $payUserPhone = $userData['phone'];
        $subOrderList = array();
        foreach ($subOrderInfo as $v) {
            $courseId = $v['courseId'];
            $course   = $courseInfoList[$courseId];
            $address  = isset($v['extData']['addressInfo']) ? $v['extData']['addressInfo'] : array();
            $express  = isset($v['extData']['trade']) ? $v['extData']['trade'] : array();
            if (isset($v['extData']['originalPrice'])) {
                $originalPrice = $v['extData']['originalPrice'];
            } elseif ($course['price'] < $v['payment']) {
                $originalPrice = $v['payment'];
            } else {
                $originalPrice = $course['price'];
            }
            $status = $v['status'];
            if ($status == self::STATUS_PAYED) {
                if ($type == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                    //预约订单如果未使用，总是可以退款
                } else {
                    //获取课程结束时间
                    $onlineStop = 0;
                    foreach ($course['extInfo']['extLesson']['allLesson'] as $lesson) {
                        if ($lesson['startTime'] > $onlineStop) {
                            $onlineStop = $lesson['startTime'];
                        }
                    }
                    //课程已下线或者完结了
                    if ($course['status'] != Hkzb_Ds_Fudao_Advanced_Course::STATUS_ONLINE || $onlineStop < time()) {
                        $status = self::STATUS_FINISH;
                    }
                }
            }
            $course['teachMaterial']   = intval($courseAttributeList[$courseId]['teachMaterial']);
            $subOrder                  = $v;
            $subOrder['status']        = $status;
            $subOrder['originalPrice'] = $originalPrice;
            $subOrder['address']       = $address;
            $subOrder['express']       = $express;
            $subOrder['course']        = $course;
            $subOrder['gift']          = isset($tradeGiftInfo[$courseId]) ? $tradeGiftInfo[$courseId] : array();
            if (in_array('refundDetail', $options)) {
                $subOrder['refundDetail'] = self::getRefundDetail($subOrder);
            }
            $subOrderList[$v['orderId']] = $subOrder;
        }
        //如果是预约订单，组合商品必须一起退款，这里合并预约订单中的组合商品子订单,
        $preGroupOrderList           = self::getPreGroupOrderList($subOrderInfo);
        $arrRes                      = $tradeRecord;
        $arrRes['purchaseOrderId']   = $purchaseOrderId;
        $arrRes['type']              = $type;
        $arrRes['subOrderList']      = $subOrderList;
        $arrRes['boundList']         = $boundList;
        $arrRes['preGroupOrderList'] = $preGroupOrderList;
        if (in_array('consumeDetail', $options)) {
            foreach ($arrRes['subOrderList'] as $id => $orderInfo) {
                $arrRes['subOrderList'][$id]['consumeDetail'] = self::getConsumeDetail($orderInfo, $arrRes, $giftRefundType);
            }
        }

        return $arrRes;
    }

    //预约订单中，组合商品的子订单关联关系列表array(orderId1=>array(orderId2))
    public static function getPreGroupOrderList($subOrderInfo)
    {
        $preGroupOrderList = array();
        if (empty($subOrderInfo)) {
            return $preGroupOrderList;
        }
        $skuIdOrderList = array();
        foreach ($subOrderInfo as $subOrder) {
            $type = isset($subOrder['type']) ? $subOrder['type'] : 0;
            //如果不是预约单，过滤
            if ($type != Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                continue;
            }
            $skuId = isset($subOrder['extData']['skuId']) ? $subOrder['extData']['skuId'] : 0;
            if (empty($skuId)) {
                continue;
            }
            $skuIdOrderList[$skuId][] = $subOrder;
        }
        if (empty($skuIdOrderList)) {
            return $preGroupOrderList;
        }
        foreach ($skuIdOrderList as $skuId => $subOrderArr) {
            //如果不是组合商品，干掉
            if (count($subOrderArr) <= 1) {
                continue;
            }
            foreach ($subOrderArr as $subOrder) {
                $orderId             = $subOrder['orderId'];
                $preGroupOrderList[] = $orderId;
            }
        }

        return $preGroupOrderList;
    }

    /**
     * 获取退款基准金额等信息
     * @param array $orderInfo 子订单结构
     * @param array $tradeRecord 支付订单结构
     * @return array
     */
    public static function getOrderRefundBasePaymentInfo($orderInfo, $tradeRecord)
    {
        if (isset($orderInfo['extData']['expendDiscount'])) {
            //有预约单，需要补预约单价格
            $orderInfo['payment'] += $orderInfo['extData']['expendDiscount']['payment'];
        }
        $arrOutput = array(
            'refundBasePayment' => $orderInfo['payment'],
            'oriPayment'        => $orderInfo['payment'],
            'comment'           => '',
        );
        if (isset($orderInfo['extData']['summerMulSubOrderList']) && count($orderInfo['extData']['summerMulSubOrderList']) > 1) {
            $arrOutput = self::getMulSubOrderRefundBasePaymentInfo($orderInfo, $tradeRecord);

            return $arrOutput;
        }
        if (isset($tradeRecord['extData']['boundList']) && !empty($tradeRecord['extData']['boundList'])) {
            $arrOutput = self::getBoundOrderRefundBasePaymentInfo($orderInfo, $tradeRecord);

            return $arrOutput;
        }

        return $arrOutput;
    }

    /**
     * 2018寒春之前"赠品/教材全额退"； 2018寒春之后"赠品/教材全额扣"
     * @return 1:全额退  2:全额扣
     */
    private static function getGiftRefundType($courseIds) 
    {
        return self::GIFT_PRICE_ZERO;
    }

    private static function getBoundOrderRefundBasePaymentInfo($orderInfo, $tradeRecord)
    {
        $arrOutput = array(
            'refundBasePayment' => $orderInfo['payment'],
            'oriPayment'        => $orderInfo['payment'],
            'comment'           => '',
        );

        return $arrOutput;
    }

    private static function getMulSubOrderRefundBasePaymentInfo($orderInfo, $tradeRecord)
    {
        $arrOutput = array(
            'refundBasePayment' => $orderInfo['payment'],
            'oriPayment'        => $orderInfo['payment'],
            'comment'           => '',
        );
        if ($orderInfo['status'] == Hkzb_Ds_Fudao_TradeRecord::STATUS_FORZEN || $orderInfo['status'] == Hkzb_Ds_Fudao_TradeRecord::STATUS_REFUND) {
            if (isset($orderInfo['extData']['refundBasePayment'])) {
                $arrOutput['refundBasePayment'] = $orderInfo['extData']['refundBasePayment'];
            }

            return $arrOutput;
        }
        $refundList           = array();
        $leftList             = array();
        $sumSummerPayment     = 0;
        $sumRefundBasePayment = 0;
        foreach ($orderInfo['extData']['summerMulSubOrderList'] as $courseId => $orderId) {
            $subOrder         = $tradeRecord['subOrderList'][$orderId];
            $sumSummerPayment += $subOrder['payment'];
            if ($subOrder['status'] == Hkzb_Ds_Fudao_TradeRecord::STATUS_FORZEN || $subOrder['status'] == Hkzb_Ds_Fudao_TradeRecord::STATUS_REFUND) {
                $refundList[] = $orderId;
                if (isset($subOrder['extData']['refundBasePayment'])) {
                    $sumRefundBasePayment += $subOrder['extData']['refundBasePayment'];
                } else {
                    $sumRefundBasePayment += $subOrder['payment'];
                }
            } elseif ($orderId == $orderInfo['orderId']) {
                //当前订单不计算
            } else {
                $leftList[] = $orderId;
            }
        }
        $leftSummerPayment = $sumSummerPayment - $sumRefundBasePayment;
        if ($leftSummerPayment < 0) {
            $leftSummerPayment = 0;
        }
        $cnt = count($leftList);
        if ($cnt > 3) {
            $cnt = 3;
        }
        if ($cnt == 0) {
            $arrOutput['refundBasePayment'] = $leftSummerPayment;
            $arrOutput['comment']           = "联合售卖最后一门课退款，退款基准${leftSummerPayment}";
        } else {
            $dicountRatio = $orderInfo['extData']['summerMulSubDiscountRule'][$cnt];
            if ($dicountRatio == 0) { //没有配优惠
                $dicountRatio = 100;
            }
            $leftDiscountPrice = 0;
            foreach ($leftList as $id) {
                $histPrice = intval(round($tradeRecord['subOrderList'][$id]['originalPrice'] * $dicountRatio / 100 / 100) * 100);
                if ($tradeRecord['subOrderList'][$id]['extData']['couponInfo']['couponPrice'] > 0) {
                    $histPrice = $histPrice - $tradeRecord['subOrderList'][$id]['extData']['couponInfo']['couponPrice'];
                    if ($histPrice < 0) {
                        $histPrice = 0;
                    }
                }
                $leftDiscountPrice += $histPrice;
            }
            $arrOutput['refundBasePayment'] = $sumSummerPayment - $sumRefundBasePayment - $leftDiscountPrice;
            if ($arrOutput['refundBasePayment'] < 0) {
                $arrOutput['refundBasePayment'] = 0;
            }
            $refundCnt            = count($refundList);
            $arrOutput['comment'] = "联合售卖总金额${sumSummerPayment}；已退款${refundCnt}门，已退款总基准金额${sumRefundBasePayment}；剩余${cnt}门，折扣为${dicountRatio},优惠价格为${leftDiscountPrice}；故当前退款基准金额为{$arrOutput['refundBasePayment']}";
        }

        return $arrOutput;
    }

    public static function getConsumeDetail($orderInfo, $tradeRecord, $giftRefundType=0)
    {
        if (0 == $giftRefundType) {
            $giftRefundType = self::getGiftRefundType(array($orderInfo['courseId']));
        }
        $refundBasePaymentInfo = self::getOrderRefundBasePaymentInfo($orderInfo, $tradeRecord);
        if ($orderInfo['type'] == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
            //预约单使用后，无法直接退款
            $refundPayment = $refundBasePaymentInfo['refundBasePayment'];
            $arrOutput     = array(
                'orderId'               => $orderInfo['orderId'],
                'refundBasePayment'     => $refundBasePaymentInfo['refundBasePayment'],
                'oriPayment'            => $refundBasePaymentInfo['oriPayment'],
                'comment'               => $refundBasePaymentInfo['comment'],
                'lessonCnt'             => 0,
                'lessonUsedCnt'         => 0,
                'lessonLeftCnt'         => 0,
                'coreLessonCnt'         => 0,
                'coreLessonUsedCnt'     => 0,
                'coreLessonLeftCnt'     => 0,
                'otherLessonCnt'        => 0,
                'otherLessonUsedCnt'    => 0,
                'otherLessonLeftCnt'    => 0,
                'calLessonCnt'          => 0,
                'calLessonUsedCnt'      => 0,
                'calLessonLeftCnt'      => 0,
                'teachMaterial'         => 0,
                'totalRefundFee'        => $refundPayment,
                'materialFee'           => 0,
                'refundPayment'         => $refundPayment,
                'materialRefundPayment' => 0,
                'sendStatus'            => 0,
                'canShowSendStatus'     => 0,
                'summerCnt'             => 0,
                'summerDiscountRatio'   => 0,
                'giftRefundInfo'        => array(),
            );
        } else {
            $courseInfo         = $orderInfo['course'];
            $subCourse          = $courseInfo['extInfo']['extLesson']['subCourse'];
            $allLesson          = $courseInfo['extInfo']['extLesson']['allLesson'];
            $lessonCnt          = 0; //章节总数
            $lessonUsedCnt      = 0; //消耗
            $lessonLeftCnt      = 0; //剩余
            $coreLessonCnt      = 0; //核心课总数
            $coreLessonUsedCnt  = 0;
            $coreLessonLeftCnt  = 0;
            $otherLessonCnt     = 0; //提升课总数
            $otherLessonUsedCnt = 0;
            $otherLessonLeftCnt = 0;
            $calLessonCnt       = 0; //退款需要考虑的课节总数
            $calLessonUsedCnt   = 0;
            $calLessonLeftCnt   = 0;
            foreach ($subCourse as $subCourseInfo) {
                $weight     = $subCourseInfo['weight'];
                $lessonList = $subCourseInfo['lessonIdList'];
                foreach ($lessonList as $lessonId) {
                    $lesson = $allLesson[$lessonId];
                    if (isset($lesson['extData']['stopReason']) && $lesson['extData']['stopReason'] <> '') {
                        continue;
                    }
                    if ($lesson['status'] == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                        if ($weight == 1) {
                            $coreLessonLeftCnt++;
                        } else {
                            $otherLessonLeftCnt++;
                        }
                    }
                    if ($weight == 1) {
                        $coreLessonCnt++;
                    } else {
                        $otherLessonCnt++;
                    }
                }
            }
            $lessonCnt          = $coreLessonCnt + $otherLessonCnt;
            $coreLessonUsedCnt  = $coreLessonCnt - $coreLessonLeftCnt;
            $otherLessonUsedCnt = $otherLessonCnt - $otherLessonLeftCnt;
            $lessonLeftCnt      = $coreLessonLeftCnt + $otherLessonLeftCnt;
            $lessonUsedCnt      = $lessonCnt - $lessonLeftCnt;
            //$tradePayment   = $orderInfo['payment'];
            $tradePayment      = $refundBasePaymentInfo['refundBasePayment'];
            $canShowSendStatus = 0;
            if (date('Y', $courseInfo['onlineStart']) == '2016' && $courseInfo['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG && $courseInfo['learnSeason'] == '秋_1') {
                $calLessonCnt     = $lessonCnt;
                $calLessonLeftCnt = intval($lessonLeftCnt);
                $calLessonUsedCnt = $calLessonCnt - $calLessonLeftCnt;
                $materialFee      = 0;
            } else {
                $calLessonCnt     = intval($coreLessonCnt);
                $calLessonLeftCnt = intval($coreLessonLeftCnt);
                $calLessonUsedCnt = $calLessonCnt - $calLessonLeftCnt;
                if ($courseInfo['teachMaterial'] && $orderInfo['sendStatus']) {
                    $materialFee       = 3000;
                    $canShowSendStatus = 1;
                } else {
                    $materialFee = 0;
                }

            }
            if ($giftRefundType == self::GIFT_PRICE_ZERO) 
            {
                $materialFee = 0;
            }
            //处理赠品信息
            $giftFee = array();
            $giftName = array();
            if (!empty($orderInfo['gift'])) {
                foreach ($orderInfo['gift'] as $giftInfo) {
                    $giftSendStatus = $giftInfo['giftSendStatus'];
                    if (!$giftSendStatus) {
                        continue;
                    }
                    $giftId               = $giftInfo['giftId'];
                    $giftContributionCost = $giftInfo['giftContributionCost'];
                    $giftFee[$giftId]     = $giftContributionCost;
                    $giftName[$giftId]    = $giftInfo['giftName'];
                }
            }
            if ($calLessonCnt > 0) {
                $totalRefundFee = intval($tradePayment * $calLessonLeftCnt / $calLessonCnt);
            } else {
                $totalRefundFee = 0;
            }
            //退款金额归一化
            //学币退款需精确到角，非原价退费或学币支付时，计算退款金额舍去分位的值精确到角
            $payChannel = isset($orderInfo['extData']['payChannel']) ? $orderInfo['extData']['payChannel'] : null;
            if ($totalRefundFee != $tradePayment || $payChannel == 9) {
                $totalRefundFee = Hk_Service_Pay::priceNorm($totalRefundFee, 1);
            }
            if ($lessonUsedCnt == 0 && $materialFee > 0) {
                $materialRefundPayment = $materialFee; //教材寄回退款金额
            } else {
                $materialRefundPayment = 0;
            }
            //课节去除教材工本费退款金额
            $refundPayment = $totalRefundFee - $materialFee;
            if ($refundPayment < 0) {
                $materialFee           = $totalRefundFee;
                $refundPayment         = 0;
                if ($materialRefundPayment > 0){
                    $materialRefundPayment = $totalRefundFee; //这种情况，教材费退款不足30元
                }
            }
            //赠品费用
            $giftRefundInfo = array();
            if (!empty($giftFee)) {
                foreach ($giftFee as $giftId => $giftCost) {
                    $tmpRefundPayment = $refundPayment;
                    $refundPayment    = $refundPayment - $giftCost;
                    if ($refundPayment < 0) {
                        $refundPayment    = 0;
                        $giftRefundInfo[] = array(
                            'giftId'            => $giftId,
                            'giftName'          => $giftName[$giftId],
                            'giftRefundPayment' => $tmpRefundPayment,
                        );
                    } else {
                        $giftRefundInfo[] = array(
                            'giftId'            => $giftId,
                            'giftName'          => $giftName[$giftId],
                            'giftRefundPayment' => $giftCost,
                        );
                    }
                }
            }
            //暑假班几节课一起报
            if (isset($orderInfo['extData']['summerMulSubOrderList'])) {
                $summerCnt = count($orderInfo['extData']['summerMulSubOrderList']);
            } else {
                $summerCnt = 0;
            }
            if ($summerCnt > 0) {
                if ($summerCnt > 3) {
                    $cnt = 3;
                } else {
                    $cnt = $summerCnt;
                }
                $summerDiscountRatio = intval($orderInfo['extData']['summerMulSubDiscountRule'][$cnt]);
            } else {
                $summerDiscountRatio = 0;
            }

            $arrOutput = array(
                'orderId'               => $orderInfo['orderId'],
                'refundBasePayment'     => $refundBasePaymentInfo['refundBasePayment'],
                'oriPayment'            => $refundBasePaymentInfo['oriPayment'],
                'comment'               => $refundBasePaymentInfo['comment'],
                //'giftFee'               => $refundBasePaymentInfo['giftFee'],
                'lessonCnt'             => $lessonCnt,
                'lessonUsedCnt'         => $lessonUsedCnt,
                'lessonLeftCnt'         => $lessonLeftCnt,
                'coreLessonCnt'         => $coreLessonCnt,
                'coreLessonUsedCnt'     => $coreLessonUsedCnt,
                'coreLessonLeftCnt'     => $coreLessonLeftCnt,
                'otherLessonCnt'        => $otherLessonCnt,
                'otherLessonUsedCnt'    => $otherLessonUsedCnt,
                'otherLessonLeftCnt'    => $otherLessonLeftCnt,
                'calLessonCnt'          => $calLessonCnt,
                'calLessonUsedCnt'      => $calLessonUsedCnt,
                'calLessonLeftCnt'      => $calLessonLeftCnt,
                'teachMaterial'         => $courseInfo['teachMaterial'],
                'totalRefundFee'        => $totalRefundFee,
                'materialFee'           => $materialFee,
                'refundPayment'         => $refundPayment, //根据退款规则，课节除去教材退款金额
                'materialRefundPayment' => $materialRefundPayment, //教材如果回寄，退款金额
                'sendStatus'            => $orderInfo['sendStatus'],
                'canShowSendStatus'     => $canShowSendStatus,
                'summerCnt'             => $summerCnt,
                'summerDiscountRatio'   => $summerDiscountRatio,
                'giftRefundInfo'        => $giftRefundInfo,
            );
        }

        return $arrOutput;
    }

    public static function getRefundDetail($tradeRecord)
    {
        $arrOutput = array(
            'courseRefundFee'      => 0,
            'courseRefundStatus'   => 0,
            'materialFee'          => 0,
            'materialRefundStatus' => 0,
            'materialSendStatus'   => 0,
            'refundReqTime'        => 0,
        );

        if (in_array($tradeRecord['status'], array(self::STATUS_FORZEN, self::STATUS_REFUND))) {
            //0元退款情况
            $isZeroRefund = false;
            if ($tradeRecord['payment'] == 0) {
                $isZeroRefund = true;
            }
            $totalRefundFee = 0;

            if (isset($tradeRecord['extData']['refundPayment'])) {
                $totalRefundFee = $tradeRecord['extData']['refundPayment'] + $tradeRecord['extData']['materialRefundPayment'];
            }
            if (isset($tradeRecord['gift']) && !empty($tradeRecord['gift'])) {
                foreach ($tradeRecord['gift'] as $giftRefundItem) {
                    $giftContributionCost = $giftRefundItem['giftContributionCost'];
                    $totalRefundFee       += $giftContributionCost;
                }
            }
            if ((isset($tradeRecord['extData']['refundPayment']) || isset($tradeRecord['gift'])) && $totalRefundFee == 0) {
                $isZeroRefund = true;
            }
            if (isset($tradeRecord['extData']['frozenPrice']) && $tradeRecord['extData']['frozenPrice'] == 0) {
                $isZeroRefund = true;
            }

            if ($isZeroRefund) {
                $arrOutput['refundReqTime'] = intval($tradeRecord['extData']['frozenTime']);

                return $arrOutput;
            }

            if ($tradeRecord['status'] == self::STATUS_FORZEN) {
                $status = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_DOING;
            } else {
                $status = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_DONE;
            }

            //重复购买自动退款
            if ($tradeRecord['extData']['frozenSource'] == 'autorefund') {
                //获取退款时间
                $refundTime = isset($tradeRecord['extData']['frozenTime']) ? $tradeRecord['extData']['frozenTime'] : 0;
                if(empty($refundTime)){
                    $refundTime = isset($tradeRecord['extData']['refundTime']) ? $tradeRecord['extData']['refundTime'] : 0;
                }

                $arrOutput = array(
                    'courseRefundFee'      => $tradeRecord['payment'],
                    'courseRefundStatus'   => $status,
                    'materialFee'          => 0,
                    'materialRefundStatus' => Hkzb_Ds_Fudao_Advanced_Refund::REFUND_NO,
                    'materialSendStatus'   => 0,
                    'refundReqTime'        => $refundTime,
                );

                return $arrOutput;
            }
            //关课等脚本
            if ($tradeRecord['extData']['frozenSource'] == 'platmis') {
                $arrOutput = array(
                    'courseRefundFee'      => $tradeRecord['extData']['frozenPrice'],
                    'courseRefundStatus'   => $status,
                    'materialFee'          => 0,
                    'materialRefundStatus' => Hkzb_Ds_Fudao_Advanced_Refund::REFUND_NO,
                    'materialSendStatus'   => 0,
                    'refundReqTime'        => $tradeRecord['extData']['frozenTime'],
                );

                return $arrOutput;
            }
            //老callcenter退款
            if ($tradeRecord['extData']['frozen'] == 'callcenter') {
                $arrOutput = array(
                    'courseRefundFee'      => $tradeRecord['extData']['frozenPrice'],
                    'courseRefundStatus'   => $status,
                    'materialFee'          => 0,
                    'materialRefundStatus' => Hkzb_Ds_Fudao_Advanced_Refund::REFUND_NO,
                    'materialSendStatus'   => 0,
                    'refundReqTime'        => $tradeRecord['extData']['frozenTime'],
                );

                return $arrOutput;
            }

            //从gnmis获取退款信息
            $refundDetail = Hkzb_Ds_Fudao_Advanced_Refund::getRefundDetail($tradeRecord['orderId']);
         
            if (empty($refundDetail)) {
                //gnmis服务无此记录或请求失败，直接从tradeRecord记录中取
                if ($tradeRecord['status'] == self::STATUS_FORZEN) {
                    $status = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_DOING;
                } else {
                    $status = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_DONE;
                }
                if ($tradeRecord['extData']['materialReturn'] == 'no') {
                    $materialRefundStatus = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_FAIL;
                    $materialSendStatus   = 1;
                } elseif ($tradeRecord['extData']['materialRefundPayment'] > 0) {
                    $materialRefundStatus = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_DOING;
                    $materialSendStatus   = 1;
                } else {
                    $materialRefundStatus = Hkzb_Ds_Fudao_Advanced_Refund::REFUND_NO;
                    $materialSendStatus   = $tradeRecord['sendStatus'];
                }
                $giftRefundInfo = isset($refundDetail['giftRefundInfo']) ? $refundDetail['giftRefundInfo'] : array();
                $arrOutput      = array(
                    'courseRefundFee'      => intval($tradeRecord['extData']['refundPayment']),
                    'courseRefundStatus'   => $status,
                    'materialFee'          => intval($tradeRecord['extData']['materialRefundPayment']),
                    'materialRefundStatus' => $materialRefundStatus,
                    'materialSendStatus'   => $materialSendStatus,
                    'refundReqTime'        => intval($tradeRecord['extData']['frozenTime']),
                    'giftRefundInfo'       => $giftRefundInfo,
                );

                return $arrOutput;
            } else {
                return $refundDetail;
            }
        } else {
            return $arrOutput;
        }
    }

    /**
     * 获取预约订单信息
     * @param $intStudentUid
     * @param $intSkuId
     * @return array|bool
     */
    public static function getUserPrePayOrder($intStudentUid, $intSkuId)
    {
        $intStudentUid = intval($intStudentUid);
        $intSkuId      = intval($intSkuId);
        if ($intStudentUid <= 0 || $intSkuId <= 0) {
            Bd_Log::warning("[param error], studentUid:$intStudentUid, skuId:$intSkuId", Hk_Util_ExceptionCodes::PARAM_ERROR);

            return false;
        }
        $objAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        $skuInfo           = $objAdvancedCourse->getCourseInfo($intSkuId, array(), true);
        if ($skuInfo === false) {
            Bd_Log::warning("[db error], get sku info fail, skuId:$intSkuId");

            return false;
        }
        if (empty($skuInfo)) {
            Bd_Log::warning("sku not exist, skuId:$intSkuId", Hk_Util_ExceptionCodes::SKU_NOT_EXIST);

            return false;
        }
        $courseIdList          = $skuInfo['courseIdList'];
        $objUserTradeRecord    = new Hkzb_Ds_Fudao_UserTradeRecord();
        $userTradeRecordIdList = $objUserTradeRecord->getOrderIdListByStudentUidCourseIds($intStudentUid, $courseIdList, Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY);
        if ($userTradeRecordIdList === false) {
            Bd_Log::warning("[db error], get user trade record fail, studentUid:$intStudentUid, courseIdList:" . json_encode($courseIdList), Hk_Util_ExceptionCodes::DB_ERROR);

            return false;
        }
        if (empty($userTradeRecordIdList)) {
            return array();
        }
        $objTradeRecord  = new Hkzb_Ds_Fudao_TradeRecord();
        $tradeRecordInfo = $objTradeRecord->getTradeRecordArr($userTradeRecordIdList, array());
        if ($tradeRecordInfo === false) {
            Bd_Log::warning("[db error], get trade record fail, tradeRecordIdList:" . json_encode($userTradeRecordIdList), Hk_Util_ExceptionCodes::DB_ERROR);

            return false;
        }
        if (empty($tradeRecordInfo)) {
            return array();
        }
        $prePayOrderInfo = array();
        foreach ($tradeRecordInfo as $tradeInfo) {
            if ($tradeInfo['status'] != Hkzb_Ds_Fudao_TradeRecord::STATUS_PAYED && $tradeInfo['status'] != Hkzb_Ds_Fudao_TradeRecord::STATUS_FINISH) {
                continue;
            }
            if ($tradeInfo['type'] != Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
                continue;
            }
            $tradeSkuId                                                        = $tradeInfo['extData']['skuId'];
            $tradeCourseId                                                     = $tradeInfo['courseId'];
            $tradePayment                                                      = $tradeInfo['payment'];
            $tradeExpendPrice                                                  = $tradeInfo['extData']['expendPrice'];
            $prePayOrderInfo[$tradeSkuId]['prePayOrderList'][$tradeCourseId][] = array(
                'tradeRecordId' => $tradeInfo['orderId'],
                'skuId'         => $tradeSkuId,
                'courseId'      => $tradeCourseId,
                'payment'       => $tradePayment,
                'expendPrice'   => $tradeExpendPrice,
            );
            $prePayOrderInfo[$tradeSkuId]['payment']                           += $tradePayment;
            $prePayOrderInfo[$tradeSkuId]['expendPrice']                       += $tradeExpendPrice;
        }

        Bd_Log::notice('[prePayOrderInfo]='.json_encode($prePayOrderInfo));
        if (empty($prePayOrderInfo)) {
            return array();
        }

        $skuPrePayInfo = isset($prePayOrderInfo[$intSkuId]) ? $prePayOrderInfo[$intSkuId] : array();
        Bd_Log::notice('[skuPrePayInfo]='.json_encode($skuPrePayInfo).'-- skuId='.$intSkuId);
        if (empty($skuPrePayInfo)) {
            return array();
        }
        $arrOutput = array(
            'studentUid'      => $intStudentUid,
            'skuId'           => $intSkuId,
            'payment'         => $skuPrePayInfo['payment'],
            'expendPrice'     => $skuPrePayInfo['expendPrice'],
            'courseIdList'    => $courseIdList,
            'prePayOrderInfo' => array(),
        );
        foreach ($courseIdList as $courseId) {
            if (!isset($skuPrePayInfo['prePayOrderList'][$courseId])) {
                Bd_Log::notice('[prePayOrderList empty]');
                return array();
            }
            if (count($skuPrePayInfo['prePayOrderList'][$courseId]) > 1) {
                Bd_Log::notice('[prePayOrderList gt 1]');
                return array();
            }
            $arrOutput['prePayOrderInfo'][$courseId] = $skuPrePayInfo['prePayOrderList'][$courseId][0];
        }

        return $arrOutput;
    }
    
    /**
     * 根据订单号获取对应的实物数据
     * @param $intOrderId
     * @return array entity data
     * array(
     *      'orderId' => array(
     *          array(
     *             'orderId'        => 12345,
     *             'MaterialName'   => 'aaaaaaaaa', 
     *             'status'         => 1, 1 未寄送；2 已寄送暂扣待寄回； 3 已寄送扣除； 4 用户已寄回；
     *             'orderStatus'    => 1, 1 未结课    2 已结课
     *          )
     *          ......
     *      )
     *      'entity' => array(
     *          array(
     *              'giftId'         => '精品资料盒（高中）',
     *              'status'         => 1, 1 未寄送；2 已寄送暂扣待寄回； 3 已寄送扣除； 4 用户已寄回；
     *          )
     *          ......
     *      )
     * )
     */
    public static function getOrderEntityDetail($OrderId)
    {
        $objTradeRecord = new Hkzb_Ds_Fudao_TradeRecord();
        $tradeRecord    = $objTradeRecord->getTradeRecord($OrderId);
        $studentUid     = $tradeRecord['studentUid'];
        if (empty($tradeRecord)){
            return false;
        }
        $purchaseTradeRecord = array();
        $purchaseOrderId     = $tradeRecord['extData']['purchaseOrderId'];
        $purchaseTradeRecord = $objTradeRecord->getTradeRecord($purchaseOrderId);
        if (empty($purchaseTradeRecord)) {
            return false;
        }
        //获取联报数据
        $consistOf = $purchaseTradeRecord['extData']['consistOf'];
        $subList = $objTradeRecord->getTradeRecordArr($consistOf);
        foreach ($subList as $tradeInfo){
            $courseId = $tradeInfo['courseId'];
            $orderId  = $tradeInfo['orderId'];
            $arrCourseIds[$orderId] = $courseId;
            $courseIdMap[$courseId] = $orderId;
        }
        $boundListRaw = $purchaseTradeRecord['extData']['boundList'];
        if ($boundListRaw){
            foreach ($boundListRaw as $bound){
                $connArr = array();
                foreach ($bound as $row){
                    $connArr[] = $courseIdMap[$row['courseId']];
                }
                $boundOrderId[] = $connArr;
            }
            foreach ($boundOrderId as $bound){
                if (in_array($OrderId, $bound)){
                    $connectOrder = $bound;
                }
            }
        }
        if (empty($connectOrder)){
            $connectOrder[] = $OrderId;
        }

        $orderDetail = self::getTradeRecord($purchaseOrderId,array('consumeDetail'));
        //赠品数据
        $gift = $purchaseTradeRecord['extData']['gift'];
        if ($gift){
            foreach ($gift as $gv){
                $giftList                    = $gv['giftList'][0];
                $giftRelatedCourse           = $gv['giftRelatedCourse'];
                $isRightGift                 = true;
                foreach ($giftRelatedCourse as $course){
                    if (!in_array($courseIdMap[$course], $connectOrder)){
                        $isRightGift = false;
                    }
                }
                if ($isRightGift){
                    $hasSendReturn = false;
                    foreach ($connectOrder as $Id){
                        //已退款赠品展示
                        if ($subList[$Id]['status'] > 1){
                            $objGuoyuanChargeRefund = new Hkzb_Ds_Gnmis_GuoyuanChargeRefund();
                            $refundDetail = $objGuoyuanChargeRefund->getGuoyuanRefundByUidOrderId($studentUid, $Id);
                            foreach ($refundDetail as $refundInfo){
                                if ($refundInfo['sign'] == Hkzb_Ds_Gnmis_GuoyuanChargeRefund::SIGN_ENTITY){
                                    if ($refundInfo['extBit'] == Hkzb_Ds_Gnmis_GuoyuanChargeRefund::MATERIAL_STATUS_BAKC_YES){
                                        $hasSendReturn = true;
                                    }
                                }
                            }
                        }
                    }
                    //已退款寄回状态
                    if ($hasSendReturn && $purchaseTradeRecord['sendStatus'] == 1){
                        $outGift[] = array(
                            'giftId'        => Zb_Const_Sku::$materialMap[$giftList]['skuName'],
                            'status'        => 4,
                        );
                    } else {
                        //未退款寄回状态
                        $outGift[] = array(
                            'giftId'        => Zb_Const_Sku::$materialMap[$giftList]['skuName'],
                            'status'        => ($purchaseTradeRecord['sendStatus'] == 1) ? 2 : 1,
                        );
                    }
                }
            }
        }
        
        //教材数据
        foreach ($connectOrder as $Id){
            $objCourse = new Hkzb_Ds_Fudao_Course();
            $courseInfo = $objCourse->getCourseInfo($arrCourseIds[$Id]);
            $objCourseAttribute  = new Hkzb_Ds_Fudao_CourseAttribute();
            $courseAttributeList = $objCourseAttribute->getCourseAttributeInfo($arrCourseIds[$Id], array('teachMaterial'));
            $objGuoyuanChargeRefund = new Hkzb_Ds_Gnmis_GuoyuanChargeRefund();
            $refundDetail = $objGuoyuanChargeRefund->getGuoyuanRefundByUidOrderId($studentUid, $Id);
            //未退款数据展示
            if (empty($refundDetail)){
                $hasEndClass = 0;
                $consumeDetail = $orderDetail['subOrderList'][$Id]['consumeDetail'];
                //已结课
                if ($consumeDetail['coreLessonCnt'] == $consumeDetail['coreLessonUsedCnt']){
                    $hasEndClass = 1;
                }
                if ($subList[$Id]['sendStatus'] == 1 && $courseAttributeList['teachMaterial'] == 1){
                    $out[] = array(
                        'orderId'      => $Id,
                        'MaterialName' => $courseInfo['courseName']."教材",
                        'status'       => ($consumeDetail['lessonUsedCnt'] > 0) ? 3: 2,
                        'orderStatus'  => ($hasEndClass == 1) ? 2 : 1,
                    );
                } else {
                    $out[] = array(
                        'orderId'      => $Id,
                        'MaterialName' => $courseInfo['courseName']."教材",
                        'status'       => 1,
                        'orderStatus'  => ($hasEndClass == 1) ? 2 : 1,
                    );
                }
            } else {
                //已退款数据展示
                foreach ($refundDetail as $refundInfo){
                    if ($refundInfo['sign'] == Hkzb_Ds_Gnmis_GuoyuanChargeRefund::SIGN_MATERIAL){
                        $out[] = array(
                            'orderId'      => $Id,
                            'MaterialName' => $courseInfo['courseName']."教材",
                            'status'       => ($refundInfo['extBit'] == Hkzb_Ds_Gnmis_GuoyuanChargeRefund::MATERIAL_STATUS_BAKC_YES) ? 4: 2,
                        );
                    } else {
                        $out[] = array(
                            'orderId'      => $Id,
                            'MaterialName' => $courseInfo['courseName']."教材",
                            'status'       => 3,
                        );
                    }
                }
            }
        }
        
        $arrOutput = array(
            'orderId'   => $out,
            'entity'    => $outGift,
        );
        
        return $arrOutput;
    }
}
