<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      SingleLive.php
 * @author:        <EMAIL>
 * @desc:          单点直播 
 * @create:        2017-10-28 12:45:29
 * @last modified: 2017-10-28 12:45:29
 */
class Hkzb_Ds_Fudao_Advanced_SingleLive {

    /**
     * 生成毫秒时间戳
     *
     */
    public function getMillisecond() {
        list($t1, $t2) = explode(' ', microtime());
        return (int)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }

    public function pushMessageToStudent($studentUid, $lessonId, $milliSecond){
		$msgBody = array(
			'lessonId'      => $lessonId,
            'milliSecond'   => $milliSecond,
		);
		$objPush = new Hkzb_Ds_Fudao_Push();
		$msgId   = $objPush->fetchMsgid();
		$msgId   = intval($msgId['data']['msg_id']);
		$ret     = $objPush->pushMsgByUid($studentUid, $studentUid, Hkzb_Ds_Fudao_Push::MSG_TYPE_SINGLE_LIVE, $msgId, $msgBody, $lessonId);  
        if( ! empty($ret) && $ret['err_no'] != 0){
            Bd_Log::warning('push single live fail ,studentUid:' . $studentUid . ',params:' . json_encode($msgBody));
        }
    }

}
