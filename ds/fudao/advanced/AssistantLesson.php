<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   AssistantLesson.php
 * <AUTHOR>
 * @date   2017/3/13 14:13:18
 * @brief  辅导老师-章节
 * @note   在辅导老师以子课维度进行排课的背景下，辅导老师暂对应章节
 *
 **/
class Hkzb_Ds_Fudao_Advanced_AssistantLesson {
    
    /**
     * 获取辅导老师uid列表
     *
     * @param  int   $lessonId    章节id
     * @param  int   $classId     班级id
     * @return array|false
     */
    public function getAssistantUidList($lessonId, $classId = 0) {
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $assistantUidList = $objTeacherCourse->getAssistantUidList($subCourseId, $classId);
        if(false === $assistantUidList) {
            Bd_Log::warning("Error:[getAssistantUidList error], Detail:[subCourseId:$subCourseId classId:$classId]");
            return false;
        }

        return $assistantUidList;
    }
    
    /**
     * 判定辅导老师对章节权限
     *
     * @param  int   $assistantUid  辅导老师uid
     * @param  int   $lessonId      章节id
     * @param  int   $classId       班级id
     * @return array|false
     */
    public function checkAssistantLesson($assistantUid, $lessonId, $classId = 0) {
        if($assistantUid <= 0 || $lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid lessonId:$lessonId]");
            return false;
        }

        $assistantUidList = $this->getAssistantUidList($lessonId, $classId);
        if(empty($assistantUidList)) {
            Bd_Log::warning("Error:[getAssistantUidList error], Detail:[lessonId:$lessonId]");
            return false;
        }

        if(!in_array($assistantUid, $assistantUidList)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取班级id列表
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */
    public function getClassIdList($lessonId) {
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $classIdList = $objTeacherCourse->getClassIdList($subCourseId);
        if(false === $classIdList) {
            Bd_Log::warning("Error:[classIdList error], Detail:[subCourseId:$subCourseId]");
            return false;
        }

        return $classIdList;
    }
    /**
     * 获取班级id列表(新版，支持小班情况)
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */
    public function getClassIdListNew($lessonId) {
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $classIdList = $objTeacherCourse->getClassIdListNew($subCourseId);
        if(false === $classIdList) {
            Bd_Log::warning("Error:[classIdList error], Detail:[subCourseId:$subCourseId]");
            return false;
        }

        return $classIdList;
    }
    /**
     * 获取一个章节一个辅导老师下所有的学生
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */   
    public function getAssistantStudent($lessonId,$classId){
        $arrOutput = array();
        if($lessonId <= 0 || $classId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId classId:$classId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId','startTime'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }
        $subCourseId = intval($lessonInfo['courseId']);
        //获取该课下所有有权限的学生
        $objDsStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
        $studentCnt         = $objDsStudentCourse->getStudentCnt($subCourseId,$classId);
        if(intval($studentCnt) <= 0){
            Bd_Log::warning("Error:[get studentList 0], Detail:[lessonId:$lessonId classId:$classId]");
            return $arrOutput;
        }
        $studentList = $objDsStudentCourse->getStudentList($subCourseId,$classId,array(),0,$studentCnt); 
        if(empty($studentList)){
            Bd_Log::warning("Error:[get studentList empty], Detail:[lessonId:$lessonId classId:$classId]");
            return $arrOutput;
        }
        $objDsLessonDetail = new Hkzb_Ds_Fudao_LessonDetail();
        $lessonDetailList  = $objDsLessonDetail->getLessonDetailStudentList($lessonId,$classId);
        foreach($studentList as $student){
            $studentUid = intval($student['studentUid']);
            $lessonDetail = isset($lessonDetailList[$studentUid]) ? $lessonDetailList[$studentUid] : array(); 
            $lessonDetail['attend'] = 0;
            if(intval($lessonDetail['online']) == 1){
                $lessonDetail['attend'] = 1;
            }else if(intval($lessonDetail['attendTime']) >= intval($lessonInfo['startTime'])){
                $lessonDetail['attend'] = 1;
            }else if(intval($lessonDetail['exitTime']) >= intval($lessonInfo['startTime'])){
                $lessonDetail['attend'] = 1;
            }
            $arrOutput[$studentUid] = $lessonDetail;
        }
        return $arrOutput;
    }

    /**
     * （新）获取一个章节一个辅导老师下所有的学生
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */   
    public function getNewAssistantStudent($lessonId,$classId){
        $arrOutput = array();
        if($lessonId <= 0 || $classId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId classId:$classId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId','startTime'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }
        $subCourseId = intval($lessonInfo['courseId']);
        //获取该课下所有有权限的学生
        $objDsStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
        $studentCnt         = $objDsStudentCourse->getStudentCnt($subCourseId,$classId);
        if(intval($studentCnt) <= 0){
            Bd_Log::warning("Error:[get studentList 0], Detail:[lessonId:$lessonId classId:$classId]");
            return $arrOutput;
        }
        $studentList = $objDsStudentCourse->getStudentList($subCourseId,$classId,array(),0,$studentCnt); 
        if(empty($studentList)){
            Bd_Log::warning("Error:[get studentList empty], Detail:[lessonId:$lessonId classId:$classId]");
            return $arrOutput;
        }
        $objDsLessonDetail = new Hkzb_Ds_Fudao_LessonDetail();
        $lessonDetailList  = $objDsLessonDetail->getLessonDetailStudentList($lessonId,$classId);
        foreach($studentList as $student){
            $studentUid = intval($student['studentUid']);
            $lessonDetail = isset($lessonDetailList[$studentUid]) ? $lessonDetailList[$studentUid] : array(); 
            $lessonDetail['attend'] = 0;
            if(intval($lessonDetail['firstAttendTime']) == 0) {
                $lessonDetail['attend'] = 0;  // 未到
            }
            else if(intval($lessonDetail['online']) == 1){
                $lessonDetail['attend'] = 2;  // 已到 在线
            }
            else {
                $lessonDetail['attend'] = 1;  // 已到 离线
            }
            $arrOutput[$studentUid] = $lessonDetail;
        }
        return $arrOutput;
    }

    /**
     * 获取辅导端一个学生的报名情况，统一pcassistant和teacherapp
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */   
    public function getStudentInfo($studentUid,$learnSeason = array()){
        $arrOutput = array();
        if($studentUid <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }
        //获取student表信息
        $objStudent = new Hkzb_Ds_Fudao_Student();
        $studentInfo = $objStudent->getStudentInfo($studentUid);
        if(false === $studentInfo){
            Bd_Log::warning("Error:[get studentInfo error], Detail:[studentUid:$studentUid]");
            $studentInfo = array();
        }
        $arrOutput['studentInfo'] = $studentInfo;
        //获取uclude表信息
        $objUcloud = new Hk_Ds_User_Ucloud();
        $ucludeInfo = $objUcloud->getUserInfo($studentUid);
        if(false === $ucludeInfo){
            Bd_Log::warning("Error:[get ucludeInfo error], Detail:[studentUid:$studentUid]");
            $ucludeInfo = array();
        }
        $arrOutput['ucludeInfo'] = $ucludeInfo;
        //查看是不是新的学员
        $objNewUser = new Hkzb_Util_Fudao_NewUserCourse();
        $arrOutput['isNew'] = ($objNewUser->checkNewUser($studentUid)) ? 1 : 0;

        //获取课程信息
        $objAdVancedStudentCourse = new Hkzb_Ds_Fudao_Advanced_StudentCourse();
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $objTeacher = new Hkzb_Ds_Fudao_Teacher();
        $courseList = $objAdVancedStudentCourse->getCourseList($studentUid);
        if(false === $courseList){
            Bd_Log::warning("Error:[get courselist error], Detail:[studentUid:$studentUid]");
            $courseList = array();
        }
        $courseIds = array();
        foreach($courseList as $course){
            $courseIds[$course['courseId']] = $course['courseId'];
        }   
        $arrCourseInfos = array();
        if(!empty($courseIds)){
            $arrCourseInfos = $objCourse->getCourseInfoArray($courseIds,array('type','`inner`','pack','onlineStart','learnSeason','extData','courseId','grade','subject','courseName'));
        }   
        foreach($arrCourseInfos as $course){
            $arrCourseInfos[$course['courseId']] = $course;
        }   
        foreach($courseList as $course){
            $courseData = $arrCourseInfos[$course['courseId']];
            if($courseData['type'] != Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG){
                 continue;
            }
            if($courseData['`inner`'] == 1){
                continue;
            }
            if($courseData['pack'] == Hkzb_Ds_Fudao_Course::PACK_YESD){
                continue;
            }  
            if($courseData['onlineStart'] <= 1483200000){
                continue;
            }   
            foreach($learnSeason as $name=>$season){
                if(in_array($courseData['learnSeason'],$season['field'])){
                    $subCourse = array();
                    if($courseData['pack'] == Hkzb_Ds_Fudao_Course::PACK_YES){
                        foreach($courseData['extData']['coreCourseIds'] as $v){
                            $subCourse[] = $v['courseId'];
                        } 
                        foreach($courseData['extData']['otherCourseIds'] as $v){
                            $subCourse[] = $v['courseId'];
                        }
                    }else{
                        $subCourse[] = $courseData['courseId'];
                    }        
                    //获取所有的主讲老师
                    $arrCourseInfo = $objCourse->getCourseInfoArray($subCourse,array('teacherUid'));
                    $teacherUids = array();
                    foreach($arrCourseInfo as $row){
                        $teacherUids[] = $row['teacherUid'];
                    }   
                    $teacherInfos = array();
                    if(!empty($teacherUids)){
                        $teacherInfos = $objTeacher->getTeacherInfoArr($teacherUids,array('teacherName'));
                    }   
                    $assistantInfo = $objTeacher->getTeacherInfo($course['assistantUid'],array('teacherName'));
                    $courseData['assistantInfo'] = $assistantInfo;
                    $courseData['teacherInfos']  = $teacherInfos;            
                    $arrOutput[$name][] = $courseData;
                }
            }
        }

        //封装访谈记录
        $objDataStudentInterview = new Hkzb_Ds_Fudao_StudentInterview();
        $total = $objDataStudentInterview->getStudentInterviewCnt($studentUid);
        if(false === $total){
            Bd_Log::warning("Error:[get interviewCnt error], Detail:[studentUid:$studentUid]");
            $total = 0;
        }
        //获取一个学生的所有访谈记录
        $studentInterviews = $objDataStudentInterview->getStudentInterviewList($studentUid,array(),0,$total);
        if(false === $studentInterviews){
            Bd_Log::warning("Error:[get interviewlist error], Detail:[studentUid:$studentUid]");
            $studentInterviews = array();
        }
        foreach($studentInterviews as $k=>$v){
            //相关课程名称
            $releateCourseName = '';
            if(isset($v['extData']['courseId']) && intval($v['extData']['courseId']) > 0){
                $releateCourse = $objCourse->getCourseInfo($v['extData']['courseId'],array('courseName'));
                $releateCourseName = $releateCourse['courseName'];
            }
            $studentInterviews[$k]['releateCourseName'] = $releateCourseName;
            //呼叫记录名称
            $callId = 0;
            if(isset($v['extData']['callId']) && intval($v['extData']['callId']) > 0){
                $callId = $v['extData']['callId'];  
            }
            $studentInterviews[$k]['callId']    = $callId;
        }
        $arrOutput['studentInterviews'] = $studentInterviews;
        $arrOutput['studentInterviewCnt'] = $total;
        return $arrOutput;
    }


     /**
     * 获取辅导端一个学生的报名情况，统一pcassistant和teacherapp
     *
     * @param  int   $lessonId    章节id
     * @return array|false
     */   
    public function getStudentDetails($studentUid,$learnSeason = array(),$assistantUid=0,$courseId=0){
        $arrOutput = array();
        if($studentUid <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }
        //获取student表信息
        $objStudent = new Hkzb_Ds_Fudao_Student();
        $studentInfo = $objStudent->getStudentInfo($studentUid);
        if(false === $studentInfo){
            Bd_Log::warning("Error:[get studentInfo error], Detail:[studentUid:$studentUid]");
            $studentInfo = array();
        }
        
        //获取uclude表信息
        $objUcloud = new Hk_Ds_User_Ucloud();
        $ucludeInfo = $objUcloud->getUserInfo($studentUid);
        if(false === $ucludeInfo){
            Bd_Log::warning("Error:[get ucludeInfo error], Detail:[studentUid:$studentUid]");
            $ucludeInfo = array();
        }
        $studentInfo['sex']=Hkzb_Ds_Fudao_Coach::$SEX_ARRAY[$ucludeInfo['sex']];
        $studentInfo['grade']=Hk_Util_Category::$GRADE[$ucludeInfo['grade']];
        $arrOutput['studentInfo'] = $studentInfo;
        $arrOutput['ucludeInfo'] = $ucludeInfo;
        //查看是不是新的学员
        $objNewUser = new Hkzb_Util_Fudao_NewUserCourse();
        $arrOutput['isNew'] = ($objNewUser->checkNewUser($studentUid)) ? 1 : 0;

        //获取课程信息
        $objAdVancedStudentCourse = new Hkzb_Ds_Fudao_Advanced_StudentCourse();
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $objTeacher = new Hkzb_Ds_Fudao_Teacher();
        $courseList = $objAdVancedStudentCourse->getCourseList($studentUid);
        if(false === $courseList){
            Bd_Log::warning("Error:[get courselist error], Detail:[studentUid:$studentUid]");
            $courseList = array();
        }
        $courseIds = array();
        foreach($courseList as $course){
            $courseIds[$course['courseId']] = $course['courseId'];
        }   
        $arrCourseInfos = array();
        if(!empty($courseIds)){
            $arrCourseInfos = $objCourse->getCourseInfoArray($courseIds,array('type','`inner`','studentCnt',"teacherUid",'pack','onlineStart','learnSeason','extData','courseId','grade','subject','courseName'));
        }   
        $classStudentCount=$classCount=$specialStudentCount=$specialCount=0;
        foreach ($arrCourseInfos as $course) {
            $arrCourseInfos[$course['courseId']] = $course;
            if ($course['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG) {
                $classStudentCount++;
                if ($assistantUid == $course['teacherUid']) {
                    $classCount++;
                }
            }
            if ($course['type'] == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE) {
                $specialStudentCount++;
                if ($assistantUid == $course['teacherUid'])
                    $specialCount++;
            }
        }


        //封装访谈记录
        $objDataStudentInterview = new Hkzb_Ds_Fudao_StudentInterview();
        $total = $objDataStudentInterview->getStudentInterviewCnt($studentUid);
        //获取一个学生的所有访谈记录
        $studentInterviews = $objDataStudentInterview->getStudentInterviewList($studentUid,array(),0,$total);
        if(false === $studentInterviews){
            Bd_Log::warning("Error:[get interviewlist error], Detail:[studentUid:$studentUid]");
            $studentInterviews = array();
        }
        $m=0;
        foreach($studentInterviews as $k=>$v){
            //相关课程名称
           
            $releateCourseName = '';
            if(isset($v['extData']['courseId'])&&($v['extData']['courseId']==$courseId)&&($m<3) && intval($v['extData']['courseId']) > 0){
                $releateCourse = $objCourse->getCourseInfo($v['extData']['courseId'],array('courseName'));
                $releateCourseName = $releateCourse['courseName'];
                $m++;
            $studentInterviews[$k]['releateCourseName'] = $releateCourseName;
            //呼叫记录名称
            $callId = 0;
            if(isset($v['extData']['callId']) && intval($v['extData']['callId']) > 0){
                $callId = $v['extData']['callId'];  
            }
            $studentInterviews[$k]['callId']    = $callId;
            }  else {
                unset( $studentInterviews[$k]);
            }
        }
         
        $arrOutput['studentInterviews'] = $studentInterviews;
       // $arrOutput['studentInterviewCnt'] = $total;
        $arrOutput['classStudentCount'] = $classStudentCount;
        $arrOutput['classCount'] = $classCount;
        $arrOutput['specialStudentCount'] = $specialStudentCount;
        $arrOutput['specialCount'] = $specialCount;
        return $arrOutput;
    }
    
    
    
    /*
     * 获取辅导端一个学生的新老情况
     *
     * @param  int   $studentUid   学生uid
     * @param  int   $year         年份
     * @param  int   $learnSeason  学季
     * @return 1/0/false
    */
    public function studentIsNew($studentUid,$year,$season,$seasonNum = array()){
        if(intval($studentUid <= 0) || intval($year <=0) || strlen($season) <= 0){
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid year:$year season:$season]");
            return false;
        }
        $result = 0;
        $onlineStart = strtotime($year."0101");
        $onlineStop  = strtotime($year."1231") + 86400;
        //转换期数
        if(empty($seasonNum)){
            $seasonNum = array(1,2,3,4);
        }
        $learnSeason = array();
        foreach($seasonNum as $v){
            $learnSeason[] = $season . '_' . $v;
        }

        $objCourse = new Hkzb_Ds_Fudao_Course();
        //找到该年份该期课第一节的开始时间
        $courseConds = array();
        $courseConds['type'] = Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG;
        $courseConds['pack'] = array(Hkzb_Ds_Fudao_Course::PACK_YESD,'<>');
        $courseConds['status'] = array(Hkzb_Ds_Fudao_Course::STATUS_DELETED,'<>');
        $courseConds['`inner`'] = Hkzb_Ds_Fudao_Course::OUTER_COURSE;
        $courseConds[] = 'online_start >= ' . $onlineStart . ' and online_start < ' . $onlineStop . ' and learn_season in ("' . implode('","',$learnSeason) . '")';
        $courseCnt  = $objCourse->getCourseCntByConds($courseConds);
        $courseList = $objCourse->getCourseListByConds($courseConds,array('onlineStart'),0,$courseCnt);
        //取最早的那个时间
        $firstTime = 0; 
        foreach($courseList as $v){
            if($firstTime ==0 || $v['onlineStart'] < $firstTime){
                $firstTime = $v['onlineStart'];
            }
        }
        //获取在这个时间之前所有的班课id
        $courseConds = array();
        $courseConds['type'] = Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG;
        $courseConds['pack'] = array(Hkzb_Ds_Fudao_Course::PACK_YESD,'<>');
        $courseConds['status'] = array(Hkzb_Ds_Fudao_Course::STATUS_DELETED,'<>');
        $courseConds['`inner`'] = Hkzb_Ds_Fudao_Course::OUTER_COURSE;
        $courseConds['onlineStart'] = array($firstTime,'<');
        $courseCnt  = $objCourse->getCourseCntByConds($courseConds);
        if(intval($courseCnt) <= 0){
            return $result;
        }
        $courseList = $objCourse->getCourseListByConds($courseConds,array('courseId'),0,$courseCnt);
        $courseIds  = array();
        foreach($courseList as $v){
            $courseIds[] = $v['courseId'];
        } 
        //查看是否有包裹课
        $objContinueData = new Hkzb_Ds_Fudao_ContinueData();
        $arrConds = array();
        $arrConds['studentUid'] = $studentUid;
        $arrConds[] = 'course_id in (' . implode(',',$courseIds) . ')'; 
        $list = $objContinueData->getListByConds($arrConds,array('id'),0,1);
        if(empty($list)){
            $result = 1;
        }
        return $result;
    }
}
