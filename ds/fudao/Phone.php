<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Course.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 课程
 *  
 **/

class Hkzb_Ds_Fudao_Phone {
    //挂机类型
    static $byeType  = array(
        '1'   => '通话中取消回拨、直拨和外呼的正常结束通话',
        '2'   => '账户欠费或者设置的通话时间到',
        '3'   => '回拨通话中主叫挂断，正常结束通话',
        '4'   => '回拨通话中被叫挂断，正常结束通话',
        '-1'  => '被叫没有振铃就收到了挂断消息',
        '-2'  => '呼叫超时没有接通被挂断',
        '-3'  => '回拨主叫接通了主叫挂断',
        '-4'  => '回拨主叫通道创建了被挂断',
        '-5'  => '被叫通道建立了被挂断',
        '-6'  => '系统鉴权失败',
        '-7'  => '第三方鉴权失败',
        '-8'  => '直拨被叫振铃了挂断',
        '-9'  => '回拨被叫振铃了挂断',
        '-10' => '回拨主叫振铃了挂断',
        '-11' => '账户余额不足',
        '-14' => '回拨取消呼叫(通过取消回拨接口)',
    );

    const ALL_FIELDS = 'phoneId,callSid,department,caller,called,startTime,endTime,duration,byeType,isDown,created,extData';

    private $objDaoPhone;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoPhone = new Hkzb_Dao_Fudao_Phone();
    }

    /**
     * 新增记录
     *
     * @param  array  $arrParams 通话属性
     * @return bool true/false
     */
    public function addPhone($arrParams) {
        if((intval($arrParams['department'])<=0) ||  (trim($arrParams['callSid']) == '') || (trim($arrParams['caller']) == '') || (trim($arrParams['called'])=='')) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'callSid'       => trim($arrParams['callSid']),
            'department'    => trim($arrParams['department']),
            'caller'        => strval($arrParams['caller']),
            'called'        => strval($arrParams['called']),
            'startTime'     => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'endTime'       => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'duration'      => isset($arrParams['duration']) ? intval($arrParams['duration']) : 0,
            'byeType'       => isset($arrParams['byeType']) ? strval($arrParams['byeType']) : '',
            'isDown'        => isset($arrParams['isDown']) ? intval($arrParams['isDown']) : 0,
            'created'       => isset($arrParams['created']) ? intval($arrParams['created']) : 0,
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoPhone->insertRecords($arrFields);

        return $ret;
    }

    /**
     * 更新电话
     *
     * @param  array  条件
     * @param  array  $arrParams 电话属性
     * @return bool true/false
     */
    public function updatePhone($arrConds, $arrParams) {
        if(empty($arrConds)) {
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        $ret = $this->objDaoPhone->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取电话详情
     *
     * @param  str  $callSid 
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getPhoneInfo($callSid, $arrFields = array()) {
        if(strval($callSid) == '') {
            Bd_Log::warning("Error:[param error], Detail:[callSid:$callSid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'callSid' => strval($callSid),    
        );

        $ret = $this->objDaoPhone->getRecordByConds($arrConds, $arrFields);

        return $ret;
    } 

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getPhoneListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by created desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoPhone->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
}
