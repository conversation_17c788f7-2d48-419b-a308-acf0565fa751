<?php

class Hkzb_Ds_Fudao_ShortMessage
{
    //redis key ttl, 5天
    const REDIS_KEY_EXPIRE_TIME = 432000;

    //同步拉取文本消息的队列
    const MSG_LIST_CACHE_KEY = "class_msg_list_cache_%s";
    const MSG_LIST_CACHED_EXPIRE = "14400"; //队列存活时间
    const MSG_LIST_CACHE_MAX_LEN = 20; //最多缓存100条消息

    //写入缓存的信领号
    public static $msgTypeList = array(
        Hkzb_Const_FudaoMsgSignal::SEND_EXERCISE_CARD => 1,
        Hkzb_Const_FudaoMsgSignal::STOP_EXERCISE => 1,
        Hkzb_Const_FudaoMsgSignal::YESNO => 1,
        Hkzb_Const_FudaoMsgSignal::PUSH_YESNO_STOP => 1,
        Hkzb_Const_FudaoMsgSignal::SEND_SCORE_RED_ENVELOPE => 1,
        Hkzb_Const_FudaoMsgSignal::CLOSE_SCORE_RED_ENVELOPE => 1,
        //Hkzb_Const_FudaoMsgSignal::START_MIC => 1,
        //Hkzb_Const_FudaoMsgSignal::STOP_MIC => 1,
        Hkzb_Const_FudaoMsgSignal::EXAM_START => 1,
        Hkzb_Const_FudaoMsgSignal::EXAM_STOP => 1,
        Hkzb_Const_FudaoMsgSignal::START_BARRAGE => 1,
        Hkzb_Const_FudaoMsgSignal::STOP_BARRAGE => 1,
        Hkzb_Const_FudaoMsgSignal::PLUGIN_COMMON_START_SIGNAL => 1,

    );
    //互斥的信领号
    public static $repelType = array(
        Hkzb_Const_FudaoMsgSignal::SEND_EXERCISE_CARD => Hkzb_Const_FudaoMsgSignal::STOP_EXERCISE,
        Hkzb_Const_FudaoMsgSignal::YESNO => Hkzb_Const_FudaoMsgSignal::PUSH_YESNO_STOP,
        Hkzb_Const_FudaoMsgSignal::SEND_SCORE_RED_ENVELOPE => Hkzb_Const_FudaoMsgSignal::CLOSE_SCORE_RED_ENVELOPE,
        //Hkzb_Const_FudaoMsgSignal::START_MIC => Hkzb_Const_FudaoMsgSignal::STOP_MIC,
        Hkzb_Const_FudaoMsgSignal::EXAM_START => Hkzb_Const_FudaoMsgSignal::EXAM_STOP,
        Hkzb_Const_FudaoMsgSignal::START_BARRAGE => Hkzb_Const_FudaoMsgSignal::STOP_BARRAGE,
    );
    //缓存key pre
    public static $cacheKeyPre = 'zhibo_inclass_message_queue_v2_%s';

    //写入队列
    public static function pushShortMessage($lessonId, $signalNo, $msgId, $msgKv)
    {
        if ($lessonId <= 0) {
            return false;
        }
        //读缓存
        //取key
        $cacheKey = sprintf(self::$cacheKeyPre, $lessonId);
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $msgBody = array(
            'msgId' => $msgId,
            'signalNo' => $signalNo,
            'data' => $msgKv['msg_content'],
            'msgTime' => intval($msgKv['msg_time'])
        );
        $objStored->lpush($cacheKey, json_encode($msgBody));
        $objStored->expire($cacheKey, self::REDIS_KEY_EXPIRE_TIME);
        return true;
    }

    //读取消息
    public static function getShortMessage($lessonId)
    {
        /***读缓存***/
        //取key
        $cacheKey = sprintf(self::$cacheKeyPre, intval($lessonId));
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $shortQueue = $objStored->lrange($cacheKey, 0, 100);
        if (empty($shortQueue)) {
            $shortQueue = array();
        }
        foreach ($shortQueue as $key => $value) {
            $shortMsg = json_decode($value, true);
            $shortMsg['data'] = json_decode($shortMsg['data'], true);
            if (!$shortMsg['data']) {
                $shortMsg['data'] = array();
            }
            $shortQueue[$key] = $shortMsg;
        }
        return $shortQueue;
    }

    /**
     * @param $lessonId
     * @return array|mixed
     */
    public static function getValidShortMessage($lessonId)
    {
        $shortQueue = self::getShortMessage($lessonId);
        if (!$shortQueue || !is_array($shortQueue)) {
            return array();
        }

        $shortMessage = NULL;
        foreach($shortQueue as $messageItem){
            if(isset(self::$msgTypeList[$messageItem['signalNo']])){
                //兼容通用投票
                if (isset($messageItem['data']['data']['interactid']) && $messageItem['data']['data']['optype'] != 1) {
                    continue;
                }
                $shortMessage = $messageItem;
                break;
            }
        }

        if(!$shortMessage){
            return array();
        }

        if (in_array($shortMessage['signalNo'], self::$repelType)) {
            return array();
        }
        $list = array($shortMessage);
        return $list;
    }

    /**
     * 读取指定章节第一个班级的聊天缓存队列（用于Ajax轮询）
     *
     * @param $lessonId 指定章节id
     * @return array
     */
    public static function getChatMessageList($lessonId)
    {

        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $classList = $objLesson->getClassIdList($lessonId);
        if (empty($classList) || !is_array($classList) || !$classList[0]) {
            return array();
        }
        $classIdInDb = intval($classList[0]);

        $msgListCacheKey = sprintf(self::MSG_LIST_CACHE_KEY, $classIdInDb);
//        $objCacheV2 =  Liveservice_Util_RedisService::getZhiboInstance();
        $objCacheV2 =  Liveservice_Util_StoredService::getZhiboInstance();
        //$objCache = Hk_Service_RedisClient::getInstance("zhibo");
        $ret = $objCacheV2->lrange($msgListCacheKey, 0, -1);
        return empty($ret) ? array() : $ret;
    }


}
