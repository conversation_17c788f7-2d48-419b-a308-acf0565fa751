<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ChangeTeacher.php
 * <AUTHOR>
 * @date 2016/08/4 13:26:46
 * @brief 换老师
 *  
 **/

class Hkzb_Ds_Fudao_ChangeTeacher {

    const DUTY_TEACHER   = 1; //主讲老师
    const DUTY_ASSISTANT = 2; //辅导老师
    static $DUTY_ARRAY   = array(
        self::DUTY_TEACHER  => '主讲老师',
        self::DUTY_ASSISTANT=> '辅导老师',
    );
    const STATUS_OK      = 0; //正常
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK      => '正常',
        self::STATUS_DELETED => '已删除',
    );

    const ALL_FIELDS = 'id,courseId,duty,exTeacher,nowTeacher,num,reason,deleted,operator,createTime,extData';

    private $objDaoChangeTeacher;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoChangeTeacher = new Hkzb_Dao_Fudao_ChangeTeacher();
    }

    /**
     * 新增换老师记录
     *
     * @param  mix  $arrParams 调课记录属性
     * @return bool true/false
     */
    public function addChangeTeacher($arrParams) {

        $arrFields = array(
            'courseId'      => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'duty'          => isset($arrParams['duty']) ? intval($arrParams['duty']) : 0,
            'exTeacher'     => isset($arrParams['exTeacher']) ? intval($arrParams['exTeacher']) : 0,
            'nowTeacher'    => isset($arrParams['nowTeacher']) ? intval($arrParams['nowTeacher']) : 0,
            'num'           => isset($arrParams['num']) ? intval($arrParams['num']) : 0,
            'reason'        => isset($arrParams['reason']) ? strval($arrParams['reason']) : '',
            'deleted'       => self::STATUS_OK,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoChangeTeacher->insertRecords($arrFields);
        
        return $ret;
    }

    /**
     * 获取调课记录
     *
     * @param  int  $courseId  指定课程id
     * @param  int  $duty    老师职责
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getChangeTeacherInfo($courseId, $duty, $arrFields = array()) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'deleted'  => self::STATUS_OK,
        );
        if($duty != 0){
            $arrConds['duty'] = $duty;
        }

        $ret = $this->objDaoChangeTeacher->getListByConds($arrConds, $arrFields);

        return $ret;
    
    }
}
