<?php
/**
 * Created by PhpStorm.
 * User: 作业帮
 * Date: 2018/6/29
 * Time: 11:16
 */
/**
 * @file   StudentCourseInterimReport.php
 * <AUTHOR>
 * @date  2018/6/29
 * @brief  学习报告
 *
 **/
class Hkzb_Ds_Fudao_StudentCourseInterimReport
{

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoStudentCourseInterimReport = new Hkzb_Dao_Fudao_StudentCourseInterimReport();
    }

    /**
     * 更新学生信息（新添加的方法）
     *
     * @param  int  $courseId    课程id
     * @param  array$arrParams   课程属性
     * @return bool true/false
     */
    public function updateLessonDetailByclassId($courseId, $classId = 0, $arrStuList, $arrParams) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'classId'        => intval($classId),
            'courseId'   => intval($courseId),
        );
        if(count($arrStuList) > 0){
            $strStudentUid = implode(",",$arrStuList);
            $strStudentUid = 'student_uid in ('.$strStudentUid.')';
            $arrConds[] = $strStudentUid;
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $objDaoLessonDetail = new Hkzb_Dao_Fudao_LessonDetail();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoLessonDetail->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 新增学生课节
     *
     * @param   array
     * @return bool true/false
     */
    public function addLessonDetail($arrParams)
    {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0 || intval($arrParams['lessonId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'courseId'     => intval($arrParams['courseId']),
            'lessonId'     => intval($arrParams['lessonId']),
            'teacherUid'   => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'assistantUid' => isset($arrParams['assistantUid']) ? intval($arrParams['assistantUid']) : 0,
            'classId'      => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'startTime'    => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'stopTime'     => isset($arrParams['stopTime']) ? intval($arrParams['stopTime']) : 0,
            'online'       => self::ONLINE_NO,
            'cantalk'      => self::TALK_FREE,
            'status'       => isset($arrParams['status']) ? intval($arrParams['status']) : self::STATUS_CLASS_TOSTART,
            'createTime'   => time(),
            'updateTime'   => time(),
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoLessonDetail->insertRecords(intval($arrParams['studentUid']), $arrFields);

        //清理缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $lessonId = intval($arrParams['lessonId']);
        $classId  = intval($arrParams['classId']);
        $cacheKey = 'zhiboke_ds_lesson_getStudentUidList_' . $lessonId . '_' . $classId;
        $objMemcached->delete($cacheKey);

        return $ret;
    }

    /**
     * 学生上课
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function attendClass($studentUid, $lessonId)
    {
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
            return false;
        }

        //仅第一次更新进入课堂时间
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
            'firstAttendTime' => 0,
        );
        $arrFields = array(
            'firstAttendTime' => time(),
        );
        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        //更新状态
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
        );

        $arrFields = array(
            'online'     => self::ONLINE_YES,
            'attendTime' => time(),
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 学生下课
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function exitClass($studentUid, $lessonId)
    {
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
        );

        $arrFields = array(
            'online'     => self::ONLINE_NO,
            'exitTime'   => time(),
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 设置在线状态
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function setOnline($studentUid, $lessonId)
    {
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
            return false;
        }

        //更新状态
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
        );

        $arrFields = array(
            'online'     => self::ONLINE_YES,
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 电话推荐 开
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @return mix
     */
    public function openRemind($studentUid, $courseId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'remind'     => 1,
        );

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 电话推荐 关
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @return mix
     */
    public function shutRemind($studentUid, $courseId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'remind'     => 0,
        );

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 上课
     *
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return bool true/false
     */
    public function startClass($courseId, $lessonId)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'status'   => self::STATUS_CLASS_TOSTART,
        );

        $arrFields = array(
            'status'     => self::STATUS_CLASS_IN,
            'updateTime' => time(),
        );

        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 下课
     *
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return bool true/false
     */
    public function stopClass($courseId, $lessonId)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'status in ('.self::STATUS_CLASS_TOSTART.','.self::STATUS_CLASS_IN.')',
        );

        $arrFields = array(
            'status'     => self::STATUS_CLASS_STOPED,
            'updateTime' => time(),
        );

        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 根据用户ID和where条件获取到相关数据
     * liangshuguang (<EMAIL>)
     * @param int $intStudentId 根据此用户ID做分表
     * @param array $arrWhere
     * @param array $arrSelect
     * @return array
     */
    public function getStudentLessonInfo($intStudentId, $arrWhere, $arrSelect) {
        if ( 0 >= intval($intStudentId)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$intStudentId arrWhere:$arrWhere arrSelect:$arrSelect]");
            return false;
        }

        return $this->objDaoLessonDetail->getListByConds(intval($intStudentId), $arrWhere, $arrSelect);
    }

    /**
     * 设置扩展字段
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function setExtData($studentUid, $courseId = 0, $lessonId, $arrNewExtData)
    {
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");

            return false;
        }

        $ret = $this->getLessonDetail($studentUid, 0, $lessonId, array('extData'));
        if (empty($ret)) {
            Bd_Log::warning("Error:[getLessonDetail error], Detail:[studentUid:$studentUid  lessonId:$lessonId]");

            return false;
        }

        $arrExtData = $ret['extData'];
        foreach ($arrNewExtData as $key => $value) {
            //学生课堂上的总题数和正确题数需要累加
            if(($key == "inClassRightExerciseNum" && array_key_exists("inClassRightExerciseNum",$arrExtData))
                ||($key == "inClassTotalExerciseNum" && array_key_exists("inClassTotalExerciseNum",$arrExtData))
                ||($key == "inClassUndoExerciseNum" && array_key_exists("inClassUndoExerciseNum",$arrExtData))
                ||($key == "inClassErrorExerciseNum" && array_key_exists("inClassErrorExerciseNum",$arrExtData))
            ){
                $arrExtData[$key] = intval($arrExtData[$key]) + $value;
            }else {
                $arrExtData[$key] = $value;
            }
        }

        $arrFields = array(
            'extData'    => json_encode($arrExtData),
            'updateTime' => time(),
        );


        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
        );
        if(intval($courseId) > 0){
            $arrConds['courseId'] = $courseId;
        }

        $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取最近课节详情
     *
     * @param  int $courseId  课程id
     * @param  int $studentUid  学生id
     * @param  array $arrFields 指定属性
     * @return mix
     */
    public function getLessonDetailInfoLately($courseId, $studentUid,$arrFields = array())
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$courseId studentUid:$studentUid]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
            'studentUid' => intval($studentUid),
            'status'     => array(self::STATUS_CLASS_STOPED, "<"),
        );

        $arrAppends   = array(
            " order by start_time limit 1",
        );

        $ret = $this->objDaoLessonDetail->getRecordByConds(intval($studentUid), $arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * 获取最后课节详情
     *
     * @param  int $lessonId  课节id
     * @param  int $studentUid  学生id
     * @param  array $arrFields 指定属性
     * @return mix
     */

    public function getLessonDetailInfoLast($courseId,$studentUid, $arrFields = array())
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$courseId studentUid:$studentUid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
            'studentUid' => intval($studentUid),
            'status'     => array(self::STATUS_DELETED, "<"),
        );

        $arrAppends   = array(
            " order by start_time desc limit 1",
        );

        $ret = $this->objDaoLessonDetail->getRecordByConds(intval($studentUid), $arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function getLessonDetail($studentUid, $courseId = 0, $lessonId, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'lessonId'   => intval($lessonId),
        );
        if(intval($courseId) > 0){
            $arrConds['courseId']   = intval($courseId);
        }

        $ret = $this->objDaoLessonDetail->getRecordByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getLessonDetailListByConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "limit $offset, $limit",
        );

        $arrConds['status'] = array(self::STATUS_DELETED, '<>');


        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @param string $order
     * @return array|false
     */
    public function getLessonDetailListByCondsOrder($arrConds, $arrFields = array(),$offset = 0, $limit = 20, $order='start_time') {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by $order asc limit $offset, $limit",
        );

        $arrConds['status'] = array(self::STATUS_DELETED, '<>');

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getLessonDetailCntByConds($arrConds) {
        $arrConds['status'] = array(self::STATUS_DELETED, '<>');

        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }
        return $sumRet;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getLessonDetailListByStatusConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "limit $offset, $limit",
        );

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getLessonDetailCntByStatusConds($arrConds) {
        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }
        return $sumRet;
    }

    /**
     * 获取指定班级的本课节所有学员情况
     * @param int      $courseId
     * @param int      $lessonId
     * @param int      $classId
     * @param array    $arrFields
     * @param int      $offset
     * @param int      $limit
     * @return array
     */
    public function getLessonDetailList($courseId, $lessonId, $classId, $arrFields = array(), $offset = 0, $limit = 20, $desc = true)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId classId:$classId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'status'   => array(self::STATUS_DELETED, '<>'),
        );

        if(1953 == $lessonId) {
            $arrConds['attendTime'] = array(0, '>');
        }

        $arrAppends = array(
            "order by create_time ". ($desc ? "desc" : "asc"),
            "limit $offset, $limit",
        );

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }

    /**
     * 获取指定课程在线人数
     * @param $courseId
     * @param $lessonId
     * @param int $classId
     * @param int $online
     * @return bool|false|int
     */
    public function getLessonOnlineCnt($courseId, $lessonId, $classId=0, $online=0)
    {

        if (intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId classId:$classId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );

        if ($classId > 0){
            $arrConds['classId'] = $classId;
        }

        if ($online > 0){
            $arrConds['online'] = $online;
        }

        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }
        return $sumRet;
    }

    /**
     * 获取指定课程老师数量
     * @param int $teacherUid
     * @param int $status
     * @return bool|false|int
     */
    public function getLessonDetailCnt($courseId, $lessonId, $classId)
    {

        if (intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId classId:$classId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'status'   => array(self::STATUS_DELETED, '<>'),
        );

        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }
        return $sumRet;
    }

    /**
     * 获取指定学生待上课节列表
     * @param  int   $studentUid 学生uid
     * @param  mix   $arrFields
     * @param  int   $offset
     * @param  int   $limit
     * @return mix
     */
    public function getUnfinishLessonListByStudentUid($studentUid, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'status'     => array(self::STATUS_CLASS_IN, '<='),
        );

        $arrAppends = array(
            "order by start_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * getInclassLessonListByStudentUid
     *
     * 获取学生正在直播列表
     * @param mixed $studentUid
     * @access public
     * @return int
     */
    public function getInclassLessonListByStudentUid($studentUid, $arrFields = [], $arrAppends)
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $arrConds = [
            'studentUid' => intval($studentUid),
            'status'     => self::STATUS_CLASS_IN,
        ];

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        if (!$arrAppends) {
            $arrAppends = [
                "order by start_time DESC",
            ];
        }

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * getInclassLessonCntByStudentUid
     *
     * 获取学生正在上课数量
     * @param mixed $studentUid
     * @access public
     * @return int
     */
    public function getInclassLessonCntByStudentUid($studentUid)
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $arrConds = [
            'studentUid' => intval($studentUid),
            'status'     => self::STATUS_CLASS_IN,
        ];
        $ret = $this->objDaoLessonDetail->getCntByConds($studentUid, $arrConds);
        return $ret;
    }

    /**
     * 获取指定学生已完成课节列表
     * @param  int   $studentUid 学生uid
     * @param  mix   $arrFields
     * @param  int   $offset
     * @param  int   $limit
     * @return mix
     */
    public function getFinishedLessonListByStudentUid($studentUid, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'status'     => self::STATUS_CLASS_STOPED,
        );

        $arrAppends = array(
            "order by start_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }


    /**
     * getFinishedLessonCntByStudentUid
     *
     * 获取某学生已完成的章节数量
     * @param mixed $studentUid
     * @access public
     * @return Array
     */
    public function getFinishedLessonCntByStudentUid($studentUid)
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'status'     => self::STATUS_CLASS_STOPED,
        );

        $ret = $this->objDaoLessonDetail->getCntByConds(intval($studentUid), $arrConds);

        return $ret;
    }

    /**
     * 获取指定学生指定课程的所有课节情况
     * @param  int   $studentUid 学生uid
     * @param  mix   $arrFields
     * @param  int   $offset
     * @param  int   $limit
     * @return mix
     */
    public function getLessonListByStudentUid($studentUid, $courseId, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <=0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrAppends = array(
            "order by start_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 辅导老师更新学生课堂表现
     * @param $studentUid
     * @param $courseId
     * @param $lessonId
     * @param $classId
     * @param $assistantUid
     * @return bool
     */
    public function updateStudentRemark($studentUid, $courseId, $lessonId, $classId, $assistantUid, $remark) {
        if (intval($assistantUid) <= 0 || intval($studentUid) <= 0
            || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0 || strlen(trim($remark)) == 0
        ) {
            Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");

            return false;
        }


        do {
            //1. 开启事务 其实并没有琐记录  并没有什么用
            $ret = $this->objDaoLessonDetail->startTransaction();
            if (!$ret) {
                Bd_Log::warning("Error[START TRANSACTION FAILED]");

                return false;
            }

            //2. 查询历史记录
            $arrConds = array(
                'assistantUid' => intval($assistantUid),
                'studentUid'   => intval($studentUid),
                'courseId'     => intval($courseId),
                'lessonId'     => intval($lessonId),
                'classId'      => intval($classId),
            );
            $data     = $this->objDaoLessonDetail->getRecordByConds(intval($studentUid), $arrConds, array('id', 'extData'));
            if (!$data) {
                Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");
                $this->objDaoLessonDetail->rollback();
                return false;
            }

            //3. 拿回id 和 extdata
            $id                = intval($data['id']);
            $extData           = $data['extData'];
            $extData['remark'] = trim(strval($remark));

            //4. 拼要更新的条件
            $arrConds = array(
                'id' => $id,
            );
            $arrFields = array(
                'extData' => json_encode($extData),
            );

            //5. 更新道数据库
            $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds,$arrFields);

            if ($ret===false) {
                //6. 更新失败  回滚
                Bd_Log::warning("Error:[update lessonDetail error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId remark:$remark]");
                $ret = $this->objDaoLessonDetail->rollback();
                if (!$ret) {
                    //回滚失败
                    Bd_Log::warning("Error:[rollback lessonDetail error], Detail:[]");
                }
                return false;
            }
            //更新成功 提交
            $ret = $this->objDaoLessonDetail->commit();
            if (!$ret) {
                //提交失败
                Bd_Log::warning("Error:[update lessonDetail error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId remark:$remark]");
                return false;
            }
        } while (0);

        return $ret;
    }

    /**
     * 辅导老师更新学生学习报告
     * @param $studentUid
     * @param $courseId
     * @param $lessonId
     * @param $classId
     * @param $assistantUid
     * @return bool
     */
    public function updateStudentReport($studentUid, $courseId, $lessonId, $classId, $assistantUid, $report) {
        if (intval($assistantUid) <= 0 || intval($studentUid) <= 0
            || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0 || empty($report)
        ) {
            Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");

            return false;
        }


        do {
            //1. 开启事务 其实并没有琐记录  并没有什么用
            $ret = $this->objDaoLessonDetail->startTransaction();
            if (!$ret) {
                Bd_Log::warning("Error[START TRANSACTION FAILED]");

                return false;
            }

            //2. 查询历史记录
            $arrConds = array(
                'assistantUid' => intval($assistantUid),
                'studentUid'   => intval($studentUid),
                'courseId'     => intval($courseId),
                'lessonId'     => intval($lessonId),
                'classId'      => intval($classId),
            );
            $data     = $this->objDaoLessonDetail->getRecordByConds(intval($studentUid), $arrConds, array('id', 'extData'));
            if (!$data) {
                Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");
                $this->objDaoLessonDetail->rollback();
                return false;
            }

            //3. 拿回id 和 extdata
            $id                = intval($data['id']);
            $extData           = $data['extData'];
            $extData['report'] = $report;

            //4. 拼要更新的条件
            $arrConds = array(
                'id' => $id,
            );
            $arrFields = array(
                'extData' => json_encode($extData),
            );

            //5. 更新道数据库
            $ret = $this->objDaoLessonDetail->updateByConds(intval($studentUid), $arrConds,$arrFields);

            if ($ret === false) {
                //6. 更新失败  回滚
                Bd_Log::warning("Error:[update lessonDetail error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");
                $ret = $this->objDaoLessonDetail->rollback();
                if (!$ret) {
                    //回滚失败
                    Bd_Log::warning("Error:[rollback lessonDetail error], Detail:[]");
                }
                return false;
            }
            //更新成功 提交
            $ret = $this->objDaoLessonDetail->commit();
            if (!$ret) {
                //提交失败
                Bd_Log::warning("Error:[update lessonDetail error], Detail:[assistantUid:$assistantUid studentUid:$studentUid courseId:$courseId lessonId:$lessonId classId:$classId]");
                return false;
            }
        } while (0);

        return $ret;
    }

    /**
     * 更新课节
     *
     * @param  int $lessonId  课节id
     * @param  array $arrParams 课节属性
     * @return bool true/false
     */
    public function updateLesson($lessonId, $arrParams)
    {
        if (intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");

            return false;
        }

        $arrConds = array(
            'lessonId' => intval($lessonId),
        );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 获取学生未上的课
     * @param $studentUid
     * @param $isFinished
     * @param $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|bool
     */
    public function getStudentLessonList($studentUid, $isFinished, $arrFields, $offset=0, $limit=20){
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
        );

        if ($isFinished){
            $arrConds['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED;
            $arrAppends = array(
                "order by start_time desc",
            );
        }else{
            $arrConds[] = 'status in ('.Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART.', '.Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN.')';
            $arrAppends = array(
                "order by start_time",
            );
        }
        $arrAppends[] = "limit $offset, $limit";

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 根据课程获取课时数量
     *
     * @param  int $courseId 课程id
     * @return int
     */
    public function getStudentLessonCnt($studentUid, $isFinished)
    {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
        );

        if ($isFinished){
            $arrConds['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED;
        }else{
            $arrConds[] = 'status in ('.Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART.', '.Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN.')';
        }


        $ret = $this->objDaoLessonDetail->getCntByConds(intval($studentUid), $arrConds);

        return $ret;
    }
    /**
     * 获取该节课下该辅导员的所有学员列表
     *
     * @param  int $assistantUid 辅导员id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return int
     */
    public function getStudentListByAssistant($assistantUid,$courseId,$lessonId,$arrFields=array(), $offset=0, $limit=20)
    {
        if(intval($assistantUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'assistantUid' => intval($assistantUid),
            'courseId'     => intval($courseId),
            'lessonId'     => intval($lessonId),
        );
        $arrAppends = array(
            "order by start_time",
            "limit $offset, $limit",
        );

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }
    /**
     * 获取该节课下该辅导员的所有学员列表数量
     *
     * @param  int $assistantUid 辅导员id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return int
     */
    public function getStudentCntByAssistant($assistantUid,$courseId,$lessonId)
    {
        if(intval($assistantUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $arrConds = array(
            'assistantUid' => intval($assistantUid),
            'courseId'     => intval($courseId),
            'lessonId'     => intval($lessonId),
        );

        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }

        return $sumRet;
    }

    /**
     * 获取课程该班级下所有学员列表
     *
     * @param  int $assistantUid 辅导员id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return int
     */
    public function getStudentListByCourseClass($courseId,$classId,$arrFields=array(), $offset=0, $limit=20)
    {
        if(intval($courseId) <= 0 || intval($classId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId classId:$classId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId'     => intval($courseId),
            'classId'      => intval($classId),
        );
        $arrAppends = array(
            "order by start_time",
            "limit $offset, $limit",
        );

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }
    /**
     * 获取课程该班级下所有学员数量
     *
     * @param  int $classId 辅导员id
     * @param  int $courseId 课程id
     * @return int
     */
    public function getStudentCntByCourseClass($courseId,$classId)
    {
        if(intval($courseId) <= 0 || intval($classId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId classId:$classId]");
            return false;
        }


        $arrConds = array(
            'courseId'     => intval($courseId),
            'classId'     => intval($classId),
        );

        $sumRet = 0;
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getCntByConds($index, $arrConds);
            $sumRet += intval($ret);
        }

        return $sumRet;
    }

    /**
     * 获取所有到课的学生列表
     *
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return array
     */
    public function getStudentListOnline($courseId,$lessonId)
    {
        if(intval($courseId) <= 0 || intval($lessonId) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'courseId'     => intval($courseId),
            'lessonId'     => intval($lessonId),
            'attendTime'   => array(0, '>'),
        );

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, array('studentUid'));
            $arrRet = array_merge($arrRet, $ret);
        }

        return $arrRet;
    }

    /**
     * 更新用户章节回放信息
     * @param $intStudentUid
     * @param $intLessonId
     * @param $ext
     * @return bool
     */
    public function updateLessonExt($intStudentUid, $intLessonId, $ext = array())
    {
        if (empty($intStudentUid) || empty($intLessonId) || empty($ext)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$intStudentUid lessonId:$intLessonId ext:" . json_encode($ext) . "]");

            return false;
        }
        $arrConds   = array(
            'studentUid' => $intStudentUid,
            'lessonId'   => $intLessonId,
        );
        $lessonInfo = $this->objDaoLessonDetail->getRecordByConds($intStudentUid, $arrConds, array('extData'));
        if (empty($lessonInfo)) {
            return false;
        }
        $arrFields = array();
        $extData   = $lessonInfo['extData'];
        foreach ($ext as $key => $value) {
            $extData[$key] = $value;
        }
        $arrFields['extData']    = json_encode($extData);
        $arrFields['updateTime'] = time();
        $res                     = $this->objDaoLessonDetail->updateByConds($intStudentUid, $arrConds, $arrFields);

        return $res;
    }

    /**
     * 获取指定班级的本课节所有学员情况(去除课程概念)
     * @param int      $lessonId
     * @param int      $classId
     * @param array    $arrFields
     * @param string   $order
     * @param string   $by
     * @param int      $offset
     * @param int      $limit
     * @return array
     */
    public function getLessonDetailStudentList($lessonId, $classId = 0, $arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 0)
    {
        if (intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'lessonId' => intval($lessonId),
            'status'   => array(self::STATUS_DELETED, '<>'),
        );
        if(intval($classId) > 0){
            $arrConds['classId']    = $classId;
        }
        //排序
        $orderBy = '';
        if($order != ''){
            $orderBy .= 'order by ' .$order . ' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
        //limit限制
        if($offset >= 0 && $limit > 0){
            $orderBy .= " limit $offset,$limit";
        }else if($limit > 0){
            $orderBy .= " limit $limit";
        }

        $arrAppends = ($orderBy != '') ? array($orderBy) : NULL;

        $arrRet = array();
        for($index = 0; $index < 20; $index++) {
            $ret = $this->objDaoLessonDetail->getListByConds($index, $arrConds, $arrFields, NULL, $arrAppends);
            foreach($ret as $v){
                $studentUid = intval($v['studentUid']);
                $arrRet[$studentUid] = $v;
            }
        }

        return $arrRet;
    }

    /**
     * 获取指定学生的未取消的待上课节列表
     * @param  int   $studentUid 学生uid
     * @param  int   $courseIdArr 课程id
     * @return array |false
     */
    public function getAllUnfinishLessonListByCourseIdArr($studentUid, $courseIdArr, $arrFields = array())
    {
        if (intval($studentUid) <= 0 ||!is_array($courseIdArr)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $courseIds = implode(",", $courseIdArr);
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'status'     => array(self::STATUS_CLASS_IN, '<='),
        );
        $arrConds[] = "course_id in ({$courseIds})";
        $arrAppends = "order by start_time";

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取学生课程详情
     * @param  int   $studentArr $lessonIdArr
     * @return array |false
     */
    public function getStudentLessonByStudentArr($arrParams, $arrFields = array())
    {
        if (!is_array($arrParams)) {
            Bd_Log::warning("Error:[param error]," .json_decode($arrParams));
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        foreach ($arrParams as $v) {
            $Conds .= "(student_uid={$v[0]} and lesson_id ={$v[1]}) ||";
            $studentUid = $v[0];
        }
        $Conds = substr($Conds, 0, -3);
        $arrConds[] = $Conds;
        $arrAppends = "order by start_time";

        $ret = $this->objDaoLessonDetail->getListByConds(intval($studentUid), $arrConds, $arrFields, NULL, $arrAppends);
        if (!$ret) {
            Bd_Log::warning("Error:[get lesson detail fail]," .json_decode($arrParams));
            return false;
        }

        return $ret;
    }
}
