<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: PDFTask.php
 * @author: huanghe <<EMAIL>>
 * @date: 2018/3/5 下午9:32
 * @brief:PDF任务DS
 */
class Hkzb_Ds_Fudao_PDFTask
{
    const ALL_FIELDS = 'taskId,taskParams,taskType,taskStatus,sign,comment,pdfUrl,createTime,updateTime,creatorUid';

    //任务状态--新建
    const STATUS_NEW = 0;
    //任务状态--完成
    const STATUS_FINISHED = 1;
    //任务状态--取消
    const STATUS_CANCELED = 2;
    //任务状态--失败/出错
    const STATUS_ERROR = 3;

    //PDF任务 redis队列 KEY
    const REDIS_QUEUE_KEY = 'zb_pdf_task_queue_v1';

    //任务状态描述
    public static $STATUS_DESC = array(
        self::STATUS_NEW => '新建',
        self::STATUS_FINISHED => '完成',
        self::STATUS_CANCELED => '取消',
        self::STATUS_ERROR => '失败'
    );

    /**
     * redis对象
     * @var Hk_Service_Redis
     */
    private $redisObj;


    /**
     * 创建任务, 并返回任务id，任务id为0则创建失败
     *
     * @param string $taskParams jsonencode过的参数
     * @param string $sign 任务md5签名，用于判断重复任务
     * @param integer $creatorUid 创建者uid
     * @param integer $taskType 任务类型
     * @return integer
     */
    public function createTask($taskParams, $sign, $creatorUid, $taskType = 0)
    {
        if (empty($sign) || strlen($sign) !== 32) {
            return 0;
        }

        $daoPDFTask = new Hkzb_Dao_Fudao_PDFTask();

        $daoPDFTask->startTransaction();

        $arrFields = array(
            'taskParams' => empty($taskParams) ? '' : $taskParams,
            'sign' => $sign,
            'creatorUid' => intval($creatorUid),
            'taskType' => intval($taskType),
            'createTime' => time(),
        );

        $ret = $daoPDFTask->insertRecords($arrFields);

        if ($ret === false) {
            $daoPDFTask->rollback();
            return 0;
        }

        $lastId = $daoPDFTask->getInsertId();

        if (!$this->putTaskToQueue($lastId) || !$daoPDFTask->commit()) {
            $lastId = 0;
            $daoPDFTask->rollback();
        }

        return $lastId;
    }


    /**
     * 根据任务id更新任务信息
     *
     * @param integer $taskId 任务id
     * @param array $arrFields 任务字段
     * @return boolean
     */
    public function updateTaskInfoByTaskId($taskId, $arrFields)
    {
        $taskId = intval($taskId);
        if ($taskId <= 0 || !is_array($arrFields)) {
            return false;
        }
        if (!isset($arrFields['updateTime'])) {
            $arrFields['updateTime'] = time();
        }
        $daoPDFTask = new Hkzb_Dao_Fudao_PDFTask();
        return $daoPDFTask->updateByConds(array('taskId' => $taskId), $arrFields, null, null);
    }

    /**
     * 根据任务id获取任务详情
     *
     * @param integer $taskId 任务id
     * @param array $arrFields 需要获取的任务信息字段
     * @return array|false
     */
    public function getTaskInfoByTaskId($taskId, $arrFields = NULL)
    {
        if ($taskId <= 0) {
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'taskId' => intval($taskId),
        );

        $ret = (new Hkzb_Dao_Fudao_PDFTask())->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 根据任务签名获取任务记录列表
     *
     * @param string $sign 任务签名
     * @param array $arrFields 需要获取的任务信息字段
     * @return array|false
     */
    public function getTaskInfoListBySign($sign, $arrFields = NULL)
    {
        if (empty($sign)) {
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'sign' => $sign
        );

        $arrAppends =  array(
            'LIMIT 100',
        );

        $ret = (new Hkzb_Dao_Fudao_PDFTask())->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if(!is_array($ret)){
            Bd_Log::warning('pdf task db error');
            return array();
        }

        return $ret;
    }

    /**
     * 从队列中获取单个任务
     *
     * @return array
     */
    public function getTaskInfoFromQueue()
    {
        $this->initRedis();
        $taskId = $this->redisObj->rpop(self::REDIS_QUEUE_KEY);
        if (empty($taskId) || $taskId <= 0) {
            return false;
        }
        return $this->getTaskInfoByTaskId($taskId);
    }

    /**
     * 将任务放置在队列中
     *
     * @param integer $taskId 任务id
     * @return boolean
     */
    public function putTaskToQueue($taskId)
    {
        $taskId = intval($taskId);
        if ($taskId <= 0) {
            return false;
        }
        $this->initRedis();

        return $this->redisObj->lpush(self::REDIS_QUEUE_KEY, $taskId);
    }

    /**
     * 初始化redis实例
     */
    private function initRedis()
    {

        if (null == $this->redisObj) {
            $this->redisObj =  Hk_Service_RedisClient::getInstance("zbcourse");
        }
    }


}