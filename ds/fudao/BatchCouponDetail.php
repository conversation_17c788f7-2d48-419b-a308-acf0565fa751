<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file BatchCouponDetail.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 批量优惠券记录
 *  
 **/

class Hkzb_Ds_Fudao_BatchCouponDetail {

    const ALL_FIELDS = 'id,studentUid,batchId,isAdd,createTime,updateTime,extData';
    //类型
    const IS_ADD    = 1;
    const IS_ADD_NO = 0;

    private $objDaoBatchCouponDetail;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoBatchCouponDetail = new Hkzb_Dao_Fudao_BatchCouponDetail();
    }

    /**
     * 新增课程记录
     *
     * @param  array  $arrParams 课程属性
     * @return bool true/false
     */
    public function addBatchCouponDetail($arrParams) {
        if(intval($arrParams['batchId']) <= 0 || intval($arrParams['studentUid']) <=0 ) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'        => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'batchId'           => isset($arrParams['batchId']) ? intval($arrParams['batchId']) : 0,
            'createTime'        => time(),
            'updateTime'        => time(),
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoBatchCouponDetail->insertRecords($arrFields);

        return $ret;
    }


    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getBatchCouponListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoBatchCouponDetail->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getBatchCouponDetailCntByConds($arrConds) {
        $ret = $this->objDaoBatchCouponDetail->getCntByConds($arrConds);
        return $ret;
    }


    /**
     * 更新优惠券状态
     *
     * @param  int  $CouponId    课程uid
     * @param  int  $studentUid  学生uid
     * @return bool true/false
     */
    public function updateBatchCouponDetailAdd($id) {
        if(intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrConds = array(
            'id'   => intval($id),
        );

        $arrFields['isAdd'] = self::IS_ADD;

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoBatchCouponDetail->updateByConds($arrConds, $arrFields);

        return $ret;
    }

}
