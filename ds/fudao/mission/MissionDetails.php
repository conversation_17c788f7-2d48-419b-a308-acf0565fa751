<?php

/**
 * 任务模块
 *
 * 任务分为三部分：任务资源，任务绑定关系，做题行为
 * 任务资源     ：查询任务信息，对外提供查询任务信息接口，与题库系统衔接
 * 任务绑定关系  ：任务与课程或者章节的绑定关系，与课程系统衔接
 * 做题行为     ：用户做题行为与任务情况，与用户系统，课程系统衔接
 *
 */

/**
 * @file    MissionDetails.php
 * <AUTHOR>
 * @data    2017-12-12
 * @desc    任务详情
 */
class Hkzb_Ds_Fudao_Mission_MissionDetails {

    const RELATION_STATUS_OK = 0;
    const RELATION_STATUS_DELETE = 1;

    // 操作人uid
    protected $_intOperatorUid;
    // 操作人姓名
    protected $_strOperatorName;



    private $_objDaoMissionDetails;





    const RELATION_FIELD = 'Id,missionId,studentUid,isFinish,finishTime,extData,createTime,updateTime,operatorUid,operator,deleted';

    public function __construct($intOperateUid = 0) {
        /**
         * 在这里初始化的目的：
         * 第一是避免在该类的任何一个方法里面都添加操作记录
         * 第二是后期可以做一些显示，例如查询操作人信息为空的情况下，是否
         * 限制操作
         */
        $this->_intOperatorUid = intval( $intOperateUid );

        $objDsUcloud = new Hk_Ds_User_Ucloud();

        $arrUserInfo = $objDsUcloud->getUserInfo( $this->_intOperatorUid, true );

        $this->_strOperatorName = $arrUserInfo[ 'uname' ];

        $this->_objDaoMissionDetails  = new Hkzb_Dao_Fudao_Mission_MissionDetails();

    }





    /**
     * @param $arrConds
     * @return false|int
     */
    public function getMissionDetailsCntByConds($arrConds){

        $ret = $this->_objDaoMissionDetails->getCntByConds($arrConds);
        return $ret;

    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param $offset
     * @param $limit
     * @return array|false
     */
    public function getMissionDetailsListByConds($arrConds, $arrFields = array(), $offset, $limit){
        if(empty($arrFields)) {
            $arrFields = explode(',', self::RELATION_FIELD);
        }

        $arrAppends = array(
            "order by finish_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoMissionDetails->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }


    /**
     * @param $arrConds
     * @param array $arrFields
     * @param $offset
     * @param $limit
     * @return array|false
     */
    public function getMissionDetailsRecordByConds($arrConds,$arrFields = array()){
        if(empty($arrFields)) {
            $arrFields = explode(',', self::RELATION_FIELD);
        }

        $ret = $this->_objDaoMissionDetails->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }




}