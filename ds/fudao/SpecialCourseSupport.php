<?php

/**
 * @file        SpecialCourseSupport.php
 * <AUTHOR>
 * @create_date 2018-03-19
 * @brief       特惠课拉新记录表
 *
 **/
class Hkzb_Ds_Fudao_SpecialCourseSupport
{
    // 删除状态
    const DELETED_STATUS_NO = 0;
    const DELETED_STATUS_YES = 1;
    static $deletedStatusMap = array(
        self::DELETED_STATUS_NO => '未删',
        self::DELETED_STATUS_YES => '已删',
    );

    // 处理状态
    const STATUS_0 = 1;
    const STATUS_1 = 2;
    static $StatusMap = array(
        self::STATUS_0 => '未分配',
        self::STATUS_1 => '分配',
    );


    private $_objDaoSpecialCourseSupport;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->_objDaoSpecialCourseSupport = new Hkzb_Dao_Fudao_SpecialCourseSupport();
    }

    /**
     * 新增记录
     */
    public function addRecord($arrParams)
    {

        $arrFields = array(
            'courseId' => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'studentUid' => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'status' => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'groupId' => isset($arrParams['groupId']) ? intval($arrParams['groupId']) : 0,
            'deleted' => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createTime' => time(),
            'updateTime' => time(),
        );

        $ret = $this->_objDaoSpecialCourseSupport->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }

        return $this->_objDaoSpecialCourseSupport->getInsertId();
    }

    /**
     * 更新记录
     */
    public function updateRecord($arrConds, $arrFields, $limit = 1)
    {

        if (empty($arrConds) || empty($arrFields)) {
            return false;
        }

        $arrAppends = array(
            'LIMIT ' . $limit,
        );

        $ret = $this->_objDaoSpecialCourseSupport->updateByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $ret) {
            Bd_Log::warning("Error:[update error], arrConds:[" . json_encode($arrConds) . "]" . "arrFields:[" . json_encode($arrFields) . "]");
            return false;
        }
        return $ret;
    }

    /**
     * 获取1条记录
     * @param $arrConds
     * @param array $arrFields
     * @return array|bool
     */
    public function getInfoByConds($arrConds, $arrFields = array())
    {
        if (empty($arrConds)) {
            return array();
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', Hkzb_Dao_Fudao_SpecialCourseSupport::$allFields);
        }

        $info = $this->_objDaoSpecialCourseSupport->getRecordByConds($arrConds, $arrFields);

        if ($info === false) {
            Bd_Log::warning("Error:[getInfoByConds], Detail:[conds:]" . json_encode($arrConds));
            return false;
        }
        return $info;
    }

    /**
     * 获取列表
     * @param $arrConds
     * @param array $arrFields
     * @return array|bool
     */
    public function getListByConds($arrConds, $arrFields = array())
    {
        if (empty($arrConds)) {
            return array();
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', Hkzb_Dao_Fudao_SpecialCourseSupport::$allFields);
        }

        $list = $this->_objDaoSpecialCourseSupport->getListByConds($arrConds, $arrFields);

        if ($list === false) {
            Bd_Log::warning("Error:[getListByConds], Detail:[conds:]" . json_encode($arrConds));
            return false;
        }
        return $list;
    }

    /**
     * 获取总记录数
     *
     * @param       $arrConds
     * @return array|false
     */
    public function getAssistantSmsRecordCnt($arrConds)
    {

        $ret = $this->_objDaoSpecialCourseSupport->getCntByConds($arrConds);

        return $ret;
    }
}
