<?php
/**
 * Created by PhpStorm.
 * User: s<PERSON>hua<PERSON>@zuoyebang.com
 * Time: 2017/10/31 19:09
 */

class Hkzb_Ds_Fudao_LmsLecture
{
    //状态
    const LECTURE_STATUS_TRANS_FAIL = 1;    // 转换失败
    const LECTURE_STATUS_CHECKING   = 2;    // 审核中
    const LECTURE_STATUS_NOT_PASS   = 3;    // 审核不通过
    const LECTURE_STATUS_PASSED     = 4;    // 审核通过
    static $LECTURE_STATUS_ARRAY = array(
        self::LECTURE_STATUS_TRANS_FAIL => '上传失败',
        self::LECTURE_STATUS_CHECKING   => '待审核',
        self::LECTURE_STATUS_NOT_PASS   => '审核不通过',
        self::LECTURE_STATUS_PASSED     => '审核通过',
    );

    const LECTURE_TYPE_PLUG = 1; // 插件
    const LECTURE_TYPE_LIVE = 2; // 主讲pc
    //const LECTURE_TYPE=1;  //讲义类型
    const LECTURE_TYPE_ENCLOSURE=3; //附件类型
    
    const OPERATOR_RESEARCH = 1; // 教研老师
    const OPERATOR_THEACHER = 2; // 主讲老师
    const OPERATOR_SCHEDULE= 3; // 排课老师

    const  ALL_FIELDS = 'lectureId,lectureName,lectureType,status,lectureStatus,subject,grade,operateId,operate,operateType,createTime,updateTime,deleted,extData,lessonId';

    private $objDaoLmsLecture;

    public function __construct() {
        $this->objDaoLmsLecture = new Hkzb_Dao_Fudao_LmsLecture();
    }

    /**
     * 新增
     * @param $arrParams
     * @return bool
     */
    public function addLecture($arrParams) {

        $arrFields = array(
            'lectureName'   => isset($arrParams['lectureName']) ? $arrParams['lectureName'] : '',
            'lectureType'   => isset($arrParams['lectureType']) ? $arrParams['lectureType'] : self::LECTURE_TYPE_LIVE,
            'lectureStatus' => isset($arrParams['lectureStatus']) ? $arrParams['lectureStatus'] : self::LECTURE_STATUS_CHECKING,
            'subject'       => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'grade'         => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'operateId'     => isset($arrParams['operateId']) ? intval($arrParams['operateId']) : 0,
            'operate'       => isset($arrParams['operate']) ? strval($arrParams['operate']) : '',
            'deleted'       => 0,
            'operateType'   =>isset($arrParams['operateType'])?$arrParams['operateType']:1,
            'status'        => isset($arrParams['status'])?$arrParams['status']:0,
            'createTime'    => time(),
            'updateTime'    => time(),
            'lessonId'      => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : array(),

        );
        $ret = $this->objDaoLmsLecture->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->objDaoLmsLecture->getInsertId();
        }
        return $ret;
    }

    /**
     * 单个获取
     * @param $lectureId
     * @param array $arrFields
     * @return bool
     */
    public function getLectureInfo($lectureId, $arrFields = array()) {
        if (intval($lectureId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lectureId:$lectureId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrCond = array(
            'lectureId' => intval($lectureId),
        );
        $ret = $this->objDaoLmsLecture->getRecordByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 数组获取
     * @param $arrLectureId
     * @param array $arrFields
     * @return bool
     */
    public function getArrLectureInfo($arrLectureId, $arrFields = array()) {
        if (empty($arrLectureId)) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $str = "lecture_id in (" . implode(',', $arrLectureId) . ")";
        $arrCond[] = $str;
        $ret = $this->objDaoLmsLecture->getListByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 更新
     * @param $lectureId
     * @param $arrParams
     * @return bool
     */
    public function updateLecture($lectureId, $arrParams) {
        if(intval($lectureId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lectureId:$lectureId]");
            return false;
        }
        $arrCond = array(
            'lectureId' => intval($lectureId),
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->objDaoLmsLecture->updateByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 根据多条件获取数目
     * @param $arrCond
     * @return mixed
     */
    public function getLectureCntByCond($arrCond) {

        $ret = $this->objDaoLmsLecture->getCntByConds($arrCond);
        return $ret;
    }

    /**
     * 根据多条件更新数据
     * @param $arrCond
     * @return mixed
     */
    public function updateLectureByCond($arrCond,$arrParams) {

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->objDaoLmsLecture->updateByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 根据多条件获取
     * @param $arrCond
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return mixed
     */
    public function getLectureListByCond($arrCond, $arrFields = array(), $offset = 0, $limit = 20) {

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoLmsLecture->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        return $ret;
    }
    
    /**
     * 单个获取
     * @param $arrCond
     * @param array $arrFields
     * @return bool
     */
    public function getLectureInfoByCond($arrCond, $arrFields = array()) {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
       
        $ret = $this->objDaoLmsLecture->getRecordByConds($arrCond, $arrFields);
        return $ret;
    }

}