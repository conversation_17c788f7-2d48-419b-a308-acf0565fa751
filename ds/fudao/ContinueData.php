<?php

/**
 * @file ContinueData.php
 * <AUTHOR>
 * @date 2017/07/27 10:43:56
 * @brief 学生续报表
 *
 **/
class  Hkzb_Ds_Fudao_ContinueData {
    const ALL_FIELDS = 'id,courseId,studentUid,retention,levelOne,levelTwo,levelThree,continueCourse,createTime,updateTime,extData';

    private $objDaoContinueData;

    private $orderBy = array(
        'create_time',
        'update_time'
    );

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoContinueData = new Hkzb_Dao_Fudao_ContinueData();
    }

    /**
     * 新增记录
     *
     * @param  mix $arrParams 属性
     * @return bool true/false
     */
    public function addContinueData($arrParams) {
        if ( 0 ) {
            Bd_Log::warning( "Error:[param error],Detail:[" . json_encode( $arrParams ) . "]" );
        }
        $arrFields = array(
            'courseId'       => isset( $arrParams[ 'courseId' ] ) ? intval( $arrParams[ 'courseId' ] ) : 0,
            'studentUid'     => isset( $arrParams[ 'studentUid' ] ) ? intval( $arrParams[ 'studentUid' ] ) : 0,
            'retention'      => isset( $arrParams[ 'retention' ] ) ? intval( $arrParams[ 'retention' ] ) : 0,
            'levelOne'       => isset( $arrParams[ 'levelOne' ] ) ? intval( $arrParams[ 'levelOne' ] ) : 0,
            'levelTwo'       => isset( $arrParams[ 'levelTwo' ] ) ? intval( $arrParams[ 'levelTwo' ] ) : 0,
            'levelThree'     => isset( $arrParams[ 'levelThree' ] ) ? intval( $arrParams[ 'levelThree' ] ) : 0,
            'continueCourse' => isset( $arrParams[ 'continueCourse' ] ) ? json_encode(
                $arrParams[ 'continueCourse' ]
            ) : '[]',
            'createTime'     => isset( $arrParams[ 'createTime' ] ) ? intval( $arrParams[ 'createTime' ] ) : 0,
            'updateTime'     => isset( $arrParams[ 'updateTime' ] ) ? intval( $arrParams[ 'updateTime' ] ) : 0,
            'extData'        => isset( $arrParams[ 'extData' ] ) ? json_encode( $arrParams[ 'extData' ] ) : '[]',
        );
        $ret       = $this->objDaoContinueData->insertRecords( $arrFields );
        if ( $ret ) {
            $ret = $this->objDaoContinueData->getInsertId();
        }

        return $ret;
    }

    /**
     * 更新
     *
     * @param  int $id
     * @param  array $arrParams
     * @return bool true/false
     */
    public function updateContinueData($id, $arrParams) {
        if ( intval( $id <= 0 ) ) {
            Bd_Log::warning( "Error:[param error],Detail:[id:$id]" );

            return false;
        }
        $arrConds     = array(
            'id' => intval( $id ),
        );
        $arrFields    = array();
        $arrAllFields = explode( ',', self::ALL_FIELDS );
        foreach ( $arrParams as $key => $value ) {
            if ( !in_array( $key, $arrAllFields ) ) {
                continue;
            }
            $arrFields[ $key ] = $value;
        }
        if ( isset( $arrFields[ 'continueCourse' ] ) ) {
            $arrFields[ 'continueCourse' ] = json_encode( $arrFields );
        }
        if ( isset( $arrFields[ 'extData' ] ) ) {
            $arrFields[ 'extData' ] = json_encode( $arrFields );
        }
        $arrFields[ 'updateTime' ] = time();
        $ret                       = $this->objDaoContinueData->updateByConds( $arrConds, $arrFields );

        return $ret;
    }

    /**
     * 获取记录
     *
     * @param  int $id
     * @return  mix $arrFields  属性
     */
    public function getContinueData($id, $arrFields = array()) {
        if ( intval( $id <= 0 ) ) {
            Bd_Log::warning( "Error:[param error],Detail:[id:$id]" );

            return false;
        }
        if ( empty( $arrFields ) ) {
            $arrFields = explode( ',', self::ALL_FIELDS );
        }
        $arrConds = array(
            'id' => $id,
        );
        $ret      = $this->objDaoContinueData->getRecordByConds( $arrConds, $arrFields );

        return $ret;
    }

    /**
     * 获取多个记录
     *
     * @param  array $id
     * @return  mix $arrFields  属性
     */
    public function getContinueDataArray($arrId, $arrFields = array()) {
        if ( intval( empty( $arrId ) ) ) {
            Bd_Log::warning( "Error:[param error],Detail:[arrId:json_encode($arrId)]" );

            return false;
        }
        if ( empty( $arrFields ) ) {
            $arrFields = explode( ',', self::ALL_FIELDS );
        }
        $arrConds = array();
        //根>据状态添加条件


        $arrConds[] = " id in " . implode( ',', $arrId ) . ")";
        $ret        = $this->objDaoContinueData->getListByConds( $arrConds, $arrFields );

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCntByConds($arrConds) {
        return $this->objDaoContinueData->getCntByConds( $arrConds );
    }

    /**
     * @param array $arrConds
     * @param array $arrFields
     * @param string $order
     * @param string $by
     * @param int $offset
     * @param int $limit
     * @return list|false
     */
    public function getListByConds($arrConds, $arrFields = array(), $order = '', $by = '', $offset = 0, $limit = 0) {
        if ( empty( $arrFields ) ) {
            $arrFields = explode( ',', self::ALL_FIELDS );
        }

        $orderBy = '';
        if ( $order != '' && in_array( $order, $this->orderBy ) ) {
            $orderBy .= 'order by ' . $order . ' ';
            $orderBy .= ( $by == 'desc' ) ? 'desc' : 'asc';
        }
        if ( $offset >= 0 && $limit > 0 ) {
            $orderBy .= " limit $offset,$limit";
        } else if ( $limit > 0 ) {
            $orderBy .= " limit $limit";
        }
        $arrAppends = ( $orderBy != '' ) ? array( $orderBy ) : null;
        $ret        = $this->objDaoContinueData->getListByConds( $arrConds, $arrFields, null, $arrAppends );

        return $ret;
    }

    public function getLevelOneStudentNumByCourseId($intCourseId) {
        if ( intval( $intCourseId ) <= 0 ) {
            return 0;
        }

        $arrConds = array(
            'courseId' => $intCourseId,
            'levelOne' => 1,
        );

        return $this->getCntByConds( $arrConds );
    }

    public function getLevelTwoStudentNumByCourseId($intCourseId) {
        if ( intval( $intCourseId ) <= 0 ) {
            return 0;
        }

        $arrConds = array(
            'courseId' => $intCourseId,
            'levelTwo' => 1,
        );

        return $this->getCntByConds( $arrConds );
    }

    public function getLevelThreeStudentNumByCourseId($intCourseId) {
        if ( intval( $intCourseId ) <= 0 ) {
            return 0;
        }

        $arrConds = array(
            'courseId'   => $intCourseId,
            'levelThree' => 1,
        );

        return $this->getCntByConds( $arrConds );
    }

}