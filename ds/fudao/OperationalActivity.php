<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:OperationalActivity.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2017/2/14
 * @time: 16:31
 * @desc:
 */

class Hkzb_Ds_Fudao_OperationalActivity
{
    public static $ACTIVITY_NORMAL = 0;
    public static $ACTIVITY_DELETED = 1;

    public static $ACTIVITY_ASSORT_SUBJECT = 1;
    public static $ACTIVITY_ASSORT_GRADE = 2;

    public function __construct()
    {
        $this->_objDao = new Hkzb_Dao_Fudao_OperationalActivity();
    }

    /**
     * 创建运营活动
     * @param $arrFields
     * @return bool
     */
    public function createActivity($arrFields)
    {
        $arrInsert = array(
            'actName'     => isset($arrFields['actName']) ? trim($arrFields['actName']) : '',
            'assortType'  => isset($arrFields['assortType']) ? intval($arrFields['assortType']) : 0,
            'gradeList'   => isset($arrFields['gradeList']) ? trim($arrFields['gradeList']) : '',
            'subjectList' => isset($arrFields['subjectList']) ? trim($arrFields['subjectList']) : '',
            'extData' => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
            'createTime'  => time(),
        );
        if (empty($arrInsert['actName']) || empty($arrInsert['assortType']) || (empty($arrInsert['gradeList']) && empty($arrInsert['subjectList']))) {
            return false;
        }
        $ret = $this->_objDao->insertRecords($arrInsert);

        return $ret;
    }

    /**
     * 更新活动信息
     * @param $intActivityId
     * @param array $arrFields
     * @param array $ext
     * @return bool
     */
    public function updateActivity($intActivityId, $arrFields = array(), $ext = array())
    {
        if (empty($intActivityId) || (empty($arrFields) && empty($ext))) {
            return false;
        }
        $activityInfo = self::getActivityInfo($intActivityId, array('extData'));
        if (empty($activityInfo)) {
            return false;
        }
        $arrConds = array(
            'actId' => intval($intActivityId),
        );
        foreach ($arrFields as $key => $value) {
            if (!in_array($key, Hkzb_Dao_Fudao_OperationalActivity::$arrFields)) {
                unset($arrFields[$key]);
            }
        }
        if (!empty($ext)) {
            $extData = $activityInfo['extData'];
            foreach ($ext as $key => $value) {
                $extData[$key] = $value;
            }
            $arrFields['extData'] = json_encode($extData);
        }
        if (empty($arrFields)) {
            return false;
        }
        $res = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 获取活动信息
     * @param $intActivity
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getActivityInfo($intActivity, $arrFields = array())
    {
        if (empty($intActivity)) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_OperationalActivity::$arrFields;
        }
        $arrConds = array(
            'actId' => intval($intActivity),
        );
        $res      = $this->_objDao->getRecordByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 删除活动
     * @param $intActivity
     * @return bool
     */
    public function deleteActivity($intActivity)
    {
        if (empty($intActivity)) {
            return false;
        }
        $arrConds  = array(
            'actId' => intval($intActivity),
        );
        $arrFields = array(
            'deleted' => self::$ACTIVITY_DELETED,
        );
        $res       = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 获取活动列表
     * @param array $arrFields
     * @param int $pn
     * @param int $rn
     * @return array|false
     */
    public function getActivityList($intStatus = false, $intDeleted = false, $arrFields = array(), $pn = 0, $rn = 5)
    {
        $arrConds = array(
            1 => 1,
        );
        if ($intStatus !== false) {
            $arrConds['status'] = intval($intStatus);
        }
        if ($intDeleted !== false) {
            $arrConds['deleted'] = intval($intDeleted);
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_OperationalActivity::$arrFields;
        }
        $arrAppends = array(
            "order by create_time desc limit $pn, $rn",
        );
        $res        = $this->_objDao->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $res;
    }

    public function getActivityCnt()
    {
        $arrConds = array(
            'deleted' => self::$ACTIVITY_NORMAL,
        );
        $cnt      = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }
}