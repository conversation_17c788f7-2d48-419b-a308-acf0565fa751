<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2020/3/18
 * Time: 14:12
 */
class Hkzb_Ds_Fudao_Inclass_TeacherCacheOld {

    private $_objStored;

    public function __construct() {
        $this->_objStored = Hkzb_Util_StoredService::getZhiboInstance();
    }

    private static $INCLASS_CACHE_LESSON_TEACHER_ACTION_PRE = 'INCLASS_CACHE_LESSON_TEACHER_ACTION_LIST_';

    // 点名
    const TEACHER_INCLASS_ACTION_CALLNAME = 1;
    // 表扬
    const TEACHER_INCLASS_ACTION_PRAISE = 2;
    // 红包
    const TEACHER_INCLASS_ACTION_REDENVELOPE = 3;
    // 互动题
    const TEACHER_INCLASS_ACTION_EXERCISE = 4;
    // 是否卡
    const TEACHER_INCLASS_ACTION_YESNOCARD = 5;
    // 抢麦
    const TEACHER_INCLASS_ACTION_MIC = 6;
    // 签到
    const TEACHER_INCLASS_ACTION_SIGN = 7;
    // 堂堂测
    const TEACHER_INCLASS_ACTION_EXAM = 8;
    // 眼保健操
    const TEACHER_INCLASS_ACTION_EYE_EXERCISES = 9;

    public static $TEACHER_INCLASS_ACTION_MAP = array(
        self::TEACHER_INCLASS_ACTION_CALLNAME => '点名',
        self::TEACHER_INCLASS_ACTION_PRAISE => '表扬',
        self::TEACHER_INCLASS_ACTION_REDENVELOPE => '红包',
        self::TEACHER_INCLASS_ACTION_EXERCISE => '互动题',
        self::TEACHER_INCLASS_ACTION_YESNOCARD => '是否卡',
        self::TEACHER_INCLASS_ACTION_MIC => '抢麦',
        self::TEACHER_INCLASS_ACTION_SIGN => '签到',
        self::TEACHER_INCLASS_ACTION_EXAM => '堂堂测',
        self::TEACHER_INCLASS_ACTION_EYE_EXERCISES => '眼保健操',
    );

    /**
     * 添加教师课堂行为
     *
     * @param $intLesson int 章节id
     * @param $arrInput
     * 传入值格式：
     * array(
     *    'action' =>  int，标示行为类型
     *    'message' => array
     * )
     *
     * @return bool 添加成功或者失败
     */
    public function addTeacherAction( $intLesson, $arrInput ) {
        $intAction = intval($arrInput[ 'action' ]);

        if (!isset( self::$TEACHER_INCLASS_ACTION_MAP[ $intAction ] )) {
            return false;
        }

        $arrMsg = $arrInput[ 'message' ];

        $strKey = self::$INCLASS_CACHE_LESSON_TEACHER_ACTION_PRE . $intLesson;

        $arrValue = array(
            'action' => $intAction,
            'time' => time(),
            'message' => $arrMsg,
        );
        $strValue = json_encode($arrValue);

        $this->_objStored->lpush($strKey, $strValue);

        $intExpire = 3600 * 24 * 30;
        $this->_objStored->expire($strKey, $intExpire);

        return true;
    }

    /**
     * 根据章节id获取老师的行为列表
     *
     * @param $intLesson int 章节id
     *
     * @return array 行为列表
     * 返回值格式 二维数组
     * array(
     *     array(
     *         'action' =>  int，标示行为类型
     *         'time'    => int 发生时间
     *         'message' => array 行为信息
     *     ),
     * )
     *
     */
    public function getActionListByLessonId( $intLesson ) {
        $strKey = self::$INCLASS_CACHE_LESSON_TEACHER_ACTION_PRE . $intLesson;

        $intLength = $this->_objStored->llen($strKey);
        if($intLength > 800){
            $intLength = 800;
        }
        $arrList = $this->_objStored->lrange($strKey, 0, $intLength);

        Bd_Log::notice("get actionList from redis key is " . json_encode($arrList) . "lessonId is " . $intLesson);

        $arrOutput = array();

        // 格式化返回值
        foreach ($arrList as $value) {

            $arrValue = json_decode($value, true);

            $intAction = intval($arrValue[ 'action' ]);
            $intTime = intval($arrValue[ 'time' ]);
            $arrMessage = $arrValue[ 'message' ];

            $arrOutput[] = array(
                'action' => $intAction,
                'time' => $intTime,
                'message' => $arrMessage,
            );
        }

        return $arrOutput;
    }


}
