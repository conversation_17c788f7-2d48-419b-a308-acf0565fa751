<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: UserGoodsPrivilege.php
 * @date: 2017/6/7
 * @time: 15:58
 * @desc:
 */

class Hkzb_Ds_Fudao_UserGoodsPrivilege
{
    const PRIVILEGE_STATUS_NORMAL  = 0;//商品状态正常
    const PRIVILEGE_STATUS_INVALID = 1;//商品失效
    public static $fontPrivilegeConfList = array(
        array(
            'privilegeType'    => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_TYPE_PRIVILEGE,
            'privilegeSubtype' => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_COLORFUL_WORD_PRIVILEGE,
            'topColor'         => '#39e5a2',
            'bottomColor'      => '#13c0eb',
        )
    );
    //数据模版
    public $subTypeDataTemp = array(
        Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_AVATAR_ORNAMENT => array(
            'ornament' => array('extData','ornament'),//路径
        ),

    );
    public function __construct()
    {
        $this->_keyPrefix = 'zhiboke_ds_usergoodsprivilege_';
        $this->_memCache  = Hk_Service_Memcached::getInstance("zhiboke");
        $this->_objDao    = new Hkzb_Dao_Fudao_UserGoodsPrivilege();
    }

    /**
     * 添加特权信息
     * @param $arrFields
     * @return bool
     */
    public function addUserGoodsPrivilege($arrFields)
    {
        $arrData = array(
            'uid'              => isset($arrFields['uid']) ? intval($arrFields['uid']) : 0,
            'goodsId'          => isset($arrFields['goodsId']) ? intval($arrFields['goodsId']) : 0,
            'goodsName'        => isset($arrFields['goodsName']) ? trim($arrFields['goodsName']) : '',
            'privilegeType'    => isset($arrFields['privilegeType']) ? intval($arrFields['privilegeType']) : 0,
            'privilegeSubtype' => isset($arrFields['privilegeSubtype']) ? intval($arrFields['privilegeSubtype']) : 0,
            'chargeType'       => isset($arrFields['chargeType']) ? intval($arrFields['chargeType']) : 0,
            'totalCnt'         => isset($arrFields['totalCnt']) ? intval($arrFields['totalCnt']) : 0,
            'leftCnt'          => isset($arrFields['leftCnt']) ? intval($arrFields['leftCnt']) : 0,
            'startTime'        => isset($arrFields['startTime']) ? intval($arrFields['startTime']) : 0,
            'endTime'          => isset($arrFields['endTime']) ? intval($arrFields['endTime']) : 0,
            'createTime'       => time(),
            'extData'          => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
        );
        if ($arrData['uid'] <= 0 || $arrData['goodsId'] <= 0 || empty($arrData['goodsName']) || $arrData['privilegeType'] <= 0 || $arrData['privilegeSubtype'] < 0 || $arrData['chargeType'] <= 0) {
            return false;
        }
        $ret = $this->_objDao->insertRecords($arrData);
        if ($ret) {
            $ret = $this->_objDao->getInsertId();
            //删缓存
            $key = $this->_parseKey($arrData['uid']);
            $this->_memCache->delete($key);
        }

        return $ret;    //false则直接返回，否则返回相应的insertId
    }
    
    /*
     * @brief 获取用户头像挂件 
     * @param uid
     * @return bool || array
     * */
    public function getUserAvatarPrivilege( $intUid )
    {
        if(empty($intUid)){
            return false;
        }
        
        $keyAva = 'zhiboke_ds_usergoodsprivilege_ava_'.$intUid;
        $cacheAva = $this->_memCache->get($keyAva);
        if(!empty($cacheAva)){
            Bd_Log::addNotice('privilege_avatar_cache_hit' , $intUid);
            return json_decode(utf8_encode($cacheAva) , true);
        }
        
        //获取学生头像挂件
        $arrConds = array(
            'uid'              => $intUid,
            'chargeType'       => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NEWTIME,
            'privilegeSubtype' => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_AVATAR_ORNAMENT,
            'startTime'        => array(time(), '<'),
            'endTime'          => array(time(), '>'),
        );
        $arrFields  = Hkzb_Dao_Fudao_UserGoodsPrivilege::$arrFields;
        $res        = $this->_objDao->getListByConds($arrConds, $arrFields);
        if( empty($res) 
            || !is_array($res[0]) 
            || !isset($res[0]['endTime']) 
        ){
            return false;
        }
        
        $cacheAva = $res[0]; 
        $expireTime = intval($cacheAva['endTime']) - time();
        //缓存时间为头像剩余可使用时间
        $cacheSetRes = $this->_memCache->set($keyAva, json_encode($cacheAva), $expireTime );
        if($cacheSetRes !== false){
            Bd_Log::addNotice('privilege_avatar_cache_set' , $intUid.'_'.$expireTime);
        }
        
        return $cacheAva;
    }

    /*
     * @brief 获取用户头像挂件 
     * @param uid
     * @return bool || array
     * */
    public function getUserAvatarPrivilegeNoCache( $intUid )
    {
        if(empty($intUid)){
            return false;
        }
        //获取学生头像挂件
        $arrConds = array(
            'uid'              => $intUid,
            'chargeType'       => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NEWTIME,
            'privilegeSubtype' => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_AVATAR_ORNAMENT,
            'startTime'        => array(time(), '<'),
            'endTime'          => array(time(), '>'),
        );
        $arrFields  = Hkzb_Dao_Fudao_UserGoodsPrivilege::$arrFields;
        $arrAppends = array(
            'order by create_time desc',
        );
        $res        = $this->_objDao->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if( empty($res) 
            || !is_array($res[0]) 
            || !isset($res[0]['endTime']) 
        ){
            return false;
        }
        
        $cacheAva = $res[0]; 
        return $cacheAva;
    }
    /**
     * 按消耗类型获取用户有效特权
     * @param $intUid
     * @param $intChargeType
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getUserGoodsPrivilegeList($intUid, $intChargeType, $arrFields = array())
    {
        if ($intUid <= 0 || $intChargeType <= 0) {
            return false;
        }
        if ($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_COUNT) {
            $arrConds = array(
                'uid'        => $intUid,
                'chargeType' => $intChargeType,
                'status'     => self::PRIVILEGE_STATUS_NORMAL,
                'leftCnt'    => array(0, '>'),
            );
        } elseif ($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_TIME || $intChargeType === Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NEWTIME) {
            $arrConds = array(
                'uid'        => $intUid,
                'chargeType' => $intChargeType,
                'startTime'  => array(time(), '<'),
                'endTime'    => array(time(), '>'),
            );
        } elseif($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NOTIME){
            $arrConds = array(
                'uid'        => $intUid,
                'chargeType' => $intChargeType,
            );
        }else {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserGoodsPrivilege::$arrFields;
        }
        $arrAppends = array(
            'order by create_time asc',
        );

        $res        = $this->_objDao->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $res;
    }

    /**
     * 获取用户处于有效期内的某种商品数量
     * @param $intUid
     * @param $intGoodsId
     * @param $intChargeType
     * @return bool|false|int
     */
    public function getUserSpecificGoodsValidCnt($intUid, $intGoodsId, $intChargeType)
    {
        if ($intUid <= 0 || $intGoodsId <= 0 || $intChargeType <= 0) {
            Bd_Log::warning("Error:[getUserSpecificGoodsValidCnt] detail:[$intUid][$intGoodsId][$intChargeType]");
            return false;
        }
        $intChargeType = intval($intChargeType);
        if ($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_COUNT) {
            $arrConds = array(
                'uid'        => $intUid,
                'goodsId'    => $intGoodsId,
                'chargeType' => $intChargeType,
                'status'     => self::PRIVILEGE_STATUS_NORMAL,
                'leftCnt'    => array(0, '>'),
            );
        } elseif ($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_TIME || $intChargeType === Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NEWTIME) {
            $arrConds = array(
                'uid'        => $intUid,
                'goodsId'    => $intGoodsId,
                'chargeType' => $intChargeType,
                'startTime'  => array(time(), '<'),
                'endTime'    => array(time(), '>'),
            );
        } elseif($intChargeType == Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NOTIME){
            $arrConds = array(
                'uid'        => $intUid,
                'goodsId'    => $intGoodsId,
                'chargeType' => $intChargeType,
            );
        }else {
            return false;
        }
        $cnt = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }

    /**
     * 获取用户当前有效的特权信息
     * @param $intUid
     * @return array|bool
     */
    public function getUserCurPrivilege($intUid)
    {
        if ($intUid <= 0) {
            return false;
        }
        $key = $this->_parseKey($intUid);
        $ret = $this->_memCache->get($key);
        if (!empty($ret)) {
            $ret = json_decode(utf8_encode($ret), true);

            return $ret;
        }
        $arrOutput = array(
            Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_GET_MIKE_PRIVILEGE      => array(),//抢麦
            Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_COLORFUL_WORD_PRIVILEGE => array(),//聊天字体
            Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_AVATAR_ORNAMENT => array(),//挂件
            Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_YIKE_PRIVATE => array(),//一课专属
        );
        //获取各类特权信息
        $consumeTypeTime = self::getUserGoodsPrivilegeList($intUid, Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_TIME);
        if ($consumeTypeTime === false) {
            Bd_Log::warning("get user privilege list fail, uid: $intUid, charge type: time");

            return false;
        }
        $consumeTypeNewTime = self::getUserGoodsPrivilegeList($intUid, Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NEWTIME);
        if ($consumeTypeNewTime === false) {
            Bd_Log::warning("get user privilege list fail, uid: $intUid, charge type: time");

            return false;
        }
        //暂时合并
        $consumeTypeTime = array_merge($consumeTypeTime, $consumeTypeNewTime);

        $consumeTypeNum = self::getUserGoodsPrivilegeList($intUid, Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_COUNT);
        if ($consumeTypeNum === false) {
            Bd_Log::warning("get user privilege list fail, uid: $intUid, charge type: num");

            return false;
        }
        //获取时间无限制特权
        $consumeTypeNoTime = self::getUserGoodsPrivilegeList($intUid, Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_NOTIME);
        if ($consumeTypeNoTime === false) {
            Bd_Log::warning("get user privilege list fail, uid: $intUid, charge type: num");

            return false;
        }
        if (!empty($consumeTypeTime)) {
            //相同特权 最后购入的会覆盖之前的
            foreach ($consumeTypeTime as $t) {
                //if (empty($arrOutput[$t['privilegeSubtype']])) {
                    $arrOutput[$t['privilegeSubtype']] = array(
                        'goodsName'        => $t['goodsName'],
                        'privilegeType'    => $t['privilegeType'],
                        'privilegeSubtype' => $t['privilegeSubtype'],
                        'chargeType'       => $t['chargeType'],
                        'startTime'        => $t['startTime'],
                        'endTime'          => $t['endTime'],
                        'extData'          => $t['extData'],
                        'goodsId'          => $t['goodsId'],
                    );
                //}
                //var_dump($t);
            }
        }
        if (!empty($consumeTypeNum)) {
            foreach ($consumeTypeNum as $t) {
                if (empty($arrOutput[$t['privilegeSubtype']])) {
                    $arrOutput[$t['privilegeSubtype']] = array(
                        'privilegeType'    => $t['privilegeType'],
                        'privilegeSubtype' => $t['privilegeSubtype'],
                        'chargeType'       => $t['chargeType'],
                        'totalCnt'         => $t['totalCnt'],
                        'leftCnt'          => $t['leftCnt'],
                        'extData'          => $t['extData'],
                        'goodsId'          => $t['goodsId'],
                    );
                } else {
                    $arrOutput[$t['privilegeSubtype']]['totalCnt'] += $t['totalCnt'];
                    $arrOutput[$t['privilegeSubtype']]['leftCnt']  += $t['leftCnt'];
                }
            }
        }
        if (!empty($consumeTypeNoTime)) {
            //相同特权 最后购入的会覆盖之前的
            foreach ($consumeTypeNoTime as $t) {
                $arrOutput[$t['privilegeSubtype']] = array(
                    'goodsName'        => $t['goodsName'],
                    'privilegeType'    => $t['privilegeType'],
                    'privilegeSubtype' => $t['privilegeSubtype'],
                    'chargeType'       => $t['chargeType'],
                    'startTime'        => $t['startTime'],
                    'endTime'          => $t['endTime'],
                    'extData'          => $t['extData'],
                );
            }
        }
        //写缓存
        $this->_memCache->set($key, json_encode($arrOutput), 86400);
        return $arrOutput;
    }

    /**
     * 获取单个权限信息
     * @param $intUid
     * @param int $subType
     * @return array
     */
    public function getUserCurPrivilegeBySubType($intUid, $subType = 0){
        if ($intUid <= 0 || $subType <= 0 ) {
            return [];
        }
        $userPrivilege = $this->getUserCurPrivilege($intUid);
        if(isset($userPrivilege[$subType])){
            $userSubTypePrivilege = $userPrivilege[$subType];
            if(isset($this->subTypeDataTemp[$subType])) {
                //根据模板获取需要放到外层的数据
                foreach($this->subTypeDataTemp[$subType] as $key => $temp){
                    foreach($temp as $keyName){
                        if (!isset($midPrivilege)) {
                            $midPrivilege = $userSubTypePrivilege;
                        }
                        $userSubTypePrivilege[$key] = $midPrivilege[$keyName];
                        $midPrivilege = $userSubTypePrivilege[$key];
                    }
                }
            }
            return $userSubTypePrivilege;
        }
        return [];
    }

    /**
     * 阿喀琉斯版本
     * 获取单个权限信息
     * @param $intUid
     * @param int $subType
     * @return array
     */
    public function getUserCurPrivilegeBySubTypeV2($intUid, $subType = 0){
        if ($intUid <= 0 || $subType <= 0 ) {
            return [];
        }
        $ret = Liveservice_Acls_Student::getUserPrivilege([$intUid]);
        if(empty($ret) || $ret ===false){
            Bd_Log::warning("Liveservice_Acls_Student::getUserPrivilege failed uid = $intUid");
        }
        $userPrivilege = $ret[$intUid];
        if(isset($userPrivilege[$subType])){
            $userSubTypePrivilege = $userPrivilege[$subType];
            if(isset($this->subTypeDataTemp[$subType])) {
                //根据模板获取需要放到外层的数据
                foreach($this->subTypeDataTemp[$subType] as $key => $temp){
                    foreach($temp as $keyName){
                        if (!isset($midPrivilege)) {
                            $midPrivilege = $userSubTypePrivilege;
                        }
                        $userSubTypePrivilege[$key] = $midPrivilege[$keyName];
                        $midPrivilege = $userSubTypePrivilege[$key];
                    }
                }
            }
            return $userSubTypePrivilege;
        }
        return [];
    }

    /**
     * 消耗抢麦特权
     * @param $intUid
     * @return bool
     */
    public function consumeMicPrivilege($intUid)
    {
        if ($intUid <= 0) {
            return false;
        }
        $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
        $db     = Hk_Service_Db::getDB($dbName);
        if ($db === false) {
            Bd_Log::warning("get db connect fail, dbName: $dbName");

            return false;
        }
        $ret = $db->startTransaction();
        if ($ret === false) {
            Bd_Log::warning("start transaction fail");

            return false;
        }
        try {
            $arrConds  = array(
                'uid'              => $intUid,
                'chargeType'       => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_CONSUMETYPE_COUNT,
                'status'           => self::PRIVILEGE_STATUS_NORMAL,
                'privilegeType'    => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_TYPE_PRIVILEGE,
                'privilegeSubtype' => Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_GET_MIKE_PRIVILEGE,
                'leftCnt'          => array(0, '>'),
            );
            $arrFields = array(
                'left_cnt = left_cnt - 1',
                'status' => self::PRIVILEGE_STATUS_INVALID,
            );
            $res       = $this->_objDao->updateByConds($arrConds, $arrFields);
            if ($res === false) {
                throw  new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
        } catch (Hk_Util_Exception $e) {
            $errNo  = $e->getErrNo();
            $errMsg = $e->getErrMsg();
            Bd_Log::warning($errMsg, $errNo);
            $ret = $db->rollback();
            if ($ret === false) {
                Bd_Log::warning("db rollback fail, dbName: $dbName");

                return false;
            }
        }
        $ret = $db->commit();
        if ($ret === false) {
            Bd_Log::warning("db commit fail, dbName: $dbName");

            return false;
        }
        //删缓存
        $key = $this->_parseKey($intUid);
        $this->_memCache->delete($key);

        return true;
    }

    private function _parseKey($studentUid)
    {
        $keyIdx = $this->_keyPrefix . $studentUid;

        return $keyIdx;
    }

    /**
     * 根据主键ID获取特权信息
     *
     * <AUTHOR>
     * @param int $privilegeId 特权表主键ID
     * @return array|bool|false
     */
    public function getGoodsPrivilegeById($privilegeId)
    {
        if ($privilegeId <= 0) {
            return false;
        }

        $arrFields  = Hkzb_Dao_Fudao_UserGoodsPrivilege::$arrFields;
        $result = $this->_objDao->getRecordByConds(array('id' => $privilegeId), $arrFields);

        return $result;
    }

    /**
     * 根据主键更改特权信息
     *
     * <AUTHOR>
     * @param int $privilegeId 特权表主键ID
     * @param array $arrData 需要更改的信息
     * @return bool
     */
    public function updateGoodsPrivilegeById($privilegeId, $arrData)
    {
        if ($privilegeId <= 0) {
            return false;
        }

        //当前可以修改的字段
        $updateFields = array('status');

        //检查更改的数组Key
        foreach ($arrData as $field => $v) {
            if (!in_array($field, $updateFields)) {
                unset($arrData[$field]);
            }
        }
        if (empty($arrData)) {
            return false;
        }

        $arrData['updateTime'] = time();
        $result = $this->_objDao->updateByConds(array('id' => $privilegeId), $arrData);

        return $result;
    }
}
