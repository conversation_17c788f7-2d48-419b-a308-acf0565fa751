<?php
/**
 * @file StudentLog.php
 * <AUTHOR>
 * @date 2017-06-13
 * @brief 学生记录
 */
class Hkzb_Ds_Fudao_StudentLog {

    private $objDaoStudentLog;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoStudentLog = new Hkzb_Dao_Fudao_StudentLog();
    }

    /**
     * 新增记录
     *
     * @param  array  $arrParams 属性
     * @return bool true/false
     */
    public function addStudentLog($arrParams) {

        $arrFields = [
            'studentUid'   => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'courseId'     => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'lessonId'     => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'action'       => isset($arrParams['action']) ? intval($arrParams['action']) : 0,
            'message'      => isset($arrParams['message']) ? strval($arrParams['message']) : '',
            'createTime'   => (intval($arrParams['createTime']) > 0) ? intval($arrParams['createTime']) : time(),
        ];

        $ret = $this->objDaoStudentLog->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 获取日志记录
     *
     * @return  mix
     */
    public function getStudentLogListByConds($arrConds, $arrFields = array(), $arrAppends = array())
    {
        if(empty($arrFields) || ! is_array($arrFields)){
            $arrFields = Hkzb_Dao_Fudao_StudentLog::$allFields;
        }

        if(empty($arrAppends) || ! is_array($arrAppends)){
            $arrAppends = array(
                'ORDER BY create_time desc',
                'LIMIT 100',
            );
        }
        $ret = $this->objDaoStudentLog->getListByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }
}
