<?php
/**
 * Created by Growth AutoCode.
 * User: l<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/01/14
 * Time: 03:57
 */

class Qdlib_Ds_Ad_QudaoKuaishouCampaign
{
    const ALL_FIELDS = 'campaignId,account,channel,campaignName,systemStatus,putStatus,campaignType,thirdCreateTime,thirdUpdateTime,createTime,updateTime,adMode';
    const DELETE_DEL = 1;

    protected $objDaoQudaoKuaishouCampaign;

    public function __construct()
    {
        $this->objDaoQudaoKuaiShouCampaign = new Qdlib_Dao_Ad_QudaoKuaishouCampaign();
    }

    //新增
    public function addQudaoKuaiShouCampaign($arrParams) {
        $arrFields = array(
            'campaignId'      => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'account'         => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'channel'         => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'campaignName'    => isset($arrParams['campaignName']) ? strval($arrParams['campaignName']) : '',
            'systemStatus'    => isset($arrParams['systemStatus']) ? intval($arrParams['systemStatus']) : 0,
            'putStatus'       => isset($arrParams['putStatus']) ? intval($arrParams['putStatus']) : 0,
            'campaignType'    => isset($arrParams['campaignType']) ? intval($arrParams['campaignType']) : 0,
            'thirdCreateTime' => isset($arrParams['thirdCreateTime']) ? intval($arrParams['thirdCreateTime']) : 0,
            'thirdUpdateTime' => isset($arrParams['thirdUpdateTime']) ? intval($arrParams['thirdUpdateTime']) : 0,
            'createTime'      => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'      => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'adMode'             => isset($arrParams['adMode']) ? intval($arrParams['adMode']) : 0,

        );
        $onDup = $onDup ?? $arrFields;
        unset($onDup['campaignId']);
        unset($onDup['createTime']);

        $result = $this->objDaoQudaoKuaiShouCampaign->insertRecords($arrFields,null,$onDup);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaokuaiShouCampaign', 'addQudaokuaiShouCampaign', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoQudaoKuaiShouCampaign->getInsertId();
        return $id > 0 ? $id : true;
    }

    //编辑
    public function updateQudaoKuaiShouCampaign($arrConds = null, $arrParams = []) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoQudaoKuaiShouCampaign->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaokuaiShouCampaign', 'updateQudaokuaiShouCampaign', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoKuaiShouCampaignById($id, $arrFields) {
        $arrConds = ['campaignId' => $id];
        return $this->updateQudaoKuaiShouCampaign($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoKuaiShouCampaign($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoKuaiShouCampaign($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoKuaiShouCampaignInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['campaignId'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoKuaiShouCampaign->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaokuaiShouCampaign', 'getQudaokuaiShouCampaignInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoKuaiShouCampaignList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoKuaiShouCampaign->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaokuaiShouCampaign', 'getQudaokuaiShouCampaignList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoKuaiShouCampaignTotal($arrConds = null)
    {
        $res = $this->objDaoQudaoKuaiShouCampaign->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaokuaiShouCampaign', 'getQudaokuaiShouCampaignTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}