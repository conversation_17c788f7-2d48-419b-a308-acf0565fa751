<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/03/13
 * Time: 14:08
 */
class Qdlib_Ds_Ad_QudaoAdsObjective
{
    const ALL_FIELDS = 'id,channel,name,scope,informType,objectiveType,paramType,param,informMsg,targeting,ignoreFields,creator,operator,status,deleted,createTime,updateTime,params';
    const DELETE_DEL = 1;

    private $_objDaoQudaoAdsObjective;

    public function __construct()
    {
        $this->_objDaoQudaoAdsObjective = new Qdlib_Dao_Ad_QudaoAdsObjective();
    }

    //新增
    public function addQudaoAdsObjective($arrParams) {
        $arrFields = array(
            'id'            => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'channel'       => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'name'          => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'scope'         => isset($arrParams['scope']) ? intval($arrParams['scope']) : 0,
            'informType'    => isset($arrParams['informType']) ? intval($arrParams['informType']) : 0,
            'objectiveType' => isset($arrParams['objectiveType']) ? trim($arrParams['objectiveType']) : '',
            'paramType'     => isset($arrParams['paramType']) ? intval($arrParams['paramType']) : 0,
            'param'         => isset($arrParams['param']) ? strval($arrParams['param']) : '',
            'informMsg'     => isset($arrParams['informMsg']) ? strval($arrParams['informMsg']) : '',
            'targeting'     => isset($arrParams['targeting']) ? strval($arrParams['targeting']) : '',
            'ignoreFields'  => isset($arrParams['ignoreFields']) ? strval($arrParams['ignoreFields']) : '',
            'creator'       => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'status'        => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'deleted'       => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createTime'    => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'    => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'params'        => isset($arrParams['params']) ? json_encode($arrParams['params']) : '{}',

        );
        $result = $this->_objDaoQudaoAdsObjective->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsObjective', 'addQudaoAdsObjective', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoAdsObjective->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoAdsObjective($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        if (isset($arrParams['params'])) {
            $arrFields['params'] = !empty($arrFields['params']) ? json_encode($arrFields['params']) : '{}';
        }
        $result = $this->_objDaoQudaoAdsObjective->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsObjective', 'updateQudaoAdsObjective', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoAdsObjectiveById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoAdsObjective($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoAdsObjective($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoAdsObjective($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoAdsObjectiveInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoQudaoAdsObjective->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsObjective', 'getQudaoAdsObjectiveInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoAdsObjectiveList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoAdsObjective->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsObjective', 'getQudaoAdsObjectiveList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoAdsObjectiveTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoAdsObjective->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsObjective', 'getQudaoAdsObjectiveTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}