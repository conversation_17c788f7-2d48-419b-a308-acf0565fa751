<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/18
 * Time: 11:19
 */
class Qdlib_Ds_Ad_QudaoGdtCampaign
{
    const ALL_FIELDS = 'campaignId,channel,account,campaignName,promotedObjectType,budget,configuredStatus,speedMode,thirdCreateTime,thirdUpdateTime,createTime,updateTime,deleted,adMode';
    const DELETE_DEL = 1;

    private $_objDaoQudaoGdtCampaign;

    public function __construct()
    {
        $this->_objDaoQudaoGdtCampaign = new Qdlib_Dao_Ad_QudaoGdtCampaign();
    }


    //新增
    public function addQudaoGdtCampaign($arrParams,$options=null,$onDup=null) {
        $arrFields = array(
            'campaignId'         => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'channel'            => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'            => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'campaignName'       => isset($arrParams['campaignName']) ? strval($arrParams['campaignName']) : '',
            'promotedObjectType' => isset($arrParams['promotedObjectType']) ? strval($arrParams['promotedObjectType']) : '',
            'budget'             => isset($arrParams['budget']) ? intval($arrParams['budget']) : 0,
            'configuredStatus'   => isset($arrParams['configuredStatus']) ? intval($arrParams['configuredStatus']) : 0,
            'speedMode'          => isset($arrParams['speedMode']) ? intval($arrParams['speedMode']) : 0,
            'thirdCreateTime'    => isset($arrParams['thirdCreateTime']) ? intval($arrParams['thirdCreateTime']) : 0,
            'thirdUpdateTime'    => isset($arrParams['thirdUpdateTime']) ? intval($arrParams['thirdUpdateTime']) : 0,
            'createTime'         => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'         => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'            => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'adMode'             => isset($arrParams['adMode']) ? intval($arrParams['adMode']) : 0,

        );
        $onDup = $onDup ?? $arrFields;
        unset($onDup['campaignId']);
        unset($onDup['createTime']);

        $result = $this->_objDaoQudaoGdtCampaign->insertRecords($arrFields,$options,$onDup);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtCampaign', 'addQudaoGdtCampaign', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoGdtCampaign->getInsertId();
        return $id > 0 ? $id :true;
    }

    //编辑
    public function updateQudaoGdtCampaign($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoQudaoGdtCampaign->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtCampaign', 'updateQudaoGdtCampaign', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoGdtCampaignById($id, $arrFields) {
        $arrConds = ['campaignId' => $id];
        return $this->updateQudaoGdtCampaign($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoGdtCampaign($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoGdtCampaign($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoGdtCampaignInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['campaignId'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoGdtCampaign->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtCampaign', 'getQudaoGdtCampaignInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoGdtCampaignList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoGdtCampaign->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtCampaign', 'getQudaoGdtCampaignList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoGdtCampaignTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoGdtCampaign->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtCampaign', 'getQudaoGdtCampaignTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}