<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/18
 * Time: 11:20
 */
class Qdlib_Ds_Ad_QudaoGdtAd
{
    const ALL_FIELDS = 'adId,channel,account,adcreativeId,adgroupId,campaignId,adName,pageType,pageUrl,adcreativeTemplateId,status,thirdCreateTime,thirdUpdateTime,createTime,updateTime,deleted,systemStatus,orifrom,projectLv2';
    const DELETE_DEL = 1;

    private $_objDaoQudaoGdtAd;

    public function __construct()
    {
        $this->_objDaoQudaoGdtAd = new Qdlib_Dao_Ad_QudaoGdtAd();
    }


    //新增
    public function addQudaoGdtAd($arrParams,$options = null,$onDup = null) {
        $arrFields = array(
            'adId'                 => isset($arrParams['adId']) ? intval($arrParams['adId']) : 0,
            'channel'              => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'              => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'adcreativeId'         => isset($arrParams['adcreativeId']) ? intval($arrParams['adcreativeId']) : 0,
            'adgroupId'            => isset($arrParams['adgroupId']) ? intval($arrParams['adgroupId']) : 0,
            'campaignId'           => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'adName'               => isset($arrParams['adName']) ? strval($arrParams['adName']) : '',
            'pageType'             => isset($arrParams['pageType']) ? strval($arrParams['pageType']) : '',
            'pageUrl'              => isset($arrParams['pageUrl']) ? strval($arrParams['pageUrl']) : '',
            'adcreativeTemplateId' => isset($arrParams['adcreativeTemplateId']) ? intval($arrParams['adcreativeTemplateId']) : 0,
            'status'               => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'thirdCreateTime'      => isset($arrParams['thirdCreateTime']) ? intval($arrParams['thirdCreateTime']) : 0,
            'thirdUpdateTime'      => isset($arrParams['thirdUpdateTime']) ? intval($arrParams['thirdUpdateTime']) : 0,
            'createTime'           => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'           => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'              => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'systemStatus'         => isset($arrParams['systemStatus']) ? intval($arrParams['systemStatus']) : 0,
            'orifrom' => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'projectLv2' => isset($arrParams['projectLv2']) ? intval($arrParams['projectLv2']) : 0,

        );
        $onDup = $onDup ?? $arrFields;
        unset($onDup['adId']);
        unset($onDup['createTime']);
        
        $result = $this->_objDaoQudaoGdtAd->insertRecords($arrFields,$options,$onDup);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtAd', 'addQudaoGdtAd', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoGdtAd->getInsertId();
        return $id > 0 ? $id : true;
    }

    //编辑
    public function updateQudaoGdtAd($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoQudaoGdtAd->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtAd', 'updateQudaoGdtAd', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoGdtAdById($id, $arrFields) {
        $arrConds = ['adId' => $id];
        return $this->updateQudaoGdtAd($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoGdtAd($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoGdtAd($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoGdtAdInfoById($id, $arrFields = array(), $arrConds = null)
    {
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['adId' => $id];
        $result = $this->_objDaoQudaoGdtAd->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtAd', 'getQudaoGdtAdInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoGdtAdList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoGdtAd->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtAd', 'getQudaoGdtAdList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoGdtAdTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoGdtAd->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoGdtAd', 'getQudaoGdtAdTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}