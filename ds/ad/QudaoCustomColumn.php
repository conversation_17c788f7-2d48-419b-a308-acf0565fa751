<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/18
 * Time: 11:19
 */
class Qdlib_Ds_Ad_QudaoCustomColumn
{
    const ALL_FIELDS = 'id,name,flag,creator,sort,columns,createTime,updateTime,deleted,module,subModule';
    const DELETE_DEL = 1;

    private $_objDaoQudaoCustomColumn;

    public function __construct()
    {
        $this->_objDaoQudaoCustomColumn = new Qdlib_Dao_Ad_QudaoCustomColumn();
    }

    //新增
    public function addQudaoCustomColumn($arrParams) {
        $arrFields = array(
            'id'         => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'name'       => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'flag'       => isset($arrParams['flag']) ? intval($arrParams['flag']) : 0,
            'creator'    => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'sort'       => isset($arrParams['sort']) ? intval($arrParams['sort']) : 0,
            'columns'    => isset($arrParams['columns']) ? strval($arrParams['columns']) : '',
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'    => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'module'     => isset($arrParams['module']) ? strval($arrParams['module']) : '',
            'subModule'  => isset($arrParams['subModule']) ? strval($arrParams['subModule']) : '',

        );
        $result = $this->_objDaoQudaoCustomColumn->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoCustomColumn', 'addQudaoCustomColumn', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoCustomColumn->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoCustomColumn($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoQudaoCustomColumn->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoCustomColumn', 'updateQudaoCustomColumn', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoCustomColumnById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoCustomColumn($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoCustomColumn($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoCustomColumn($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoCustomColumnInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoQudaoCustomColumn->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoCustomColumn', 'getQudaoCustomColumnInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoCustomColumnList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoCustomColumn->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoCustomColumn', 'getQudaoCustomColumnList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoCustomColumnTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoCustomColumn->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoCustomColumn', 'getQudaoCustomColumnTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}