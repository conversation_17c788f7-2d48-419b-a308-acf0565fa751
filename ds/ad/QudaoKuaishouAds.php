<?php

/**
 * Created by Growth AutoCode.
 * User: l<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/01/14
 * Time: 06:13
 */
class Qdlib_Ds_Ad_QudaoKuaishouAds
{
    const ALL_FIELDS = 'adId,account,channel,campaignId,adName,systemStatus,putStatus,dayBudget,dayBudgetSchedule,bid,ocpxActionType,startDate,endDate,sceneId,externalUrl,studyStatus,compensateStatus,thirdCreateTime,thirdUpdateTime,createTime,updateTime,bidType,cpaBid,scheduleTime,orifrom,projectLv2,adMode';
    const DELETE_DEL = 1;

    protected $objDaoQudaoKuaiShouAds;

    public function __construct()
    {
        $this->objDaoQudaoKuaiShouAds = new Qdlib_Dao_Ad_QudaoKuaishouAds();
    }

    //新增
    public function addQudaoKuaiShouAds($arrParams)
    {
        $arrFields = array(
            'adId' => isset($arrParams['adId']) ? intval($arrParams['adId']) : 0,
            'account' => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'channel' => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'campaignId' => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'adName' => isset($arrParams['adName']) ? strval($arrParams['adName']) : '',
            'systemStatus' => isset($arrParams['systemStatus']) ? intval($arrParams['systemStatus']) : 0,
            'putStatus' => isset($arrParams['putStatus']) ? intval($arrParams['putStatus']) : 0,
            'dayBudget' => isset($arrParams['dayBudget']) ? intval($arrParams['dayBudget']) : 0,
            'dayBudgetSchedule' => isset($arrParams['dayBudgetSchedule']) ? strval($arrParams['dayBudgetSchedule']) : '',
            'bid' => isset($arrParams['bid']) ? intval($arrParams['bid']) : 0,
            'bidType' => isset($arrParams['bidType']) ? intval($arrParams['bidType']) : 0,
            'cpaBid' => isset($arrParams['cpaBid']) ? intval($arrParams['cpaBid']) : 0,
            'scheduleTime' => isset($arrParams['scheduleTime']) ? strval($arrParams['scheduleTime']) : "",
            'ocpxActionType' => isset($arrParams['ocpxActionType']) ? intval($arrParams['ocpxActionType']) : 0,
            'startDate' => isset($arrParams['startDate']) ? intval($arrParams['startDate']) : 0,
            'endDate' => isset($arrParams['endDate']) ? intval($arrParams['endDate']) : 0,
            'sceneId' => isset($arrParams['sceneId']) ? strval($arrParams['sceneId']) : '',
            'externalUrl' => isset($arrParams['externalUrl']) ? strval($arrParams['externalUrl']) : '',
            'studyStatus' => isset($arrParams['studyStatus']) ? intval($arrParams['studyStatus']) : 0,
            'compensateStatus' => isset($arrParams['compensateStatus']) ? intval($arrParams['compensateStatus']) : 0,
            'thirdCreateTime' => isset($arrParams['thirdCreateTime']) ? intval($arrParams['thirdCreateTime']) : 0,
            'thirdUpdateTime' => isset($arrParams['thirdUpdateTime']) ? intval($arrParams['thirdUpdateTime']) : 0,
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'orifrom' => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'projectLv2' => isset($arrParams['projectLv2']) ? intval($arrParams['projectLv2']) : 0,
            'adMode'             => isset($arrParams['adMode']) ? intval($arrParams['adMode']) : 0,

        );
        $onDup = $onDup ?? $arrFields;
        unset($onDup['adId']);
        unset($onDup['createTime']);

        $result = $this->objDaoQudaoKuaiShouAds->insertRecords($arrFields,null,$onDup);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoKuaiShouAds', 'addQudaoKuaiShouAds', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoQudaoKuaiShouAds->getInsertId();
        return $id > 0 ? $id : true;
    }

    //编辑
    public function updateQudaoKuaiShouAds($arrConds = null, $arrParams)
    {
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoQudaoKuaiShouAds->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoKuaiShouAds', 'updateQudaoKuaiShouAds', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoKuaiShouAdsById($id, $arrFields)
    {
        $arrConds = ['adId' => $id];
        return $this->updateQudaoKuaiShouAds($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoKuaiShouAds($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoKuaiShouAds($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoKuaiShouAdsInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['adId'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoKuaiShouAds->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoKuaiShouAds', 'getQudaoKuaiShouAdsInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoKuaiShouAdsList($arrConds = null, $arrFields = array(), $arrAppends = null)
    {
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoKuaiShouAds->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoKuaiShouAds', 'getQudaoKuaiShouAdsList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoKuaiShouAdsTotal($arrConds = null)
    {
        $res = $this->objDaoQudaoKuaiShouAds->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoKuaiShouAds', 'getQudaoKuaiShouAdsTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}