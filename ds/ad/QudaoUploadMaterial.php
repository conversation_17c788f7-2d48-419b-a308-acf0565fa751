<?php
/**
 * Created by Growth AutoCode.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/02/02
 * Time: 11:18
 */
class Qdlib_Ds_Ad_QudaoUploadMaterial
{
    const ALL_FIELDS = 'id,materialId,materialType,channel,materialRecordType,url,materialName,materialTags,ext,agentId,account,launchType,subject,launchProduct,materialCategory,createTime,updateTime,isUploaded,size,width,height,scale,format,duration';
    const DELETE_DEL = 1;

    private $_objDaoQudaoUploadMaterial;

    public function __construct()
    {
        $this->_objDaoQudaoUploadMaterial = new Qdlib_Dao_Ad_QudaoUploadMaterial();
    }


    //新增
    public function addQudaoUploadMaterial($arrParams) {
        $arrFields = array(
            'id'             => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'materialId'     => isset($arrParams['materialId']) ? intval($arrParams['materialId']) : 0,
            'materialType'   => isset($arrParams['materialType']) ? intval($arrParams['materialType']) : 0,
            'channel'        => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'materialRecordType'  => isset($arrParams['materialRecordType']) ? intval($arrParams['materialRecordType']) : 0,
            'url'              => isset($arrParams['url']) ? strval($arrParams['url']) : '',
            'materialName'     => isset($arrParams['materialName']) ? strval($arrParams['materialName']) : '',
            'materialTags'     => isset($arrParams['materialTags']) ? strval($arrParams['materialTags']) : '',
            'ext'           => isset($arrParams['ext']) ? strval($arrParams['ext']) : '',
            'agentId'      => isset($arrParams['agentId']) ? strval($arrParams['agentId']) : '',
            'account'       => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'launchType' => isset($arrParams['launchType']) ? strval($arrParams['launchType']) : '',
            'subject'    => isset($arrParams['subject']) ? strval($arrParams['subject']) : '',
            'launchProduct'  => isset($arrParams['launchProduct']) ? strval($arrParams['launchProduct']) : '',
            'materialCategory'      => isset($arrParams['materialCategory']) ? strval($arrParams['materialCategory']) : '',
            'createTime'           => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'           => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'isUploaded'        => isset($arrParams['isUploaded']) ? intval($arrParams['isUploaded']) : 0,
            'size'          => isset($arrParams['size']) ? intval($arrParams['size']) : 0,
            'width'         => isset($arrParams['width']) ? intval($arrParams['width']) : 0,
            'height'  => isset($arrParams['height']) ? intval($arrParams['height']) : 0,
            'scale'  => isset($arrParams['scale']) ? intval($arrParams['scale']) : 0,
            'format'       => isset($arrParams['format']) ? intval($arrParams['format']) : 0,
            'duration'       => isset($arrParams['duration']) ? intval($arrParams['duration']) : 0

        );

        $result = $this->_objDaoQudaoUploadMaterial->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoUploadMaterial', 'addQudaoUploadMaterial', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoUploadMaterial->getInsertId();
        return $id > 0 ? $id : true;
    }

    //编辑
    public function updateQudaoUploadMaterial($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoQudaoUploadMaterial->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoUploadMaterial', 'updateQudaoUploadMaterial', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoUploadMaterialById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoUploadMaterial($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoUploadMaterial($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoUploadMaterial($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoUploadMaterialInfoById($id, $arrFields = array(), $arrConds = null)
    {
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoQudaoUploadMaterial->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoUploadMaterial', 'getQudaoUploadMaterialInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoUploadMaterialList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoUploadMaterial->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoUploadMaterial', 'QudaoUploadMaterial', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoUploadMaterialTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoUploadMaterial->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoUploadMaterial', 'getQudaoUploadMaterialTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}