<?php

/**
 * @file   Answer.php
 * <AUTHOR>
 * @date 2018/8/18
 * @brief  互动题结果接口
 */
class Hkzb_Ds_Exam_Answer {
    private $_objCodis;
    // 构造函数
    public function __construct() {
        $this->_objCodis = Hk_Service_RedisClient::getInstance("zbcourse");
    }

    /**
     * 回答列表.
     * @param $arrInput
     * @return array
     * @throws Hk_Util_Exception
     */
    public function answerResult($arrInput)
    {
        $exerciseId = intval($arrInput['exerciseId']);
        if (empty($exerciseId)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'exerciseId is empty', $arrInput);
        }
        $arrOutput = [
            'rightList'=>$this->resultList($exerciseId,1),
            'wrongList'=>$this->resultList($exerciseId,0)
        ];
        return $arrOutput;
    }

    public function resultList($exerciseId,$fill)
    {
        $strClassNumKey = 'TYPE_ENGLISH_INTERACTION_CLASS' . '_' . $exerciseId . '_'.$fill;
        $zList = $this->_objCodis->zrange($strClassNumKey, 0, 100);
        $list = [];
        if (empty($zList)){
            return $list;
        }
        $stdentM = new Hkzb_Ds_Fudao_Student();
        $studentArs = $stdentM->getStudentInfoList($zList,['student_uid,student_name']);
        foreach ($zList as $k => $v) {
            if(!empty($studentArs))
                foreach($studentArs as $sv){
                    if($v == $sv['student_uid']){
                        $list[$k]['userName'] = !preg_match('/^1([0-9]{9})/',$sv['student_name']) ? $sv['student_name'] : substr_replace($sv['student_name'], '****', 3, 4);
                        break;
                    }
                }
            $list[$k]['uid'] = $v;
            $list[$k]['duration'] = $this->_objCodis->zscore($strClassNumKey, $v);
        }
        return $list;
    }


    /**
     * 回答列表.
     * @param $arrInput
     * @return array
     * @throws Hk_Util_Exception
     */
    public function answerResultByCodis($arrInput)
    {
        $exerciseId = intval($arrInput['exerciseId']);
        if (empty($exerciseId)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'exerciseId is empty', $arrInput);
        }
        $arrOutput = [
            'rightList'=>$this->resultListByCodis($exerciseId,1),
            'wrongList'=>$this->resultListByCodis($exerciseId,0)
        ];
        return $arrOutput;
    }

    public function resultListByCodis($exerciseId,$fill){
        $objCache = Hk_Service_RedisClient::getInstance("zbcourse");
        $strClassNumKey = 'TYPE_ENGLISH_INTERACTION_CLASS' . '_' . $exerciseId . '_'.$fill;
        $zList = $objCache->zrange($strClassNumKey, 0, 100);
        $list = [];
        if (empty($zList)){
            return $list;
        }
        $stdentM = new Hkzb_Ds_Fudao_Student();
        $studentArs = $stdentM->getStudentInfoList($zList,['student_uid,student_name']);
        foreach ($zList as $k => $v) {
            if(!empty($studentArs))
                foreach($studentArs as $sv){
                    if($v == $sv['student_uid']){
                        $list[$k]['userName'] = !preg_match('/^1([0-9]{9})/',$sv['student_name']) ? $sv['student_name'] : substr_replace($sv['student_name'], '****', 3, 4);
                        break;
                    }
                }
            $list[$k]['uid'] = $v;
            $list[$k]['duration'] = $objCache->zscore($strClassNumKey, $v);
        }
        return $list;
    }
}