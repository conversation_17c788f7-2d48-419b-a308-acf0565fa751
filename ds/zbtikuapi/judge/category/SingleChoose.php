<?php
/**
 * 单选题
 */
class Hkzb_Ds_ZbtikuApi_Judge_Category_SingleChoose implements Hkzb_Ds_ZbtikuApi_Judge_Category_Base_CategoryBase {

    private $_arrDetail;

    //判断题目
    public function Judge() {
        $arrJudgeRs = array();
        $questionInfo = $this->_questionInfo['question'];

        $rightAnswer = $questionInfo['choose'];
        $arrAnswerInfo = $this->_filedAnswer($this->_arrAnswerInfo[0]);
        if($arrAnswerInfo==$rightAnswer){
            $arrJudgeRs[] = 1;
        }else {
            $arrJudgeRs[] = 0;
        }

        $this->_arrDetail[] = array(
            'stuAnswer'    => $arrAnswerInfo, //学生答题数据
            'rightAnswer'  => $rightAnswer,//正确答案
            'correctRet'   => $arrJudgeRs[0], //批改结果
        );

        return $arrJudgeRs;
    }

    public function getDetailInfo() {
        return $this->_arrDetail;
    }

    private function _filedAnswer($studentAnswer) {
       $studentAnswer = isset($studentAnswer) ? strtoupper(trim($studentAnswer)) : '';
       return $studentAnswer;
    }
    
    //注册学生答案
    public function setStudentAnswer($arrAnswerInfo) {
        $this->_arrAnswerInfo = $arrAnswerInfo;
        return $this;
    }
    /** 注册题目信息 */
    public function setQuestionInfo($arrQuestionInfo) {
        $this->_questionInfo = $arrQuestionInfo;
        return $this;
    }
}