<?php

class Hkzb_Ds_ZbtikuApi_Judge_Formatter_PreviewFormat {

	//正则表达式
	private $_regList;
	private $_replaceList = array(
		//中文标点转英文
		'cnToEn' => array(
			'，' => ',',
			'。' => '.',
			'’' => '\'',
			'‘' => '\'',
			'“' => '"',
			'”' => '"',
			'；' => ';',
			'？' => '?',
			'：' => ':',
			'！' => '!',
		),
		'enToCn' => array(
			',' => '，',
			'。' => '.',
			';' => '；',
			'?' => '？',
			':' => '：',
		),
		//黑名单替换
		'always' => array(
		    '+' => '+',
            '-' => '-',
            '﹢' => '+',
            '﹣' => '-',
            '＋' => '+',
            '－' => '-',
            '​﹣' => '-',
			'​－' => '-',
			'×' => '',
			'÷' => '',
			'±' => '',
			'≠' => '',
			'≈' => '',
			'∥' => '',
            '//' => '',
			'＞' => '',
			'＜' => '',
            '›' => '',
            '‹' => '',
            '﹥' => '',
            '﹤' => '',
			'≥' => '',
			'≤' => '',
			'∈' => '',
			'∩' => '',
			'∪' => '',
			'∠' => '',
			'π' => '',
			'⊥' => '',
			'⊙' => '',
			'℃' => '',
			'°' => '',
			'→' => '',
			'←' => '',
			'∽' => '',
			'≌' => '',
			'％' => '',
            '﹪' => '',
            '%'  => '',
			'‰'  => '',
			'∵'  => '',
			'∴'  => '',
		),
	);
	private $_regReplace = array(
		//清除换行
		'clearBoth' => array(
			'from' => '/<p>(\s|&nbsp;)*(<br(\\\\)?\/?>)*(\s|&nbsp;)*<(\\\\)?\/p>/',
			'to' => '',
		),
	);

	private $_arrFontHtml = [
	    "\mathrm{", //正体
        "\mathbf{", //正体加粗
        "\boldsymbol {",//加粗
        "\boldsymbol{",
    ];

    private $_arrHtmlSpace = [
        '/(mathrm{.*?})(\s+)/', //正体后带空格
    ];

	private $_similarFormula = [
        "\\triangle" => "\Delta", //三角号 -> delta符号
        "\cdot"      => "\\times", //点乘->叉乘
    ];

	public function __construct() {
//		$numReg = '([\+\-]?[a-zA-Z\d]+(\.\d+)?(%)?)+';
        $numReg = '([\+\-]?[a-zA-Z\d]+(\.\d+)?)+';
        $this->_regList = array(
			//获取html标签内的内容
			'content' => '/(?<=>)([^<]+)(?=<((?:\\\\)?\/)*)/',
			'htmlContent' => '/(?<=>)([^<]*)(?=<((?:\\\\)?\/)*)/',
			//获取数字、字母、表达式
			'expression' => '/(\s*(?:' . $numReg . ')\s*(?:[\+\-\*\/]\s*' . $numReg . ')*\s*(?:([=<>]|==)\s*' . $numReg . ')*)/',
		);
	}

	public function formatter($str, $formatType = array(), $except = array())
    {
        if ('' === $str || '' === trim($str)){ //有为0的答案
            return $str;
        }

        $str = trim($str);
        $str = $this->_replaceSimilarFormula($str);
        $str = $this->_clearHtmlSpace($str); //去掉正体后面的空格
        $str = $this->_clearHtmlFont($str); //去掉正体
        $str = str_replace('&nbsp;',' ',htmlentities($str));
        $str = preg_replace("#([\w\x{4e00}-\x{9fa5}]+)\s+(?=[\w\x{4e00}-\x{9fa5}])#u",'\1 ',trim($str));
        $str = $this->trans($str, $formatType, $except);
        return $str;
    }

	public function trans($str, $formatType = array(), $except = array()) {
		$matches = array();
		//获取HTML标签内的内容
		if (preg_match_all($this->_regList['content'], $str, $matches)) {
			if ($matches[0]) {
				foreach ($matches[0] as $content) {
					$newContent = $content;
					//将数字、字母转为latex的形式
					if (in_array('latex', $formatType)) {
						$newContent = $this->_transToLatex($content);
					}
					//内容替换
					$newContent = $this->_replace($newContent, $formatType, $except);
					$str = str_replace('>' . $content . '<', '>' . $newContent . '<', $str);
				}
			}
		} else {
			if (!preg_match($this->_regList['htmlContent'], $str, $matches)) {
				//将数字、字母转为latex的形式
				if (in_array('latex', $formatType)) {
					$str = $this->_transToLatex($str);
				}
				//内容替换
				$str = $this->_replace($str, $formatType, $except);
			}
		}
		if (in_array('clearBoth', $formatType)) {
			$str = $this->_clearBoth($str);
		}
		return $str;
	}

	private function _replace($content, $formatTypes, $except = array()) {
		$formatTypes[] = 'always';
		$content = html_entity_decode($content, ENT_QUOTES | ENT_HTML401);
		if (in_array('clearBoth', $formatTypes)) {
			$nbsp = chr(194);
			$content = trim($content, ' ' . $nbsp);
		}
		$contentData = preg_split('/(?<!\\\\)\$/', $content);
		foreach ($contentData as $key => $contentVal) {
			if ($key % 2 != 0) {
				continue;
			}
			if (!empty($formatTypes)) {
				foreach ($formatTypes as $formatType) {
					if (!isset($this->_replaceList[$formatType])) {
						continue;
					}
					$replaceList = $this->_replaceList[$formatType];
					if ($except) {
						foreach ($except as $value) {
							if (isset($replaceList[$value])) {
								unset($replaceList[$value]);
							}
						}
					}
					$from = array_keys($replaceList);
					$to = array_values($replaceList);

                    $contentVal = str_replace($from, $to, $contentVal);
                    $contentData[$key] = str_replace($from, $to, $contentVal);
				}
			}
		}
		$content = implode('$', $contentData);

		return htmlentities($content);
	}

	private function _clearBoth($content) {
		return preg_replace($this->_regReplace['clearBoth']['from'], $this->_replaceList['clearBoth']['to'], $content);
	}

	private function _transToLatex($content) {
		$content = html_entity_decode($content, ENT_QUOTES | ENT_HTML401);
		//按照“$”进行内容分割，\$不做分割
		$contentData = preg_split('/(?<!\\\\)\$/', $content);
		foreach ($contentData as $key => $contentValue) {
			//只处理数组下标为偶数的数据，奇数为latex编码
			if ($key % 2 != 0) {
				continue;
			}
			preg_match_all($this->_regList['expression'], $contentValue, $matches);
//            $contentValue = preg_replace('/(?<!\\\\)%/','\%',$contentValue);
			$contentData[$key] = preg_replace($this->_regList['expression'], '$$1$', $contentValue);
		}
		$newContent = implode('$', $contentData);
		return htmlentities($newContent);
	}

    /**
     * 去掉字符中正体后面的空格
     * 例：input:  $2\\mathrm{\\pi} $
     *    output: $2\\mathrm{\\pi}$
     * @param $content
     * @return string
     */
    private function _clearHtmlSpace($content)
    {
        if (empty($content)){
            return $content;
        }
        $replace = '$1';
        $contentReplace = $content;
        foreach ($this->_arrHtmlSpace as $pattern) {
            $contentReplace = preg_replace($pattern,$replace,$contentReplace);
        }
        $contentRes = !empty($contentReplace) ? $contentReplace : $content;
        return $contentRes;
    }

    /**
     * 去掉字符中的 正体,正体加粗样式
     * 正体格式： \mathrm{}
     * 例：input: \mathrm{\color{red}{\square}}
     *    output: \color{red}{\square}
     * @param $content
     * @return string
     */
	private function _clearHtmlFont($content)
    {
        if (empty($content)){
            return $content;
        }

        foreach ($this->_arrFontHtml as $fontHtml){
            $fontHtmlLength = strlen($fontHtml);

            while(false !== ($pos = strpos($content,$fontHtml))){
                $length   = strlen($content);
                $cnt      = 1;
                $startPos = $pos + $fontHtmlLength;
                for($i = $startPos; $i < $length; $i++){
                    $str = substr($content,$i,1);
                    switch ($str){
                        case '}':
                            $cnt--;
                            break;
                        case '{':
                            $cnt++;
                            break;
                        default:
                            break;
                    }

                    if ($cnt){
                        continue;
                    }

                    $content = substr($content,0,$pos) . substr($content, $startPos, ($i-$startPos)) . substr($content, $i+1, ($length - $i)) ;
                    break;
                }
            }
        }


        return $content;
    }

    private function _replaceSimilarFormula($content)
    {
        if (empty($content)){
            return $content;
        }

        foreach ($this->_similarFormula as $formula=>$similarFormula){
            $content = str_replace($formula, $similarFormula, $content);
        }

        return $content;
    }
}
