<?php


class Qdlib_Ds_ActivityidConf
{
    const ALL_FIELDS = 'id,uniqid,apiName,compileStatus,interfaceStatus,createUid,createName,editUid,editName,activityContent,interfaceAttr,createTime,updateTime,skuStatus';
    const DELETE_DEL = 1;

    const skuStatusNoHas = 0;
    const skuStatusHas = 1;

    static $skuStatusMap = [
        self::skuStatusNoHas => '不包含',
        self::skuStatusHas   => '包含',
    ];

    private $_objDaoActivityidConf;

    public function __construct()
    {
        $this->_objDaoActivityidConf = new Qdlib_Dao_ActivityidConf();
    }

    //新增
    public function addActivityidConf($arrParams)
    {

        $result = $this->_objDaoActivityidConf->insertRecords($arrParams);
        if ($result === false) {
            Qdlib_Util_Log::warning('activityid', 'Qdlib_Ds_ActivityidConf', 'addActivityidConf', '数据库插入失败', json_encode($arrParams));
            return false;
        }

        $id = $this->_objDaoActivityidConf->getInsertId();

        return $id;
    }

    //编辑
    public function updateActivityidConf($arrConds, $arrParams)
    {
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        $result = $this->_objDaoActivityidConf->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('activityid', 'Qdlib_Ds_ActivityidConf', 'updateActivityidConf', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }

        return $result;
    }

    //通过id编辑
    public function updateActivityidConfById($id, $arrFields)
    {
        $arrConds = ['id' => $id];
        return $this->updateActivityidConf($arrConds, $arrFields);
    }



    //获取列表
    public function getActivityidConfList($arrConds = null, $arrFields = array(), $arrAppends = null)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoActivityidConf->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('activityid', 'Qdlib_Ds_ActivityidConf', 'getActivityidConfList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }

        return $result;
    }

    //获取总数
    public function getActivityidConfTotal($arrConds = null)
    {
        $res = $this->_objDaoActivityidConf->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('activityid', 'Qdlib_Ds_ActivityidConf', 'getActivityidConfTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }

        return $res;
    }

    //获取信息
    public function getActivityidConfInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = ['id' => $id];

        $result = $this->_objDaoActivityidConf->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'Qdlib_Ds_ActivityidConf', 'getActivityidConfInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
}
