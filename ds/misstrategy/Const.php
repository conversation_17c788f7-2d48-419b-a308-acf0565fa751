<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      Const.php
 * @author:        <EMAIL>
 * @desc:          策略常量 
 * @create:        2020-05-08 15:38:47
 * @last modified: 2020-05-08 15:38:47
 */
class Saaslib_Ds_MisStrategy_Const{

    # 策略连接 || &&
    const STR_COND_AND   = 1;
    const STR_COND_OR    = 2;

    # 全量开关
    const STR_ALL_ON     = 1;
    const STR_ALL_OFF    = 0;

    # 判断条件类型
    const CHK_UNAME      = 1;

    # 支持的元策略方法<br>
    # 如果配置此项，则表示已支持，并需要在chkFuncMap中配置对应的方法
    public static $supportType = array(
        self::CHK_UNAME  => "邮箱前缀策略",
    );

    # 元策略对应Saaslib_Ds_MisStrategy_Caller中的方法名字
    public static $chkFuncMap  = array(
        self::CHK_UNAME        => "checkUname",
    );

}
