<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/01/13
 * Time: 15:28
 */
class Qdlib_Ds_WxMsgAll
{
    const ALL_FIELDS = 'id,title,appidStr,errNum,sendType,sendTime,status,content,deleted,createTime,updateTime,pageUpdateTime';
    const DELETE_DEL = 1;

    private $_objDaoWxMsgAll;

    public function __construct()
    {
        $this->_objDaoWxMsgAll = new Qdlib_Dao_WxMsgAll();
    }

    //新增
    public function addWxMsgAll($arrParams) {

        $result = $this->_objDaoWxMsgAll->insertRecords($arrParams);
        if ($result === false) {
            Qdlib_Util_Log::warning('wx', 'WxMsgAll', 'addWxMsgAll', '数据库插入失败', json_encode($arrParams));
            return false;
        }
        $id = $this->_objDaoWxMsgAll->getInsertId();
        return $id;
    }

    //编辑
    public function updateWxMsgAll($arrConds, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);

        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }

        $result = $this->_objDaoWxMsgAll->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('wx', 'WxMsgAll', 'updateWxMsgAll', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateWxMsgAllById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateWxMsgAll($arrConds, $arrFields);
    }

    //软删除
    public function deleteWxMsgAll($arrConds)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateWxMsgAll($arrConds, $arrFields);
    }

    //获取信息
    public function getWxMsgAllInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoWxMsgAll->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('wx', 'WxMsgAll', 'getWxMsgAllInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getWxMsgAllList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoWxMsgAll->getListByConds($arrConds, $arrFields, null, $arrAppends);

        if ($result === false) {
            Qdlib_Util_Log::warning('wx', 'WxMsgAll', 'getWxMsgAllList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getWxMsgAllTotal($arrConds = null)
    {
        $res = $this->_objDaoWxMsgAll->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('wx', 'WxMsgAll', 'getWxMsgAllTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}