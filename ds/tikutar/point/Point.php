<?php
/**
 * @file Point.php
 * <AUTHOR>
 * @date 2018/05/09 14:13:18
 * @brief 知识点
 *  
 **/

class Hkzb_Ds_TikuTar_Point_Point 
{
    
    const POINT_TREE_URL         = "/tikumis/point/searchpoint/"; //知识点搜索接口
    const POINT_TREE_TOKEN       = "zyb2018";    //知识点获取token
    const POINT_TREE_INTREE_TYPE = 6;

    /**
     * 获取格式化后的知识点信息
     * @param  [type] $courseId 课程
     * @param  [type] $gradeId  学部
     * @return [type]           
     */
    public function getPointListMap($courseId,$gradeStage) {

        $pointMap = array();
        $pointInfoArr = $this->_getPointTreeByRal($courseId,$gradeStage);
        if(empty($pointInfoArr)) {
            return $pointMap;
        }
        foreach ($pointInfoArr as  $pointInfo) {
           $pointMap[$pointInfo['pintId']] = $pointInfo['title'];
        }
        return $pointMap;
    }

     /**
     * 根据年级学科获取知识树
     * @param  [type] $courseid [description]
     * @param  [type] $gradeid  [description]
     * @return [array]           [description]
     */
    public function mkPointTree($courseId,$gradeId)
    {

        $pointInfoArr = $this->_getPointTreeByRal($courseId,$gradeId);
        $pintData = array();
        if(!$pointInfoArr) {
            return $pintData;
        }
        //1级
        foreach ($pointInfoArr as $k=>$pointInfo) {
            if($pointInfo['level']==1) {
                $this->_turnInt($pointInfo);
                $pintData[] = $pointInfo;
                unset($pointInfoArr[$k]);
            }
        }

        //2级
        foreach ($pintData as &$pointInfoLevel) {
            if(!empty($pointInfoLevel)){
                foreach ($pointInfoArr as $k=>$pointInfoA) {
                    if($pointInfoA['parentId'] == $pointInfoLevel['pintId']) {
                        $this->_turnInt($pointInfoA);
                        $pointInfoLevel['children'][] = $pointInfoA;
                        unset($pointInfoArr[$k]);
                    }
                }
            }
        }
        //3级
        foreach ($pintData as &$threeV) {
            if(!empty($threeV)){
                foreach ($threeV['children'] as &$threeVV) {
                    foreach ($pointInfoArr as $k=>$pointInfoB) {
                        if($pointInfoB['parentId'] == $threeVV['pintId']) {
                            $this->_turnInt($pointInfoB);
                            $threeVV['children'][] = $pointInfoB;
                           unset($pointInfoArr[$k]);
                        }
                    }
                }
            }
        }
        //4级
        foreach($pintData as &$pointV) {
            if(!empty($pointV)){
                foreach ($pointV['children'] as &$pointVV) {
                    foreach ($pointVV['children'] as &$pointVVV) {
                        foreach ($pointInfoArr as $k=>$pointInfoFore) {
                            if($pointInfoFore['parentId'] == $pointVVV['pintId']) {
                                $this->_turnInt($pointInfoFore);
                                $pointVVV['children'][] = $pointInfoFore;
                                unset($pointInfoArr[$k]);
                            }
                        }
                    }
                }
            }
        }

        //5级
        foreach($pintData as &$pointV) {
            foreach ($pointV['children'] as &$pointVV) {
                foreach ($pointVV['children'] as &$pointVVV) {
                    if($pointVVV['children']){
                        foreach ($pointVVV['children'] as &$pointVVVV) {
                           foreach ($pointInfoArr as $k=>$pointInfoFive) {
                                if($pointInfoFive['parentId'] == $pointVVVV['pintId']) {
                                    $this->_turnInt($pointInfoFive);
                                    $pointVVVV['children'][] = $pointInfoFive;
                                    unset($pointInfoArr[$k]);
                                }
                            }
                        }
                    }
                }
            }
        }
        return $pintData;
    }

    private function _getPointTreeByRal($courseId,$gradeId)
    {   
        //学部映射 平台默认 小初高为1，2，3
        if($gradeId>1 && $gradeId<=20) {
            $gradeId = 2;
        }else if($gradeId>20) {
            $gradeId = 3;
        }

        $header['pathinfo'] = self::POINT_TREE_URL;
        $data['token']      = self::POINT_TREE_TOKEN;
        $data['type']       = "all";
        $data['tree']       = self::POINT_TREE_INTREE_TYPE;
        $data['courseid']   = $courseId;
        $data['gradeid']    = $gradeId;

        $ralRet = ral('tikumis','post',$data,rand(),$header);
        //  if(!$ralRet) {
        //     //qa环境ral不可用兼容，上线去掉
        //     $ralRet = (new Zhibo_Service_CurlTool())->CurlPost("http://platmis.zuoyebang.cc/tikumis/point/searchpoint",$data);
        // }
        $pointList = json_decode($ralRet,true);
        if( !$pointList || empty($pointList['data']) ) {
            Bd_log::warning("mkPointTree ral error detail：".json_encode($data));
            return false;
        }
        return $pointList['data']['data'];
    }

    /**
     * 知识点格式化
     * @param  [type] &$pointInfo [description]
     * @return [type]             [description]
     */
    private function _turnInt(&$pointInfo)
    {
        $pointInfo['pintId']   = (int)$pointInfo['pintId'];
        $pointInfo['parentId'] = (int)$pointInfo['parentId'];
    }

}
