<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Pos.php
 * <AUTHOR>
 * @date   2018-06-05
 * @brief  运营-运营位
 *
 **/
class Oplib_Ds_Pos
{
    private $_objDaoPos = null;
    // 查询项
    public static $ALL_FIELDS = 'id,posId,name,canAdg,opConds,status,strategy,createTime,bit,ext';
    public function __construct() {
        $this->_objDaoPos = new Oplib_Dao_OperationPos();
    }
    
    /**
     * @获取位置信息
     * @param int $posId
     * @return boolean|Ambigous <array, false>
     */
    public function getPosInfo($posId, $isCache = true) {
        $posId = intval($posId);
        if (0 >= $posId) {
            return false;
        }
        // 先读缓存
        $posKey   = sprintf(Oplib_Const_Cache::OP_MC_POS_INFO, $posId);
        $strPos   = Oplib_Common_CachePos::getIns()->get($posKey);
        if (is_array($strPos) && !empty($strPos)) {
            $posInfo = $strPos;
            Bd_Log::addNotice($posKey . 'hit', 1);

            return $posInfo;
        }
        Bd_Log::addNotice($posKey . 'hit', 0);
        $arrConds = [
            'pos_id' => $posId,
        ];
        $arrFields = explode(',', self::$ALL_FIELDS);
        $posInfo = $this->_objDaoPos->getRecordByConds($arrConds, $arrFields);
        if (false === $posInfo) {
            Bd_Log::warning("Error[sql] Abstract[select] Detail[posId:{$posId}]");
            return false;
        }
        // 写缓存
        if ($posInfo) {
            $ret = Oplib_Common_CachePos::getIns()->set($posKey, $posInfo, Oplib_Const_Cache::OP_MC_POS_INFO_TIME);
        }

        return $posInfo;
    }
}