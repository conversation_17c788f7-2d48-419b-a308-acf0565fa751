<?php

/**
 * @file: Task.php
 * <AUTHOR>
 * @Datetime: 2018/5/15 14:43
 * @brief : description
 */
class Hkzb_Ds_Vt_VtTask
{
    private $_vtDaoObj = null;

    const TASK_RESTORE_NEED = 1;//任务是否需要转存
    const TASK_RESTORE_NOT_NEED = 0;
    const TASK_ENCODE_NEED = 1;//是否需要加密保存
    const TASK_ENCODE_NOT_NEED = 0;
    const TASK_STATUS_WATING = 0;//任务状态：0排队中，1进行中，2成功，3失败，4取消
    const TASK_STATUS_IN = 1;
    const TASK_STATUS_SUCCESS = 2;
    const TASK_STATUS_FAILED = 3;
    const TASK_STATUS_CANCELED = 4;
    const TASK_RETRY_TIMES_LIMIT = 3;//最多重试次数

    const ALL_FIELDS = "taskId,videoId,source,opUid,originVideoUrl,needRestore,needEncode,retryTimes,status,queueId,taskFailReason,startTime,retryStartTime,endTime,callbackUrl,callbackErrno,callbackErrmsg,transData,videoData,createTime,extData,videoHash";

    public function __construct()
    {
        $this->_vtDaoObj = new Hkzb_Dao_Vt_VtTask();
    }

    public function addTask($arrParams)
    {
        $arrField = array(
            'source' => isset($arrParams['source']) ? strval($arrParams['source']) : '',
            'opUid' => isset($arrParams['opUid']) ? intval($arrParams['opUid']) : 0,
            'originVideoUrl' => isset($arrParams['originVideoUrl']) ? strval($arrParams['originVideoUrl']) : '',
            'needRestore' => isset($arrParams['needRestore']) ? intval($arrParams['needRestore']) : self::TASK_RESTORE_NOT_NEED,
            'needEncode' => isset($arrParams['needEncode']) ? intval($arrParams['needEncode']) : self::TASK_ENCODE_NEED,
            'status' => self::TASK_STATUS_WATING,
            'callbackUrl' => isset($arrParams['callbackUrl']) ? strval($arrParams['callbackUrl']) : '',
            'transData' => isset($arrParams['transData']) ? strval($arrParams['transData']) : '',
            'createTime' => time(),
            'videoHash' => isset($arrParams['videoHash']) ? strval($arrParams['videoHash']) : '',
        );
        if (empty($arrField['videoHash']) || empty($arrField['originVideoUrl'])) {
            Bd_Log::warning("Error:[param error], Detail:[json_encode($arrField)]");
            return false;
        }

        $res = $this->_vtDaoObj->insertRecords($arrField);
        return $res ? $this->_vtDaoObj->getInsertId() : false;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTaskListByConds($arrConds, $arrFields = array(), $orderBy = "create_time desc", $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by " . $orderBy,
            "limit $offset, $limit",
        );

        $ret = $this->_vtDaoObj->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 更新任务信息
     * @param $taskId
     * @param $arrParams
     * @return bool
     */
    public function updateTask($taskId, $arrParams)
    {
        if (intval($taskId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[taskId:$taskId]");
            return false;
        }

        $arrConds = array(
            'taskId' => intval($taskId),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->_vtDaoObj->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    public function updateFailedReason($taskId, $message)
    {
        if (intval($taskId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[taskId:$taskId]");
            return false;
        }

        $arrConds = array(
            'taskId' => intval($taskId),
        );

        $arrFields = array(
            "task_fail_reason" => $message,
            'status' => self::TASK_STATUS_FAILED
        );
        $ret = $this->_vtDaoObj->updateByConds($arrConds, $arrFields);
        return $ret;
    }
}