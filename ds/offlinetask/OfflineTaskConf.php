<?php
/**
 * User: <EMAIL>
 */

class Oplib_Ds_OfflineTask_OfflineTaskConf{
    const  ARR_ALL_FIELDS = Oplib_Dao_OfflineTask_OfflineTaskConf::ARR_ALL_FIELDS;
    //状态
    const STATUS_NO_DELETE = 1;//有效
    const STATUS_DELETED   = 2;//已删除
    private $_objDaoOfflineTaskConf;

    public function __construct(){
        $this->_objDaoOfflineTaskConf = new Oplib_Dao_OfflineTask_OfflineTaskConf();
    }

    /**
     * 新增记录
     * @param $arrParams
     * @return bool
     */
    public function add($arrParams){
        $arrFields = [];
        foreach ($arrParams as $key => $value) {
            if($value !== false){
                if($key=='keyMap' || $key=='extData'){
                    $arrFields[$key] = is_array($value) ? json_encode($value) : '{}';
                }else{
                    $arrFields[$key] = $value;
                }
            }
        }
        $ret = $this->_objDaoOfflineTaskConf->insertRecords($arrFields);
        return $ret;

    }

    /**
     * 修改记录
     * @param $pageId
     * @param $arrParams
     * @return bool
     */
    public function updateBySign($projects,$apiSign,$arrParams){
        if($id <= 0){
            Bd_Log::warning("Error:[param error],Detail:[$id]");
            return false;
        }
        $arrConds = ['projects' => $projects, 'apiSign'=>$apiSign];
        $arrFields = array();
        foreach($arrParams as $key => $val){
            if(!in_array($key,self::ARR_ALL_FIELDS)){
                continue;
            }
            $arrFields[$key] = $val;
        }
        if(isset($arrFields['keyMap'])){
            $arrFields['keyMap'] = json_encode($arrFields['keyMap']);
        }
        if(isset($arrFields['extData'])){
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->_objDaoOfflineTaskConf->updateByConds($arrConds,$arrFields);
        return $ret;
    }

    /**
     * 根据项目和标识获取一条记录
     */
    public function getOneBySign($projects, $apiSign, $arrParams = array()){
        if(empty($projects) || empty($apiSign)){
            Bd_Log::warning("Error:[param error],Detail:[projects:$projects,apiSign:$apiSign]");
            return false;
        }
        $arrFields = array();
        $allFields = self::ARR_ALL_FIELDS;
        if(empty($arrParams)){
            $arrFields = $allFields;
        }else{
            foreach ($arrParams as  $val){
                if(!in_array($val,$allFields)){
                    continue;
                }
                $arrFields[] = $val;
            }
        }
        $arrConds = array(
            'projects'  => $projects,
            'apiSign'  => $apiSign,
        );
        $ret = $this->_objDaoOfflineTaskConf->getRecordByConds($arrConds,$arrFields);

        return $ret;
    }

    /**
     * 逻辑删除该条记录
     * @param $actId
     * @return bool
     */
    public function deleteOneByActId($id){
        if(intval($id) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds = array(
          'id'  => $id,
        );
        $arrFields = array(
           'deleted' => self::STATUS_DELETED,
        );
        $ret = $this->_objDaoOfflineTaskConf->updateByConds($arrConds,$arrFields);
        return $ret;
    }

    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoOfflineTaskConf->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }
    //通过id编辑
    public function updateById($id, $arrFields)
    {
        $arrConds = ['id' => $id];
        return $this->update($arrConds, $arrFields);
    }
    //编辑
    public function update($arrConds = null, $arrParams)
    {
        $arrAllFields = self::ARR_ALL_FIELDS;
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoOfflineTaskConf->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Oplib_Util_Log::warning(
                'fnmis',
                'Oplib_Ds_OfflineTask',
                'update',
                '数据库更新失败',
                json_encode(['fields' => $arrFields, 'conds' => $arrConds])
            );
            return false;
        }
        return $result;
    }

	//获取总数
	public function getCnt($arrConds = null)
	{
		$res = $this->_objDaoOfflineTaskConf->getCntByConds($arrConds);
		if ($res === false) {
			Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
		}
		return $res;
	}
}
