<?php
/**
 * Created by PhpStorm.
 * User: ni<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/7/7
 * Time: 13:27
 * Desc: 蜂鸟二期模板商品信息
 */

class Oplib_Ds_HummingBirdSku
{
    private $_objDao;

    //状态
    const STATUS_ONLINE = 1;
    const STATUS_DEL    = 2;

    //定义缓存
    const BIRD_CACHE_KEY = 'fengniaov2';

    static $ARR_STATUS  = [
        self::STATUS_ONLINE => '在线',
        self::STATUS_DEL    => '删除',
    ];

    //构造方法
    public function __construct(){
        $this->_objDao = new Oplib_Dao_HummingBirdSku();
    }

    /**
     * @param $intActId
     * @param bool $intStatus
     * @param array $arrFields
     * @param bool $pn
     * @param bool $rn
     * @return array|false
     * @desc 更具活动id 获取 商品列表
     */
    public function getSkuListByActId($intActId, $intStatus = false, $arrFields = []){

        if ($intActId <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[actId:$intActId");
            return false;
        }

        $arrConds = [
            'actId'   => intval($intActId),
            'status'  => self::STATUS_ONLINE,
        ];

        if ($intStatus !== false) {
            $arrConds['status'] = intval($intStatus);
        }

        if (empty($arrFields)) {
            $arrFields = Oplib_Dao_HummingBirdSku::$arrFields;
        }


        $arrAppends = [
            "order by create_time desc",
        ];

        $res = $this->_objDao->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $res;
    }



    /**
     * 添加商品
     * @param $arrFields
     * @return bool
     * @desc 给活动添加商品信息
     */
    public function addActSku($arrFields){

        if (empty($arrFields)) {
            Bd_Log::waring("Error:[param error],Detail:[arrFields::".json_encode($arrFields)."]");
            return false;
        }

        $arrInsert = [
            'actId'   => isset($arrFields['actId']) ? intval($arrFields['actId']) : 0,
            'skuId'   => isset($arrFields['skuId']) ? intval($arrFields['skuId']) : 0,
            'compId'  => isset($arrFields['compId']) ? intval($arrFields['compId']) : 1,
            'compType'=> isset($arrFields['compType']) ? intval($arrFields['compType']) : 1,
            'tabName' => isset($arrFields['tabName']) ? strval($arrFields['tabName']) : '',
            'status'  => isset($arrFields['status']) ? strval($arrFields['status']) : 1,
            'createTime' => time(),
        ];

        if (empty($arrInsert['actId']) || empty($arrInsert['skuId']) || empty($arrInsert['compType']) || empty($arrInsert['tabName'])) {
            return false;
        }

        $res = $this->_objDao->insertRecords($arrInsert);

        return $res;
    }


    /**
     * @param $arrValue
     * @return array|bool|false
     * @批量插入数据
     */


    public function addSkuBatch($arrValue){

        if(empty($arrValue)){
            Bd_Log::warning("Error:[param error],Detail:[arrValue:$arrValue");
            return false;
        }

        $sql = 'insert into tblHummingBirdSku (`act_id`,`sku_id`,`comp_id`,`comp_type`,`tab_name`,`status`,`create_time`) values ';

        foreach ($arrValue as $value){
            $actId      = intval($value['actId']);
            $skuId      = intval($value['skuId']);
            $compId     = intval($value['compId']);
            $compType   = intval($value['compType']);
            $tabName    = strval($value['tabName']);
            $status     = self::STATUS_ONLINE;
            $createTime = time();
            $sql .= "($actId,$skuId,$compId,$compType,'$tabName',$status,$createTime),";
        }

        $sql = rtrim($sql,',');

        $ret = $this->_objDao->query($sql);

        return $ret;
    }


    /**
     * @param $intActId
     * @param $intCompId
     * @param $tabName
     * @return bool
     * @desc 物理删除商品信息
     */


    public function delSkuByActIdCompId($intActId,$intCompId,$tabName){

        $intActId    = intval($intActId);
        $intCompId   = intval($intCompId);

        if( empty($intActId) || empty($intCompId) ){
            Bd_log::warning("Error:[param error],Detail:[actId:$intActId,compId:$intCompId]");
            return false;
        }

        //修改条件
        $arrConds = [
            'actId'     => $intActId,
            'compId'    => $intCompId,
        ];

        if(!empty($tabName)){
            $arrConds['tabName'] = $tabName;
        }

        $res = $this->_objDao->deleteByConds($arrConds);

        return $res;

    }


    /**
     * @param $intActId
     * @return bool
     * @desc 标记删除 商品信息
     */

    public function delSkuByActId($intActId){

        $intActId    = intval($intActId);

        if( empty($intActId)){
            Bd_log::warning("Error:[param error],Detail:[actId:$intActId]");
            return false;
        }

        //修改条件
        $arrConds = [
            'actId'     => $intActId,
        ];

        //修改字段
        $arrFields['status']        =  Oplib_Ds_HummingBirdSku::STATUS_DEL;
        $arrFields['updateTime']    =  time();

        $res = $this->_objDao->updateByConds($arrConds,$arrFields);

        return $res;

    }

}