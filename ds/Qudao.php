<?php

class Qdlib_Ds_Qudao
{
    const DELETED_YES = 1;
    const DELETED_NO = 0;

    /**
     * @var Qdlib_Dao_Qudao
     */
    protected $dao;

    protected function __construct(Qdlib_Dao_Qudao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * @return array
     */
    public function getAllFields()
    {
        return $this->dao->getAllFields();
    }

    /**
     * @return false|int
     */
    public function insertRecords($fields)
    {
        $ret = $this->dao->insertRecords($fields);
        if (!$ret) {
            return false;
        }
        return $this->dao->getInsertId();
    }

    /**
     * @return false|int
     */
    public function multiInsert($fields, $values, $onDup = null)
    {
        return $this->dao->multiInsert($fields, $values, $onDup);
    }

    /**
     * @return false|int
     */
    public function updateByConds($conditions, $fields)
    {
        return $this->dao->updateByConds($conditions, $fields);
    }

    /**
     * @return false|array
     */
    public function getRecordByConds($conditions, $fields = null, $appends = null, $options = null, $index = null)
    {
        if (!$appends) {
            $appends = 'limit 1';
        }
        return $this->dao->getRecordByConds($conditions, $fields, $options, $appends, $index);
    }

    /**
     * @return false|array
     */
    public function getListByConds($conditions, $fields = null, $appends = null, $options = null, $index = null)
    {
        return $this->dao->getListByConds($conditions, $fields, $options, $appends, $index);
    }

    /**
     * @return false|int
     */
    public function getCntByConds($conditions)
    {
        return $this->dao->getCntByConds($conditions);
    }

    public function reconnect()
    {
        return $this->dao->reconnect();
    }
}