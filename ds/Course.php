<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Course.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/4
 * @time: 19:32
 * @desc: 课程
 */

class Zhibo_Ds_Course
{
    //类型
    const TYPE_PRIVATE       = 0;
    const TYPE_PUBLIC        = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE => '专题课',
        self::TYPE_PUBLIC => '公开课',
        self::TYPE_PRIVATE_LONG => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
    );
    
    const PACK_UNPACK = 0;
    const PACK_PACK   = 1;
    const PACK_PACKED = 2;
    static $PACK_ARRAY = array(
        self::PACK_UNPACK => '非打包课',
        self::PACK_PACK   => '打包课',
        self::PACK_PACKED => '被打包课',
    );
    
    //类型
    const DEGREE_LOWER = 0;
    const DEGREE_HIGHER = 1;
    static $DEGREE_ARRAY = array(
        self::DEGREE_LOWER => '神虎班',
        self::DEGREE_HIGHER => '神龙班',
    );

    //状态
    const STATUS_TOPREPARE = 0; //待备课
    const STATUS_TOAUDIT = 1; //待审核
    const STATUS_TOONLINE = 2; //待上线
    const STATUS_ONLINE = 3; //已上线
    const STATUS_FINISHED = 4; //已结束
    const STATUS_STOP = 5; //已中止
    const STATUS_DELETED = 6; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_TOPREPARE => '待备课',
        self::STATUS_TOAUDIT => '待审核',
        self::STATUS_TOONLINE => '待上线',
        self::STATUS_ONLINE => '已上线',
        self::STATUS_FINISHED => '已结束',
        self::STATUS_STOP => '已中止',
        self::STATUS_DELETED => '已删除',
    );
    //专属服务
    static $services = array(
        1  => '直播课',
        2  => '课程回放',
        3  => '周学习报告',
        4  => '学期学习报告',
        5  => '期中复习计划',
        6  => '期末复习计划',
        7  => '作业讲解',
        8  => '一对一批改',
        9  => '7×24答疑',
        10 => '错题本',
        11 => '个性化作业',
        12 => '天天练',
        13 => '试卷分析',
        14 => '短信提醒',
        15 => '电话提醒',
        16 => '家长课',
        17 => '升学指导',
        18 => '专属辅导老师',
        19 => '纸质教材',
    );

    //班课课程标签
    static $bkTag = array(
        '0' => array(
            'title'    => '辅',
            'desc'     => '专属辅导老师，作业批改',
            'color'    => 'FFFFFF',
            'backColor'=> 'f6ab6a',
        ),
        '1' => array(
            'title'    => '书',
            'desc'     => '配套纸质版教材辅导',
            'color'    => 'FFFFFF',
            'backColor'=> 'a4e748',
        ),
        '2' => array(
            'title'    => '赠',
            'desc'     => '专属精美赠品',
            'color'    => 'FFFFFF',
            'backColor'=> 'ef77c7',
        ),
        '3' => array(
            'title'    => '限',
            'desc'     => '限量购买',
            'color'    => 'FFFFFF',
            'backColor'=> 'c38ef4',
        ),
        '4' => array(
            'title'    => '服',
            'desc'     => '课程配套服务',
            'color'    => 'FFFFFF',
            'backColor'=> 'f6699b',
        ),
        '5' => array(
            'title'    => '研',
            'desc'     => '科学研究的专属课程',
            'color'    => 'FFFFFF',
            'backColor'=> 'f9a867',
        ),
        '6' => array(
            'title'    => '惠',
            'desc'     => '特惠课程',
            'color'    => 'FFFFFF',
            'backColor'=> 'ff635b',
        ),
    );
    //专题课标签
    static $ztkTag = array(
        '0' => array(
            'title'    => '惠',
            'desc'     => '',
            'color'    => 'FFFFFF',
            'backColor'=> 'ff635b',
        ),
        '1' => array(
            'title'    => '赠',
            'desc'     => '',
            'color'    => 'FFFFFF',
            'backColor'=> 'ef77c7',
        ),
        '2' => array(
            'title'    => '研',
            'desc'     => '科学研究的专属课程',
            'color'    => 'FFFFFF',
            'backColor'=> 'f9a867',
        ),
    );
    //课程属性
    static $attribute = array(
        1 => '运营活动',
    );
    
    static $seasonConf = array(
        '春_1' => 1,
        '春_2' => 1,
        '春_3' => 1,
        '春_4' => 1,
        '暑_0' => 2,
        '暑_1' => 2,
        '暑_2' => 2,
        '暑_3' => 2,
        '暑_4' => 2,
        '暑_5' => 2,
        '暑_6' => 2,
        '秋_1' => 3,
        '秋_2' => 3,
        '秋_3' => 3,
        '秋_4' => 3,
        '寒_1' => 4,
        '寒_2' => 4,
        '寒_3' => 4,
        '寒_4' => 4,
    );

    private $objDaoCourse;

    public function __construct()
    {
        $this->objDaoCourse = new Zhibo_Dao_Course();
    }

    /**
     * 获取课程详情
     *
     * @param  int $courseId 课程id
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getCourseInfo($courseId, $arrFields = array())
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
        );
        $ret = $this->objDaoCourse->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 批量获取课程详情(不包含已删除)
     *
     * @param  array $arrCourseId 课程ids
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getCourseInfoArray($arrCourseId, $arrFields = array())
    {
        if (empty($arrCourseId)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        } elseif (!in_array('courseId', $arrFields)) {
            $arrFields[] = 'courseId';
        }
        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $arrCourseId = array_unique($arrCourseId);
        $strCourseIds = implode(',', $arrCourseId);
        $arrConds[] = " course_id in ($strCourseIds)";
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        $arrRes = array();
        foreach ($arrCourseId as $courseId) {
            $arrRes[$courseId] = array();
        }
        foreach ($ret as $course) {
            $courseId = $course['courseId'];
            $arrRes[$courseId] = $course;
        }
        return $arrRes;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getOnLineCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20, $order = 'create_time', $sort = 'desc')
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        }
        $arrConds['status'] = self::STATUS_ONLINE;
        $arrAppends = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 获取在线课程类型
     * @return array|false
     */
    public function getCourseTypeOnline($arrConds = array(), $arrFields = array())
    {
        if(empty($arrFields)) {
            $arrFields = array(
                "type",
                "grade",
                "subject",
                "count(*) as cnt",
            );
        }
        if(empty($arrConds)) {
            $arrConds = array(
                'status' => self::STATUS_ONLINE,
                'isShow' => 1,
                'registerStartTime' => array(time(), '<'),
                'registerStopTime' => array(time(), '>')
            );
        }
        $arrAppends = array(
            "group by type, grade, subject",
        );
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseCntByConds($arrConds)
    {
        $ret = $this->objDaoCourse->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 根据条件获取所有课程
     */
    public function getAllCourseListByConds($arrConds, $arrFields = array())
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        }
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @param array $orderList
     * @param array $sortList
     * @return array|false
     */
    public function getCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20, $orderList = array('create_time'), $sortList = array('desc'))
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        }
        $orderString = ' order by ';
        foreach ($orderList as $key => $order) {
            $sort = $sortList[$key];
            $orderString .= $order . ' ' . $sort . ',';
        }
        $orderString = rtrim($orderString, ',');
        $arrAppends = array(
            $orderString,
            "limit $offset, $limit",
        );
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 更新课程
     * @param int $courseId 课程id
     * @param array $arrParams 课程属性
     * @return bool true/false
     */
    public function updateCourse($courseId, $arrParams)
    {
        $courseId = intval($courseId);
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return 0;
        }
        $arrConds = array(
            'courseId' => $courseId,
        );
        $arrFields = array();
        foreach ($arrParams as $key => $value) {
            if (in_array($key, Zhibo_Dao_Course::$allFields)) {
                $arrFields[$key] = $value;
            }
        }
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $arrFields['updateTime'] = time();
        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 更新课程报名人数
     * @param  int $courseId 课程id
     */
    public function updateCourseStudentCnt($courseId)
    {
        $courseId = intval($courseId);
        if ($courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return 0;
        }
        $arrConds = array(
            'courseId' => $courseId,
        );
        $arrFields = array(
            'student_cnt = student_cnt + 1',
        );
        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * @desc 获取学季信息
     * @param $arrConds
     * @return array|false
     */
    public function getCourseSeason($arrConds)
    {
        $arrFields = array(
            "learnSeason",
        );
        $arrAppends = array(
            "group by learn_season order by start_time desc",
        );
        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
    
    /**
     * 获取打包课程信息（未打包课程，也返回打包格式，以方便上层业务逻辑开发）
     * @param int $courseId
     * @throws Hk_Util_Exception
     * @return array|boolean
     */
    public function getPackCourseInfo($courseId) {
        if ($courseId <= 0) {
            return array();
        }
        $courseInfo = $this->getCourseInfo($courseId);
        if (false === $courseInfo) {
            Bd_Log::warning("db error, get course fail, courseId[$courseId]");
            return false;
        }
        if (empty($courseInfo)) {
            return array();
        }
        $courseInfo = $this->formatCourseInfo($courseInfo);
        if ($courseInfo['pack'] == Zhibo_Ds_Course::PACK_PACK) { //打包课
            $coreCourseIds = self::getCoreCourseIds($courseInfo);
            $otherCourseIds = self::getOtherCourseIds($courseInfo);
            $courseInfo['defaultCoreCourseId'] = intval($coreCourseIds[0]);
            $courseIds = array_merge($coreCourseIds, $otherCourseIds);
            $arrCourseInfo = $this->getCourseInfoArray($courseIds);
            if (false === $arrCourseInfo) {
                Bd_Log::warning("db error, get course fail");
                return false;
            }
            $courseInfo['coreCourse'] = array();
            foreach ($coreCourseIds as $courseId) {
                $courseInfo['coreCourse'][$courseId] = $this->formatCourseInfo($arrCourseInfo[$courseId]);
            }
            $courseInfo['otherCourse'] = array();
            foreach ($otherCourseIds as $courseId) {
                $courseInfo['otherCourse'][$courseId] = $this->formatCourseInfo($arrCourseInfo[$courseId]);
            }
        } else { //非打包课，结构上也认为是打包课，打包内容为自身，便于上层业务逻辑开发
            $courseInfo['coreCourse'][$courseInfo['courseId']] = $courseInfo;
            $courseInfo['defaultCoreCourseId'] = $courseInfo['courseId'];
            $courseInfo['otherCourse'] = array();
        }
        return $courseInfo;
    }
    
    /**
     * 课程格式化
     * @param array $courseInfo
     * @return array
     */
    public function formatCourseInfo($courseInfo) {
        if (empty($courseInfo)) {
            return $courseInfo;
        }
        if ($courseInfo['pack'] == Zhibo_Ds_Course::PACK_PACK) {
            $courseInfo['onlineTime'] = date('m月d日', $courseInfo['onlineStart']) . '-' . date('m月d日', $courseInfo['onlineStop']);
        } else {
            $courseInfo['onlineTime'] = Zhibo_Util::formatToday(strval($courseInfo['extData']['onlineTime']));
        }
        $courseHour = intval($courseInfo['extData']['courseHour']);
        if($courseHour > 1) {
            $onlineList = explode(' ', $courseInfo['onlineTime']);
            $courseInfo['onlineTime'] = $onlineList[0].' '.$courseHour.'课时';
        }
        $qqList = $courseInfo['extData']['qqList'];
        if (!empty($qqList)) {
            $lastQQInfo = end($qqList);
        } else {
            $lastQQInfo = array();
        }
        $courseInfo['qqKey'] = isset($lastQQInfo['key']) ? $lastQQInfo['key'] : '';
        $courseInfo['qqKeyI'] = isset($lastQQInfo['qqKeyI']) ? $lastQQInfo['qqKeyI'] : '';
        $courseInfo['uin'] = isset($lastQQInfo['uin']) ? $lastQQInfo['uin'] : '';
        return $courseInfo;
    }
    
    /**
     * 获取打包课的核心课id
     * @param array $courseInfo
     * @return array
     */
    public static function getCoreCourseIds($courseInfo)
    {
        if ($courseInfo['pack'] == Zhibo_Ds_Course::PACK_PACK) { //打包课
            $arrCourseIds = array();
            $defaultCoreCourseId = 0;
            foreach ($courseInfo['extData']['coreCourseIds'] as $course) {
                if ($course['type'] == 1) {
                    $defaultCoreCourseId = $course['courseId'];
                } else {
                    $arrCourseIds[] = $course['courseId'];
                }
            }
            array_unshift($arrCourseIds, $defaultCoreCourseId); //把默认核心课放到最前面
        } else { //非打包课
            $arrCourseIds[] = $courseInfo['courseId'];
        }
        return $arrCourseIds;
    }
    
    /**
     * 获取打包课的非核心课列表
     * @param array $courseInfo
     * @return array
     */
    public static function getOtherCourseIds($courseInfo) {
        if ($courseInfo['pack'] == zhibo_ds_course::PACK_PACK) {
            $arrCourseIds = array();
            foreach ($courseInfo['extData']['otherCourseIds'] as $course) {
                $arrCourseIds[] = $course['courseId'];
            }
        } else {
            $arrCourseIds = array();
        }
        return $arrCourseIds;
    }
    
    /**
     * 打包课中所有课程id
     * @param array $courseInfo
     * @return array
     */
    public static function getAllCourseIds($courseInfo) {
        $coreCourseIds = self::getCoreCourseIds($courseInfo);
        $otherCourseIds = self::getOtherCourseIds($courseInfo);
        return array_merge($coreCourseIds, $otherCourseIds);
    }
    
    /**
     * 打包课核心课默认id
     * @param array $courseInfo
     * @return array
     */
    public static function getDefaultCoreCourseId($courseInfo) {
        $coreCourseIds = self::getCoreCourseIds($courseInfo);
        return $coreCourseIds[0];
    }

    //模糊查找
    public function getCourseListByName($courseName, $arrFields = array()) {
        $arrOutput = array();
        if(strlen($courseName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseName:$courseName]");
            return $arrOutput;
        }

        if(empty($arrFields)) {
            $arrFields = Zhibo_Dao_Course::$allFields;
        }

        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );

        $binName = bin2hex("%$courseName%");
        $arrConds[] = "course_name like unhex('$binName')";

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields);

        return $ret;
    }
}