<?php
/**
 * file: QudaoPyqAdsData.php
 * User: <EMAIL>
 * Date: 2019/7/12
 * brief:
 */
class Qdlib_Ds_QudaoPyqAdsData
{
    private $_objDaoQudaoPyqAdsData;

    public function __construct()
    {
        $this->_objDaoQudaoPyqAdsData = new Qdlib_Dao_QudaoPyqAdsData();
    }

    public function addPyqAdsData($arrParams)
    {
        if (empty($arrParams)) {
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqAdsData', 'addPyqAdsData', "param: empty");
            return false;
        }
        $arrFields = array(
            'channel'                           => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                           => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'thirdAdId'                         => isset($arrParams['thirdAdId']) ? intval($arrParams['thirdAdId']) : 0,
            'thirdCampaignId'                   => isset($arrParams['thirdCampaignId']) ? intval($arrParams['thirdCampaignId']) : 0,
            'beginTime'                         => isset($arrParams['beginTime']) ? intval($arrParams['beginTime']) : 0,
            'endTime'                           => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'positionId'                        => isset($arrParams['positionId']) ? intval($arrParams['positionId']) : 0,
            'status'                            => isset($arrParams['status']) ? strval($arrParams['status']) : '',
            'bidAmount'                         => isset($arrParams['bidAmount']) ? intval($arrParams['bidAmount']) : 0,
            'thirdCreativeId'                   => isset($arrParams['thirdCreativeId']) ? intval($arrParams['thirdCreativeId']) : 0,
            'thirdCustomAudienceName'           => isset($arrParams['thirdCustomAudienceName']) ? strval($arrParams['thirdCustomAudienceName']) : '',
            'thirdExcludedCustomAudienceName'   => isset($arrParams['thirdExcludedCustomAudienceName']) ? strval($arrParams['thirdExcludedCustomAudienceName']) : '',
            'pageType'                          => isset($arrParams['pageType']) ? strval($arrParams['pageType']) : '',
            'pageUrl'                           => isset($arrParams['pageUrl']) ? strval($arrParams['pageUrl']) : '',
            'orifrom'                           => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'pageSpec'                          => isset($arrParams['pageSpec']) ? strval($arrParams['pageSpec']) : '',
            'createTime'                        => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'updateTime'                        => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : time(),
            'extData'                           => isset($arrParams['extData']) ? strval($arrParams['extData']) : json_encode([]),
        );
        $res = $this->_objDaoQudaoPyqAdsData->insertRecords($arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqAdsData', 'addPyqAdsData', 'db error', json_encode($arrFields));
            return false;
        }
        return $res;
    }

    public function updatePyqAdsData($arrConds,$arrInput){
        if(empty($arrInput) || empty($arrConds)){
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqAdsData', 'updatePyqAdsData', "param: empty");
            return false;
        }
        $arrInput[] = 'update_time='.time();
        $res = $this->_objDaoQudaoPyqAdsData->updateByConds($arrConds, $arrInput, null, null);
        if($res===false){
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqAdsData', 'updatePyqAdsData', 'db error', json_encode($arrInput));
        }
        return $res;
    }

    public function getPyqAdsDataList($where, $field = [], $arrAppends = null)
    {
        if (!$field) {
            $field = $this->_objDaoQudaoPyqAdsData->getAllFields();
        }
        $res = $this->_objDaoQudaoPyqAdsData->getListByConds($where, $field, null, $arrAppends);
        if ($res === false) {
            $sql = $this->_objDaoQudaoPyqAdsData->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqAdsData', 'getPyqAdsDataList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

}