<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2019/2/16
 * Time: 16:50
 */

class Oplib_Ds_OperationPosAdRankByDate{
    const ALL_FIELDS='id,date,grade,posId,adIdRank,founderUid,createTime,updateTime,extData';
    private $objDaoOperationPosAdRankByDate;

    public function __construct(){
        $this->objDaoOperationPosAdRankByDate = new Oplib_Dao_OperationPosAdRankByDate();
    }

    /**
     *  新增排序记录
     * @param $arrParams
     * @return bool
     */
    public function addOperationAdRankByDate($arrParams){
        if(intval($arrParams['posId']) <= 0 || intval($arrParams['grade']) <= 0 || intval($arrParams['date']) <= 0 || !$arrParams['adIdRank']){
            Bd_Log::warning("Error:[param error],Detail:[".json_encode($arrParams)."]");
            return false;
        }
        $arrFeilds = array(
            'date'       => intval($arrParams['date']),
            'grade'      => intval($arrParams['grade']),
            'posId'      => intval($arrParams['posId']),
            'adIdRank'   => strval($arrParams['adIdRank']),
            'founderUid' => intval($arrParams['founderUid']),
            'createTime' => time(),
            'updateTime' => time(),
            'extData'    => is_array($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );
        $ret = $this->objDaoOperationPosAdRankByDate->insertRecords($arrFeilds);
        if($ret){
            $ret = $this->objDaoOperationPosAdRankByDate->getInsertId();
        }
        return $ret;
    }

    /**
     * 修改排序
     * @param $id
     * @param $arrParams
     * @return bool
     */
    public function updateOperationAdRankByDate($id,$arrParams){
        if(intval($id) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds = array(
            'id' => intval($id),
        );
        $arrFields = array(
            'date' => intval($arrParams['date']),
            'grade'    => intval($arrParams['grade']),
            'posId'    => intval($arrParams['posId']),
            'adIdRank' => strval($arrParams['adIdRank']),
            'updateTime'=> time(),
        );

        if(isset($arrFields['ext'])){
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->objDaoOperationPosAdRankByDate->updateByConds($arrConds,$arrFields);
        return $ret;
    }

    /**根据日期、年级、广告位获取记录
     * @param $date
     * @param $grade
     * @param $posId
     * @param array $arrFields
     * @return bool
     */
    public function getOperationAdRankByDate($grade,$posId,$date=0,$arrFields = array()){
        if(intval($grade) <= 0 || intval($posId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[date:$date,grade:$grade,posId:$posId]");
            return false;
        }
        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }
        $arrConds = array(
            'grade' => $grade,
            'posId' => $posId,
        );
        if($date > 0){
            $arrConds['date'] = $date;
        }

        $ret = $this->objDaoOperationPosAdRankByDate->getListByConds($arrConds,$arrFields);
        return $ret;
    }

}