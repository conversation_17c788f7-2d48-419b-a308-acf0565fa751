<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Banner.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/6
 * @time: 10:54
 * @desc: banner图
 */

class Zhibo_Ds_Banner
{
    const DELETE_YES = 1;
    const DELETE_NO = 0;
    const TYPE_PC = 0;//pc端banner
    const TYPE_NA = 1;//na端banner
    const TYPE_NA_CLASS = 2;//na端班课banner
    const TYPE_NA_ALL = 10;//新版na端banner
    const TYPE_YIKE     = 20;//一课端banner
    const URL_TYPE_URL = 0;//url类型 网页
    const URL_TYPE_CID = 1;//url类型 cid
    public static $urlTypeConf = array(
        self::URL_TYPE_URL => '网页',
        self::URL_TYPE_CID => '课程',
    );
    
    public function __construct()
    {
        $this->objDaoBanner = new Zhibo_Dao_Banner();
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getBannerListByConds($arrConds, $arrFields = array(), $order = 'create_time', $sort = 'asc', $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Banner::$allFields;
        }
        $arrAppends = array(
            "order by " . $order . ' ' . $sort,
            " limit $offset, $limit",
        );
        if (!isset($arrConds['type'])) {
            $arrConds['type'] = self::TYPE_PC;
        }
        $ret = $this->objDaoBanner->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
}