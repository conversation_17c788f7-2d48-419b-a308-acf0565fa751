<?php

class  Qdlib_Ds_Finance_ChannelPermission extends Qdlib_Ds_Finance_FinanceBase
{
    const DAOOBJ = 'Qdlib_Dao_Finance_ChannelPermission';

    public function __construct()
    {
        $daoObj = self::DAOOBJ;
        $this->_tableDaoObj = new $daoObj();
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'id'            => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'permissionId'  => isset($arrParams['permissionId']) ? strval($arrParams['permissionId']) : 0,
            'channelId'     => isset($arrParams['channelId']) ? strval($arrParams['channelId']) : 0,
            'sourceId'      => isset($arrParams['sourceId']) ? strval($arrParams['sourceId']) : 0,
        );
        return $arrFields;
   }
}
