<?php

/**
 * @file    ApplyMoney.php
 * <AUTHOR>
 * @date    2018-01-15
 * @brief   三条-财务转账审核
 */
class Hkzb_Ds_Dati_ApplyMoney
{
    private $_objDaoApplyMoney;

    //审核状态--审核中
    const EXAMINE_STATUS_NEW = 1;
    //审核状态--审核通过
    const EXAMINE_STATUS_PASS = 2;
    //审核状态--无法审核通过
    const EXAMINE_STATUS_FAILED = 3;

    //用户订单状态--审核中
    const STATUS_NEW = 0;
    //用户订单状态--已到账
    const STATUS_FINISH = 1;
    //用户订单状态--已退款（因为用户自身原因导致退款）
    const STATUS_ERROR = 2;

    public static $arrStatus = array(
        self::STATUS_NEW => '审核中',
        self::STATUS_FINISH => '已到账',
        self::STATUS_ERROR => '已退回',
    );

    //删除-否
    const DELELE_NO = 0;
    //删除-是
    const DELETE_YES = 1;

    //第三方类型-支付宝
    const TYPE_ALIPAY = 0;
    //第三方类型-微信
    const TYPE_WECHAT = 1;
    //第三方类型-银行卡
    const TYPE_BANKCARD = 2;
    //智慧英雄强制转学币
    const TYPE_TURNCOIN = 3;

    const ALL_FIELDS = 'id,uid,uname,money,account,identityNum,tradeNo,thirdTradeNo,status,deleted,type,createTime,updateTime,examineUid,examineName,examineStatus,examineTime,refuseReason,payTime';

    public function __construct()
    {
        $this->_objDaoApplyMoney = new Hkzb_Dao_Dati_ApplyMoney();
    }

    /**
     * 新增 提现订单
     * @param  array $arrParams
     * @return bool true/false
     */
    public function addApply($arrParams)
    {

        if (empty($arrParams['uid']) || empty($arrParams['money']) || empty($arrParams['account']) || empty($arrParams['tradeNo'])
            || empty($arrParams['uname']) || empty($arrParams['identityNum'])
        ) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . var_export($arrParams, true) . "]");
            return false;
        }

        $arrFields = array(
            'uid' => intval($arrParams['uid']),
            'tradeNo' => intval($arrParams['tradeNo']),
            'uname' => isset($arrParams['uname']) ? strval($arrParams['uname']) : '',
            'money' => isset($arrParams['money']) ? strval($arrParams['money']) : '',
            'account' => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'identityNum' => strval($arrParams['identityNum']),
            'status' => self::STATUS_NEW,
            'examineStatus' => self::EXAMINE_STATUS_NEW,
            'deleted' => self::DELELE_NO,
            'type' => isset($arrParams['type']) ? intval($arrParams['type']) : self::TYPE_ALIPAY,
            'createTime' => time(),
        );

        $ret = $this->_objDaoApplyMoney->insertRecords($arrFields);

        return $ret;
    }

    /**
     * 单个获取
     * @param $id
     * @param array $arrFields
     * @return bool
     */
    public function getApplyInfo($id, $arrFields = array())
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrCond = array(
            'id' => intval($id),
            'deleted' => self::DELELE_NO,
        );
        $ret = $this->_objDaoApplyMoney->getRecordByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 根据uid获取用户提现记录
     * @param $uid
     * @return array|bool
     */
    public function getUserApplyRecord($uid)
    {
        $list = array();
        if (empty($uid)) {
            return $list;
        }

        $arrFields = explode(',', self::ALL_FIELDS);

        $arrCond = array(
            'uid' => $uid,
            'deleted' => self::DELELE_NO,
        );

        $arrAppends = array(
            "order by id desc",
        );

        $ret = $this->_objDaoApplyMoney->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        if (false === $ret) {
            Bd_Log::warning("Error[db error] Detail[input:" . json_encode($arrCond) . "]");
            return $list;
        }
        if (!empty($ret)) {
            foreach ($ret as $k => $v) {
                $suffix = '';
                if ($v['type'] == self::TYPE_TURNCOIN) {
                    $suffix = '（转学币）';
                }
                $list[] = array(
                    'id' => $v['id'],
                    'time' => date('m-d H:i', $v['createTime']),
                    'money' => sprintf('%.2f', $v['money'] / 100),
                    'status' => isset(self::$arrStatus[$v['status']]) ? self::$arrStatus[$v['status']] : '审核中',
                    'type'   =>'奖学金提现' . $suffix,
                );
            }
        }

        return $list;
    }

    /**
     * 用户account获取
     * @param $account
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return bool
     */
    public function getAccountApplyInfo($account, $arrFields = array(), $offset = 0, $limit = 100)
    {
        if (empty($account)) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrCond = array(
            'account' => $account,
        );
        $arrAppends = array(
            "order by id desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoApplyMoney->getListByConds($arrCond, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 更新
     * @param $id
     * @param $arrParams
     * @return bool
     */
    public function updateApplyRecord($id, $arrParams)
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }
        $arrCond = array(
            'id' => intval($id),
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $ret = $this->_objDaoApplyMoney->updateByConds($arrCond, $arrFields);
        return $ret;
    }


    /**
     * 根据多条件获取数目
     * @param $arrCond
     * @return mixed
     */
    public function getCntByCond($arrCond)
    {
        $ret = $this->_objDaoApplyMoney->getCntByConds($arrCond);
        return $ret;
    }

    /**
     * 根据多条件获取
     * @param $arrCond
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return mixed
     */
    public function getListByCond($arrCond, $arrFields = array(), $offset = 0, $limit = 100)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time asc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoApplyMoney->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 开启事务
     * @return bool
     */
    public function startTransaction()
    {
        return $this->_objDaoApplyMoney->startTransaction();
    }

    /**
     * 回滚
     * @return bool
     */
    public function rollback()
    {
        return $this->_objDaoApplyMoney->rollback();
    }

    /**
     * 提交事务
     * @return bool
     */
    public function commit()
    {
        return $this->_objDaoApplyMoney->commit();
    }

    /**
     * 根据uid获取用户申请的最后一条记录
     * @param $uid
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getLatestApplyInfo($uid, $arrFields = array()) {
        if (intval($uid) <= 0) {
            Bd_Log::warning("Error[param error] Detail[uid:$uid]");
            return false;
        }
        $arrConds = array(
            'uid' => $uid,
            'deleted' => self::DELELE_NO,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            'order by id desc',
            'limit 1',
        );
        $ret = $this->_objDaoApplyMoney->getRecordByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }


}
