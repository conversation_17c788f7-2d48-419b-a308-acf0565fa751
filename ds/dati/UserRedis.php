<?php

/**
 * @file    UserRedis.php
 * <AUTHOR>
 * @date    2018-01-14
 * @brief   用户Redis缓存
 * */
class Hkzb_Ds_Dati_UserRedis
{
    const CACHE_REDIS = 'santiao'; // redis 实例

    private $_objRedis;
    private static $userSynKey = 'DATI_CACHE_USER_SYN_'; // 用户同步队列
    private static $stopQueueKey = 'DATI_CACHE_STOP_QUEUE_'; // 停止队列key

    public function __construct()
    {
        $instanceName    = self::CACHE_REDIS;
        $redisConf       = Bd_conf::getConf("/hk/redis/" . $instanceName);
        $this->_objRedis = new Hk_Service_Redis($redisConf['service']);
    }

    /**
     * 写List - 用户数据到队列
     *
     * @param  array $params
     * @return bool
     */
    public function pushUserData($params)
    {
        if (empty($params)) {
            return false;
        }
        $listNo   = $params['uid'] % 10; // 0-9个redis队列
        $cacheKey = self::$userSynKey . $listNo;
        $this->_objRedis->lpush($cacheKey, json_encode($params));

        $intExpire = 3600 * 24 * 30;
        $ret       = $this->_objRedis->expire($cacheKey, $intExpire);

        return $ret;
    }

    /**
     * 读缓存List - 从用户队列中读数据
     *
     * @param int $listNo
     * @return string
     */
    public function getUserData($listNo = 0)
    {
        $cacheKey = self::$userSynKey . $listNo;
        if ($this->getListLength($listNo) > 0) {
            $itemInfo = $this->_objRedis->rpop($cacheKey);
        }

        return (isset($itemInfo) && !empty($itemInfo)) ? json_decode($itemInfo, true) : array();
    }

    /**
     * 读缓存List - 从用户队列中读数据
     *
     * @param int $listNo
     * @return int
     */
    public function getListLength($listNo = 0)
    {
        $cacheKey = self::$userSynKey . $listNo;
        $count    = $this->_objRedis->llen($cacheKey);

        $count = intval($count);

        return !empty($count) ? $count : 0;
    }

    /*
    * 停止队列信息
    */
    public function sendQueueStop()
    {
        $key = self::$stopQueueKey;

        $this->_objRedis->set($key, 'stop');
        $this->_objRedis->expire($key, 60);

        return true;
    }

    /**
     * 检查队列停止信息
     */
    public function checkStop()
    {
        $key = self::$stopQueueKey;

        $ret = $this->_objRedis->get($key);

        return $ret;
    }
}
