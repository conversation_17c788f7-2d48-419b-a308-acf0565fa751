<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:LessonAllState.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/13
 * @time: 11:24
 * @desc:
 */

class Zhibo_Ds_LessonAllState
{
    //学习报告状态
    const REPORT_NO = 0;  //发送报告
    const REPORT_YES = 1; //未发送

    private $objDaoLessonAllState;

    public function __construct()
    {
        $this->objDaoLessonAllState = new Zhibo_Dao_LessonAllState();
    }

    /**
     * 课后作业
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @return bool true/false
     */
    public function addHomeworkNum($studentUid, $courseId, $num = 0)
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid]");
            return false;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
            'studentUid' => intval($studentUid),
        );
        $arrFields = array(
            'homeworkNum' => intval($num),
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonAllState->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 答题正确率
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @return bool|array
     */
    public function addAnswerRate($studentUid, $courseId, $answerRate)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }
        //将未提交作业的状态置为已提交
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
        );
        $arrFields = array(
            'answerRate' => intval($answerRate),
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonAllState->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 课前预习正确数
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @return bool|array
     */
    public function addPreRightNum($studentUid, $courseId, $rightNum = 0)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
        );
        $arrFields = array(
            'preRightNum' => intval($rightNum),
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonAllState->updateByConds($arrConds, $arrFields);
        return $ret;
    }
    
    /**
     * 新增学生课节状态
     *
     * @param  mix $arrParams 状态属性
     * @return bool true/false
     */
    public function addLessonAllState($arrParams) {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0 ||  intval($arrParams['teacherUid']) <= 0 || intval($arrParams['assistantUid']) <= 0 || intval($arrParams['classId']) <= 0 ) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return 0;
        }
        $now = time();
        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'courseId'     => intval($arrParams['courseId']),
            'classId'      => intval($arrParams['classId']),
            'teacherUid'   => intval($arrParams['teacherUid']),
            'assistantUid' => intval($arrParams['assistantUid']),
            'createTime'   => $now,
            'updateTime'   => $now,
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->objDaoLessonAllState->insertRecords($arrFields);
        return $ret;
    }
}