<?php
/**
 * @file TradeRecord.php
 * <AUTHOR>
 * @date 2016年7月15日 下午4:38:03
 * @version $Revision$ 
 * @brief 用户课程订单表
 *  
 **/
class Zhibo_Ds_TradeRecord
{
    const STATUS_TOPAY = 0;
    const STATUS_PAYED = 1;
    const STATUS_REFUND = 2;
    const STATUS_FORZEN = 3;
    static $STATUS_ARRAY = array(
        self::STATUS_TOPAY => '待支付',
        self::STATUS_PAYED => '已支付',
        self::STATUS_REFUND => '已退款',
        self::STATUS_FORZEN => '已冻结',
    );


    private $_objDaoTradeRecord;

    public function __construct()
    {
        $this->_objDaoTradeRecord = new Zhibo_Dao_TradeRecord();
    }

    public function getTradeRecord($orderId, $arrFields = array())
    {
        $orderId = intval($orderId);
        if ($orderId <= 0) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_TradeRecord::$allFields;
        }
        $arrConds = array(
            'orderId' => $orderId,
        );
        $ret = $this->_objDaoTradeRecord->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 新增交易记录
     *
     * @param  array $arrParams 交易属性
     * @return bool|integer true/false
     */
    public function addTradeRecord($arrParams)
    {
        if (intval($arrParams['orderId']) <= 0) {
            return 0;
        }
        $now = time();
        $arrFields = array(
            'orderId' => intval($arrParams['orderId']),
            'studentUid' => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'courseId' => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'grade' => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject' => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'teacherUid' => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'assistantUid' => isset($arrParams['assistantUid']) ? intval($arrParams['assistantUid']) : 0,
            'payment' => isset($arrParams['payment']) ? intval($arrParams['payment']) : 0,
            'tradeChannel' => isset($arrParams['tradeChannel']) ? strval($arrParams['tradeChannel']) : '',
            'tradeCode' => isset($arrParams['tradeCode']) ? strval($arrParams['tradeCode']) : '',
            'status' => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime' => $now,
            'updateTime' => $now,
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->_objDaoTradeRecord->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 查询指定学生指定课程已支付的订单号
     * $param int $studentUid 学生uid
     * $param int $courseId   课程id
     * $return int orderId    订单id
     */
    public function getOrderIdByCourse($studentUid, $courseId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId' => $courseId,
            'status' => Zhibo_Ds_TradeRecord::STATUS_PAYED,
        );
        $ret = $this->_objDaoTradeRecord->getRecordByConds($arrConds, array('orderId'));
        return $ret['orderId'];
    }
    
    /**
     * 根据uid和course获取订单信息
     * @param int $studentUid
     * @param int $courseId
     * @return array
     */
    public function getTradeRecordByUidCourseId($studentUid, $courseId, $arrFields=array()) {
        $studentUid = intval($studentUid);
        $courseId = intval($courseId);
        if ($studentUid <= 0 || $courseId <= 0) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_TradeRecord::$allFields;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId' => $courseId,
            'status' => Zhibo_Ds_TradeRecord::STATUS_PAYED,
        );
        $ret = $this->_objDaoTradeRecord->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 设置扩展字段
     *
     * @param  int $orderId 订单id
     * @param  array $arrNewExtData
     * @return bool
     */
    public function setExtData($orderId, $arrNewExtData)
    {
        if (intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[orderId:$orderId]");
            return false;
        }
        $ret = $this->getTradeRecord($orderId, array('extData'));
        if (empty($ret)) {
            Bd_Log::warning("Error:[getTraderRecord error], Detail:[orderId:$orderId ]");
            return false;
        }
        $arrExtData = $ret['extData'];
        foreach ($arrNewExtData as $key => $value) {
            $arrExtData[$key] = $value;
        }
        $arrFields = array(
            'extData' => json_encode($arrExtData),
            'updateTime' => time(),
        );
        $arrConds = array(
            'orderId' => intval($orderId),
        );
        $ret = $this->_objDaoTradeRecord->updateByConds($arrConds, $arrFields);
        return $ret;
    }
}