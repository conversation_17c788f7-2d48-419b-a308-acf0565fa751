<?php
/**
 * 语音SDK选择策略(先声SDK，作业帮SDK、作业帮SDK(带降级))
 * User: Administrator
 * Date: 2019/5/17
 * Time: 15:56
 */
class Hkzb_Ds_Xeng_AudioSdkSelector {

    // 先声、作业帮sdk能承接的QPS
    const QPS_MAX_XIANSHENG  = 2000;
    const QPS_MAX_ZUOYEBANG  = 3000;

    // sdk channel定义
    const SDK_CHANNEL_XIANSHENG = 0;
    const SDK_CHANNEL_ZUOYEBANG = 1;

    // 延迟时间定义(单位ms)(用于前端随机延迟提交)
    const DELAY_TIME_MIN = 100;
    const DELAY_TIME_MAX = 300;

    // 计数key
    const XENG_KEY_USER_TOTAL = 'xeng_user_total_%s_%s';   // 总数 courseId_lessonId
    const XENG_KEY_USER_RANK  = 'xeng_user_rank_%s_%s_%s'; // 用户排序序号 courseId,lessonId,uid

    // 课程白名单
    static private $whiteCourseIds = array(
        'specific' => [
            258377 => [
                'sdkChannel' => self::SDK_CHANNEL_ZUOYEBANG, // 样例数据: courseId =1 , 指定作业帮sdk通道
            ],
            229715=> [
                'sdkChannel' => self::SDK_CHANNEL_ZUOYEBANG, // 样例数据: courseId =1 , 指定作业帮sdk通道
            ],
            529901=> [
                'sdkChannel' => self::SDK_CHANNEL_XIANSHENG, // 内部测试课, 全部走先声
            ],
        ],
        'random' => [
            240085 => [
                'xsSdk' => 50,
                'zybSdk' => 50,
                'zybSdkDown' => 0, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],
            235862 => [
                'xsSdk' => 70,
                'zybSdk' => 30,
                'zybSdkDown' => 0, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],
            258065=> [
                'xsSdk' => 0,
                'zybSdk' => 100,
                'zybSdkDown' => 0, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],

            // set at 20190624  255707,255708,255709
            255707 => [
                'xsSdk' => 50,
                'zybSdk' => 50,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            255708 => [
                'xsSdk' => 50,
                'zybSdk' => 50,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            255709=> [
                'xsSdk' => 50,
                'zybSdk' => 50,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],

            // 0708号添加如下
            224935=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            224940=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            224946=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            224951=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            224962=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            224968=> [
                'xsSdk' => 60,
                'zybSdk' => 40,
                'zybSdkDown' => 0, // 表示分发给各个sdk通道的占比，先声(50%),作业帮(50%)
            ],
            // 0723  将暑期课程全部添加配置
            //2期
            224936=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224941=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224947=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224952=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224957=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224963=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224969=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224973=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            //3期
            261682=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224942=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224948=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224953=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224958=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224964=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224970=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            //4期
            224937=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224943=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224949=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224954=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224959=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224965=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224971=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            224974=> ['xsSdk' => 60, 'zybSdk' => 40, 'zybSdkDown' => 0,],
            //5期
            266288=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224938=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224944=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224955=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224960=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224966=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224975=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            //6期
            224939=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224945=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224950=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224956=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224961=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224967=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224972=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],
            224976=> ['xsSdk' => 40, 'zybSdk' => 60, 'zybSdkDown' => 0,],

            // 秋季课
            255720=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255719=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255718=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255717=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255716=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255715=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            255714=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226243=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226242=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226241=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226240=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226239=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226238=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226237=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            226236=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],

            // 十一扩课
            334945=> ['xsSdk' => 50, 'zybSdk' => 50, 'zybSdkDown' => 0,],
            334963=> ['xsSdk' => 50, 'zybSdk' => 50, 'zybSdkDown' => 0,],
            334964=> ['xsSdk' => 50, 'zybSdk' => 50, 'zybSdkDown' => 0,],
            335003=> ['xsSdk' => 50, 'zybSdk' => 50, 'zybSdkDown' => 0,],

            // 寒季课
            315087=> ['xsSdk' => 0, 'zybSdk' => 100, 'zybSdkDown' => 0,], // 全部走作业帮sdk
            315088=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315089=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315090=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315091=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315092=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315093=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315094=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315095=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315096=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315097=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315098=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315099=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315100=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315101=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315102=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315103=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],

            // 2020春季课
            315104=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315105=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315106=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315113=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315124=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315125=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315126=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315132=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            315133=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334130=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334131=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334132=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334133=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334134=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],
            334135=> ['xsSdk' => 20, 'zybSdk' => 80, 'zybSdkDown' => 0,],

        ],
    );
    // lesson白名单
    static private $whiteLessonIds = array(
        'specific' => [
            1 => [
                'sdkChannel' => self::SDK_CHANNEL_ZUOYEBANG, // 样例数据: lessonId =1 , 指定作业帮sdk通道
            ],
        ],
        'random' => [
            1 => [
                'xsSdk' => 30,
                'zybSdk' => 60,
                'zybSdkDown' => 30, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],
        ],
    );
    // 用户白名单
    static private $whiteUserIds = array(
        'specific' => [
            1 => [
                'sdkChannel' => self::SDK_CHANNEL_ZUOYEBANG, // 样例数据: uid =1 , 指定作业帮sdk通道
            ],
        ],
        'random' => [
            1 => [
                'xsSdk' => 30,
                'zybSdk' => 60,
                'zybSdkDown' => 30, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],
        ],
    );
    // tid白名单
    static private $whiteTids = array(
        'specific' => [
            1 => [
                'sdkChannel' => self::SDK_CHANNEL_ZUOYEBANG, // 样例数据: tid =1 , 指定作业帮sdk通道
            ],
        ],
        'random' => [
            1 => [
                'xsSdk' => 30,
                'zybSdk' => 60,
                'zybSdkDown' => 30, // 样例数据： 表示分发给各个sdk通道的占比，先声(30/120=25%),作业帮(60/120=50%),降级作业帮(30/120=25%)
            ],
        ],
    );



    /**
     * 检查是否命中指定通道白名单
     * @param int $courseId
     * @param int $lessonId
     * @param int $uid
     * @param int $tid
     * @return int|bool 命中则返回通道值，未命中返回false
     */
    private static function checkSpecificWhiteListAndRet($courseId = 0, $lessonId = 0, $uid = 0, $tid = 0) {
        if ($courseId >0 && isset(self::$whiteCourseIds['specific'][$courseId])) {
            Bd_Log::addNotice("hitSpecificWhiteCourseId",$courseId);
            return self::$whiteCourseIds['specific'][$courseId]['sdkChannel'];
        }

        if ($lessonId > 0 && isset(self::$whiteLessonIds['specific'][$lessonId])) {
            Bd_Log::addNotice("hitSpecificWhiteLessonId",$lessonId);
            return self::$whiteLessonIds['specific'][$lessonId]['sdkChannel'];
        }

        if ($uid > 0 && isset(self::$whiteUserIds['specific'][$uid])) {
            Bd_Log::addNotice("hitSpecificWhiteUid",$uid);
            return self::$whiteUserIds['specific'][$uid]['sdkChannel'];
        }

        if ($tid > 0 && isset(self::$whiteTids['specific'][$tid])) {
            Bd_Log::addNotice("hitSpecificWhiteTid",$tid);
            return self::$whiteTids['specific'][$tid]['sdkChannel'];
        }

        // 未命中指定通道白名单
        return false;
    }

    /**
     * 检查是否命中随机通道白名单
     * @param int $courseId
     * @param int $lessonId
     * @param int $uid
     * @param int $tid
     * @return int|bool 命中则返回通道值，未命中返回false
     */
    private static function checkRandomWhiteListAndRet($courseId = 0, $lessonId = 0, $uid = 0, $tid = 0) {
        if ($courseId >0 && isset(self::$whiteCourseIds['random'][$courseId])) {
            Bd_Log::addNotice("hitRandomWhiteCourseId",$courseId);
            return self::$whiteCourseIds['random'][$courseId];
        }

        if ($lessonId > 0 && isset(self::$whiteLessonIds['random'][$lessonId])) {
            Bd_Log::addNotice("hitRandomWhiteLessonId",$lessonId);
            return self::$whiteLessonIds['random'][$lessonId];
        }

        if ($uid > 0 && isset(self::$whiteUserIds['random'][$uid])) {
            Bd_Log::addNotice("hitRandomWhiteUid",$uid);
            return self::$whiteUserIds['random'][$uid];
        }

        if ($tid > 0 && isset(self::$whiteTids['random'][$tid])) {
            Bd_Log::addNotice("hitRandomWhiteTid",$tid);
            return self::$whiteTids['random'][$tid];
        }

        // 未命中随机通道白名单
        return false;
    }


    /**
     * 获取SDK选择概率(用于主讲端推送互动题)
     * @param $courseId
     * @param $lessonId
     * @return array()
     * */
    public static function getAudioSdkDistributeResult($courseId, $lessonId) {
        $result = array(
            'xsSdk' => 0,
            'zybSdk' => 0,
            'zybSdkDown' => 0,
        );

        // 互动题中90%给平台sdk, 10%给先声 2020年3月3号
        // 互动题中80%给平台sdk, 20%给先声 2020年3月12号
        // 互动题中100%给平台sdk, 0%给先声 2020年4月28号
        return array("xsSdk" => 0, "zybSdk" => 100, 'zybSdkDown' => 0);

        // 白名单检查
        $specificRet = self::checkSpecificWhiteListAndRet($courseId, $lessonId);
        if ($specificRet !== false) {
            if ($specificRet == self::SDK_CHANNEL_ZUOYEBANG) {
                return array("xsSdk" => 0, "zybSdk" => 1, 'zybSdkDown' => 0);
            } else if ($specificRet == self::SDK_CHANNEL_XIANSHENG) {
                return array("xsSdk" => 1, "zybSdk" => 0, 'zybSdkDown' => 0);
            }
        }
        $randomRet = self::checkRandomWhiteListAndRet($courseId, $lessonId);
        if ($randomRet !== false) {
            return $randomRet; // 格式与结果一致，无需转换
        }

        // 根据lessonId获取在线人数
        $objOnlineCache  = new Hkzb_Ds_Fudao_Inclass_OnlineCache();
        $onlineStuNum = $objOnlineCache->getLessonOnlineStuNum($lessonId);

        $onlineStuNum = intval($onlineStuNum);
        if ($onlineStuNum <= self::QPS_MAX_XIANSHENG) {
            $result['xsSdk'] = $onlineStuNum;
        } else if ($onlineStuNum <= (self::QPS_MAX_XIANSHENG + self::QPS_MAX_ZUOYEBANG)) {
            $result['xsSdk'] = self::QPS_MAX_XIANSHENG;
            $result['zybSdk'] = $onlineStuNum - self::QPS_MAX_XIANSHENG;
        } else {
            $result['xsSdk'] = self::QPS_MAX_XIANSHENG;
            $result['zybSdk'] = self::QPS_MAX_ZUOYEBANG;
            $result['zybSdkDown'] = $onlineStuNum - self::QPS_MAX_XIANSHENG - self::QPS_MAX_ZUOYEBANG;
        }

        return $result;
    }

    /**
     *  针对用户到课顺序将语音请求分别指派SDK
     * [0 self::QPS_MAX_XIANSHENG] 指派给先声SDK
     * [self::QPS_MAX_XIANSHENG+1 self::QPS_MAX_XIANSHENG+self::QPS_MAX_ZUOYEBANG] 指派给作业帮SDK
     * [self::QPS_MAX_XIANSHENG+self::QPS_MAX_ZUOYEBANG +1 ∞] 指派给作业帮SDK并采用降级策略
     * @param $courseId 课程id
     * @param $lessonId 章节Id
     * @param $uid  用户id
     * @param $tid  题目id
     * @return
     */
    public static function getUserSdkResult($courseId, $lessonId, $uid , $tid = 0) {
        $result = array(
            'sdkChannel' => 0, // 0 先声sdk, 1 作业帮sdk
            'delayTime' => 0,  // 延迟时间,单位毫秒
        );

        if ($courseId == 529901) { // 内部测试课,全部走先声,先声迁移测试用
            return array(
                'sdkChannel' => 0, // 0 先声sdk, 1 作业帮sdk
                'delayTime' => 0,  // 延迟时间,单位毫秒
            );
        }

        // 除了互动题之外，课前课后试卷中的语音题100%走平台sdk 2020年3月3号
        return array(
            'sdkChannel' => 1, // 0 先声sdk, 1 作业帮sdk
            'delayTime' => 0,  // 延迟时间,单位毫秒
        );

        if (empty($courseId) || empty($lessonId) || empty($uid)) {
            Bd_Log::warning("invalid params,courseId[$courseId],lessonId[$lessonId],uid[$uid]");
            return false;
        }

        // 白名单处理
        $specificRet = self::checkSpecificWhiteListAndRet($courseId, $lessonId, $uid, $tid);
        if ($specificRet !== false) {
            return array(
                'sdkChannel' => $specificRet,
                'delayTime' => 0,
            );
        }
        $randomRet = self::checkRandomWhiteListAndRet($courseId, $lessonId, $uid, $tid);
        if ($randomRet !== false) {
            return array(
                'sdkChannel' => self::getSdkChannelFromRandomRatio($randomRet),
                'delayTime' => 0,
            );
        }

        if (in_array($uid, self::$whiteUserIds) || in_array($tid, self::$whiteTids)) {
            return array(
                'sdkChannel' => 1,
                'delayTime' => 0,
            );
        }

        // 获取用户的到课顺序
        $userRankValue = self::getUserRank($courseId, $lessonId, $uid);
        $result['sdkChannel'] = self::getSdkChannelByUserRankValue($userRankValue);
        $result['delayTime']  = self::getDelayTimeByUserRankValue($userRankValue);

        //打点
        Bd_Log::addNotice("rankUid",$uid);
        Bd_Log::addNotice("userRankValue",$userRankValue);
        Bd_Log::addNotice("sdkChannel", $result['sdkChannel']);
        Bd_Log::addNotice("delayTime", $result['delayTime']);

        return $result;
    }


    public static function getUserRank($courseId, $lessonId, $uid) {

        $objRedis = Hk_Service_RedisClient::getInstance("zhibo");

        $userRankKey = self::getUserRankKey($courseId, $lessonId, $uid);
        $userRankValue = $objRedis->get($userRankKey);

        $userRankValue = intval($userRankValue);
        if ($userRankValue <= 0) {
            // 用户还未计数
            $userTotalKey = self::getUserTotalKey($courseId, $lessonId);
            $newRank = $objRedis->incr($userTotalKey);

            $objRedis->set($userRankKey, $newRank);

            return $newRank;
        }

        return $userRankValue;
    }

    private static function getSdkChannelFromRandomRatio($randomRatio) {
        $xsSdkNum  = intval($randomRatio['xsSdk']);
        $zybSdkNum = intval($randomRatio['zybSdk']);
        $zybSdkDownNum = intval($randomRatio['zybSdkDown']);

        $total = $xsSdkNum + $zybSdkNum + $zybSdkDownNum;
        if ($total <= 0) {
            // 默认走先声
            return self::SDK_CHANNEL_XIANSHENG;
        }
//
//        // 归一化到10000
//        if ($total < 10000 && $total > 0) {
//            $factor = round(10000 / $total);
//            $xsSdkNum = $xsSdkNum > 0 ? round($xsSdkNum * $factor) : 0;
//            $zybSdkNum = $zybSdkNum > 0 ? round($zybSdkNum * $factor) : 0;
//            $zybSdkDownNum = $zybSdkDownNum > 0 ? round($zybSdkDownNum * $factor) : 0;
//        }
        $randValue = mt_rand(1,$xsSdkNum + $zybSdkNum + $zybSdkDownNum);
        if ($xsSdkNum > 0 && $randValue <= $xsSdkNum) {
            return self::SDK_CHANNEL_XIANSHENG;
        } else if ($zybSdkNum > 0 && $randValue > $xsSdkNum && $randValue <= $xsSdkNum+$zybSdkNum) {
            return self::SDK_CHANNEL_ZUOYEBANG;
        } else {
            // 暂不细分降级的情况
            return self::SDK_CHANNEL_ZUOYEBANG;
        }
    }

    private static function getSdkChannelByUserRankValue($userRankValue) {
        return $userRankValue > self::QPS_MAX_XIANSHENG ? self::SDK_CHANNEL_ZUOYEBANG : self::SDK_CHANNEL_XIANSHENG;
    }

    private static function getDelayTimeByUserRankValue($userRankValue) {
        $delayTime = 0;
        if ($userRankValue > (self::QPS_MAX_XIANSHENG + self::QPS_MAX_ZUOYEBANG)) {
            $delayTime = mt_rand(self::DELAY_TIME_MIN, self::DELAY_TIME_MAX);
        }

        return $delayTime;
    }

    // 获取redis key
    private static function getUserRankKey($courseId, $lessonId, $uid) {
        return sprintf(self::XENG_KEY_USER_RANK, $courseId, $lessonId, $uid);
    }

    private static function getUserTotalKey($courseId, $lessonId) {
        return sprintf(self::XENG_KEY_USER_TOTAL, $courseId, $lessonId);
    }

}