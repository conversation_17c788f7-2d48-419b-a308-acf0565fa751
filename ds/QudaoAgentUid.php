<?php

/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2019/06/18
 * Time: 19:57
 */
class Qdlib_Ds_QudaoAgentUid
{
    const ALL_FIELDS = 'id,agentId,uid,userName,deleted,createdTime,updateTime,operator,creator';
    const DELETE_DEL = 1;

    private $_objDaoQudaoAgentUid;

    public function __construct()
    {
        $this->_objDaoQudaoAgentUid = new Qdlib_Dao_QudaoAgentUid();
    }

    //新增
    public function addQudaoAgentUid($arrParams)
    {
        $arrFields = array(
//            'id'          => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'agentId' => isset($arrParams['agentId']) ? intval($arrParams['agentId']) : 0,
            'uid' => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
            'userName' => isset($arrParams['userName']) ? strval($arrParams['userName']) : '',
            'deleted' => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createdTime' => isset($arrParams['createdTime']) ? intval($arrParams['createdTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'operator' => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'creator' => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
        );
        $result = $this->_objDaoQudaoAgentUid->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoAgentUid', 'addQudaoAgentUid', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoAgentUid->getInsertId();
        return $id;
    }

     //获取列表
     public function getQudaoAgentUidList($arrConds = array(), $arrFields = array(), $arrAppends = null) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoAgentUid->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoAgentUid', 'getQudaoAgentUidList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }


}