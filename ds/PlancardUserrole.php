<?php

/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file PlancardUserrole.php
 * @brief xk卡片角色管理
 *
 **/
class Oplib_Ds_PlancardUserrole {

	const STATUS_ONLINE  = 1;
	const STATUS_OFFLINE = 2;
	static $STATUS_ARRAY = array(
		self::STATUS_ONLINE => '上线',
		self::STATUS_OFFLINE=> '下线',
	);
	const PAGE_LIMIT = 2000;

	const ALL_FIELDS='roleId,roleName,status,createTime,updateTime';

	private $objDaoPlancardUserrole;

	private $orderBy = array('roleId','createTime');

	/**
	 * 构造函数
	 *
	 */
	public function __construct(){
		$this->objDaoPlancardUserrole = new Oplib_Dao_PlancardUserrole();
	}

	/**
	 * 新增角色
	 *
	 * @param  mix  $arrParams 属性
	 * @return bool true/false
	 */
	public function addPlancardUserrole($arrParams){
		$arrFields = array(
			'roleId'      =>isset($arrParams['roleId']) ? intval($arrParams['roleId']) : 0,
			'roleName'    =>isset($arrParams['roleName']) ? strval($arrParams['roleName']) : '',
			'status'      =>isset($arrParams['status']) ? intval($arrParams['status']) : self::STATUS_OFFLINE,
			'createTime'  =>time(),
			'updateTime'  =>time(),
			'ext'         =>isset($arrParams['ext']) ? json_encode($arrParams['ext']):'[]',
		);

		$ret = $this->objDaoPlancardUserrole->insertRecords($arrFields);
		if($ret){
			$ret = $this->objDaoPlancardUserrole->getInsertId();
		}
		return $ret;
	}

	/**
	 * 更新
	 *
	 * @param  int $id
	 * @param  array $arrParams
	 * @return bool true/false
	 */
	public function updatePlancardUserrole($id,$arrParams){
		if(intval($id) <= 0){
			Bd_Log::warning("Error:[param error],Detail:[id:$id]");
			return false;
		}
		$arrConds = array(
			'roleId' => intval($id),
		);
		$arrFields = ['updateTime' => time()];
		$arrAllFields = explode(',',self::ALL_FIELDS);
		foreach($arrParams as $key => $value){
			if(!in_array($key,$arrAllFields)){
				continue;
			}
			$arrFields[$key] = $value;
		}
		if(isset($arrFields['ext'])){
			$arrFields['ext'] = json_encode($arrFields['ext']);
		}
		$ret = $this->objDaoPlancardUserrole->updateByConds($arrConds,$arrFields);
		return $ret;
	}

	/**
	 * 获取记录
	 *
	 * @param  int $id
	 * @return  mix $arrFields  属性
	 */
	public function getPlancardUserrole($id,$arrFields = array()){
		if(intval($id) <= 0){
			Bd_Log::warning("Error:[param error],Detail:[id:$id]");
			return false;
		}
		if(empty($arrFields)){
			$arrFields = explode(',',self::ALL_FIELDS);
		}

		$arrConds = array(
		    'roleId' => $id,
		);

		$ret = $this->objDaoPlancardUserrole->getRecordByConds($arrConds,$arrFields);
		return $ret;
	}

    /**
     * 根据roleId、角色名称、上线状态获取列表
     *
     * @param  string $name
     * @return  array $arrFields  属性
     */
    public function getPlancardUserroleByName($roleId=null,$roleName=null,$status=null,$arrFields = [], $offset=0, $limit=10, $order='createTime'){
        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }

		$arrConds = [];
		if (isset($roleId)) {
			$arrConds['roleId'] = $roleId;
		}
		if (isset($roleName)) {
			$arrConds['roleName'] = $roleName;
		}
		if (isset($status)) {
			$arrConds['status'] = $status;
		}
		$arrConds = $arrConds ? $arrConds : null;

		$orderBy = '';
        if($order != '' && in_array($order,$this->orderBy)){
            $orderBy .= 'order by '. $order . ' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
		if($offset >= 0 && $limit > 0 ){
            ($limit > self::PAGE_LIMIT) && $limit = self::PAGE_LIMIT;
            $orderBy .= " limit $offset,$limit";
        }else if($limit > 0){
            $orderBy .= " limit $limit";
        }
		$arrAppends = ($orderBy != '') ? [$orderBy] : null;

		$ret = $this->objDaoPlancardUserrole->getListByConds($arrConds, $arrFields, null, $arrAppends);
        $count = $this->objDaoPlancardUserrole->getCntByConds($arrConds);
        return ['list'=>$ret,'total'=>$count];
        return $ret;
    }

    /**
     * @param $posId
     * @return array|bool|cnt
     */

    public function getPlancardUserroleCntByPosId($posId,$id = 0,$grade = 0,$status = 0){
        if(intval($posId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[posId:$posId]");
            return false;
        }

        $arrConds = array(
            'posId' => intval($posId),
        );
        if($grade > 0){
            $arrConds['grade'] = intval($grade);
        }
        if($status > 0){
            $arrConds['status'] = intval($status);
        }else{
            $arrConds['status'] = array(self::STATUS_INVALID,'<>');
        }

        if($id > 0){
            $arrConds['roleId'] = $id;
        }

        $ret = $this->objDaoPlancardUserrole->getCntByConds($arrConds);


        return $ret;

    }

    /**
     * @param $id
     * @return bool
     * @desc
     */

    public function deletePlancardUserroleById($id){

        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'  => intval($id),
        );
        $arrFields = array(
            'status' => self::STATUS_INVALID,
        );
        $res  = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;

    }

    /**
     * @param $id
     * @return bool
     * @desc 上线
     */

    public function statusPlancardUserroleById($id){

        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'        => intval($id),
        );
        $info = $this->getPlancardUserrole($id,array('ext'));
        $ext  = $info['ext'];
        $ext['manualOffline'] = 0;
        $arrFields = array(
            'status' => self::STATUS_ONLINE,
            'ext'    => json_encode($ext),
        );
        $res = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * @param $id
     * @return bool
     * @ desc下线
     */

    public function offlinePlancardUserroleById($id){

        if (intval($id) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'        => intval($id),
        );
        $info = $this->getPlancardUserrole($id,array('ext'));
        $ext  = $info['ext'];
        $ext['manualOffline'] = 1;
        $arrFields = array(
            'status' => self::STATUS_OFFLINE,
            'ext'    => json_encode($ext),//手动下线自动脚本不做上线操作
        );
        $res = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }


    /**
     * @param $id
     * @return bool
     * @ desc隐藏
     */

    public function hidePlancardUserroleById($id){

        if (intval($id) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'        => intval($id),
        );
        $info = $this->getPlancardUserrole($id,array('ext'));
        $ext  = $info['ext'];
        $ext['manualOffline'] = 1;
        $arrFields = array(
            'status' => self::STATUS_HIDE,
            'ext'    => json_encode($ext),//手动下线自动脚本不做上线操作
        );
        $res = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }


    /**
     * @param $id
     * @return bool
     * @ 核对课程是否有效的下线脚本
     */

    public function offlineAdById($id,$isOnline = true){

        if (intval($id) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'        => intval($id),
        );
        $info = $this->getPlancardUserrole($id,array('ext'));
        $ext  = $info['ext'];
        if($isOnline === true){
            $ext['manualOffline'] = 0;    //脚本 可 维护上线
        }else{
            $ext['manualOffline'] = 1;    //脚本不可维护上线
        }

        $arrFields = array(
            'status' => self::STATUS_OFFLINE,
            'ext'    => json_encode($ext),//手动下线自动脚本不做上线操作
        );
        $res = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }


    /**
     * @return bool
     * 该方法 脚本稳定后 以后要删除
     */

    public function revive(){


        $arrConds  = array(
            'status'        => self::STATUS_HIDE,
            'posId'         => 10002,
        );
        $arrFields = array(
            'status' => self::STATUS_OFFLINE,
        );
        $res = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }


    /**
     * @param $posId
     * @param $srrAdId
     * @desc 根据posId 和批量的adId获取广告位信息
     */
    public function getPlancardUserroleBatch($posId,$grade = 0,$arrAdId = [],$status = false){

        if(empty($posId) || !is_array($arrAdId)){
            Bd_Log::warning("Error:[param error],Detail:[posId: ".$posId."; grade ".$grade." ; arrAdId ".json_encode($arrAdId)."]");
            return [];
        }

        $arrConds['posId']  = $posId;
        if($grade){
            $arrConds['grade']  = $grade;
        }

        if($status === false){
            $status = self::STATUS_ONLINE;
        }
        $arrConds['status'] = $status;

        $strAdId = implode(',',$arrAdId);
        if($strAdId){
            $arrConds[] = "id in ($strAdId)";
        }

        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }

        $ret = $this->objDaoPlancardUserrole->getListByConds($arrConds,$arrFields);

        if(!$ret){
            Bd_Log::warning("Error:[get_data_error_getPlancardUserroleBatch],Detail:[ret:".json_encode($ret).";cond:".json_encode(['posId'=> $posId,'grade' => $grade,'arrAdId' => $arrAdId])."]");
        }

        return $ret;
    }





    /**
     * @param $id
     * @param $rankgit
     * @return bool
     * @对列表进行排序
     */

    public function sortPlancardUserroleById($id,$rank){

        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$id]");
            return false;
        }
        $arrConds  = array(
            'roleId'        => intval($id),
        );
        $arrFields = array(
            'rank' => intval($rank),
        );
        $res  = $this->objDaoPlancardUserrole->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 根据资源结束时间获取运营数据
     * <AUTHOR>
     * @DateTime 2018-08-16
     * @param    int      $startTime 资源开始时间
     * @param    int      $endTime   资源结束时间
     * @param    array    $arrFields 筛选字段信息
     * @param    int      $status    资源状态
     * @return   array
     */
    public function getPlancardUserroleListByEndtimeAndStatus($startTime, $endTime, $arrFields = [], $status = self::STATUS_ONLINE)
    {
        $startTime = intval($startTime);
        $endTime   = intval($endTime);
        $status    = intval($status);

        if ($startTime <= 0 || $endTime <= 0 || !isset(self::$STATUS_ARRAY[$status])) {
            Bd_Log::warning("Error:[param error],Detail:[startTime:{$startTime},endTime:{$endTime},status:{$status}]");
            return false;
        }

        empty($arrFields) && $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = [
            "status" => $status,
            "stop_time >= {$startTime}",
            "stop_time < {$endTime}",
        ];

        return $this->objDaoPlancardUserrole->getListByConds($arrConds, $arrFields);
    }

    /**
     * 根据资源开始时间获取运营数据
     * <AUTHOR>
     * @DateTime 2018-08-16
     * @param    int      $startTime 资源开始时间
     * @param    int      $endTime   资源结束时间
     * @param    array    $arrFields 筛选字段信息
     * @param    int      $status    资源状态
     * @return   array
     */
    public function getPlancardUserroleListByStarttimeAndStatus($startTime, $endTime, $arrFields = [], $status = self::STATUS_ONLINE)
    {
        $startTime = intval($startTime);
        $endTime   = intval($endTime);
        $status    = intval($status);

        if ($startTime <= 0 || $endTime <= 0 || !isset(self::$STATUS_ARRAY[$status])) {
            Bd_Log::warning("Error:[param error],Detail:[startTime:{$startTime},endTime:{$endTime}]");
            return false;
        }

        empty($arrFields) && $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = [
            "status" => $status,
            "start_time >= {$startTime}",
            "start_time < {$endTime}",
        ];

        return $this->objDaoPlancardUserrole->getListByConds($arrConds, $arrFields);
    }

    public function getListByConds($arrConds, $arrFields = [], $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL)
    {
        if (0 >= count($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        return $this->objDaoPlancardUserrole->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function getCntByConds($arrConds)
    {
        return $this->objDaoPlancardUserrole->getCntByConds($arrConds);
    }

    /**
     * 根据年级获取活动数据 拼团专用
     * <AUTHOR>
     * @DateTime 2018-10-11
     * @param    int                $grade     学生年级
     * @param    array              $arrFields 字段信息
     * @return   array
     */
    public function getPlancardUserroleByGrade($grade, $arrFields = [])
    {
        if ($grade <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[grade:{$grade}}]");
            return [];
        }

        empty($arrFields) && $arrFields = explode(',', self::ALL_FIELDS);
        // 首页访问 需要加cache
        $mcKey        = Oplib_Const_Cache::OP_POS_JIAONANG_AD_KEY . $grade . '_' . md5(json_encode($arrFields));
        $objMemcached = Hk_Service_Memcached::getInstance(Oplib_Const_Cache::TUAN_MEMCACHE_SERVER_NAME);
        $mcValue       = $objMemcached->get($mcKey);
        if (is_array($mcValue) && !empty($mcValue)) {
            Bd_Log::addNotice($mcKey . '_hit', 1);
            return $mcValue;
        }
        Bd_Log::addNotice($mcKey . '_hit', 0);
        $arrConds = [
            'grade'    => $grade,
            'act_type' => self::ACTIVITY_TYPE_PT,
            'pos_id'   => Oplib_Const_Operation::POS_JIAONANG,
            'status'   => self::STATUS_ONLINE,
        ];

        $record =  $this->objDaoPlancardUserrole->getRecordByConds($arrConds, $arrFields);
        if (false === $record) {
            Bd_Log::warning("Abstract[tuan] Error:[select error], Detail:[param: " . json_encode($arrConds) . "]");
            return $record;
        }
        // 加cache
        if ($record) {
            $objMemcached->set($mcKey, $record, Oplib_Const_Cache::OP_POS_JIAONANG_AD_TIME);
        }
        return $record;
    }

    /**
     * @param $arrPosId
     * @param $grade
     * @param int $status
     * @param array $arrFields
     * @return array|false
     * @desc 更具posId批量获取广告位信息
     */

    public function getPlancardUserroleListByArrPosId($arrPosId,$grade,$status = 0,$arrFields = []){
        $arrPosId = is_array($arrPosId) ? $arrPosId : [];
        $grade    = intval($grade);
        if(empty($arrPosId) || $grade <= 0){
                Bd_Log::warning("Error:[param error],Detail:[grade ".$grade." ; arrPosId ".json_encode($arrPosId)."]");
                return [];
        }

        $strPosIdList = join(',', $arrPosId);
        $arrConds  = array("pos_id in ($strPosIdList)");
        if($grade > 0){
            $arrConds['grade'] = intval($grade);
        }
        if($status > 0){
            $arrConds['status'] = intval($status);
        }else{
            $arrConds['status'] = array(self::STATUS_INVALID,'<>');
        }

        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }

        $ret = $this->objDaoPlancardUserrole->getListByConds($arrConds,$arrFields);

        if (false === $ret) {
            Bd_Log::warning("Abstract[oppos] Error:[select error], Detail:[param: " . json_encode($arrConds) . "]");
            return $ret;
        }

        return $ret;

    }
}
