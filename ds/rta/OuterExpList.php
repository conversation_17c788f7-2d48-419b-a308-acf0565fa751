<?php

class Qdlib_Ds_Rta_OuterExpList extends Qdlib_Ds_Rta_RtaBase
{
    const STATUS_ENABLED = 1;
    const STATUS_PAUSED = 2;
    const STATUS_FINISHED = 3;
    const STATUS_NAMES = [
        self::STATUS_ENABLED => '已启用',
        self::STATUS_PAUSED => '已暂停',
        self::STATUS_FINISHED => '已结束',
    ];
    const GDT_SITE_SET = [
        15 => '移动联盟',
        21 => '微信',
        25 => '移动内部站点',
        27 => '腾讯新闻流量',
        28 => '腾讯视频流量',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->_tableDaoObj = new Qdlib_Dao_Rta_OuterExpList();
    }

    protected function getFormatDbData($arrParams)
    {
        return [
            'channel' => isset($arrParams['channel']) ? (string)$arrParams['channel'] : '',
            'expId' => isset($arrParams['expId']) ? (string)$arrParams['expId'] : '',
            'name' => isset($arrParams['name']) ? (string)$arrParams['name'] : '',
            'rate' => isset($arrParams['rate']) ? (int)$arrParams['rate'] : '',
            'beginTime' => isset($arrParams['beginTime']) ? (int)$arrParams['beginTime'] : '',
            'endTime' => isset($arrParams['endTime']) ? (int)$arrParams['endTime'] : '',
            'status' => isset($arrParams['status']) ? (int)$arrParams['status'] : '',
            'expRange' => isset($arrParams['expRange']) ? (string)$arrParams['expRange'] : '',
            'expCreatedTime' => isset($arrParams['expCreatedTime']) ? (int)$arrParams['expCreatedTime'] : '',
            'expLastModifiedTime' => isset($arrParams['expLastModifiedTime']) ? (int)$arrParams['expLastModifiedTime'] : '',
            'createTime' => isset($arrParams['createTime']) ? (int)$arrParams['createTime'] : '',
            'updateTime' => isset($arrParams['updateTime']) ? (int)$arrParams['updateTime'] : '',
            'extChecksum' => isset($arrParams['extChecksum']) ? (string)$arrParams['extChecksum'] : '',
            'ext' => isset($arrParams['ext']) ? (string)$arrParams['ext'] : '',
        ];
    }

    public function multiInsert($fields, $values)
    {
        return $this->_tableDaoObj->multiInsert($fields, $values);
    }

    public function updateRecords($conditions, $fields)
    {
        return $this->_tableDaoObj->updateByConds($conditions, $fields);
    }
}