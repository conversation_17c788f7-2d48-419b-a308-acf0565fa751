<?php
/**
 * File: AppUpdateTask.php
 * User: lih<PERSON>jie
 * Date: 2018/4/24
 * Time: 上午11:18
 * Desc: app更新任务
 */
class Hkzb_Ds_Update_AppUpdateTask
{
    const ALL_FIELDS = 'id,name,type,appId,os,vc,zhiboVc,vcname,title,content,url,latest,strategy,position,forceUp,startTime,endTime,operatorUid,status,createTime,updateTime,extData';

    const FIN_TYPE_ALL = 0;
    const FIN_TYPE_NOT = 1;
    const FIN_TYPE_HAD = 2;
    public static $finTypeMap = array(
        self::FIN_TYPE_ALL => '全部',
        self::FIN_TYPE_NOT => '进行中',
        self::FIN_TYPE_HAD => '已完成',
    );

    const STATUS_VALID    = 0;
    const STATUS_PAUSE    = 1;
    const STATUS_FINISHED = 2;
    const STATUS_DELETED  = 3;
    public static $pubStatusMap = array(
        self::STATUS_VALID      => "已生效",
        self::STATUS_PAUSE      => "暂停",
        self::STATUS_FINISHED   => "完成",
        self::STATUS_DELETED    => "已删除",
    );

    const APPID_HOMEWORK = 'homework';//作业帮
    const APPID_AIRCLASS = 'airclass';//一课
    public static $appIdMap = array(
        self::APPID_HOMEWORK => '作业帮',
        self::APPID_AIRCLASS => '一课',
    );

    const OS_ANDROID = 1;
    const OS_IOS = 2;
    public static $osMap = array(
        self::OS_ANDROID => 'android',
        self::OS_IOS => 'ios',
    );

    const POSITION_INDEX = 1;
    const POSITION_ENTERCLASS = 2;
    const POSITION_MYCOURSE = 4;
    const POSITION_ENTERIM = 8;
    const POSITION_NEWINDEX = 16;
    //顺序不能变!!!
    public static $positionMap = array(
        self::POSITION_INDEX => 'index',
        self::POSITION_ENTERIM => 'enterIm',
        self::POSITION_ENTERCLASS => 'enterClass',
        self::POSITION_MYCOURSE => 'myCourse',
        self::POSITION_NEWINDEX => 'new_index',
    );
    public static $positionDescMapHomework = array(
        self::POSITION_INDEX => '【作业帮】一课tab',
        self::POSITION_ENTERCLASS => '【作业帮】进入教室',
        self::POSITION_MYCOURSE => '【作业帮】我的课程',
        self::POSITION_ENTERIM => '【作业帮】IM群',
        self::POSITION_NEWINDEX => '【作业帮】上课tab',
    );
    public static $positionDescMapAirclass = array(
        self::POSITION_INDEX => '【一课】选课tab',
        self::POSITION_ENTERCLASS => '【一课】进入教室',
        self::POSITION_MYCOURSE => '【一课】我的课程',
        self::POSITION_ENTERIM => '【一课】IM群',
        self::POSITION_NEWINDEX => '【一课】上课tab',
    );

    const TYPE_GRAY = 1;
    const TYPE_NORMAL = 2;
    public static $typeMap = array(
        self::TYPE_GRAY => '灰度升级',
        self::TYPE_NORMAL => '普通升级',
    );

    const LATEST_NO = 0;
    const LATEST_YES = 1;

    public static $appIdZhibovcKeyMap = array(
        self::APPID_HOMEWORK => 'zbkvc',
        self::APPID_AIRCLASS => 'ykvc',
    );

    const MEM_KEY_PUBLISHED_TASK = 'update_task_published_';

    private static $Ins;
    private $cache;
    private $_objDaoAppUpdateTask;

    public function __construct()
    {
        $this->_objDaoAppUpdateTask = new Hkzb_Dao_Fudao_AppUpdateTask();
    }

    public static function getIns() {
        if (empty(self::$Ins)) {
            self::$Ins = new self();
        }
        return self::$Ins;
    }

    public function getCache() {
        if (empty($this->cache)) {
            $this->setCache(Hk_Service_Memcached::getInstance('zhiboke'));
        }
        return $this->cache;
    }

    public function setCache($cache) {
        $this->cache = $cache;
    }

    public function addTask($arrParams)
    {
        if (empty($arrParams) || !is_array($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $data = array(
            'name'            => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'type'            => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'appId'           => isset($arrParams['appId']) ? strval($arrParams['appId']) : '',
            'os'              => isset($arrParams['os']) ? strval($arrParams['os']) : '',
            'vc'              => isset($arrParams['vc']) ? intval($arrParams['vc']) : 0,
            'zhiboVc'         => isset($arrParams['zhiboVc']) ? intval($arrParams['zhiboVc']) : 0,
            'vcname'          => isset($arrParams['vcname']) ? strval($arrParams['vcname']) : '',
            'title'           => isset($arrParams['title']) ? strval($arrParams['title']) : '',
            'content'         => isset($arrParams['content']) ? strval($arrParams['content']) : '',
            'url'             => isset($arrParams['url']) ? strval($arrParams['url']) : '',
            'latest'          => isset($arrParams['latest']) ? intval($arrParams['latest']) : 0,
            'strategy'        => isset($arrParams['strategy']) ? json_encode($arrParams['strategy']) : '[]',
            'position'        => isset($arrParams['position']) ? intval($arrParams['position']) : 0,
            'forceUp'         => isset($arrParams['forceUp']) ? intval($arrParams['forceUp']) : 0,
            'startTime'       => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'endTime'         => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'operatorUid'     => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'status'          => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime'      => time(),
            'updateTime'      => time(),
            'extData'         => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );

        $ret = $this->_objDaoAppUpdateTask->insertRecords($data);
        if ($ret === false) {
            Bd_Log::warning("Insert app update task DB error!");
            return false;
        }
        $taskId = $this->_objDaoAppUpdateTask->getInsertId();
        return $taskId;
    }

    public function getPublishedTaskByAppId($appId, $intOs, $arrFields = array())
    {
        if (!isset(self::$appIdMap[$appId])) {
            Bd_Log::warning("Error:[param error], Detail:[appId:$appId]");
            return false;
        }
        $cacheKey = self::MEM_KEY_PUBLISHED_TASK . '_' . $appId . '_' . $intOs;
        $cacheValue = $this->getCache()->get($cacheKey);
        if ($cacheValue) {
            return $cacheValue;
        }
        $now = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $arrConds = array(
            'appId' => $appId,
            'status' => self::STATUS_VALID,
            'os' => $intOs,
            'end_time = 0 or (start_time < ' . $now . ' and end_time > ' . $now . ')',
        );
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoAppUpdateTask->getListByConds($arrConds, $arrFields,null,['order by id desc']);
        if ($ret) {
            $this->getCache()->set($cacheKey, $ret, 60);
        }
        return $ret;
    }

    public function getTaskByFinType($finType, $arrFields = array(), $offset = 0, $limit = 0)
    {
        $arrConds = array();
        if ($finType == self::FIN_TYPE_ALL) {
            $arrConds['status'] = array(self::STATUS_DELETED, '<>');
        } elseif ($finType == self::FIN_TYPE_NOT) {
            $arrConds[] = 'status in ('.self::STATUS_VALID.','.self::STATUS_PAUSE.')';
        } elseif ($finType == self::FIN_TYPE_HAD) {
            $arrConds['status'] = self::STATUS_FINISHED;
        } else {
            Bd_Log::warning('Error:[param error] Detail:[finType:'.$finType.']');
            return false;
        }
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        if ($limit > 0) {
            $arrAppends = array(
                "limit $offset,$limit",
            );
        }
        $ret = $this->_objDaoAppUpdateTask->getListByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }

    public function getCntByFinType($finType)
    {
        $arrConds = array();
        if ($finType == self::FIN_TYPE_ALL) {
            $arrConds['status'] = array(self::STATUS_DELETED, '<>');
        } elseif ($finType == self::FIN_TYPE_NOT) {
            $arrConds[] = 'status in ('.self::STATUS_VALID.','.self::STATUS_PAUSE.')';
        } elseif ($finType == self::FIN_TYPE_HAD) {
            $arrConds['status'] = self::STATUS_FINISHED;
        } else {
            Bd_Log::warning('Error:[param error] Detail:[finType:'.$finType.']');
            return false;
        }
        $ret = $this->_objDaoAppUpdateTask->getCntByConds($arrConds);
        return $ret;
    }

    public function getTaskById($id, $arrFields = array())
    {
        $id = intval($id);
        if ($id < 1) {
            Bd_Log::warning('Error:[param error] Detail:[id:'.$id.']');
            return false;
        }
        $arrConds = array(
            'id' => $id,
        );
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoAppUpdateTask->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    public function changeStatus($id, $status)
    {
        $id = intval($id);
        $status = intval($status);
        if ($id < 1 || !isset(self::$pubStatusMap[$status])) {
            Bd_Log::warning('Error:[param error] Detail:[id:'.$id.' status:'.$status.']');
            return false;
        }
        $arrConds = array(
            'id' => $id,
        );
        $arrFields = array(
            'status' => $status,
        );
        $ret = $this->_objDaoAppUpdateTask->updateByConds($arrConds, $arrFields);
        return $ret;
    }
}
