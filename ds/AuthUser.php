<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/07/27
 * Time: 11:44
 */
class Qdlib_Ds_AuthUser
{
    const ALL_FIELDS = 'id,userId,uname,name,email,phone,roles,hetuRoles,addDataAuth,createTime,updateTime,creator,operator,status,orgId,showCost,highAuth,reportHighAuth,highScope,reportModule,addRule';
    const DELETE_DEL = 1;

    private $_objDaoAuthUser;
    private $objRedis;

    const ORG_INFO_REDIS_KEY = 'tf_user_info_key_%s';

    public function __construct()
    {
        $this->_objDaoAuthUser = new Qdlib_Dao_AuthUser();
        $this->objRedis = Qdlib_Util_Cache::getQudaoRedis();
    }

    //新增
    public function addAuthUser($arrParams) {
        $arrFields = array(
            'id'          => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'userId'      => isset($arrParams['userId']) ? strval($arrParams['userId']) : '',
            'uname'       => isset($arrParams['uname']) ? strval($arrParams['uname']) : '',
            'name'        => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'email'       => isset($arrParams['email']) ? strval($arrParams['email']) : '',
            'phone'       => isset($arrParams['phone']) ? strval($arrParams['phone']) : '',
            'roles'       => isset($arrParams['roles']) ? strval($arrParams['roles']) : '',
            'addDataAuth' => isset($arrParams['addDataAuth']) ? intval($arrParams['addDataAuth']) : 1,
            'hetuRoles'   => isset($arrParams['hetuRoles']) ? strval($arrParams['hetuRoles']) : '',
            'createTime'  => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'  => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'creator'     => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'operator'    => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'status'      => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'orgId'       => isset($arrParams['orgId']) ? intval($arrParams['orgId']) : 0,
            'showCost'    => isset($arrParams['showCost']) ? intval($arrParams['showCost']) : 0,
            'highAuth'    => isset($arrParams['highAuth']) ? strval($arrParams['highAuth']) : '',
            'reportHighAuth' => isset($arrParams['reportHighAuth']) ? strval($arrParams['reportHighAuth']) : '',
            'highScope'    => isset($arrParams['highScope']) ? intval($arrParams['highScope']) : 0,
            'addRule'      => isset($arrParams['addRule']) ? intval($arrParams['addRule']) : 0,
            'reportModule' => isset($arrParams['reportModule']) ? strval($arrParams['reportModule']) : '',
        );
        $result = $this->_objDaoAuthUser->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'addAuthUser', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoAuthUser->getInsertId();
        return $id;
    }

    //编辑
    public function updateAuthUser($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $list = $this->_objDaoAuthUser->getListByConds($arrConds, $arrAllFields);
        $result = $this->_objDaoAuthUser->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'updateAuthUser', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        // 清除缓存
        foreach ($list as $k => $v){
            $key = sprintf(self::ORG_INFO_REDIS_KEY, $v["uname"]);
            $res = $this->objRedis->del($key);
        }
        return $result;
    }

    //通过id编辑
    public function updateAuthUserById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateAuthUser($arrConds, $arrFields);
    }

    //软删除
    public function deleteAuthUser($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateAuthUser($arrConds, $arrFields);
    }

    //获取信息
    public function getAuthUserInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoAuthUser->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'getAuthUserInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    public function getAuthUserInfoByUname($uname, $arrFields = array(), $arrConds = null)
    {
        $arrConds = [];
        $arrConds['uname'] = $uname;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoAuthUser->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'getAuthUserInfoByUname', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getAuthUserList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoAuthUser->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'getAuthUserList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getAuthUserTotal($arrConds = null)
    {
        $res = $this->_objDaoAuthUser->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthUser', 'getAuthUserTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    public function getInfoFromCacheByUname($uname)
    {
        $key = sprintf(self::ORG_INFO_REDIS_KEY, $uname);
        $info = $this->objRedis->get($key);
        if (!empty($info)) {
            return json_decode($info, true);
        }
        $info = $this->getAuthUserInfoByUname($uname);
        if (empty($info)) {
            return [];
        }
        $this->objRedis->set($key, json_encode($info), 3600 * 24 * 2);
        return $info;
    }
}