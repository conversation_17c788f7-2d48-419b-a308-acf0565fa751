<?php

/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @file  : OperationTuanActSku.php
 * @author: <EMAIL>
 * @date  : 2018年10月9日 下午2:46:50
 * @desc  : 活动对应的拼团商品策略
 *        【即将废弃不要使用】 @date 20200605
 */
class Oplib_Ds_OperationTuanActSku
{
    // 拍搜推荐状态
    const SEARCH_PAGE_RECOMMEND = 1;
    // 商品状态 - 有效
    const SKU_STATUS_VALID = 1;
    private $_objDaoTuanAs = null;

    // 查询项
    public static $ALL_FIELDS = 'id,posadId,tuanstId,skuId,isSearchPage,weight,createTime,updateTime,updatorUid,updatorName,status,stime,etime';

    public function __construct()
    {
        $this->_objDaoTuanAs = new Oplib_Dao_OperationTuanActSku();
    }

    public function getListByConds($arrConds, $arrFields = [], $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL)
    {
        if (0 >= count($arrFields)) {
            $arrFields = explode(',', self::$ALL_FIELDS);
        }
        return $this->_objDaoTuanAs->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function getCntByConds($arrConds)
    {
        return $this->_objDaoTuanAs->getCntByConds($arrConds);
    }

    public function addOperationTuanActSku($arrParams){
        if(empty($arrParams)){
            Bd_Log::warning("Error:[param error],Detail:[".json_encode($arrParams)."]");
            return false;
        }

        $ret = $this->_objDaoTuanAs->insertRecords($arrParams);
        if($ret){
            $ret = $this->_objDaoTuanAs->getInsertId();
        }
        return $ret;
    }

    public function updateOperationTuanActSku($id, $arrParams){
        if(intval($id) <= 0 || empty($arrParams)){
            Bd_Log::warning("Error:[param error],Detail:[id:$id, arrfields:". json_encode($arrParams) ."]");
            return false;
        }
        $arrConds = array(
            'id' => intval($id),
        );
        $arrFields = array();
        $arrAllFields = explode(',',self::$ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key,$arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }

        $ret = $this->_objDaoTuanAs->updateByConds($arrConds,$arrFields);
        return $ret;
    }

    public function updateOperationTuanActSkuByTusanStId($tusanStId, $arrParams){
        if(intval($tusanStId) <= 0 || empty($arrParams)){
            Bd_Log::warning("Error:[param error],Detail:[tusanStId:$tusanStId, arrfields:". json_encode($arrParams) ."]");
            return false;
        }
        $arrConds = array(
            'tuanstId' => intval($tusanStId),
        );
        $arrFields = array();
        $arrAllFields = explode(',',self::$ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key,$arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }

        $ret = $this->_objDaoTuanAs->updateByConds($arrConds,$arrFields);
        return $ret;
    }

    /**
     * 根据活动id获取团活动商品
     * <AUTHOR>
     * @DateTime 2018-10-12
     * @param    int             $posId           活动id
     * @param    bool            $is_search_page  是否在拍搜展示
     * @param    array           $arrFields       字段信息
     * @param    int             $offset          偏移量
     * @param    int             $limit           限制量
     * @param    string          $order           排序字段
     * @param    string          $by              排序标准
     * @return   array
     */
    public function getTuanActSkuListByPosId($posId, $isSearchPage = false, $arrFields = [], $offset = null, $limit = null, $order = '', $by = '')
    {
        if (intval($posId) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[posId:$posId]");
            return [];
        }

        $time = time();
        $arrConds = [
            'posad_id' => $posId,
            'status'   => self::SKU_STATUS_VALID,
            "stime <= {$time}",
            "etime >= {$time}",
        ];
        $isSearchPage && $arrConds['is_search_page'] = self::SEARCH_PAGE_RECOMMEND;
        empty($arrFields) && $arrFields = explode(',', self::$ALL_FIELDS);
        $arrAppends = Oplib_Util_Tool::makeSelectOrderAndLimit($offset, $limit, $order, $by);

        return $this->_objDaoTuanAs->getListByConds($arrConds, $arrFields, null, $arrAppends);
    }
}