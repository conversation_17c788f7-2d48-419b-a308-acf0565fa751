<?php

class Qdlib_Ds_Project_ProjectLv1
{
    private $_daoLv1;

    public function __construct()
    {
        $this->_daoLv1 = new Qdlib_Dao_Project_ProjectLv1();
    }

    //新增
    public function add($param)
    {
        if (empty($param) || !is_array($param)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $param
            ]);
        }

        $ret = $this->_daoLv1->insertRecords($param);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }

        return $this->_daoLv1->getInsertId();
    }

    //编辑
    public function update($conds, $param)
    {
        if (empty($param) || !is_array($param)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $param
            ]);
        }

        $arrAllFields = $this->_daoLv1->getAllFields();
        foreach ($param as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $ret = $this->_daoLv1->updateByConds($conds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;
    }

    public function getByConds($arrConds = null, $arrFields = [], $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL)
    {
        if (!is_array($arrConds) && !empty($arrConds)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $arrConds
            ]);
        }
        $arrConds = empty($arrConds) ? null : $arrConds;
        if (empty($arrFields)) {
            $arrFields = $this->_daoLv1->getAllFields();
        }
        $ret = $this->_daoLv1->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;

    }

    public function getCntByConds($arrConds = null)
    {
        $ret = $this->_daoLv1->getCntByConds($arrConds);
        if (false === $ret) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");

            return false;
        }

        return $ret;
    }

    public function delByConds($arrConds)
    {
        if (!is_array($arrConds) || empty($arrConds)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $arrConds
            ]);
        }
        $ret = $this->_daoLv1->deleteByConds($arrConds);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;

    }

}