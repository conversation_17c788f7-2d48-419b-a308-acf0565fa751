<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/12/21
 * Time: 11:30
 */

class Oplib_Ds_BuildToolPublished{


    const ALL_FIELDS = 'id,pageId,pageName,sendChannel,share,shareTitle,shareDesc,sharePic,content,bgColor,discuss,status,deleted,founder,founderUid,createTime,updateTime,extData,needLogin,actendPage,tplType,pageConfig';
    const SHOW_PUBLISH_BASE_FIELDS = 'pageId,pageName,bgColor,content,discuss,sendChannel,share,shareTitle,shareDesc,sharePic,sendStatus,updateTime,extData,needLogin,actendPage,tplType,pageConfig';

    private $objDaoBuildToolPublished;
    private $orderBy = array('create_time','update_time','page_id');

    private $arrHotPageId = [103061,103062,103063,103064,103065,103066];

    /**
     * Services_Data_BuildToolPublished constructor.
     */
    public function __construct(){
        $this->objDaoBuildToolPublished = new Oplib_Dao_BuildToolPublished();
    }

    /**
     * 新增记录
     * @param $arrParams
     * @return bool
     */
    public function addBuildToolPublished($arrParams){

        $arrFields = array(
            'pageId'          => isset($arrParams['pageId'])  ? intval($arrParams['pageId']) : 0,
            'pageName'        => isset($arrParams['pageName']) ? strval($arrParams['pageName']):'',
            'sendChannel'     => isset($arrParams['sendChannel']) ? intval($arrParams['sendChannel']):0,
            'share'           => isset($arrParams['share']) ? strval($arrParams['share']):0,
            'shareTitle'      => isset($arrParams['shareTitle']) ? strval($arrParams['shareTitle']):'',
            'shareDesc'       => isset($arrParams['shareDesc']) ? strval($arrParams['shareDesc']):'',
            'sharePic'        => isset($arrParams['sharePic']) ? strval($arrParams['sharePic']):'',
            'content'         => isset($arrParams['content']) ? strval($arrParams['content']):'',
            'bgColor'         => isset($arrParams['bgColor']) ? strval($arrParams['bgColor']):'',
            'founder'         => isset($arrParams['founder']) ? strval($arrParams['founder']) : '',
            'founderUid'      => isset($arrParams['founderUid']) ? intval($arrParams['founderUid']):0,
            'discuss'         => isset($arrParams['discuss'])? intval($arrParams['discuss']): 0,
            'status'          => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'type'            => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'needLogin'       => isset($arrParams['needLogin']) ? intval($arrParams['needLogin']) : 0,
            'actendPage'      => isset($arrParams['actendPage']) ? intval($arrParams['actendPage']) : 0,
            'tplType'         => isset($arrParams['tplType']) ? intval($arrParams['tplType']) : 0,
            'createTime'      => time(),
            'updateTime'      => time(),
            'extData'         => isset($arrParams['extData']) ? json_encode($arrParams['extData']):'[]',
            'pageConfig'      => isset($arrParams['pageConfig']) ? json_encode($arrParams['pageConfig']):'[]',
        );
        if($arrFields['pageId'] <= 0 || empty($arrFields['pageName']) || empty($arrFields['content'])){
            Bd_Log::warning("Error:[param error],Detail:[".json_encode($arrParams)."]");
            return false;
        }
        $ret = $this->objDaoBuildToolPublished->insertRecords($arrFields);
        if($ret){
            $this->getPublishContentFromCache($arrFields['pageId'],true);
        }
        return $ret;

    }

    /**
     * 修改记录
     * @param $pageId
     * @param $arrParams
     * @return bool
     */
    public function updateBuildToolPublished($pageId,$arrParams){
        if(intval($pageId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[pageId:$pageId]");
            return false;
        }
        $arrConds = array(
            'pageId' => intval($pageId),
        );
        $arrFields = array();
        foreach($arrParams as $key => $val){
            if(!in_array($key,explode(',',self::ALL_FIELDS))){
                continue;
            }
            $arrFields[$key] = $val;
        }
        if(isset($arrFields['extData'])){
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        if(isset($arrFields['pageConfig'])){
            $arrFields['pageConfig'] = json_encode($arrFields['pageConfig']);
        }
        $arrFields['updateTime'] = time();
        $ret = $this->objDaoBuildToolPublished->updateByConds($arrConds,$arrFields);
        if($ret !== false){
            // 强制更新下缓存
            $this->getPublishContentFromCache($pageId,true);
        }

        return $ret;
    }



    /**
     * 根据筛选条件查询数量
     * @param $arrConds
     * @return false|int
     */
    public function getCntByConds($arrConds){
        return $this->objDaoBuildToolPublished->getCntByConds($arrConds);
    }

    /**
     * 根据筛选条件查询记录
     * @param $arrConds
     * @param array $arrParams
     * @param string $order
     * @param string $by
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getListByConds($arrConds,$arrParams = array(),$order = '',$by = '',$offset = 0,$limit = 0){
        $arrFields = array();
        $allFields = explode(',',self::ALL_FIELDS);
        if(empty($arrParams)){
            $arrFields = $allFields;
        }else{
            foreach ($arrParams as $val){
                if(!in_array($val,$allFields)){
                    continue;
                }
                $arrFields[] = $val;
            }
        }
        $orderBy = '';
        if(!empty($order) && in_array($order,$this->orderBy)){
            $orderBy .= 'order by '.$order.' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
        if($offset >= 0 && $limit > 0 ){
            $orderBy .= " limit $offset,$limit";
        }else if($limit > 0){
            $orderBy .= " limit $limit";
        }
        $arrAppends = (!empty($orderBy)) ? array($orderBy) : null;
        $ret = $this->objDaoBuildToolPublished->getListByConds($arrConds,$arrFields,null,$arrAppends);
        return $ret;
    }

    /**
     * 根据pageId获取一条记录
     * @param $pageId
     * @param array $arrParams
     * @return array|bool|false
     */
    public function getOneByPageId($pageId,$arrParams = array()){
        if(intval($pageId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[pageId:$pageId]");
            return false;
        }
        $arrFields = array();
        $allFields = explode(',',self::ALL_FIELDS);
        if(empty($arrParams)){
            $arrFields = $allFields;
        }else{
            foreach ($arrParams as  $val){
                if(!in_array($val,$allFields)){
                    continue;
                }
                $arrFields[] = $val;
            }
        }
        $arrConds = array(
            'pageId'  => $pageId,
            'deleted'  => Oplib_Ds_BuildTool::STATUS_NO_DELETE,
        );
        $ret = $this->objDaoBuildToolPublished->getRecordByConds($arrConds,$arrFields);
        return $ret;
    }

    /**
     * 根据页面ID数组获取数据
     * @param $arrId
     * @param $arrParams
     * @return array|bool|false
     */
    public function getArrByPageIds($arrId,$arrParams){
        if(!is_array($arrId) || empty($arrId)){
            Bd_Log::warning("Error:[param error],Detail:[arrId:".json_encode($arrId)."]");
            return false;
        }
        $arrConds[] = "page_id in (".implode(',',$arrId).") ";
        $arrFields = array();
        $allFields = explode(',',self::ALL_FIELDS);
        if(empty($arrParams)){
            $arrFields = $allFields;
        }else{
            foreach ($arrParams as  $val){
                if(!in_array($val,$allFields)){
                    continue;
                }
                $arrFields[] = $val;
            }
        }
        $ret = $this->objDaoBuildToolPublished->getListByConds($arrConds,$arrFields);
        return $ret;
    }


    /**
     * 逻辑删除该条记录
     * @param $pageId
     * @return bool
     */
    public function deleteOneByPageId($pageId){
        if(intval($pageId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[pageId:$pageId]");
            return false;
        }
        $arrConds = array(
            'pageId'  => $pageId,
            'deleted'  => Oplib_Ds_BuildTool::STATUS_NO_DELETE,
        );
        $arrFields = array(
           'deleted' => Oplib_Ds_BuildTool::STATUS_DELETED,
           'updateTime' => time(),
        );
        $ret = $this->objDaoBuildToolPublished->updateByConds($arrConds,$arrFields);
        if($ret !== false){
            // 强制更新下缓存
            $this->getPublishContentFromCache($pageId,true);
        }
        return $ret;
    }
    /**
     * 下线该条记录
     * @param $pageId
     * @return bool
     */
    public function OutlineByPageId($pageId){
        if(intval($pageId) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[pageId:$pageId]");
            return false;
        }
        $arrConds = array(
          'pageId'  => $pageId,
        );
        $arrFields = array(
           'status' => Oplib_Ds_BuildTool::STATUS_OUTLINE,
           'updateTime' => time(),
        );
        $ret = $this->objDaoBuildToolPublished->updateByConds($arrConds,$arrFields);
        if($ret !== false){
            // 强制更新下缓存
            $this->getPublishContentFromCache($pageId,true);
        }
        return $ret;
    }

    //###########从缓存中读取数据 ##################
    /**
     * @brief  根据pageId获取发布后的内容，内容发布会需要调用该方法，强制更新下缓存
     * <AUTHOR>
     * @date   2019/3/30 16:01
     * @param      $pageId
     * @param bool $isForceUpdateCahce  是否强制更新缓存
     * @return array|bool|false|mixed
     */
    public function getPublishContentFromCache($pageId, $isForceUpdateCahce = false)
    {
        $arrOutput = ['data' => []];

        $pageId = intval($pageId);
        if ($pageId <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[pageId:{$pageId}]");

            return false;
        }
        if(in_array($pageId, $this->arrHotPageId)){
            if( !$isForceUpdateCahce ){
                // 临时增加yac缓存
                $keys = 'BUILDTOOL_PAGE_' . $pageId;
                $yac = Hk_Service_YacClient::getInstance();
                $oriRet = $yac->get($keys);
                if($oriRet!== false){
                    Bd_Log::addNotice($keys . 'yac_hit2','yes');
                    $ret        = $oriRet ? (array)json_decode($oriRet, true) : [];
                    // 命中缓存
                    return $ret['data'];
                }else{
                    Bd_Log::addNotice($keys . 'yac_hit2','no');
                }
            }

        }else{
            $pageIdForCache = $pageId;
        }
        $cacheKey = Oplib_Util_BuildToolCache::getCacheKey(Oplib_Util_BuildToolCache::PUBLISHED_INFO_KEY, $pageIdForCache);
        $objRedis = Oplib_Util_BuildToolCache::getCacheInstance('redis');
        $arrFields = explode(',', self::SHOW_PUBLISH_BASE_FIELDS);
        // 强制更新缓存，读数据库
        if ($isForceUpdateCahce) {
            $ret = $this->getOneByPageId($pageId, $arrFields);
            if ($ret === false) {

                return $ret;
            }
            $arrOutput['data'] = $ret;

            if(in_array($pageId, $this->arrHotPageId)){
                // 临时增加yac缓存
                $keys = 'BUILDTOOL_PAGE_' . $pageId;
                $yac = Hk_Service_YacClient::getInstance();
                $oriRet = $yac->set($keys,json_encode($arrOutput),$ret ? 300 : 60);
                Bd_Log::addNotice($keys . 'yac_set2','yes');
                return $ret;
            }

            // 没有查询到数据，设置缓存60秒缓存
            if (empty($ret)) {
                $objRedis && $objRedis->set($cacheKey, json_encode($arrOutput), 60);
            } else {
                // 查询到数据设置缓存一天时间
                $objRedis && $objRedis->set($cacheKey, json_encode($arrOutput), 86000 + rand(1, 600));
            }

            return $ret;
        }
        if ($objRedis && !in_array($pageId, $this->arrHotPageId)) {
            $cacheValue = $objRedis->get($cacheKey);
            $ret        = $cacheValue ? (array)json_decode($cacheValue, true) : [];
            // 命中缓存
            if (array_key_exists('data', $ret)) {
                Bd_Log::addNotice($cacheKey . 'hit', 1);

                return $ret['data'];
            }
            // 没有命中缓存
            Bd_Log::addNotice($cacheKey . 'hit', 0);
        }

        // 查询数据库
        $ret = $this->getOneByPageId($pageId, $arrFields);
        if ($ret === false) {

            return $ret;
        }
        $arrOutput['data'] = $ret;
        // 没有查询到数据，设置缓存60秒缓存
        if( in_array($pageId, $this->arrHotPageId) ){
            // 临时增加yac缓存
            $keys = 'BUILDTOOL_PAGE_' . $pageId;
            $yac = Hk_Service_YacClient::getInstance();
            $oriRet = $yac->set($keys,json_encode($arrOutput),$ret ? 300 : 60);
            Bd_Log::addNotice($keys . 'yac_set2','yes');
        }else{
            if (empty($ret)) {
                $objRedis && $objRedis->set($cacheKey, json_encode($arrOutput), 60);
            } else {
                // 查询到数据设置缓存一天时间
                $objRedis && $objRedis->set($cacheKey, json_encode($arrOutput), 86000 + rand(1, 600));
            }
        }

        return $ret;
    }
}