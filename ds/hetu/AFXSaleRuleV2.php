<?php

class Qdlib_Ds_Hetu_AFXSaleRuleV2 extends Qdlib_Ds_Hetu_Hetu
{
    const NOLIMIT = -1;

    const STATUS_ON = 1;
    const STATUS_OFF = 2;
    const STATUS_NAMES = [
        self::STATUS_ON => '开启',
        self::STATUS_OFF => '关停',
    ];

    const CACHE_CONF_TEMPLATE = 'hetu:act:stock:conf:%d:%d:%d:%d:%d:%s';
    const CACHE_PAID_TEMPLATE = 'hetu:act:stock:paid:%d';
    const CACHE_REFUNDED_TEMPLATE = 'hetu:act:stock:refunded:%d';
    const CACHE_TTL = 600;
    const CACHE_LOCK_TEMPLATE = 'hetu:act:rule:lock:%d';
    const CACHE_LOCK_TTL = 10;

    const Incrby_Script = 'local exists = redis.call("exists", KEYS[1]); if (exists == 1) then return redis.call("incrby", KEYS[1], ARGV[1]); end return -1;';
    const Incrby_Script_SHA1 = '86a52483af872952adca1e8e5c0495005b0c278d';

    private $redis;

    public function __construct()
    {
        parent::__construct(new Qdlib_Dao_Hetu_AFXSaleRuleV2());
        $this->redis = Qdlib_Util_Cache::getQudaoRedis();
    }

    public function lock(int $actId): bool
    {
        if ($this->redis->set(sprintf(self::CACHE_LOCK_TEMPLATE, $actId), '1', ['NX', 'EX' => self::CACHE_LOCK_TTL])) {
            return true;
        }
        return false;
    }

    public function unlock(int $actId): bool
    {
        return is_int($this->redis->del(sprintf(self::CACHE_LOCK_TEMPLATE, $actId)));
    }

    public function option(int $skuId, int $gradeId, int $seasonId, $agentId = 0): string
    {
        $val = implode(',', [$skuId, $gradeId, $seasonId, $agentId]);
        return $val;
    }

    public function getConfigs(int $actId, int $instId, array $options, int $currentTimestamp)
    {
        $options = array_values($options);
        $item = [
            'id' => 0,
            'beginTime' => 0,
            'endTime' => 0,
            'threshold' => self::NOLIMIT,
        ];
        $date = date('Ymd', $currentTimestamp);
        $keys = [];
        $result = [];
        foreach ($options as $index => $option) {
            $result[$option] = $item;
            $keys[$index] = $this->configCacheKey($actId, $instId, $option, $date);
        }

        // redis mget
        $values = $this->redis->mget($keys);
        Qdlib_Util_Log::addNotice('sale_rule_conf_cache_mget', json_encode([
            'keys' => $keys,
            'values' => $values,
        ], JSON_UNESCAPED_UNICODE));

        $dbOptions = [];
        if (is_array($values) && count($values) == count($keys)) {
            foreach ($values as $index => $value) {
                if ($value && $value = json_decode($value, true)) {
                    $result[$options[$index]] = $value;
                } else {
                    $dbOptions[$index] = $options[$index];
                }
            }
        } else {
            $dbOptions = $options;
        }

        // db select
        if (!empty($dbOptions)) {
            $conditions = [
                'actId' => $actId,
                'instId' => $instId,
                'beginTime' => [$currentTimestamp, '<'],
                'endTime' => [$currentTimestamp, '>'],
                'status' => self::STATUS_ON,
            ];
            $conditions[] = '(sku_id,grade_id,season_id,agent_id) in ((' . implode('),(', $dbOptions) . '))';
            $records = $this->getListByConds($conditions, array_merge(array_keys($item), ['skuId', 'gradeId', 'seasonId', 'agentId']));
            if (false === $records) {
                return false;
            }
            foreach ($records as $record) {
                $result[$this->option($record['skuId'], $record['gradeId'], $record['seasonId'], $record['agentId'])] = array_intersect_key($record, $item);
            }

            // redis set
            foreach ($dbOptions as $index => $option) {
                $tmp = $this->redis->set($keys[$index], json_encode($result[$option]), self::CACHE_TTL);
                Qdlib_Util_Log::addNotice('sale_rule_conf_cache_set_' . (string)$index, json_encode([
                    'key' => $keys[$index],
                    'value' => $result[$option],
                    'result' => $tmp,
                ], JSON_UNESCAPED_UNICODE));
            }
        }

        return $result;
    }

    public function getNum(int $configId, bool $cache = true)
    {
        $result = [
            'paidNum' => 0,
            'refundedNum' => 0,
        ];

        if ($cache) {
            $paidKey = $this->paidCacheKey($configId);
            $refundedKey = $this->refundedCacheKey($configId);
            $values = Qdlib_Util_Cache::getQudaoRedis()->mget([$paidKey, $refundedKey]);
            if (is_array($values)) {
                foreach ($values as $index => $value) {
                    if (!is_string($value) || strlen($value) == 0) {
                        unset($values[$index]);
                    }
                }
                if ($values && count($values) == 2) {
                    Qdlib_Util_Log::addNotice('sale_rule_num_cache_mget', json_encode([
                        'keys' => [$paidKey, $refundedKey],
                        'result' => $values,
                    ], JSON_UNESCAPED_UNICODE));
                    return array_combine(array_keys($result), array_map('intval', $values));
                }
            }
        }

        $record = $this->getRecordByConds(['id' => $configId], ['paidNum', 'refundedNum']);
        if (false === $record) {
            return false;
        }

        if (empty($record)) {
            return false;
        }

        if ($cache) {
            $tmp1 = Qdlib_Util_Cache::getQudaoRedis()->set($paidKey, $record['paidNum'], self::CACHE_TTL);
            Qdlib_Util_Log::addNotice('sale_rule_paidnum_cache_set', json_encode([
                'key' => $paidKey,
                'value' => $record['paidNum'],
                'result' => $tmp1,
            ], JSON_UNESCAPED_UNICODE));
            $tmp2 = Qdlib_Util_Cache::getQudaoRedis()->set($refundedKey, $record['refundedNum'], self::CACHE_TTL);
            Qdlib_Util_Log::addNotice('sale_rule_refundednum_cache_set', json_encode([
                'key' => $refundedKey,
                'value' => $record['refundedNum'],
                'result' => $tmp2,
            ], JSON_UNESCAPED_UNICODE));
        }

        $result['paidNum'] = $record['paidNum'];
        $result['refundedNum'] = $record['refundedNum'];
        return $result;
    }

    public function delConfCache(int $actId, array $instIdOptionsMap, int $currentTimestamp): bool
    {
        $keys = [];
        $date = date('Ymd', $currentTimestamp);
        foreach ($instIdOptionsMap as $instId => $options) {
            foreach ($options as $option) {
                $keys[] = $this->configCacheKey($actId, $instId, $option, $date);
            }
        }
        $tmp = Qdlib_Util_Cache::getQudaoRedis()->del($keys);
        Qdlib_Util_Log::addNotice('sale_rule_conf_cache_del', json_encode([
            'keys' => $keys,
            'result' => $tmp,
        ], JSON_UNESCAPED_UNICODE));
        return is_int($tmp);
    }

    public function paidNumIncr(int $configId, int $num)
    {
        return $this->updateByConds(['id' => $configId], ['paid_num=paid_num+' . (string)$num]);
    }

    public function refundedNumIncr(int $configId, int $num)
    {
        return $this->updateByConds(['id' => $configId], ['refunded_num=refunded_num+' . (string)$num]);
    }

    public function paidNumCacheIncr(int $configId, int $num)
    {
        return $this->cacheIncrby($this->paidCacheKey($configId), $num);
    }

    public function refundedNumCacheIncr(int $configId, int $num)
    {
        return $this->cacheIncrby($this->refundedCacheKey($configId), $num);
    }

    private function cacheIncrby(string $key, int $value)
    {
        $result = (Qdlib_Util_Cache::getQudaoRedis())->evalSha(self::Incrby_Script_SHA1, [$key, $value], 1);
        if (false === $result) {
            Qdlib_Util_Log::addNotice('sale_rule_incrby_script', 'miss');
            $result = (Qdlib_Util_Cache::getQudaoRedis())->eval(self::Incrby_Script, [$key, $value], 1);
        }
        Qdlib_Util_Log::addNotice('sale_rule_cache_incrby', json_encode([
            'key' => $key,
            'value' => $value,
            'result' => $result,
        ], JSON_UNESCAPED_UNICODE));
        if (is_int($result)) {
            return $result;
        }
        return false;
    }

    private function configCacheKey(int $actId, int $instId, string $option, string $date): string
    {
        $tmp = explode(',', $option);
        $confKey = sprintf(self::CACHE_CONF_TEMPLATE, $actId, $instId, $tmp[0], $tmp[1], $tmp[2], $date);
        if (count($tmp) > 3) {
            $confKey = $confKey . ':' . $tmp[3];
        }
        return $confKey;
    }

    private function paidCacheKey(int $configId)
    {
        return sprintf(self::CACHE_PAID_TEMPLATE, $configId);
    }

    private function refundedCacheKey(int $configId)
    {
        return sprintf(self::CACHE_REFUNDED_TEMPLATE, $configId);
    }
}