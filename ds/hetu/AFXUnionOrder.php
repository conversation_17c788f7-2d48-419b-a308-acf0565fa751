<?php

class Qdlib_Ds_Hetu_AFXUnionOrder extends Qdlib_Ds_Hetu_Hetu
{
    /**
     * 订单来源
     */
    const SOURCE_H5 = 1;
    const SOURCE_CDKEY = 2;
    const SOURCE_MANUAL = 3;
    const SOURCE_NAMES = [
        self::SOURCE_H5 => 'H5',
        self::SOURCE_CDKEY => '兑换码',
        self::SOURCE_MANUAL => '手动导单',
    ];

    /**
     * 订单域
     * http://ued.zuoyebang.cc/documents/docs/dds/domain.html
     */
    const DOMAIN_DAR = 3; // dar订单

    /**
     * 订单状态
     * http://ued.zuoyebang.cc/documents/docs/dds/tradeStatus.html
     */
    const STATUS_PAID = 1;
    const STATUS_REFUNDED = 2;
    const STATUS_REFUNDPART = 21;
    const STATUS_NAMES = [
        self::STATUS_PAID => '已支付',
        self::STATUS_REFUNDED => '已退款',
        self::STATUS_REFUNDPART => '部分退款',
    ];

    /**
     * 订单最终状态
     */
    const FINAL_STATUS_PAID = 1;
    const FINAL_STATUS_REFUNDED = 2;
    const FINAL_STATUS_REFUNDPART = 21;
    const FINAL_STATUS_NAMES = [
        self::FINAL_STATUS_PAID => '已支付',
        self::FINAL_STATUS_REFUNDED => '已退款',
        self::FINAL_STATUS_REFUNDPART => '部分退款',
    ];

    /**
     * 订单状态机
     */
    const FINAL_STATUS_STATE_MACHINE = [
        self::FINAL_STATUS_PAID => [
            self::FINAL_STATUS_REFUNDED => true,
            self::FINAL_STATUS_REFUNDPART => true,
        ],
        self::FINAL_STATUS_REFUNDPART => [
            self::FINAL_STATUS_REFUNDED => true,
        ],
        self::FINAL_STATUS_REFUNDED => [
            self::FINAL_STATUS_REFUNDPART => true, // 联运订单如果先收到拆单后的已退款订单 然后收到拆单后的已支付订单 需要将主订单状态从退款改为部分退款
        ],
    ];

    public function __construct()
    {
        parent::__construct(new Qdlib_Dao_Hetu_AFXUnionOrder());
    }
}