<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/07/26
 * Time: 20:44
 */
class Qdlib_Ds_Hetu_CouponCodeTask
{
    const ALL_FIELDS = 'recordId,recordDesc,applyNum,createName,createTime,processStep,itemId,discount,itemDeleted';

    protected $objDaoCouponCodeTask;

    public function __construct()
    {
        $this->objDaoCouponCodeTask = new Qdlib_Dao_Hetu_CouponCodeTask();
    }

    //新增
    public function addCouponCodeTask($arrParams) {
        $arrFields = array(
            'recordId'    => isset($arrParams['recordId']) ? intval($arrParams['recordId']) : 0,
            'recordDesc'  => isset($arrParams['recordDesc']) ? strval($arrParams['recordDesc']) : '',
            'applyNum'  => intval($arrParams['applyNum']),
            'createName'  => isset($arrParams['createName']) ? strval($arrParams['createName']) : '',
            'createTime'  => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'processStep' => isset($arrParams['processStep']) ? strval($arrParams['processStep']) : '',
            'itemId'      => isset($arrParams['itemId']) ? intval($arrParams['itemId']) : 0,
            'discount' => intval($arrParams['discount']),
            'itemDeleted' => isset($arrParams['itemDeleted']) ? intval($arrParams['itemDeleted']) : 0,
        );
        $result = $this->objDaoCouponCodeTask->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'addCouponCodeTask', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        return $this->objDaoCouponCodeTask->getAffectedRows() ? $arrFields['recordId'] : 0;
    }

    //新增多条
    public function addCouponCodeTaskList($rows)
    {
        $fields = explode(",", self::ALL_FIELDS);
        $new_rows = array();
        foreach($rows as $row)
        {
            $new_row = array();
            foreach($fields as $field)
            {
                $new_row[$field] = $row[$field];
            }
            $new_rows[] = $new_row;
        }
        return $this->objDaoCouponCodeTask->multiInsert($new_rows, $fields, 'ignore');
    }

    //编辑
    public function updateCouponCodeTask($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoCouponCodeTask->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'updateCouponCodeTask', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateCouponCodeTaskById($recordId, $arrFields) {
        $arrConds = ['recordId' => $recordId];
        return $this->updateCouponCodeTask($arrConds, $arrFields);
    }

    //获取信息
    public function getCouponCodeTaskInfoById($recordId, $arrFields = array(), $arrConds = null)
    {
        $arrConds['recordId'] = $recordId;
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoCouponCodeTask->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'getCouponCodeTaskInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getCouponCodeTaskList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoCouponCodeTask->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'getCouponCodeTaskList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getCouponCodeTaskTotal($arrConds = null)
    {
        $res = $this->objDaoCouponCodeTask->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'getCouponCodeTaskTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    //获取itemList
    public function getItemList($arrConds = null){
        $result = $this->objDaoCouponCodeTask->getListByConds($arrConds, ['itemId'], 'distinct');
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'CouponCodeTask', 'getItemList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }
}