<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : TeacherSaysComments.php
 * Author: niuxion<PERSON><PERSON>@zuoyebang.com
 * Date: 2019/6/11
 * Time: 10:42
 * Desc:
 */
class Oplib_Ds_TeacherSay_TeacherSaysComments
{

    //发布者类型
    const PUBLISH_STUDENT = 0;
    const PUBLISH_TEACHER = 1;
    static $PUBLISH_ARRAY = array(
        self::PUBLISH_STUDENT => '学生',
        self::PUBLISH_TEACHER => '老师',
    );

    //评论类型
    const COMMENT_REVIEW = 0;
    const COMMENT_REPLY  = 1;
    static $COMMENT_ARRAY = array(
        self::COMMENT_REVIEW => '评论',
        self::COMMENT_REPLY  => '回复',
    );

    //评论状态
    const STATUS_WAITCHECK = 0;
    const STATUS_PASS = 1;
    const STATUS_NOTPASS = 2;
    const STATUS_DELETED = 3;
    static $STATUS_ARRAY = array(
        self::STATUS_WAITCHECK => '待审核',
        self::STATUS_PASS      => '审核通过',
        self::STATUS_NOTPASS   => '审核不通过',
        self::STATUS_DELETED   => '删除',
    );

    //操作人类型
    const OPTYPE_STUDENT = 0;
    const OPTYPE_TEACHER = 1;
    const OPTYPE_MIS     = 2;
    const OPTYPE_MIS_ANTI = 3;
    static $OPTYPE_ARRAY = array(
        self::OPTYPE_STUDENT => '学生',
        self::OPTYPE_TEACHER => '老师',
        self::OPTYPE_MIS     => '后台人员',
        self::OPTYPE_MIS_ANTI => '反作弊后台',
    );

    const ALL_FIELDS = 'id,msgId,pubUid,pubType,commentId,parentId,content,type,opname,optype,stat,createTime,updateTime,likeCnt';

    private $objDaoTeacherSaysComments;
    private $objDaoTeacherSays;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoTeacherSaysComments = new Oplib_Dao_TeacherSay_TeacherSaysComments();
        $this->objDaoTeacherSays = new Oplib_Dao_TeacherSay_TeacherSays();
    }

    /**
     * 新增评论
     *
     * @param  array $arrParams 评论属性
     * @return bool true/false
     */
    public function addTeacherSaysComments($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['msgId']) <= 0 || intval($arrParams['pubUid']) <= 0 || intval($arrParams['commentId']) < 0 || intval($arrParams['parentId']) < 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'msgId'        => isset($arrParams['msgId']) ? intval($arrParams['msgId']) : 0,
            'pubUid'       => isset($arrParams['pubUid']) ? intval($arrParams['pubUid']) : 0,
            'pubType'      => isset($arrParams['pubType']) ? intval($arrParams['pubType']) : 0,
            'commentId'    => isset($arrParams['commentId']) ? intval($arrParams['commentId']) : 0,
            'parentId'     => isset($arrParams['parentId']) ? intval($arrParams['parentId']) : 0,
            'content'      => isset($arrParams['content']) ? strval($arrParams['content']) : 0,
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'opname'       => isset($arrParams['opname']) ? intval($arrParams['opname']) : 0,
            'optype'       => isset($arrParams['optype']) ? intval($arrParams['optype']) : 0,
            'stat'         => isset($arrParams['stat']) ? intval($arrParams['stat']) : 0,
            'createTime'   => time(),
            'updateTime'   => time(),
        );

        $ret = $this->objDaoTeacherSaysComments->insertRecords($arrParams['msgId'], $arrFields);

        return $ret === true ? $this->objDaoTeacherSaysComments->getInsertId() : false;
    }


    /**
     * 更新评论
     *
     * @param  int $msgId     动态id，用于分表
     * @param  int $id id   评论id
     * @param  array $arrParams  评论属性
     * @return bool true/false
     */
    public function updateTeacherSaysComments($msgId, $id, $arrParams,$conds=array())
    {
        if (empty($arrParams) || intval($msgId) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgId:$msgId arrParams:$arrParams]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        if (is_array($conds)) {
            $arrConds = array_merge($arrConds, $conds);
        } else {
            $arrConds = array_merge($arrConds, array($conds));
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoTeacherSaysComments->updateByConds($msgId, $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 更新评论
     *
     * @param  int $msgId     动态id，用于分表
     * @param  int $arrCond 主评论id type回复   评论id
     * @param  array $arrParams  评论属性
     * @return bool true/false
     */
    public function updateTeacherSaysCommentsByConds($msgId, $arrCond, $arrParams)
    {
        if (empty($arrParams) || intval($msgId) <= 0 || !is_array($arrCond) || empty($arrCond)) {
            Bd_Log::warning("Error:[param error], Detail:[arrConds:$arrCond msgId:$msgId arrParams:$arrParams]");
            return false;
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);

        $arrConds   = array();
        foreach ($arrCond as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrConds[$key] = $value;
        }

        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoTeacherSaysComments->updateByConds($msgId, $arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定评论信息
     *
     * @param  int $msgId     动态id，用于分表
     * @param  int $id id    评论id
     * @param  array $arrFields 评论属性
     * @return array|false
     */
    public function getTeacherSaysCommentsInfo($msgId, $id, $arrFields = array())
    {
        if (intval($msgId) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgId:$msgId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->objDaoTeacherSaysComments->getRecordByConds($msgId, $arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定动态下所有的评论信息
     *
     * @param  int $msgId     动态id，用于分表
     * @param  array $arrFields 评论属性
     * @return array|false
     */
    public function getAllTeacherSaysComments($msgId, $arrFields = array())
    {
        if (intval($msgId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$msgId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'msgId' => intval($msgId),
        );

        $ret = $this->objDaoTeacherSaysComments->getListByConds($msgId, $arrConds, $arrFields);

        return $ret;
    }


    /**
     * 根据条件获取评论信息
     *
     * @param  int $msgId     动态id，用于分表
     * @param  array $arrConds    评论id
     * @param  array $arrFields 评论属性
     * @param  int  $limit      每次获取的个数
     * @return array|false
     */
    public function getTeacherSaysCommentsByConds($msgId, $arrConds, $arrFields = array(), $limit = 20)
    {
        if (intval($msgId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgId:$msgId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by id desc",
            "limit $limit",
        );

        $ret = $this->objDaoTeacherSaysComments->getListByConds($msgId, $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getInsertId(){
        return $this->objDaoTeacherSaysComments->getInsertId();
    }


    /**
     * 删除评论
     *
     * @param  int $msgId     动态id，用于分表
     * @param  int  $id  评论id
     * @return bool true/false
     */
    public function deleteTeacherSaysComments($msgId, $id)
    {
        if (intval($msgId) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgId:$msgId]");
            return false;
        }

        $arrParams = array(
            'stat' => self::STATUS_DELETED,
        );

        $ret = $this->updateTeacherSaysComments($msgId, $id, $arrParams);

        return $ret;
    }

    /**
     * 质检后台评论列表查询
     * @param mixed $filters    [查询过滤条件]
     * @param int  $rn          [每页条数]
     * @param int $tableIndex   [分表索引]
     * @param int $postion      [上次最小commentid]
     *
     */
    public function getAllCommentsWithSharding($filters = array(), $rn = 30, $tableIndex = null, $postion = 0, $previous = false) {
        $result = $arrOutput = array();
        $tblIndex = 0;   //初始化分表索引
        $resultCnt = $rn;
        $sql = 'SELECT id, msg_id, pub_uid, content, stat, create_time FROM {#table} WHERE {#where} {#orderBy} LIMIT {#offset}';
        //查询条件
        $where = array('1 = 1');

        if (isset($filters['datetime']) && $filters['datetime']) {
            $starttime = strtotime($filters['datetime']);
            $endtime = $starttime + 86400;
            $where[] = 'create_time > ' . $starttime . ' AND create_time <= ' . $endtime;
        }

        // 根据老师uid 获取对应动态下的所有评论
        if (isset($filters['teacherUid']) && $filters['teacherUid']) {
            $teacherUids = '';
            if (!is_array($filters['teacherUid'])) {
                Bd_Log::warning("Error:[param error], Detail:[teacherUid: array need]");
                return false;
            }

            $teacherUids = implode(',', $filters['teacherUid']);

            $arrConds = array(
                'pub_uid in (' . $teacherUids .')',
            );

            $arrAppends = array(
                "order by id desc",
            );


            $arrTeacherSays = $this->objDaoTeacherSays->getListByConds($arrConds, ['id'], NULL, $arrAppends);
            if (empty($arrTeacherSays)){
                return array(array(),0);
            }
            $arrayReindexed = array();
            foreach ($arrTeacherSays as $arrTeacherSayInfo){
                $arrayReindexed[] = $arrTeacherSayInfo['id'];
            }

            $msgIds = implode(',', $arrayReindexed);

            $where[] = 'msg_id in (' . $msgIds .')';
        }

        // 评论状态：0 待审核；1 审核通过；2 审核不通过；3 删除
        if (isset($filters['stat']) && $filters['stat'] !== '') {
            $where[] = 'stat =' . intval($filters['stat']);
        }

        $where[] = 'type = 0';   //所有评论


        if ($postion) {
            if ($previous) {
                $where[] = 'id > ' . $postion;
            } else {
                $where[] = 'id < ' . $postion;
            }
        }

        if ($tableIndex !== null) {
            $tblIndex = $tableIndex;
        }

        $hasMore = 0;
        $isSharding = false;  // 表示在遍历表时，是否已经跨越一次分表了

        if ($previous === false) {
            while ($tblIndex < 10) {
                $table = 'tblTeacherSaysComments'. $tblIndex;

                $strWhere = implode(' AND ', $where);
                $strSql = str_replace(array('{#table}', '{#where}', '{#orderBy}', '{#offset}'), array($table, $strWhere, 'ORDER BY id DESC',$rn + 1), $sql);

                $res = $this->objDaoTeacherSaysComments->query($strSql);
                if (empty($res)) $res = array();

                $result = array_merge($result, $res);
                $extraCnt = $rn - count($res);
                if ($extraCnt <= 0) {
                    break;
                }

                //到这里说明跨表了, 需要剔除 id < 条件, 只允许一次pop
                if ($postion && $isSharding === false) {
                    array_pop($where);
                    $isSharding = true;
                }

                $rn = $extraCnt;
                $tblIndex++;
            }
        } else {
            while ($tblIndex >= 0) {
                $table = 'tblTeacherSaysComments'. $tblIndex;

                $strWhere = implode(' AND ', $where);
                $strSql = str_replace(array('{#table}', '{#where}', '{#orderBy}', '{#offset}'), array($table, $strWhere, 'ORDER BY id ASC', $rn + 1), $sql);

                $res = $this->objDaoTeacherSaysComments->query($strSql);
                if (empty($res)) $res = array();

                $result = array_merge($res, $result);
                $extraCnt = $rn - count($res);
                if ($extraCnt <= 0) {
                    break;
                }

                //到这里说明跨表了, 需要剔除 id > 条件, 只允许一次pop
                if ($postion && $isSharding === false) {
                    array_pop($where);
                    $isSharding = true;
                }

                $rn = $extraCnt;
                $tblIndex--;
            }
        }

        if (count($result) > $resultCnt) {
            array_pop($result);
            $hasMore = 1;
        }

        $objStudent   = new Hkzb_Ds_Fudao_Student();
        $objTeacherSays = new Oplib_Ds_TeacherSay_TeacherSays();
        $objTeacher = new Hkzb_Ds_Fudao_Teacher();

        if (is_array($result)) {
            $objDsUcloud = new Hk_Ds_User_Ucloud();
            foreach ($result as $key => $item) {
                $studentInfo = $objDsUcloud->getUserInfo( $item['pub_uid'], true );

                // 根据动态msg_id获取老师uid
                $teacherSaysInfo = $objTeacherSays->getTeacherSaysInfo($item['msg_id'], ['pub_uid']);
                $teacherInfo = $objTeacher->getTeacherInfo(intval($teacherSaysInfo['pub_uid']));

                $arrOutput[$key] = array(
                    'id' => $item['id'],
                    'studentUid' => $item['pub_uid'],
                    'studentName' => $studentInfo['uname'],
                    'account' => $studentInfo['phone'],
                    'msgId' => $item['msg_id'],
                    'teacherName' => $teacherInfo['teacherName'],
                    'content' => base64_decode($item['content']),
                    'tblIndex' => intVal($item['msg_id']) % 10,
                    'stat' => intval($item['stat'])
                );
            }
        }

        return [$arrOutput, $hasMore];
    }


    /**
     * 接收后台评论审核结果
     *
     * @param  array $arrCommentList    后台传入的审核数据
     * @param  int    $intStatus        审核结果
     * @param  int    $opname           注意：是操作人员的uid
     * @return bool true/false
     */
    public function pushCheckedComments($arrCommentList, $intStatus, $opname)
    {
        if (!in_array($intStatus, array(0, 1, 2, 3))) {
            Bd_Log::warning("Error:[param error], Detail:[intStatus:$intStatus]");
            return false;
        }
        if (!empty($arrCommentList)) {

            $objTeacherSaysNotices  = new Oplib_Ds_TeacherSay_TeacherSaysNotices();
            $objTeacherSays         = new Oplib_Ds_TeacherSay_TeacherSays();
            $objTeacherClue         = new Oplib_Ds_TeacherSay_TeacherSaysClue();
            foreach ($arrCommentList as $comment) {
                $msgId = $comment['msgId'];
                $commentId = $comment['commentId'];

                $arrParams = array(
                    'stat' => $intStatus,
                );
                if ($intStatus == self::STATUS_DELETED) {
                    $arrParams['opname'] = $opname;
                    $arrParams['optype'] =Oplib_Ds_TeacherSay_TeacherSaysComments::OPTYPE_MIS;
                }

                //修改评论表中评论的状态
                $ret = self::updateTeacherSaysComments($msgId, $commentId, $arrParams);

                if (!$ret) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::UPDATE_ERROR, '状态更新失败', array(
                        'msgId'     => $msgId,
                        'commentId' => $commentId,
                    ));
                }

                $commentInfo = self::getTeacherSaysCommentsInfo($msgId, $commentId);
                $msg = $objTeacherSays->getTeacherSaysInfo($msgId);
                $pub = $msg['pubUid'];
                $arrConds = array(
                    'commentId' => $commentId,
                    'msgId'     => $msgId,
                    'parentId'  => $commentInfo['parentId'],
                );

                $teacherSaysNotices =  $objTeacherSaysNotices->getTeacherSaysNoticesByConds($pub,$arrConds);

                if(!empty($teacherSaysNotices)){
                    //学生端评论或者回复-----删除消息表中的消息
                    $objTeacherClue->UpdateNoticeQueueInfo($pub,$teacherSaysNotices[0]['id'],1);
                    $objTeacherSaysNotices->deleteTeacherSaysNoticesByConds($pub, $arrConds);
                }
                //先审后发，将审核通过的评论放到通知表中,动态表中对应动态的评论数加1
                if ($intStatus == Oplib_Ds_TeacherSay_TeacherSaysComments::STATUS_PASS) {
                    //修改动态的评论数
                    $msgInfo = $objTeacherSays->getTeacherSaysInfo($msgId);
                    $arrParams = array('comment_cnt = comment_cnt + 1');
                    $ret = $objTeacherSays->updateTeacherSays($msgId,$arrParams);
                    if(!$ret){
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::UPDATE_ERROR,'',array(
                            'msgId' => $msgId,
                        ));
                    }

                    //清理缓存
                    $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
                    $cacheKeyCom  = 'teachersays_commentcnt_' . $msgId;
                    $objMemcached->delete($cacheKeyCom);

                    $pubUid = $msgInfo['pubUid'];

                    $content = array(
                        'comment' => $commentInfo['content'],
                    );

                    $arrFields = array(
                        'pubUid'    => $commentInfo['pubUid'],
                        'pubType'   => $commentInfo['pubType'],
                        'msgId'     => $commentInfo['msgId'],
                        'commentId' => $commentId,
                        'parentId'  => $commentInfo['parentId'],
                        'msgPubUid' => $pubUid,
                        'content'   => $content,
                        'type'      => Oplib_Ds_TeacherSay_TeacherSaysNotices::TYPE_REVIEW_NO,
                        'stats'     => Oplib_Ds_TeacherSay_TeacherSaysNotices::STATUS_NO,
                        'createTime' => $commentInfo['createTime'],
                        'updateTime' => $commentInfo['updateTime'],

                    );
                    if ($msgInfo['type'] == Oplib_Ds_TeacherSay_TeacherSays::DELETED_NO) {
                        $ret = $objTeacherSaysNotices->addTeacherSaysNotices($arrFields);
                        if (!$ret) {
                            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::INSERT_ERROR, '', array(
                                'msgId' => $msgId,
                                'commentId' => $commentId,
                            ));
                        }
                        $objTeacherClue->UpdateNoticeQueueInfo($pubUid,$ret,0);
                    }
                }
            }
        }
        return true;
    }


    /**
     * 后台切换先发后审或先审后发开关
     *
     * @param  int   $intType     开关类型， 1 评论开关 2教师端小流量开关 3 学生端小流量开关'
     * @param  int   $intModel   切换的模式 0先审后发，1先发后审
     * @return bool true/false
     */
    public function switchCheckModel($intType, $intModel)
    {
        if (!in_array($intType, array(1, 2, 3)) || !in_array($intModel, array(0, 1))) {
            Bd_Log::warning("Error:[param error], Detail:[intModel:$intModel intType:$intType]");
            return false;
        }

        $objTeacherSaysConf = new Oplib_Ds_TeacherSay_TeacherSaysConf();

        $ret = false;
        $arrParams = array();
        switch ($intType) {
            case 1://评论开关
                if ($intModel == Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FIRSTCHECK) {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FIRSTCHECK,
                    );
                } else {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FIRSTSEND,
                    );
                }
                $ret = $objTeacherSaysConf->updateTeacherSaysConf(1, $arrParams);
                break;
            case 2://教师端小流量开关
                if ($intModel == Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_PART) {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_PART,
                    );
                } else {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_ALL,
                    );
                }
                $ret = $objTeacherSaysConf->updateTeacherSaysConf(2, $arrParams);
                break;
            case 3://学生端小流量开关
                if ($intModel ==Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_PART) {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_PART,
                    );
                } else {
                    $arrParams = array(
                        'stats' => Oplib_Ds_TeacherSay_TeacherSaysConf::STATUS_FLOW_ALL,
                    );
                }
                $ret = $objTeacherSaysConf->updateTeacherSaysConf(3, $arrParams);
                break;
        }

        if (!$ret) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::UPDATE_ERROR, '开关切换失败', array());
        }

        return true;
    }

}

