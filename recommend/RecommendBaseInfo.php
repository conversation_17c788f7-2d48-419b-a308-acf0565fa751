<?php
/**
 * Created by PhpStorm.
 * User: zuoyebang
 * Date: 2019/5/10
 * Time: 13:21
 */
class Oplib_Recommend_RecommendBaseInfo
{
    private  $objRecommendCourse;
    private  $objZbCoreDsDakInterface;
    public function __construct()
    {
        $this->objRecommendCourse      = new Oplib_Ds_NewRecommendCourse();
    }

    public function  getSkuIdsFromCourseIds($courseIdArr){
        $this->objZbCoreDsDakInterface = new Zb_Core_Ds_Dak_Interface();
        $skuIdList = array();
        $skuRet =  $this->objZbCoreDsDakInterface->getSkuCourseIdListByCourseIds($courseIdArr);
        Bd_Log::trace(sprintf("Dak statistic oplib getSkuCourseIdListByCourseIds, uri:%s,func:%s", $_SERVER['PHP_SELF'], __FUNCTION__));
        foreach ($skuRet as $itemSku){
            $skuIdList[$itemSku['courseId']]=$itemSku['skuId'];
        }
        return $skuIdList;
    }
    public function  getDeepKVBySkuIds($skuIdList){

        $arrSkuInfoRet = Zb_Service_Dak_Sku::getDeepKVBySkuIds($skuIdList);
        Bd_Log::trace(sprintf("Dak statistic oplib getDeepKVBySkuIds, uri:%s,func:%s", $_SERVER['PHP_SELF'], __FUNCTION__));
        $arrSkuInfoRet = $arrSkuInfoRet['data'];
        return $arrSkuInfoRet;
    }

    /*
     * 获取sku和课程信息
     */
    public function getSkuCourseInfo($skuIds) {
        $ret = Oplib_Recommend_ServiceSku::getSkuKvList($skuIds);
        $skuList = $ret['data'] ?? [];
        $courseFields = array(
            'courseId',
            'courseName',
            'grades',
            'subjects',
            'learnSeason',
            'year',
        );
        $lessonFields = array(
            'lessonId',
        );
        $skuId2courseList = Oplib_Recommend_ServiceSku::getSkuCourseList($skuList, $courseFields, $lessonFields);
        $lid2cid = array();
        foreach ($skuId2courseList as $skuId => $courseList) {
            foreach ($courseList as $courseId => $course) {
                foreach ($course['lessonList'] as $lessonId => $lesson) {
                    $lid2cid[$lessonId] = $courseId;
                }
            }
        }
        $lid2tid = array();
        $ret = Zb_Service_Dat_TeacherLesson::getTeacherUidByLessonIdArr(array_keys($lid2cid), ['teacherUid']);
        foreach ($ret['data'] as $lessonId => $one) {
            $lid2tid[$lessonId] = $one['teacherUid'];
        }
        $ret = Zb_Service_Dau_Teacher::getKVByTeacherUids(array_unique($lid2tid), ['teacherUid', 'teacherName']);
        $teachers = $ret['data'];

        $output = array();
        foreach ($skuId2courseList as $skuId => $courseList) {
            $one = $skuList[$skuId];
            $one['courseIds'] = array_keys($courseList);
            $one['courseInfoList'] = array();
            foreach ($courseList as $courseId => $course) {
                $one['courseInfoList'][$courseId] = $course;
                $one['courseInfoList'][$courseId]['lessonInfoList'] = array();
                foreach ($course['lessonList'] as $lessonId => $lesson) {
                    $tid = $lid2tid[$lessonId];
                    $one['courseInfoList'][$courseId]['lessonInfoList'][$lessonId] = array(
                        'teacherInfo' => $teachers[$tid],
                    );
                }
                unset($one['lessonList']);
            }
            $output[$skuId] = $one;
        }

        return $output;
    }

    public function formatCourseInfo($arrSkuInfoRet,$formatCourseInfo=array(),$isChild=0){

        $courseMapSkuInfo = array();
        foreach ($arrSkuInfoRet as &$skuInfo){

            $skuId = $skuInfo['skuId'];
            $courseIds = $skuInfo['courseIds'];
            foreach ($courseIds as $tempCourseId){
                $courseInfo =  isset($skuInfo['courseInfoList'][$tempCourseId]) ? $skuInfo['courseInfoList'][$tempCourseId] : array();
                $teacherInfoList = array();

                //获取该课程的老师信息
                foreach($courseInfo['lessonInfoList'] as $lessonInfo){
                    $teacherInfo = $lessonInfo['teacherInfo'];
                    $teacherUid = isset($teacherInfo['teacherUid']) ? $teacherInfo['teacherUid'] : 0;

                    $teacherInfoList[$teacherUid] = strval($teacherInfo['teacherName']);
                }
                //去除key值
                $teacherInfoList = array_values($teacherInfoList);
                if(!$isChild){
                    $isHasRecommendCourse = 0;
                    $arrConds = array(
                        'courseId'=>$tempCourseId,
                    );
                    $recommendInfo = $this->objRecommendCourse->getRecommendInfo($arrConds);
                    if(!empty($recommendInfo)){
                        $isHasRecommendCourse= 1;
                        if(empty($courseMapSkuInfo[$tempCourseId]['recommendId'])){
                            $courseMapSkuInfo[$tempCourseId]['recommendId']    = $recommendInfo[0]['id'];
                        }
                    }
                    $courseMapSkuInfo[$tempCourseId]['isHasRecommendCourse']    = $isHasRecommendCourse;
                }
                $courseMapSkuInfo[$tempCourseId]['teacherName']    = implode(",",$teacherInfoList);
                $courseMapSkuInfo[$tempCourseId]['skuId']           = $skuId;
                $courseMapSkuInfo[$tempCourseId]['courseId']        = $tempCourseId;
                $courseMapSkuInfo[$tempCourseId]['grade']           =  Hk_Util_Category::$GRADE[$courseInfo['grades'][0]];
                $courseMapSkuInfo[$tempCourseId]['subject']         =  Hk_Util_Category::$COURSE[$courseInfo['subjects'][0]];
                $courseMapSkuInfo[$tempCourseId]['courseName']      = $courseInfo['courseName'];
                $courseMapSkuInfo[$tempCourseId]['season']          = Zb_Const_LearnSeason::$learnSeasonMap[$courseInfo['learnSeason']];
                $courseMapSkuInfo[$tempCourseId]['year']            = $courseInfo['year'];
            }

            if(!empty($formatCourseInfo)){
                foreach ($formatCourseInfo as $recommendId => &$itemRecommend){
                    $courseId = $itemRecommend['courseId'];
                    $itemRecommend['teacherName']     = $courseMapSkuInfo[$courseId]['teacherName'];
                    $itemRecommend['skuId']           = $courseMapSkuInfo[$courseId]['skuId'];
                    $itemRecommend['grade']           = $courseMapSkuInfo[$courseId]['grade'];
                    $itemRecommend['subject']         = $courseMapSkuInfo[$courseId]['subject'];
                    $itemRecommend['courseName']      = $courseMapSkuInfo[$courseId]['courseName'];
                    $itemRecommend['season']          = $courseMapSkuInfo[$courseId]['season'];
                    $itemRecommend['year']            = $courseMapSkuInfo[$courseId]['year'];
                }
            }
        }
        return empty($formatCourseInfo) ? $courseMapSkuInfo : $formatCourseInfo;
    }
}