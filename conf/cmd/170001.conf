#Course订单同步Trade
[groupKey]
type:int
must:1
remark:时序保证
[payChannel]
type : int
must : 0
remark : 支付渠道
[jumpFrom]
type : string
must : 0
remark : 跳转来源
[os]
type : string
must : 0
remark : 系统
[moduleFrom]
type : string
must : 0
remark : 上游模块
[dataInfo]
type : string
must : 1
remark : 数据信息
[dataType]
type : int 
must : 1
remark : 数据类型
[purchaseId]
type : int 
must : 1
remark : Course订单流水ID
[tradeId]
type : int 
must : 1
remark : Course父订单ID
[vc]
type : string
must : 0
remark : 版本
[vcname]
type : string
must : 0
remark : 版本
[cuid]
type : string
must : 0
remark : cuid
[appChannel]
type : string
must : 0
remark : app渠道
[uid]
type : int
must : 1
remark : uid
[phone]
type : string
must : 0
remark : phone
[ua]
type : string
must : 0
remark : ua
[remark]
type : string
must : 0
remark : 后台批量给用户加课标识(batchcourse)
[skipLaxin]
type : int 
must : 0
remark : 拉新订单是否进入销售系统
[bizInfo]
type : string
must : 0
remark : biz策略信息
[useCouponInfo]
type : string
must : 0
remark : 优惠券信息

