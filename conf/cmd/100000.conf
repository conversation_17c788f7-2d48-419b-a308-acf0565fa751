#命令体配置样例

[a]
type : int
must : 1
remark : 整型字段a

[b]
type : string
must : 1
remark : 字符型字段b

[c]
type : list
must : 0
remark : 列表c(索引数组) 
[.list]
type : int
must : 1
remark : 整型

[d]
type : map
must : 1
remark : 字典d(关联数组)
[.map]
[..bb1]
type : string
must : 1
remark : 字符bb1 
[..bb2]
type : int
must : 0
remark : 整型bb2

[e]
type : list
must : 1
remark : 列表c(索引数组) 
[.list]
type : map
must : 1
remark : 字典d(关联数组)
[..map]
[...bb1]
type : string
must : 1
remark : 字符bb1 
[...bb2]
type : int
must : 0
remark : 整型bb2
