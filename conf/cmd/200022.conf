#用户完成体验

[leadsId]
type : int
must : 1
remark : 例子ID
[customUid]
type : int
must : 1
remark : 学生Uid
[lessonId]
type : int
must : 1
remark : 课程章节ID
[duration]
type : int
must : 1
remark : 体验时长
[isAttended]
type : int
must : 1
remark : 是否到课
[isAttendedLong]
type : int
must : 1
remark : 是否到课30分钟
[isFinished]
type : int
must : 1
remark : 是否完课
[isPlayback]
type : int
must : 1
remark : 是否是看回放
[attendTime]
type : int
must : 1
remark : 章节到课时间
[leadsAttendTime]
type : int
must : 1
remark : 例子最早到课时间
[groupKey]
type:int
must:1
remark:时序保证
