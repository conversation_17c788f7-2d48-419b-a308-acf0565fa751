#交易3.0下单履约
[groupKey]
type : int
must : 1
remark : 时序保证
[requestId]
type : int
must : 1
remark : 业务编号（业务方自定义，保证业务唯一）
[userId]
type : int
must : 1
remark : 用户id
[orderId]
type : int
must : 1
remark : 订单ID
[ofcType]
type : int
must : 1
remark : 服务单类型
[source]
type : int
must : 0
remark : 业务线
[shopId]
type : int
must : 0
remark : 店铺Id
[skuSnapshot]
type : string
must : 1
remark : 商品快照，sku, p的映射关系详情
[skuMap]
type : string
must : 1
remark : 商品与P履约描述，sku, p的数量关系
[ofcInfo]
type : string
must : 1
remark : 履约信息
[commitTime]
type : int
must : 1
remark : 命令发送时间