#调配中转
[courseId]
type : int
must : 1
remark : 课程Id

[stuUid]
type : int
must : 1
remark : 用户uid

[saleMode]
type : int
must : 1
remark : 销售模式

[leadsId]
type : int
must : 0
remark : 例子Id

[fromPersonUid]
type : int
must : 0
remark :原辅导老师真人账号


[fromUserId]
type : int
must : 0
remark : 原辅导老师业务账号账号


[toPersonUid]
type : int
must : 0
remark : 新辅导老师真人账号


[toUserId]
type : int
must : 0
remark : 原辅导老师业务账号账号

[oldMsg]
type : string
must : 1
remark : 原有NMQ消息

[groupKey]
type:int
must:1
remark:时序保证