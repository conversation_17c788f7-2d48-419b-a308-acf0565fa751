<?php
/**
 * 获取有海报素材的分销活动
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/10/9 15:07
 */
class Service_Page_Poster_GetActivityList
{
    private $objDsPosterMaterial;
    private $objDsActivity;

    public function __construct()
    {
        $this->objDsPosterMaterial = new Service_Data_Poster_PosterMaterial();
        $this->objDsActivity = new Service_Data_Fenxiao_Config();
    }

    public function execute($paramsInput)
    {
        $activityList = [];
        // 搜索条件：活动进行中
        $searchParams[] = 'activity_start <= ' . ActPlatMis_Common_Tool::getCurrentTimestamp() . ' and activity_end >= ' . ActPlatMis_Common_Tool::getCurrentTimestamp();
        if ($paramsInput['activityId']) {
            $searchParams['activityId'] = $paramsInput['activityId'];
        }
        $list = $this->objDsPosterMaterial->searchMaterialList($searchParams, ['id', 'activityId', 'inviteMessage', 'memoTag', 'category', 'materialName', 'picId'], 1, 1000, 'id desc');
        if (!$list || count($list) === 0) {
            return $activityList;
        }

        foreach ($list as $v) {
            $activity = $this->objDsActivity->getConfigOneByConfigId($v['activityId'], ['name']);
            $activityList[] = array(
                'id' => $v['id'],
                'category' => $v['category'],
                'label' => $v['activityId'] . '-' . (empty($v['memoTag']) ? $activity['name'] : $v['memoTag']),
                'value' => strval($v['id']),
                'inviteMessage' => $v['inviteMessage'],
                'materialName' => $v['materialName'],
                'picUrl' => Hk_Util_Image::getImgUrlBySrc($v['picId'])
            );
        }

        return $activityList;
    }
}
