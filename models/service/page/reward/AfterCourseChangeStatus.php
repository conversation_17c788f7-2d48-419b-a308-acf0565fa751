<?php
/**
 * 活动状态流转
 * Created by PhpStorm.
 * User: zhangyutian
 * Date: 2020/9/15
 * Time: 15:48
 */

class Service_Page_Reward_AfterCourseChangeStatus
{
    private $objDsAfterCourseActivity;
    private $objDsAfterCourseActivityLog;
    private $objDsAfterCourseCfg;

    public function __construct()
    {
        $this->objDsAfterCourseActivity = new Service_Data_Reward_AfterCourseActivity();
        $this->objDsAfterCourseActivityLog = new Service_Data_Reward_AfterCourseActivityLog();
        $this->objDsAfterCourseCfg = new Service_Data_Cytool_AfterCourseActivityCfg();
    }

    public function execute($inputParams)
    {
        $ret = $this->objDsAfterCourseActivity->searchActivity(['id' => $inputParams['activityId']]);
        if (!$ret || count($ret) === 0) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '活动不存在');
        }

        $activity = $ret[0];
        if ($inputParams['activityStatus'] == ActPlatMis_Const_Reward_AfterCourseConst::STATUS_DEPLOY) {
            // 发布活动前提校验
            if ($activity['activityStatus'] != ActPlatMis_Const_Reward_AfterCourseConst::STATUS_NEW) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::STATUS_ERROR, '只有新建的活动才可以发布');
            }
            if ($activity['cashTime'] < time()) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::STATUS_ERROR, '打款时间已经过期，不允许发布');
            }

            $cfg = $this->objDsAfterCourseCfg->getConfig();
            if (!$cfg['systemSwitch']) {
                // 系统开关检测
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::STATUS_ERROR, '系统当前处于关闭期间，不允许发布活动，如果需要开启发布窗口请联系负责的产品老师');
            }
        }

        try {
            $ret = $this->objDsAfterCourseActivity->updateByConds(['id'=>$inputParams['activityId']], ['activityStatus'=>$inputParams['activityStatus']]);
            if (!$ret) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '修改状态失败');
            }

            if ($inputParams['activityStatus'] == ActPlatMis_Const_Reward_AfterCourseConst::STATUS_DEPLOY) {
                // 发布活动的时候，去奖励履约系统注册APP，方便用户录入支付密钥
                $this->registerActivityApp($activity['id'], $activity['activityName'], $activity['rewardType']);

                // 钉钉通知，提示支付密钥金额池子所需大小
                if ($activity['activityMethod'] == ActPlatMis_Const_Reward_AfterCourseConst::ACTIVITY_METHOD_MANUAL && $activity['rewardType'] == ActPlatMis_Const_Reward_AfterCourseConst::ACT_REWARD_PAY_TYPE) {
                    ActPlatMis_Common_Tool::SendOneToOne($inputParams['uname'].'@zuoyebang.com', sprintf('温馨提示：购课返现活动【活动ID%d-%s】已经发布，现在可以去黑蝙蝠录入支付凭据了。活动总金额：【%s元】，请确保申请的支付密钥的金额足够，避免返现失败~', $activity['id'], $activity['activityName'], $activity['totalFee'] * 0.01), 'actrewardnotification');
                }
            } else if ($inputParams['activityStatus'] == ActPlatMis_Const_Reward_AfterCourseConst::STATUS_APPROVED && $activity['rewardType'] == ActPlatMis_Const_Reward_AfterCourseConst::ACT_REWARD_PAY_TYPE) {
                // 分销购课类活动通过审核
                ActPlatMis_Common_Tool::SendOneToOne($inputParams['uname'].'@zuoyebang.com', sprintf('温馨提示：购课返现活动【活动ID%d-%s】已经通过审核，现在可以去黑蝙蝠录入支付凭据了。活动总金额：【%s元】，请确保申请的支付密钥的金额足够，避免返现失败~', $activity['id'], $activity['activityName'], $activity['totalFee'] * 0.01), 'actrewardnotification');
            }

            // 记录日志
            $logs = ['activityId'=>$inputParams['activityId'], 'operatorUser'=>$inputParams['uname'], 'opType'=>'editStatus', 'detail'=>json_encode(['fromStatus'=>$activity['activityStatus'], 'toStatus'=>$inputParams['activityStatus']], JSON_UNESCAPED_UNICODE)];
            $this->objDsAfterCourseActivityLog->addLog($logs);
        } catch (Exception $e) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, $e->getMessage());
        }

        return true;
    }

    /**
     * 去履约奖励系统注册app
     * @param $activityId
     * @param $activityName
     * @param $rewardType
     * @throws Oplib_Common_Exception
     */
    private function registerActivityApp($activityId, $activityName, $rewardType)
    {
        $input = [
            'name'         => $activityName,
            'activityType' => ActPlatMis_Const_Reward_AfterCourseConst::ACT_REWARD_TYPE,
            'activityId'   => $activityId,
            'isNeedConf'   => $rewardType == ActPlatMis_Const_Reward_AfterCourseConst::ACT_REWARD_PAY_TYPE ? 1 : 2, // 需要支付凭据，（1：需要 2：不需要)
        ];

        $result = Oplib_Util_RalClient::ralPost('actreward', $input, '/actreward/api/addapp');
        if ($result['errNo'] != 0 || !$result['data']['appKey'] || !$result['data']['appSecret']) {
            Oplib_Util_Log::fatal(APP, __CLASS__, __FUNCTION__, '奖励系统活动接入失败', $input, 18610082615);
            ActPlatMis_Common_Tool::buildException(ActPlatMis_Common_ExceptionCodes::ACT_ACCESS_FAIL, $input);
        }

        $this->objDsAfterCourseActivity->updateByConds(['id' => $activityId], ['ext' => json_encode($result['data'])]);
    }
}
