<?php
/**************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-7-28 14:19
 * @brief       兑换码详情
 **/
class Service_Page_CouponCode_Info
{
    const DURATIONTYPE_LIMIT_DAY = 1;//表示发放之后N天失效
    const DURATIONTYPE_LIMIT_DEADLINE = 2;//截止到某个日期失效

    public function execute($arrInput)
    {
        // 返回值
        $arrOutput = [
            'codeInfo'  => [],
            'itemInfo'  => [],
        ];
        if(empty($arrInput['codeName']))
        {
            return $arrOutput;
        }

        //转换成售卖中台接口能够接受的格式
        $params = [
            'codeNameList' => [$arrInput['codeName']],
        ];
        //调用获取兑换码列表接口
        list($isTrue,$errno,$data) = Qdlib_Service_Moat_ZbCoupon::getCouponCodeList($params);
        if(!$isTrue || !is_array($data['list']) || count($data['list']) == 0){
            return $arrOutput;
        }

        $codeInfo = current($data['list']);
        $codeInfo['recordDesc'] = $codeInfo['subActDesc'];
        //构造第二个接口请求参数
        $params = [
            'itemIdList' => [$codeInfo['itemIds'][0]],
        ];
        list($isTrue,$errno,$data) = Qdlib_Service_Moat_ZbCoupon::getCouponItemList($params);
        //var_dump($isTrue,$errno,$data);
        if($isTrue && is_array($data['itemList']) && count($data['itemList']) > 0){
            $itemInfo = current($data['itemList']);
            $itemInfo['itemId'] = strval($itemInfo['itemId']);
            //处理数据
            if($itemInfo['unit'] == 1)//现金券
            {
                $itemInfo['discount'] /= 100.0;
            }
            if ($itemInfo['durationType'] == self::DURATIONTYPE_LIMIT_DAY) {
                $itemInfo['endTimeText'] = "领取".$itemInfo['endTime']."日内有效";
            }
            else{
                $itemInfo['endTimeText'] = date('Y-m-d H:i:s', $itemInfo['endTime']);
                if (!empty($itemInfo['startTime'])) {
                    $itemInfo['endTimeText'] = date('Y-m-d H:i:s', $itemInfo['startTime']) . ' - ' . $itemInfo['endTimeText'];
                } else {
                    $itemInfo['endTimeText'] = "截止到" . $itemInfo['endTimeText'];
                }
            }
            $itemInfo['unit'] = $itemInfo['unit'] == 1 ? "现金券" : "折扣券";
            $itemInfoStrMap = [1 => '单品券', 2 => '订单券', 3 =>'运费券'];
            $itemInfo['itemType'] = $itemInfoStrMap[$itemInfo['itemType']];
            $arr1 = ['codeName' => '1','endTime' => '1','recordDesc' => '1','maxNum' => '1','leftNum' => '1'];
            $arr2 = ['itemId' => '1','checkBillsNum' => '1','activityName' => '1','itemName' => '1','unit' => '1','discount' => '1','ruleDescription' => '1','itemType' => '1','endTimeText' => '1'];
            $codeInfo = array_intersect_key($codeInfo,$arr1);
            $itemInfo = array_intersect_key($itemInfo,$arr2);
            $codeInfo['endTime'] = date('Y-m-d H:i:s', $codeInfo['endTime']);
            $arrOutput['codeInfo'] = $codeInfo;
            $arrOutput['itemInfo'] = $itemInfo;
        }
        return $arrOutput;
    }
}
