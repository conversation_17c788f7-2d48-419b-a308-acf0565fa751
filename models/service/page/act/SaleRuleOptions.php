<?php

class Service_Page_Act_SaleRuleOptions
{
    private $dsAct;
    private $dsInst;

    public function __construct()
    {
        $this->dsAct = new Qdlib_Ds_Hetu_AFXAct();
        $this->dsInst = new Service_Data_Afx_AFXOrgan();
    }

    public function execute($params)
    {
        $actInfo = $this->dsAct->getRecordByConds(['actId' => $params['actId']], ['appId', 'businessLine', 'type', 'endTime', 'apiInfo', 'extData']);
        if (false === $actInfo) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '查询活动信息失败');
        }
        if (empty($actInfo)) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::PARAM_ERROR, '活动不存在');
        }

        $result = [
            'inst' => [],
            'sku' => [],
            'grade' => [],
            'season' => [],
        ];

        // inst
        if (!empty($actInfo['extData']['whiteList']) && is_array($actInfo['extData']['whiteList'])) {
            $instList = $this->dsInst->getList([
                'id in (' . implode(',', array_map('intval', $actInfo['extData']['whiteList'])) . ')',
                'status' => Service_Data_Afx_AFXOrgan::STATUS_ON,
                'appId' => $actInfo['appId'],
            ], ['id', 'name']);
            if (false === $instList) {
                throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '查询机构信息失败');
            }
            $result['inst'] = array_column($instList, 'name', 'id');;
        }

        // h5的投放页面
        if (Oplib_Const_AFX::ACT_TYPE_H5 == $actInfo['type'] && Oplib_Const_AFX::API_SOURCE_TF == $actInfo['apiInfo']['apiSource'] && isset($actInfo['apiInfo']['pageType'])) {
            // 洛书有课程接口的页面
            if (Qdlib_Ds_Hetu_AFXAct::PAGE_TYPE_LS === $actInfo['apiInfo']['pageType'] && !empty($actInfo['apiInfo']['param'])) {
                $skuList = ActPlatMis_Service_QdMis::getSkuList($actInfo['apiInfo']['param']);
                if (false === $skuList) {
                    throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED, '查询课程接口失败');
                }

                // 直播课
                if (Qdlib_Const_LastFrom::BUSINESS_NUM_YK == $actInfo['businessLine']) {
                    foreach ($skuList as $skuInfo) {
                        if (isset($skuInfo['skuId']) && isset($skuInfo['skuId'])) {
                            $result['sku'][$skuInfo['skuId']] = $skuInfo['skuId'];
                        }
                        if (isset($skuInfo['gradeId']) && isset($skuInfo['gradeZn'])) {
                            $result['grade'][$skuInfo['gradeId']] = $skuInfo['gradeZn'];
                        }
                        if (isset($skuInfo['seasonId']) && isset($skuInfo['seasonZn'])) {
                            $result['season'][$skuInfo['seasonId']] = $skuInfo['seasonZn'];
                        }
                    }
                    return $result;
                }

                // 非直播课
                $result['sku'] = array_column($skuList, 'skuId', 'skuId');
                return $result;
            }

            // 实物售卖H5的页面
            if (Qdlib_Ds_Hetu_AFXAct::PAGE_TYPE_CUBE === $actInfo['apiInfo']['pageType']) {
                if (!empty($actInfo['apiInfo']['spuId'])){
                    $allSpuIds = ActPlatMis_Service_Cube::getAllSpuIds($actInfo['apiInfo']['spuId'], $actInfo['apiInfo']['actId'] ?? 0);
                    if (false === $allSpuIds) {
                        throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED, '查询推荐spu失败');
                    }
                    if (!empty($allSpuIds)) {
                        $spuSkuMap = Qdlib_Service_Moat_Common::returnDataOrFalse(Qdlib_Service_Moat_Newgoodsplatform::getskuidlistbyspuids($allSpuIds));
                        if (false === $spuSkuMap) {
                            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED, '查询spu对应的sku失败');
                        }
                        foreach ($spuSkuMap as $skuIds) {
                            foreach ($skuIds as $skuId) {
                                $result['sku'][$skuId] = $skuId;
                            }
                        }
                    }
                    return $result;
                }

                if (!empty($actInfo['apiInfo']['param'])){
                    $skuList = ActPlatMis_Service_QdMis::getSkuList($actInfo['apiInfo']['param']);
                    if (false === $skuList) {
                        throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED, '查询课程接口失败');
                    }
                    foreach ($skuList as $skuInfo) {
                        if (isset($skuInfo['skuId']) && isset($skuInfo['skuId'])) {
                            $result['sku'][$skuInfo['skuId']] = $skuInfo['skuId'];
                        }
                        if (isset($skuInfo['gradeId']) && isset($skuInfo['gradeZn'])) {
                            $result['grade'][$skuInfo['gradeId']] = $skuInfo['gradeZn'];
                        }
                        if (isset($skuInfo['seasonId']) && isset($skuInfo['seasonZn'])) {
                            $result['season'][$skuInfo['seasonId']] = $skuInfo['seasonZn'];
                        }
                    }
                    return $result;
                }
            }
        }

        return $result;
    }
}