<?php

class Service_Page_Act_SaleRuleFill
{
    private $dsAct;
    private $dsInst;
    private $afxUser;
    protected $actInfo = [];
    protected $instKV = [];
    protected $agentKV = [];
    protected $pageLuoshu = 0;

    public function __construct()
    {
        $this->dsAct = new Qdlib_Ds_Hetu_AFXAct();
        $this->dsInst = new Service_Data_Afx_AFXOrgan();
        $this->afxUser = new Service_Data_Afx_AFXUser();
    }

    public function execute($params)
    {
        $this->getActInfo($params['actId']);
        $this->getInstNameById($params['instList'], $this->actInfo['appId']);
        $this->getAgentNameById($params['agentList'], $this->actInfo['appId']);

        $middleParam[] = [
            "key" => "sTime",
            "value" => [$params['sTime']],
        ];
        $middleParam[] = [
            "key" => "eTime",
            "value" => [$params['eTime']],
        ];

        //叉乘项：代理机构
        $middleParam[] = [
            "key" => "instId",
            "value" => explode(",", $params['instList'])
        ];

        //叉乘项：代理商
        $middleParam[] = [
            "key" => "agentId",
            "value" => explode(",", $params['agentList'])
        ];

        //叉乘项：skuidList
        if (!empty($params['skuList'])) {
            $middleParam[] = [
                "key" => "skuId",
                "value" => explode(",", $params['skuList'])
            ];
        }
        //叉乘项：年级
        if (!empty($params['gradeList'])) {
            $middleParam[] = [
                "key" => "gradeId",
                "value" => explode(",", $params['gradeList'])
            ];
        }
        //叉乘项：学季
        if (!empty($params['seasonList'])) {
            $middleParam[] = [
                "key" => "seasonId",
                "value" => explode(",", $params['seasonList'])
            ];
        }
        $ret = $this->cartesian($middleParam);
        if ((!empty($params['gradeList']) && $params['gradeList'] != -1) || (!empty($params['seasonList']) && $params['seasonList'] != -1)) {
            $this->getPageType();
            $skuList = $this->getSkuList();
            // 直播课
            if (Qdlib_Const_LastFrom::BUSINESS_NUM_YK == $this->actInfo['businessLine']) {
                foreach ($skuList as $skuInfo) {
                    if (isset($skuInfo['gradeId']) && isset($skuInfo['gradeZn'])) {
                        $grade[$skuInfo['gradeId']] = $skuInfo['gradeZn'];
                    }
                    if (isset($skuInfo['seasonId']) && isset($skuInfo['seasonZn'])) {
                        $season[$skuInfo['seasonId']] = $skuInfo['seasonZn'];
                    }
                    $gradeSeason[$skuInfo['gradeId']][] = $skuInfo['seasonId'];
                }
            }
        }

        //格式化返回
        return $this->formateRet($ret, $grade, $season, $gradeSeason);
    }

    /**
     * @param $param
     * @param array $grade
     * @param array $season
     * @param array $gradeSeason
     * @return array
     */
    protected function formateRet($param, $grade = [], $season = [], $gradeSeason = [])
    {
        $data = [];
        foreach ($param as $item) {
            $tmp = $item;
            $tmp["instName"] = $this->instKV[$item['instId']];
            $tmp["agentName"] = $item['agentId'] == -1 ? "不限" : $this->agentKV[$item['agentId']];
            if (!empty($item['skuId'])) {
                $tmp['skuName'] = $item['skuId'] == -1 ? "不限" : $item['skuId'];
            }
            if ((empty($item['gradeId']) && empty($item['seasonId'])) || ($item['gradeId'] == -1 && $item['seasonId'] == -1)) {
                $tmp['seasonName'] = "不限";
                $tmp['gradeName'] = "不限";
                $data[] = $tmp;
                continue;
            }
            if ($item['gradeId'] != -1) {
                if ($item['seasonId'] == -1) {
                    if (!empty($grade[$item['gradeId']])) {
                        $tmp['gradeName'] = $grade[$item['gradeId']];
                        $tmp['seasonName'] = "不限";
                        $data[] = $tmp;
                    }
                } else {
                    if (in_array($item['seasonId'], $gradeSeason[$item['gradeId']])) {
                        $tmp['gradeName'] = $grade[$item['gradeId']];
                        $tmp['seasonName'] = $season[$item['seasonId']];
                        $data[] = $tmp;
                    }
                }
                continue;
            }
            if ($item['seasonId'] != -1) {
                if (!empty($season[$item['seasonId']])) {
                    $tmp['seasonName'] = $season[$item['seasonId']];
                    $tmp['gradeName'] = "不限";
                    $data[] = $tmp;
                }
            }
        }
        return $data;
    }

    /**
     * @param $arr
     * @param array $str
     * @return mixed
     * 叉乘逻辑
     */
    protected function cartesian($arr, $str = array())
    {
        //去除第一个元素
        $first = array_shift($arr);
        $tmpKey = $first['key'];
        $first = $first['value'];
        //判断是否是第一次进行拼接
        if (count($str) >= 1) {
            foreach ($str as $k => $val) {
                foreach ($first as $key => $value) {
                    if (is_array($value)) {
                        $str2[] = array_merge($val, [$tmpKey => $key]);

                    } else {
                        $str2[] = array_merge($val, [$tmpKey => $value]);
                    }
                }
            }
        } else {
            foreach ($first as $key => $value) {
                if (is_array($value)) {
                    $str2[] = [$tmpKey => $key];

                } else {
                    $str2[] = [$tmpKey => $value];
                }
            }
        }
        //递归进行拼接
        if (count($arr) > 0) {
            $str2 = $this->cartesian($arr, $str2);
        }
        //返回最终笛卡尔积
        return $str2;
    }

    /**
     * 页面类型判断
     */
    protected function getPageType()
    {
        if (Oplib_Const_AFX::ACT_TYPE_H5 == $this->actInfo['type']
            && Oplib_Const_AFX::API_SOURCE_TF == $this->actInfo['apiInfo']['apiSource']
            && isset($this->actInfo['apiInfo']['pageType'])) {
            // 洛书有课程接口的页面
            if (Qdlib_Ds_Hetu_AFXAct::PAGE_TYPE_LS === $this->actInfo['apiInfo']['pageType'] && !empty($this->actInfo['apiInfo']['param'])) {
                $this->pageLuoshu = 1;
            }
        }
        return;
    }

    protected function getSkuList()
    {
        if ($this->pageLuoshu) {
            // 洛书有课程接口的页面
            $skuList = ActPlatMis_Service_QdMis::getSkuList($this->actInfo['apiInfo']['param']);
            if (false === $skuList) {
                throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::RAL_TALK_TO_SERVER_FAILED, '查询课程接口失败');
            }
            return $skuList;
        }
        return [];
    }


    /**
     * 活动查询
     * @param $id
     * @throws Qdlib_Common_Exception
     */
    protected function getActInfo($id)
    {
        //活动详情
        $actInfo = $this->dsAct->getRecordByConds(
            ['id' => $id],
            ['appId', 'businessLine', 'type', 'endTime', 'apiInfo', 'extData']
        );
        if (false === $actInfo) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '查询活动信息失败');
        }
        if (empty($actInfo)) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::PARAM_ERROR, '活动不存在');
        }
        $this->actInfo = $actInfo;
        return;
    }

    /**
     * 机构查询
     * @param $ids
     * @param $appId
     * @throws Qdlib_Common_Exception
     */
    protected function getInstNameById($ids, $appId = 1)
    {
        $instList = $this->dsInst->getList([
            Qdlib_Util_DB::whereIn("id", explode(",", $ids)),
            'appId' => $appId,
        ], ['id', 'name']);
        if (false === $instList) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '查询机构信息失败');
        }
        $instList = array_column($instList, 'name', 'id');
        $this->instKV = $instList;
    }

    /**
     * 代理商查询
     * @param $ids
     * @param $appId
     * @throws Qdlib_Common_Exception
     */
    protected function getAgentNameById($ids, $appId = 1)
    {
        if ($ids == "0") {
            return;
        }
        $agentList = $this->afxUser->getList([
            Qdlib_Util_DB::whereIn("id", explode(",", $ids)),
            'appId' => $appId,
        ], ['id', 'nickname']);
        if (false === $agentList) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '查询代理商信息失败');
        }
        $agentList = array_column($agentList, 'nickname', 'id');
        $this->agentKV = $agentList;
    }


}