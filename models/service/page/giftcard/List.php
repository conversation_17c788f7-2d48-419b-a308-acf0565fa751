<?php
/**
 * 礼品卡明细
 * <AUTHOR>
 * @date 2019-10-21
 */
class Service_Page_Giftcard_List
{
    const USER_STATUS_YES = 1; # 礼品卡已使用
    const USER_STATUS_NO  = 2; # 礼品卡未使用

    private $dataTactics = null;
    private $dataGiftcard = null;
    private $tacticsHash = [];

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2019-10-21
     */
    public function __construct()
    {
        $this->dataTactics = new Service_Data_Giftcard_GiftcardTactics();
        $this->dataGiftcard = new Service_Data_Giftcard_HaveGiftcard();
    }

    /**
     * 固有执行方法
     * <AUTHOR>
     * @DateTime 2019-10-21
     * @param    array  $arrInput 输入参数
     * @return   array
     */
    public function execute($arrInput = [])
    {
        $uid = (int)$arrInput['uid'];
        $page = (int)$arrInput['page'];
        $limit = (int)$arrInput['limit'];
        $useStatus = (int)$arrInput['useStatus'];
        $tacticsId = (int)$arrInput['tacticsId'];

        if ($tacticsId <= 0) {
            $tacticsId = 120;
        }

        $status = $this->getGiftcardUseStatus($useStatus);
        $arrFields = ['uid', 'tacticsId', 'count AS cardTotal', 'usedCount'];
        $offset = ActPlatMis_Common_Tool::getPageOffset($page, $limit);
        $list   = $this->dataGiftcard->getGiftcardDetailList($uid, $tacticsId, $status, $arrFields, $offset, $limit);
        $list   = $this->format($list);
        $total  = $this->dataGiftcard->getGiftcardDetailTotal($uid, $tacticsId, $status);

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 格式化数据
     * <AUTHOR>
     * @DateTime 2021-06-04
     * @param    [type]                $list [description]
     * @return   [type]                      [description]
     */
    private function format($list)
    {
        if (empty($list)) {
            return $list;
        }

        $validMap = ['否', '是'];

        foreach ($list as &$item) {
            $tactics = $this->getTacticsInfoById($item['tacticsId']);
            $isValidTime = ActPlatMis_Common_Tool::isValidTime($tactics['startTime'], $tactics['stopTime']);
            $giftcardCount = $this->getGiftcardCount($isValidTime, $item['cardTotal'], $item['usedCount']);

            $item['isValid']      = $validMap[(int)$isValidTime];
            $item['invalidCount'] = $giftcardCount['invalidCount'];
            $item['surplusCount'] = $giftcardCount['surplusCount'];
        }

        return $list;
    }

    /**
     * 获取礼品卡数量
     * <AUTHOR>
     * @DateTime 2021-06-04
     * @param    [type]                $isValidTime [description]
     * @param    [type]                $cardTotal   [description]
     * @param    [type]                $usedCount   [description]
     * @return   [type]                             [description]
     */
    private function getGiftcardCount($isValidTime, $cardTotal, $usedCount)
    {
        if ($isValidTime) {
            $invalidCount = 0;
            $surplusCount = $cardTotal - $usedCount;
        } else {
            $invalidCount = $cardTotal - $usedCount;
            $surplusCount = 0;
        }

        return ['invalidCount' => $invalidCount, 'surplusCount' => $surplusCount];
    }

    /**
     * 获取礼品卡使用状态
     * <AUTHOR>
     * @DateTime 2020-08-26
     */
    private function getGiftcardUseStatus($useStatus)
    {
        // 礼品卡是否使用，0全部，1是，2否
        if ($useStatus == self::USER_STATUS_YES) {
            $result = Service_Data_Giftcard_HaveGiftcard::STATUS_USED;
        } elseif ($useStatus == self::USER_STATUS_NO) {
            $result = Service_Data_Giftcard_HaveGiftcard::STATUS_UNUSED;
        } else {
            $result = null;
        }

        return $result;
    }

    /**
     * 获取策略信息
     * <AUTHOR>
     * @DateTime 2019-10-29
     * @param    int                $id 策略id
     * @return   array|bool
     */
    private function getTacticsInfoById($id)
    {
        if (!isset($this->tacticsHash[$id])) {
            $arrFields = ['startTime', 'stopTime'];
            $this->tacticsHash[$id] = $this->dataTactics->getTacticsById($id, $arrFields);
        }

        return $this->tacticsHash[$id];
    }
}