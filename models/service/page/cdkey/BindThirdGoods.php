<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      BindThirdGoods.php
 *
 * @author:        <EMAIL>
 * @desc:          关联第三方商品
 * @create:        2019-09-04 15:40:40
 * @last modified: 2019-09-04 15:40:40
 */
class Service_Page_CdKey_BindThirdGoods
{
    public function __construct()
    {
        $this->_objCdkey = new Service_Data_Cdkey();
        $this->_objCdkeySeries = new Service_Data_CdkeySeries();
    }

    public function execute($arrInput)
    {
        $userInfo = $arrInput['userInfo'];
        $seriesId = (int)$arrInput['seriesId'];
        $lastfrom = trim($arrInput['lastfrom']);
        $thirdPartyGoodId = trim($arrInput['thirdPartyGoodId']);

        if ($seriesId <= 0 || empty($thirdPartyGoodId) || empty($lastfrom)) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '', $arrInput);
        }

        $this->_objCdkeySeries->startTransaction();

        $seriesInfo = $this->_objCdkeySeries->getSeriesBySeriesId($seriesId);
        if (false === $seriesInfo) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, $seriesId);
        }

        $channelId = $seriesInfo['channelId'];
        if ($seriesInfo['thirdPartySkuid']) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, $seriesId . ' has bind');
        }

        //查询是否存在已绑定的第三方商品的批次下有未使用的兑换码
        $isExist = $this->_objCdkeySeries->getSeriesbythirdPartySkuid($thirdPartyGoodId, $channelId);
        if (false === $isExist) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, $thirdPartyGoodId);
        }

        if (!empty($isExist)) {
            $seriesIds = [];
            foreach ($isExist as $value) {
                $seriesIds[] = $value['id'];
            }

            $cnt = $this->_objCdkey->getInitCdKeyCntByseriesId($seriesIds);
            if (false === $cnt) {
                $this->_objCdkeySeries->rollback();
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '', $seriesIds);
            }

            if (!empty($cnt)) {
                foreach ($cnt as $value) {
                    if ($value['cnt'] > 0) {
                        $this->_objCdkeySeries->rollback();
                        throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '第三方商品存在未使用的兑换码');
                    }
                }
            }
        }

        //添加日志
        $mId  = ActPlatMis_Const_BackLogMid::OPERATION_CDKEYSERIES;
        $type = Service_Data_OperateBackLog::TYPE_BIND;
        $bool = ActPlatMis_Common_BackLog::insertLogByType($mId, $seriesId, $userInfo, $type);
        if ($bool === false) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::NETWORK_ERROR, '', ['mId' => $mId, 'adId' => $seriesId]);
        }

        // 同步优惠策略到营销系统的接口
        // http://yapi.afpai.com/project/218/interface/api/48275
        $pathinfo = '/misbiz/discountbiz/asyncdiscountbiz';
        $arrParams = [
            'discountTicket' => $seriesId,
            'saleChannel'    => $channelId,
            'startTime'      => $seriesInfo['startTime'],
            'stopTime'       => $seriesInfo['stopTime'],
            'discountPrice'  => $seriesInfo['price'] / 100,
            'bizType'        => 1,
            'createUid'      => $userInfo['uid'],
            'createName'     => $userInfo['uname'],
            'saveType'       => 1,
            'source'         => $seriesInfo['source'],
        ];
        $ret = Oplib_Util_RalClient::ralPost('misbiz', $arrParams, $pathinfo);
        if ($ret['errNo'] !== 0) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::NETWORK_ERROR, '', $arrParams);
        }

        $seriesInfo['ext']['lastfrom']      = $lastfrom;
        $seriesInfo['ext']['discountBizId'] = $ret['data']['discountBizId'];
        $affectedRows = $this->_objCdkeySeries->bindSeriesBySeriesId($seriesId, $thirdPartyGoodId, $seriesInfo['ext']);
        if (empty($affectedRows)) {
            $this->_objCdkeySeries->rollback();
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '', ['seriesId' => $seriesId]);
        }

        $this->_objCdkeySeries->commit();
        return;
    }
}
