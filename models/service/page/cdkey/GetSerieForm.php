<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      GetSerieForm.php
 *
 * @author:        <EMAIL>
 * @desc:          获取兑换序列筛选栏
 * @create:        2019-09-04 15:43:27
 * @last modified: 2019-09-04 15:43:27
 */
class Service_Page_CdKey_GetSerieForm {
    public function execute($arrInput) {
        $uname = $arrInput['userInfo']['uname'] ?? '';  // 用户名
        $res = [];
        $res['menuList'] = [
            [
                "type"      =>"primary",
                "name"      => "查询",
                "role"      => "search",
                "url"       => "/actplatmis/cdkey/getserieslist",
                "iconType"  => "search"
            ],
            [
                'type'      => 'default',
                'name'      => '重置',
                'role'      => 'reset',
                'url'       => '',
                'iconType'  => '',
            ],
            [
                "type"      => "primary",
                "name"      => "操作记录",
                "role"      => "oper",
                "url"       => "",
                "iconType"  => "oper"
            ],
            [
                "type"      => "primary",
                "name"      => "新建兑换码",
                "role"      => "add",
                "url"       => "",
                "iconType"  => "add"
            ]
        ];

        $formList = $this->_getFormList($uname);
        $res['formList'] = $formList;
        return $res;
    }
    private function _getFormList($uname) {
        $source = ActPlatMis_Channel::getChannelNameHash();
        $sourceOpt = [];
        $sourceOpt[] = [
            'label' => '全部',
            'value' => -1,
        ];
        foreach ($source as $key => $value) {
            $sourceOpt[] = [
                'label' => $value,
                'value' => $key,
            ];
        }

        // 授权的业务线
        $authSourceList = ActPlatMis_Auth_SourceInfo::getAuthCDKeySourceList($uname);
        return [
            [
                'type'         => 'text',
                'name'         => 'seriesId',
                'label'        => '兑换码序列ID',
                'defaultValue' => '',
                'labelWidth'   => '120px'
            ],
            [
                'type'         => 'text',
                'name'         => 'seriesName',
                'label'        => '兑换码序列名称',
                'defaultValue' => '',
                'labelWidth'   => '120px'
            ],
            [
                'type'         => 'text',
                'name'         => 'skUid',
                'label'        => '作业帮skuid',
                'defaultValue' => '',
                'labelWidth'   => '120px'
            ],
            [
                'type'         => 'text',
                'name'         => 'thirdPartyGoodId',
                'label'        => '第三方商品ID',
                'defaultValue' => '',
                'labelWidth'   => '120px'
            ],
            [
                'type'         => 'select',
                'name'         => 'channelId',
                'label'        => '售卖渠道',
                'defaultValue' => -1,
                'options'      => $sourceOpt
            ],
            [
                'type'  => 'datetimerange',
                'name'  => 'effectTime',
                'label' => '兑换码有效期',
                'labelWidth'   => '120px',
                'valueFormat'  => 'timestamp'
            ],
            [
                'type'         => 'select',
                'name'         => 'source',
                'label'        => '业务线',
                'options'      => $authSourceList,
                'defaultValue' => $authSourceList[0]['value'] ?? '',
            ],

        ];
    }
}
