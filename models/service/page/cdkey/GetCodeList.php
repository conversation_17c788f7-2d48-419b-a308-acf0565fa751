<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      GetCodeList.php
 *
 * @author:        <EMAIL>
 * @desc:          获取兑换码列表
 * @create:        2019-09-04 15:44:20
 * @last modified: 2019-09-04 15:44:20
 */
class Service_Page_CdKey_GetCodeList {
    private $objCdkey   = null;
    private $dataSeries = null;
    private $seriesHash = [];

    public function __construct() {
        $this->objCdkey  = new Service_Data_Cdkey();
        $this->dataSeries = new Service_Data_CdkeySeries();
    }

    public function execute($arrInput) {
        $uname  = $arrInput['userInfo']['uname'] ?? '';
        $source = intval($arrInput['source'] ?? -1); // 默认-1表示，取配置的数据权限，业务线第一个选项

        // 授权的业务线信息
        $authSourceList = ActPlatMis_Auth_SourceInfo::getAuthCDKeySourceList($uname);
        $authSourceMap  = array_column($authSourceList,'label','value');
        if (empty($authSourceMap) || (-1 != $source && !array_key_exists($source, $authSourceMap))) {
            $dataInfo = ['dataInfo' => json_encode(compact('uname', 'source', 'authSourceMap'), JSON_UNESCAPED_UNICODE)];
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '你没有当前业务线操作权限', $dataInfo);
        }
        if (-1 == $source) {
            $source = $authSourceList[0]['value']; // 默认使用第一个
        }

        Hk_Util_Log::start('gconds');
        $arrConds = $this->_getConds($arrInput);
        Hk_Util_Log::stop('gconds');
        $arrConds['source'] = $source; // 加上业务线条件

        $arrData = $this->objCdkey->getCodeListByConds($arrConds, $arrInput['pn'], $arrInput['rn']);
        if (false === $arrData) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '', $arrConds);
        }
        return [
            'config' => $this->_getConfig(),
            'list'   => $this->_format($arrData['list'], $arrInput['pn'], $arrInput['rn'], $authSourceMap),
            'meta'   => [
                'pn' => $arrInput['pn'],
                'rn' => $arrInput['rn'],
                'total' => $arrData['total'],
            ]
        ];
    }

    // 获取批次信息
    private function getSeriesInfoById($id)
    {
        if (!isset($this->seriesHash[$id])) {
            $arrFields = ['startTime', 'stopTime'];
            $this->seriesHash[$id] = $this->dataSeries->getSeriesBySeriesId($id, $arrFields);
        }

        return $this->seriesHash[$id];
    }

    /**
     * _format
     * 格式化输出数据
     * @param mixed $arrData
     * @param mixed $pn
     * @param mixed $rn
     * @access private
     * @return Array
     */
    private function _format($arrData, $pn, $rn, $authSourceMap){
        $arrOutput =  [];
        if(empty($arrData)){
            return $arrOutput;
        }
        $arrOutput = [];
        $i = 1 + $pn * $rn;
        foreach ($arrData as $value) {
            //操作按钮根据状态进行相应修改
            $oper[0] = ['label' => '停用', 'role' => 'stop'];
            $oper[1] = ['label' => '退款', 'role' => 'refund'];

            if ($value['status'] == Service_Data_Cdkey::STATUS_INIT) {
                $oper[0]['disabled'] = false;
            } else {
                $oper[0]['disabled'] = true;
            }

            if ($value['status'] == Service_Data_Cdkey::STATUS_SELL) {
                $oper[1]['disabled'] = false;
            } else {
                $oper[1]['disabled'] = true;
            }

            $seriesInfo = $this->getSeriesInfoById($value['seriesId']);

            $operList = [];
            $operList = $oper;
            // 没有实付价取成交价
            $price = is_numeric($value['actualPrice']) ? $value['actualPrice'] : $value['thirdPartyPrice'];
            ($price > 0) && $price /= 100;
            $arrOutput[] = [
                "id"                => $i++,
                'seriesId'          => $value['seriesId'],
                'cdkeyId'           => $value['id'],
                "cdkey"             => str_replace('==', '', $value['cdkey']),
                'channel'           => $value['channelId'] > 0 ? ActPlatMis_Channel::getChannelName($value['channelId']) : '',
                "thirdPartyOrderid" => !empty($value['thirdPartyOrderid']) ? $value['thirdPartyOrderid'] : '',
                "buyPhone"          => !empty($value['buyPhone']) ? $value['buyPhone'] :'',
                "thirdPartyPrice"   => is_numeric($price) ? $price . '元' : '',
                "skuId"             => !empty($value['skuId']) ? $value['skuId'] : '',
                "tradeId"           => ($value['tradeId'] > 0) ? $value['tradeId'] : '',
                "exchangePhone"     => !empty($value['exchangePhone']) ? $value['exchangePhone'] : '',
                "status"            => Service_Data_Cdkey::$ARR_STATUS_VAL[$value['status']],
                "statusNum"         => $value['status'],
                'createTime'        => $value['createTime'] > 0 ? date('Y-m-d H:i:s', $value['createTime']) : '',
                'updateTime'        => $value['updateTime'] > 0 ? date('Y-m-d H:i:s', $value['updateTime']) : '',
                'exchangeTime'      => $value['exchangeTime'] > 0 ? date('Y-m-d H:i:s', $value['exchangeTime']) : '',
                'startTime'         => $seriesInfo['startTime'] > 0 ? date('Y-m-d H:i:s', $seriesInfo['startTime']) : '',
                'stopTime'          => $seriesInfo['stopTime'] > 0 ? date('Y-m-d H:i:s', $seriesInfo['stopTime']) : '',
                "oper"              => $operList,
                'isCancel'          => (int)self::isCancel($value['channelId'], $value['status']),
                // 业务线
                'sourceDesc'        => strval($authSourceMap[$value['source']] ?? $value['source']),
                'source'            => $value['source'],
            ];
        }
        return $arrOutput;

    }

    /**
     * 是否能作废
     * <AUTHOR>
     * @DateTime 2020-04-28
     * @param    [type]                $channelId [description]
     * @param    [type]                $status    [description]
     * @return   boolean                          [description]
     */
    private static function isCancel($channelId, $status)
    {
        if (ActPlatMis_Channel::isOfflineChannel($channelId) && $status == Service_Data_Cdkey::STATUS_SELL) {
            return true;
        }

        return false;
    }

    /**
     * _getConds
     * 获取输出条件
     * @param mixed $arrInput
     * @access private
     * @return Array
     */
    private function _getConds($arrInput)
    {
        $arrConds = [];
        if ($arrInput['seriesId'] > 0) {
            $arrConds['seriesId'] = $arrInput['seriesId'];
        }
        if ($arrInput['channel'] > 0) {
             $arrConds['channelId'] = (int)$arrInput['channel'];
        }
        if (!empty($arrInput['cdkey'])) {
            $arrConds['cdkey'] = base64_encode($arrInput['cdkey']);
        }
        if (!empty($arrInput['thirdPartyOrderid'])) {
            $arrConds['thirdPartyOrderid'] = $arrInput['thirdPartyOrderid'];
        }
        if (!empty($arrInput['buyPhone'])) {
            $arrConds['buyPhone'] = $arrInput['buyPhone'];
        }
        if ($arrInput['tradeId'] > 0) {
            $arrConds['tradeId'] = $arrInput['tradeId'];
        }
        if (!empty($arrInput['exchangePhone'])) {
            $arrConds['exchangePhone'] = $arrInput['exchangePhone'];
        }
        if ($arrInput['skuId'] > 0) {
            $arrConds['skuId'] = $arrInput['skuId'];
        }
        if ($arrInput['status'] > -1) {
            $arrConds['status'] = $arrInput['status'];
        }
        if ($arrInput['startTime'] > 0) {
            $arrConds['updateTime'][] = $arrInput['startTime'];
            $arrConds['updateTime'][] = '>=';
        }
        if ($arrInput['stopTime'] > 0) {
            $arrConds['updateTime'][] = $arrInput['stopTime'];
            $arrConds['updateTime'][] = '<=';
        }
        $arrConds['source'] = intval($arrInput['source']); //添加业务线限制权限

        return $arrConds;
    }

    /**
     * _getConfig
     * 获取表头
     * @access private
     * @return Array
     */
    private function _getConfig(){
        return [
            [
                "label" => "#",
                "name"  => "id"
            ],
            [
                "label" => "序列ID",
                "name"  => "seriesId"
            ],
            [
                "label" => "兑换码ID",
                "name"  => "cdkeyId"
            ],
            [
                "label" => "兑换码",
                "name"  => "cdkey"
            ],
            [
                "label" => "渠道名称",
                "name"  => "channel"
            ],
            [
                "label" => "第三方订单",
                "name"  => "thirdPartyOrderid"
            ],
            [
                "label" => "购买手机号",
                "name"  => "buyPhone"
            ],
            [
                "label" => "第三方成交价格",
                "name"  => "thirdPartyPrice"
            ],
            [
                "label" => "作业帮交易id",
                "name"  => "tradeId"
            ],
            [
                "label" => "兑换手机号",
                "name"  => "exchangePhone"
            ],
            [
                'name'  => 'sourceDesc',
                'label' => '业务线',
            ],
            [
                "label" => "作业帮商品skuId",
                "name"  => "skuId"
            ],
            [
                "label" => "兑换码状态",
                "name"  => "status"
            ],
            [
                "label" => "创建时间",
                "name"  => "createTime"
            ],
            [
                "label" => "兑换时间",
                "name"  => "exchangeTime"
            ],
            [
                "label" => "兑换码状态更新时间",
                "name"  => "updateTime"
            ],
            [
                "label" => "操作",
                "name"  => "oper"
            ]
        ];
    }
}
