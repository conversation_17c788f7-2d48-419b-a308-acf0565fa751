<?php

class Service_Page_Oms_DownloadDetail
{

    const LIMIT = 1000;

    private $csv = null;
    private $dataDetail = null;

    public function __construct()
    {
        $this->csv = new Oplib_Util_Actplat_Csv();
        $this->dataDetail = new Service_Data_Oms_ImportOrderDetail();
    }

    public function execute($arrInput = [])
    {
        $id = $arrInput["id"];
        $importType = $arrInput["importType"];
        //1:成功 2:导入失败 3:成单失败 4:下载导入模板
        if($id < 0 || !in_array($importType, [1,2,3,4])) {
            ActPlatMis_Common_Tool::buildException(
                ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $arrInput, '请求参数错误'
            );
            return;
        }
        //导入模板
        if ($importType == 4) {
            $this->csv->setCsvHeader($this->getHeader($importType));
            $this->downloadFile();
            exit(0);
        }

        $offest = 0;
        $arrConds = [];
        $arrConds["taskId"] = intval($id);
        if ($importType == 1){
            $arrConds[] = 'err_no = 0 and import_code = 0';
        }else if ($importType == 2){
            $arrConds[] = sprintf("err_no != 0 or import_code in (%d,%d)", ActPlatMis_Const_Oms_Const::ERROR_PARAMS, ActPlatMis_Const_Oms_Const::ERROR_REPEAT);
        }else if ($importType == 3){
            $arrConds[] = sprintf("err_no = 0 and import_code not in (%d,%d)", ActPlatMis_Const_Oms_Const::ERROR_PARAMS, ActPlatMis_Const_Oms_Const::ERROR_REPEAT);
        }
        $this->csv->setCsvHeader($this->getHeader($importType));
        do {
            $dataList = [];
            $omsDataList = $this->dataDetail->getImportOrderDetailListByCondition($arrConds, $this->getArrFields($importType), $offest, self::LIMIT);
            foreach ($omsDataList as $dataItem) {
                $extDataDecode = json_decode($dataItem["ext"], true);
                $integrationDataItem = $this->integrationCsvData($dataItem, $extDataDecode, $importType);
                $dataList[] = $integrationDataItem;
            }
            $this->csv->setCsvBody($dataList);
            $offest += self::LIMIT;
        } while(count($dataList) == self::LIMIT);
        $this->downloadFile();
        exit(0);
    }


    private function integrationCsvData($srcData, $extData, $importType)
    {
        $data = [];
        $data["thirdOrderid"] = isset($extData[0]) ? $extData[0] : "";
        $data["phone"] = isset($extData[1]) ? $extData[1] : "";
        $data["wmsName"] = isset($extData[2]) ? $extData[2] : "";
        $data["wmsPhone"] = isset($extData[3]) ? $extData[3] : "";
        $data["wmsProvince"] = isset($extData[4]) ? $extData[4] : "";
        $data["wmsCity"] = isset($extData[5]) ? $extData[5] : "";
        $data["wmsPrefecture"] = isset($extData[6]) ? $extData[6] : "";
        $data["wmsTown"] = isset($extData[7]) ? $extData[7] : "";
        $data["wmsAddress"] = isset($extData[8]) ? $extData[8] : "";
        $data["class"] = isset($extData[9]) ? $extData[9] : "";
        $data["tradeFee"] = isset($extData[10]) ? $extData[10] : "";
        if ($importType != 1){
            if ($srcData["errMsg"] != ""){
                $data["errMsg"] = $srcData["errMsg"];
            }else{
                $data["errMsg"] = $srcData["importMsg"];
            }
        }
        return $data;
    }

    private function getHeader($importType)
    {
        $header = [];
        $header[] = '三方订单id';
        $header[] = '开通作业帮手机号';
        $header[] = "收件人姓名";
        $header[] = "收件人手机号";
        $header[] = "收件人地址省";
        $header[] = "收件人地址市";
        $header[] = "收件人地址区";
        $header[] = "收件人地址镇";
        $header[] = "收件人详细地址";
        $header[] = "用户购买课程年级";
        $header[] = "用户实付金额";
        if ($importType != 1 && $importType != 4){
            $header[] = "失败原因";
        }
        return $header;
    }

    private function downloadFile()
    {
        $file = $this->csv->getFullPath();

        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Transfer-Encoding: binary');
        header('Accept-Ranges: bytes');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        header('Content-Disposition: attachment; filename=' . basename($file));

        ob_end_clean();
        readfile($file);
    }

    private function getArrFields($importType)
    {
        $fields = [];
        $fields[] = "ext";
        if ($importType != 1) {
            $fields[] = "errNo";
            $fields[] = "errMsg";
            $fields[] = "importCode";
            $fields[] = "importMsg";
        }
        return $fields;
    }
}