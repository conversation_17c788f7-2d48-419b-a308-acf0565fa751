<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   BindRelationList.php
 * <AUTHOR>
 * @date   2019/12/24 15:34
 * @brief  【分销绑定关系】列表(分销员和新用户)
 **/
class Service_Page_Fenxiao_BindRelationList
{
    const PAGE_SIZE_MAX = 100; // 分页大小，最大值
    const DIRECT_DOWNLOAD_MAX_CNT = 500; // 直接下载最大数500条
    private $objDsBR;

    public function __construct()
    {
        $this->objDsBR = new Service_Data_Fenxiao_BindRelation();
    }


    public function execute($arrInput = [])
    {
        $arrOutput = [
            'total' => 0,
            'list'  => [],
        ];

        $this->checkParams($arrInput);
        // 如果是导出数据操作，添加导出数据参数
        if ($arrInput['export']) {
            $arrOutput['header'] = [
                ['k' => 'id', 'v' => 'ID'],
                ['k' => 'configId', 'v' => '活动id'],
                ['k' => 'actName', 'v' => '活动名称'],
                ['k' => 'recruiterUid', 'v' => '招募员uid'],
                ['k' => 'inviterUid', 'v' => '分销员uid'],
                ['k' => 'inviteeUid', 'v' => '新用户uid'],
                ['k' => 'createTime', 'v' => '绑定开始时间'],
                ['k' => 'expireTime', 'v' => '绑定结束时间'],
                ['k' => 'isZhuanHua', 'v' => '是否转化'],
            ];
            $arrOutput['export'] = 1;
        }
        // 查询数据
        $ret = $this->getBRList($arrInput);
        if (!$ret['isCanDirectDownload']) {
            // 不可以直接下载，则调用异步下载
            $this->asynchronousDownload($arrInput);
            $arrOutput['isCanDirectDownload'] = 0;

            return $arrOutput;
        }

        if (empty($ret['list'])) {
            return $arrOutput;
        }
        $arrOutput['total'] = $ret['total'];
        $actNameMap = [];
        foreach ($ret['list'] as $v) {
            isset($actNameMap[$v['configId']]) || $actNameMap[$v['configId']] = '';
        }

        $actNameMap = $this->getActNameList(array_keys($actNameMap));
        $curTime = time();
        // 组合格式化处理
        // 数据格式化处理见需求文档： http://wiki.afpai.com/pages/viewpage.action?pageId=67453541
        foreach ($ret['list'] as $v) {
            // 招募员uid为0时，设置为空字符串
            $v['recruiterUid'] = $v['recruiterUid'] ?: '';
            $v['actName']     = $actNameMap[$v['configId']]['name'] ?? '';
            $v['isZhuanHua']  = $v['buyStatus'] ? '是' : '否'; // 是否转化
            if($curTime < $v['expireTime']){
                $v['isCanUnBind'] = $v['buyStatus'] ? 0 : 1; // 是否可以解绑【0：否，1：可以】
            }else{
                $v['isCanUnBind'] = 0;
            }
            $v['createTime'] = date('Y.m.d H:i:s',$v['createTime']);
            $v['expireTime'] = date('Y.m.d H:i:s',$v['expireTime']);
            unset($v['buyStatus']);
            $arrOutput['list'][] = $v;
        }

        return $arrOutput;
    }


    // 异步下载任务
    private function asynchronousDownload($arrInput)
    {
        // 验证参数
        $userInfo = $arrInput['userInfo'] ?? [];
        if (!isset($userInfo['uname']) || 0 >= strlen($userInfo['uname'])) {
            echo "获取当前登录用户的用户名失败，不能创建异步下载任务" . PHP_EOL;
            return;
        }
        // 搜索参数
        $searchParam = array_filter($arrInput);
        unset($searchParam['export']);
        unset($searchParam['userInfo']);
        unset($searchParam['pn']);
        unset($searchParam['rn']);
        $fileName     = sprintf('绑定关系-%s-%s.csv', date('Ymd_H:i:s'), rand());
        $callbackInfo = ['type' => 'dingding', 'cfg' => ['account' => $userInfo['uname'], 'nickName' => $userInfo['nickName'] ?? '']];
        // 创建异步任务
        $offlineTask = new Oplib_Util_OfflineTask_OfflineTask();
        $searchParam['rn'] = self::PAGE_SIZE_MAX; // 分页大小
        $fields      = ["id", "configId", "actName", "recruiterUid", "inviterUid", "inviteeUid", "createTime", "expireTime", "isZhuanHua"];
        $ret         = $offlineTask->create($fileName, 'actplatmis', 'Fenxiao_BindRelationList', $userInfo['uname'], $searchParam, $fields, $callbackInfo);
        if ($ret === false) {
            echo "导出下载任务提交失败" . PHP_EOL;
            return;
        }

        echo "已经成功创建了导出任务，生成的文件名为【{$fileName}】，文件一般需要10分钟左右时间准备完毕，准备完毕后会【邮件】或者【钉钉】通知您进行下载！请注意查收，请稍后~".PHP_EOL;
    }

    // 获取活动名称k-v
    private function getActNameList($configIds)
    {
        $objDs = new Service_Data_Fenxiao_Config();
        $list  = $objDs->getKVConfigInfo($configIds, ['id', 'name']);
        if (false === $list) {
            $errMsg = '查询数据库错误';
            $data   = compact('configIds');
            ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::DB_ERROR, $errMsg, $data);
        }
        return $list;
    }

    private function getBRList($arrInput)
    {
        $arrOutput = [
            'total' => 0,
            'list'  => [],
            'isCanDirectDownload' => true, // 是否可以直接下载，默认是true
        ];

        $arrConds      = ['source' => 0]; // 来源 0:分销 1:拼团
        $arrCondFields = ['configId', 'inviterUid', 'inviteeUid', 'recruiterUid'];
        foreach ($arrCondFields as $field) {
            if (is_null($arrInput[$field])) {
                continue;
            }
            $arrConds[$field] = $arrInput[$field];
        }
        $arrFields  = ['id', 'configId', 'inviterUid', 'inviteeUid', 'recruiterUid', 'createTime', 'expireTime', 'status', 'buyStatus'];
        $arrOptions = null;
        $limit      = $arrInput['rn'];
        $offset     = ($arrInput['pn'] - 1) * $limit;
        $arrAppends = [sprintf('ORDER BY bind_relation_id LIMIT %s,%s', $offset, $limit)];
        Bd_Log::addNotice('export', $arrInput['export']);
        // 如果是导出数据功能，不分页
        if($arrInput['export']){
            $arrAppends = ['ORDER BY bind_relation_id'];
        }

        $total = $this->objDsBR->getCntByConds($arrConds);
        if (false === $total) {
            $errMsg = '查询数据库错误';
            $data   = compact('arrConds', 'arrAppends');
            ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::DB_ERROR, $errMsg, $data);
        }
        // 如果是导出，并且要导出的数据大于最大可直接导出的数据，返回不能直接导出
        if ($arrInput['export'] && $total > self::DIRECT_DOWNLOAD_MAX_CNT) {
            $arrOutput['total']               = $total;
            $arrOutput['isCanDirectDownload'] = false;
            return $arrOutput;
        }

        $list       = $this->objDsBR->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
        if (false === $list) {
            $errMsg = '查询数据库错误';
            $data   = compact('arrConds', 'arrAppends');
            ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::DB_ERROR, $errMsg, $data);
        }
        if ($offset == 0 && empty($list)) {
            // 没有查询到数据，直接返回
            return $arrOutput;
        }

        $arrOutput['list']  = $list;
        $arrOutput['total'] = $total;

        return $arrOutput;
    }

    private function checkParams(&$arrInput)
    {
        // 如果是标量类型并且不是boolean类型，则调用trim()函数去除收尾空格
        $arrInput = array_map(function ($v) {
            if (is_scalar($v) && !is_bool($v)) {
                return trim($v);
            }
            return $v;
        }, $arrInput);

        $configId   = $arrInput['configId'] ?? '';
        $inviterUid = $arrInput['inviterUid'] ?? '';
        $inviteeUid = $arrInput['inviteeUid'] ?? '';
        $recruiterUid = $arrInput['recruiterUid'] ?? '';
        $pn         = $arrInput['pn'] ?? '';
        $rn         = $arrInput['rn'] ?? '';
        $export     = $arrInput['export'] ?? 0; // 为1时，则导出数据

        $errMsgMap = [
            'configId'   => '活动ID(configId)必须为大于0的整数',
            'inviterUid' => '分销员UID(inviterUid)必须为大于0的整数',
            'inviteeUid' => '新用户UID(inviteeUid)必须为大于0的整数',
            'recruiterUid' => '招募员UID(recruiterUid)必须为大于0的整数',
            'pn'         => '页码(pn)必须为大于0的整数',
            'rn'         => '分页大小(rn)必须为大于0的整数',
        ];

        // 如果是导出数据，则pn,rn设置为：1，以通过下面的验证
        if ('1' === strval($export)) {
            $export = 1;
            $pn     = 1;
            $rn     = 1;
        } else {
            $export = 0;
        }

        // 验证是否是空字符串或者是数字字符串
        foreach ($errMsgMap as $key => $errMsg) {
            if (!preg_match('/^([1-9]|[1-9]\d+|)$/', ${$key})) {
                ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $errMsg, $arrInput);
            }
        }

        // 判断configId、inviterUid、inviteeUid、recruiterUid是否全为空
        if (!$configId && !$inviterUid && !$inviteeUid && !$recruiterUid) {
            $errMsg = '活动ID(configId)、分销员UID(inviterUid)、新用户UID(inviteeUid)、招募员UID(recruiterUid)不能全部为空';
            ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $errMsg, $arrInput);
        }
        // pn,rn为空时设置为默认值
        strlen($pn) == 0 && $pn = 1;
        strlen($rn) == 0 && $rn = 20;

        $arrInput['configId']   = $configId ? intval($configId) : null;
        $arrInput['inviterUid'] = $inviterUid ? intval($inviterUid) : null;
        $arrInput['inviteeUid'] = $inviteeUid ? intval($inviteeUid) : null;
        $arrInput['recruiterUid'] = $recruiterUid ? intval($recruiterUid) : null;
        $arrInput['pn']         = intval($pn);
        $arrInput['rn']         = intval($rn);
        $arrInput['export']     = intval($export);

        // 不是导出数据的，需要验证rn参数
        if ($export == 0 && $arrInput['rn'] > self::PAGE_SIZE_MAX) {
            $errMsg = '分页大小最大为' . self::PAGE_SIZE_MAX;
            ActPlatMis_Common_Tool::throwException(ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $errMsg, $arrInput);
        }

        return $arrInput;
    }
}