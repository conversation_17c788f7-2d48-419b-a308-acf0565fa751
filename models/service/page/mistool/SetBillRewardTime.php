<?php
/**
 * 设置账单奖励时间
 * <AUTHOR>
 * @date 2021-01-28
 */
class Service_Page_MisTool_SetBillRewardTime
{
    private $dataBill = null;
    // 白名单
    private $whiteList = ['liusong'];

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2021-01-28
     */
    public function __construct()
    {
        $this->dataBill = new Service_Data_Reward_ActRewardBill();
    }

    /**
     * 固有执行方法
     * <AUTHOR>
     * @DateTime 2021-01-28
     * @param    array  $arrInput 输入参数
     * @return   array
     */
    public function execute($arrInput = [])
    {
        $id = (int)$arrInput['id'];
        $operator = $arrInput['operator'];

        if ($id <= 0) {
            ActPlatMis_Common_Tool::buildException(ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $arrInput, '无效的账单ID');
        }

        if (empty($operator) || !in_array($operator, $this->whiteList)) {
            ActPlatMis_Common_Tool::buildException(ActPlatMis_Common_ExceptionCodes::PARAM_ERROR, $arrInput, '没有权限操作');
        }

        $bill = $this->dataBill->getBillOneById($id, ['extData']);
        if (empty($bill)) {
            ActPlatMis_Common_Tool::buildException(ActPlatMis_Common_ExceptionCodes::DB_ERROR, $arrInput, '用户账单数据不存在');
        }

        $nowTime = ActPlatMis_Common_Tool::getCurrentTimestamp();
        $extData = array_merge($bill['extData'], ['operator' => $operator, 'reason' => '修改测试数据']);
        $params = ['extData' => $extData, 'rewardTime' => $nowTime, 'startPTime' => $nowTime];
        $bool = $this->dataBill->changeBillOneById($id, $params);
        if ($bool === false) {
            ActPlatMis_Common_Tool::buildException(ActPlatMis_Common_ExceptionCodes::DB_ERROR, $arrInput, '更新用户账单失败');
        }

        return [];
    }
}