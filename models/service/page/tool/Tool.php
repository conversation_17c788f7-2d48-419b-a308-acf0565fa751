<?php

class Service_Page_Tool_Tool
{
    const ALLOWLIST = [
        'yanxinyuan' => true,
        'maochenyang' => true,
        'wenronghao' => true,
        'xujianlong' => true,
        'zhangting19' => true,
        'wangsiqi12' => true,
        'chenping' => true,
        'chenxiaona01' => true,
        'huangqiqi01' => true,
        'lifeifei07' => true,
        'gaoxinyue' => true,
        'tianmengyue' => true,
        'liuluyi01' => true,
        'xumeng12' => true,
        'baixuejing' => true,
        'zhangmengyang07' => true,
        'nanlaixin' => true,
        'gaolin01' => true,
        'liuna26' => true,
        'zhangbobo02' => true,
        'liulu60' => true,
        'zhuchaofeng' => true,
        'cuizheng' => true,
        'wuxiaofang04' => true,
        'liyuncong01' => true,
    ];

    public function __construct($uname)
    {
        if (Hk_Util_Env::RunEnvTest !== Qdlib_Util_Tool::getEnviron() && !isset(self::ALLOWLIST[$uname])) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::AUTH_ERROR);
        }
    }
}