<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 **/
class Service_Page_Afx_AuthSave
{
    public function __construct()
    {
        $this->objAuth = new Service_Data_Afx_AFXAuth();
    }

    public function execute($arrInput)
    {
        $id = (int)$arrInput['id'];
        $pid = (int)$arrInput['pid'];
        $type = (int)$arrInput['type'];
        $name = (string)$arrInput['name'];
        $rank = (int)$arrInput['rank'];
        $url = (string)$arrInput['url'];
        $icon = (string)$arrInput['icon'];
        $isJump = (int)$arrInput['isJump'];
        $status = (int)$arrInput['status'];
        $arrOutPut = [];

        // 待保存的内容
        $arrFields = array(
            'pid'    => $pid,
            'type'   => $type,
            'name'   => $name,
            'rank'   => $rank,
            'url'    => $url,
            'icon'   => $icon,
            'isJump' => $isJump,
            'status' => $status,
        );

        // 是否新增
        if(empty($id)){
            $authId = $this->objAuth->add($arrFields);
            if ($authId === false) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '保存失败');
            }
        }else{
            if ($id == $pid) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '不能以自己作为上级');
            }
            $authInfo = $this->objAuth->getInfoById($id);
            if( empty($authInfo) ){
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '权限不存在');
            }


            if($pid!=0){
                $pAuthInfo = $this->objAuth->getInfoById($pid);
                if( empty($pAuthInfo) ){
                    throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '父权限不存在');
                }
            }
            if( $pAuthInfo['type']==Service_Data_Afx_AFXAuth::TYPE_API && $pid==0 ){
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '接口不能成为父节点');
            }

            // 仅保存
            $arrFields['updateTime'] = time();
            $ret = $this->objAuth->updateById($id, $arrFields);
            if ($ret === false) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '保存失败');
            }
        }

        return $arrOutPut;
    }
}
