<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief       账单邮件任务类型详情设置为发送
 **/
class Service_Page_Afx_BillMailSendDecide
{
    public function __construct()
    {
        $this->objSendMailDetailed = new Service_Data_Afx_AFXSendMailDetailed();
    }

    public function execute($arrInput)
    {
        $searchText = (string)$arrInput['searchText'] ? (string)$arrInput['searchText'] : '';
        $taskId = (int)$arrInput['taskId'] ? (int)$arrInput['taskId'] : 0;
        $mailIds = !empty($arrInput['mailIds']) && is_array($arrInput['mailIds']) ? $arrInput['mailIds'] : [];
        $cancelMailIds = !empty($arrInput['cancelMailIds']) && is_array($arrInput['cancelMailIds']) ? $arrInput['cancelMailIds'] : [];
        $isAllChoose = (int)$arrInput['isAllChoose']>0 ? 1 : 0;
        // 返回值
        $arrOutput = [];

        if( empty($taskId) ){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '没有传入任务id');
        }

        //检索数据拼装
        $arrConds['taskId'] = $taskId;
        $arrConds['type'] = Service_Data_Afx_AFXSendMailDetailed::TYPE_DETAILED;
        if($isAllChoose>0){
            if($cancelMailIds){
                $arrConds[] = 'id not in ('.implode(',',$cancelMailIds).')';
            }
        }else{
            if($mailIds){
                $arrConds[] = 'id in ('.implode(',',$mailIds).')';
            }else{
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '没有选择发送项');
            }
        }
        if($searchText){
            $arrConds[] = 'name like "%'.$searchText.'%" or receive_mail like "%'. $searchText.'%"';
        }
        $arrFields = [
            'type' => Service_Data_Afx_AFXSendMailDetailed::TYPE_SEND,
        ];
        $ret = $this->objSendMailDetailed->update($arrConds, $arrFields);
        if ($ret === false || !$ret) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '确定失败');
        }

        return $arrOutput;
    }
}
