<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-09
 * @brief       导出订单列表
 **/
class Service_Page_Afx_UnionOrderList
{
    public function execute($arrInput)
    {
        $objAct = new Service_Data_Afx_AFXAct();
        $objUser = new Service_Data_Afx_AFXUser();
        $objUserCommon = new ActPlatMis_Afx_UserCommon();

        // 超管和测管可以选择负责人
        // 普管负责人为自己
        if (!in_array(ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_SUPER_ADMIN, $arrInput['founderRole']['dictAuthFormat'][ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_KEY]) && !in_array(ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_TEST_ADMIN, $arrInput['founderRole']['dictAuthFormat'][ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_KEY])) {
            $arrInput['cadre'] = $arrInput['founder'];
        }
        Qdlib_Util_Log::addNotice("Service_Page_Afx_UnionOrderList",json_encode($arrInput));
        $objUnionOrderListByDoris = new Qdlib_Service_Hetu_AFXUnionOrderListByDoris($objUser, $objUserCommon, $objAct);
        if(isset($arrInput['inst']) && !empty($arrInput['inst'])){
            $arrInput['instId'] = $arrInput['inst'];
        }
        return $objUnionOrderListByDoris->execute($arrInput, true);
    }

}
