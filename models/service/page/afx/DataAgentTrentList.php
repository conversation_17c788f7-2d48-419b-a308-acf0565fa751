<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @brief       代理商发展趋势
 **/
class Service_Page_Afx_DataAgentTrentList
{
    public function __construct()
    {
        $this->objOrgan = new Service_Data_Afx_AFXOrgan();
        $this->objUser = new Service_Data_Afx_AFXUser();
        $this->objDorisLeadsDetail = new Service_Data_Afx_AFXDorisLeadsDetail();
    }

    public function execute($arrInput)
    {
        $founderRole = $arrInput['founderRole'] ? $arrInput['founderRole'] : [];
        $founder = (string)$arrInput['founder'] ? (string)$arrInput['founder'] : '';

        $appId = $arrInput['appId'];
        $cadre = $arrInput['cadre'];
        $sTime = $arrInput['sTime'];
        $eTime = $arrInput['eTime'];

        // 返回值
        $arrOutput = [
            'list'  => [
                'regionList'=>[],
                'trendList'=>[]
            ],
        ];

        //获取全部区域
        $arrParams = [
            'areaId'  => 0,
        ];
        $data = Oplib_Util_RalClient::ralPost('zbui', $arrParams, '/goods/na/address/getaddress');
        if($data === false){
            return $arrOutput;
        }
        if($data['errNo'] != 0){
            return $arrOutput;
        }
        //区域数据
        //区域划分
        $regionList = [];
        foreach($data['data']['areaList'] as $key=>$pInfo){
            //默认值
            $tmpRegionList[$key]['areaName'] = $pInfo['areaName'];
            $tmpRegionList[$key]['count'] = 0;
            $tmpRegionList[$key]['buyCnt'] = 0;
            $tmpRegionList[$key]['transPv'] = 0;
            $tmpRegionList[$key]['conversionRate'] = 0;

            $organConds['app_id'] = $appId;
            $organConds['province'] = $pInfo['areaName'];
            $organFields = [
                'province',
                'id',
            ];
            $organList = $this->objOrgan->getList($organConds, $organFields, '', 0, 10000);
            if(empty($organList)){
                continue;
            }
            $organIds = [];
            foreach($organList as $val){
                $organIds[] = $val['id'];
            }

            //获取用户列表
            $userConds = [
                'app_id='.$appId,
                'inst_id in ('.implode(',', $organIds).')'
            ];
            if($cadre){
                $userConds['cadre'] = $cadre;
            }
            $agentInfoList = $this->objUser->getList($userConds, [], '', 0, 1000000);
            if(empty($agentInfoList)){
                continue;
            }
            $agentUids = [];
            foreach($agentInfoList as $agentInfo){
                $agentUids[] = $agentInfo['uid'];
            }

            //获取数据
            $detailConds = [
                'app_id='.$appId,
                'uid in ('.implode(',', $agentUids).')'
            ];
            $detailFields = [
                'count(distinct uid) as count',
                'sum(buy_cnt) as buyCnt',
                'sum(trans_pv) as transPv',
                'sum(leads_cnt) as leadsCnt',
            ];
            $userCountByProvince = $this->objDorisLeadsDetail->getList($detailConds, $detailFields, 'buyCnt desc', 'app_id', 0, 1);
            $tmpRegionList[$key]['count'] = count($agentUids);
            $tmpRegionList[$key]['buyCnt'] = $userCountByProvince[0]['buyCnt'];
            $tmpRegionList[$key]['transPv'] = $userCountByProvince[0]['transPv'];
            $tmpRegionList[$key]['conversionRate'] = intval($userCountByProvince[0]['leadsCnt'])>0 ? number_format(intval($userCountByProvince[0]['transPv'])/intval($userCountByProvince[0]['leadsCnt']),'4')*100 : 0;
        }

        $userNoPConds = [
            'app_id='.$appId,
            'inst_id=0',
        ];
        if($cadre){
            $userNoPConds['cadre'] = $cadre;
        }
        $agentNoPInfoList = $this->objUser->getList($userNoPConds, [], '', 0, 1000000);
        $agentNoPUids = [];
        foreach($agentNoPInfoList as $agentInfo){
            $agentNoPUids[] = $agentInfo['uid'];
        }
        $detailNoPConds = [
            'app_id='.$appId,
            'uid in ('.implode(',', $agentNoPUids).')'
        ];
        $detailNoPFields = [
            'count(distinct uid) as count',
            'sum(buy_cnt) as buyCnt',
            'sum(trans_pv) as transPv',
            'sum(leads_cnt) as leadsCnt',
        ];
        $userCountByNoProvince = $this->objDorisLeadsDetail->getList($detailNoPConds, $detailNoPFields, 'buyCnt desc', 'app_id', 0, 1);
        $tmp['count'] = count($agentNoPUids);
        $tmp['buyCnt'] = $userCountByNoProvince[0]['buyCnt'];
        $tmp['transPv'] = $userCountByNoProvince[0]['transPv'];
        $tmp['conversionRate'] = intval($userCountByNoProvince[0]['leadsCnt'])>0 ? number_format(intval($userCountByNoProvince[0]['transPv'])/intval($userCountByNoProvince[0]['leadsCnt']),'4')*100 : 0;
        $tmp['areaName'] = '未知';
        $tmpRegionList[] = $tmp;

        $regionList = ActPlatMis_Afx_Util::chartMap($tmpRegionList, 'areaName');

        //发展趋势
        $trendList = $dayAgents = $monthAgents = [];

        $getMonthNum = ActPlatMis_Afx_Util::getMonthNum($sTime, $eTime);
        $timeFormat = $getMonthNum>=6 ? '%Y-%m' :'%Y-%m-%d';
        //发展趋势天
        $utConds = [
            'app_id='.$appId,
            'create_time>='.$sTime,
            'create_time<='.$eTime,
        ];
        $utFields = [
            'FROM_UNIXTIME(create_time,"'.$timeFormat.'") as day',
            'count(uid) as count',
            'role_id as roleId'
        ];
        $dayAgents = $this->objUser->getList($utConds, $utFields, '', 0, 500, "FROM_UNIXTIME(create_time,'".$timeFormat."'), role_id");
        if($dayAgents){
            $trendList = $tmp = [];
            foreach($dayAgents as $info){
                $tmp[$info['day']][$info['roleId']] = $info['count'];
            }
            foreach($tmp as $key=>$roleList){
                $trendList['product'][] = $key;
                foreach(Service_Data_Afx_AFXUser::ROLE_AGENT_MAP as $roleId=>$role){
                    $trendList[$role][] = intval($roleList[$roleId]);
                }
            }
        }

        $arrOutput['total'] = $total;
        $arrOutput['list']['regionList'] = $regionList;
        $arrOutput['list']['trendList'] = $trendList;

        return $arrOutput;
    }
}
