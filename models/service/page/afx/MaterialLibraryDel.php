<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 **/
class Service_Page_Afx_MaterialLibraryDel
{
    public function __construct()
    {
        $this->objDsAFXMaterial = new Service_Data_Afx_AFXMaterial();
    }

    public function execute($arrInput)
    {
        $founder = (string)$arrInput['founder'];
        $id = (int)$arrInput['id'];

        if(empty($id)){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, 'id错误');
        }

        $ret = $this->objDsAFXMaterial->deleteById($id);
        if ($ret === false) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '素材删除失败');
        }

        //记录日志
        ActPlatMis_Afx_Log::doInvalid(
            ActPlatMis_Afx_Log::OBJ_TYPE_MATERIAL,
            $id,
            $founder,
            []
        );

        return ['affectedRows' => $ret];
    }
}
