<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 **/
class Service_Page_Afx_BillMailTaskCreate
{
    public function __construct()
    {
        $this->_uploadFile = 'file';
        $this->objSendMailTask = new Service_Data_Afx_AFXSendMailTask();
    }

    public function execute($arrInput)
    {
        $founderRole = $arrInput['founderRole'] ? $arrInput['founderRole'] : [];
        $founder = (string)$arrInput['founder'];
        $taskName = (string)$arrInput['taskName'];
        $arrOutPut = [];
        if( empty($taskName) ){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '没有输入任务名称');
        }
        if(!is_uploaded_file($_FILES[$this->_uploadFile]['tmp_name'])){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR, '没有上传文件');
        }

        //文件上传至
        $filename = $_FILES[$this->_uploadFile]["name"];
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        if ($extension!='xlsx') {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ERROR_UPLOAD_ERR_NO_FILE);
        }
        $sTime = time();
        $rand = rand();
        $strObject = md5($_FILES[$filename]["name"] . $sTime . $rand);
        $url = 'afxmisSendMail/' . $strObject;
        $fileRet = Hk_Service_Tcos::uploadLocalFile('toufang', $_FILES[$this->_uploadFile]['tmp_name'], $extension, $url, 1);
        if (!$fileRet) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ERROR_UPLOAD_ERR_NO_FILE);
        }
        $url .= '.' . $extension;

        $arrConds[] = 'name like "%'.$taskName.'%"';
        $taskCnt = $this->objSendMailTask->getCnt($arrConds);
        if($taskCnt>0){
            $num = $taskCnt+1;
            $taskName = $taskName.'_'.$num;
        }

        // 保存的内容
        $arrFields = array(
            'name'          => $taskName,
            'createUser'    => $founder,
            'file'          => $url,
            'operationUser' => '',
            'bindTemplate'  => 0,
            'mailFormat'    => [],
            'sendSucNum'    => 0,
            'sendErrNum'    => 0,
            'status'        => Service_Data_Afx_AFXSendMailTask::STATUS_TODO,
        );
        $newTaskId = $this->objSendMailTask->add($arrFields);
        if(empty($newTaskId) || $newTaskId===false){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::DB_ERROR, '任务保存失败');
        }

        //记录日志
        ActPlatMis_Afx_Log::doAdd(
            ActPlatMis_Afx_Log::OBJ_TYPE_SEND_MAIL_TASK,
            $newTaskId,
            $founder,
            $arrFields
        );

        return $arrOutPut;
    }
}
