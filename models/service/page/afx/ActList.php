<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2019-08-26 17:57:41
 * @brief       机构列表
 **/
class Service_Page_Afx_ActList
{
    public function __construct()
    {
        $this->objDsAFXApp = new Service_Data_Afx_AFXApp();
        $this->objAct = new Service_Data_AFX_AFXAct();
        $this->objDsAFXMaterial = new Service_Data_Afx_AFXMaterial();
    }

    public function execute($arrInput)
    {
//        $founderRole = $arrInput['founderRole'] ?: [];
//        $appId = $arrInput['appId'] ? strval($arrInput['appId']) : "";
        $promoteBusiness = (int)$arrInput['promoteBusiness'] ?: 0;
        $actId = (int)$arrInput['actId'] ?: 0;
        $actName = (string)$arrInput['actName'] ?: '';
        $urlKeywords = (string)$arrInput['urlKeywords'] ?: '';
        $status = (int)$arrInput['status'] ?: Service_Data_AFX_AFXAct::STATUS_NOT_ALL;
        $sTime = (int)$arrInput['sTime'] ?: 0;
        $eTime = (int)$arrInput['eTime'] ?: 0;
        $pn = (int)$arrInput['pn'] ?: 1;
        $rn = (int)$arrInput['rn'] ?: 10;
        $type = (int)$arrInput['type'] ?: 1;
        $offset = intval($rn*($pn-1));

        // 返回值
        $arrOutput = [
            'pn'    => $pn,
            'total' => 0,
            'list'  => [],
        ];

        //获取当前用户下面的角色列表
//        $appRole = $founderRole['dictAuthFormat'][ActPlatMis_Const_Afx_Const::USER_DATA_APPID_ROLE_KEY];
//        $appAuthMap = array_flip(ActPlatMis_Const_Afx_Const::$appAuthMap);
//        $appIds = [];
//        foreach ($appRole as $appAuthId) {
//            if(isset($appAuthMap[$appAuthId])) {
//                $appIds[] = $appAuthMap[$appAuthId];
//            }
//        }

        //type为2检索全部的活动
        //拼装查询条件
//        if ($type == 1){
//            if (!empty($appId)) {
//                $arrConds[] = sprintf('app_id IN (%s)', $appId);
//            }
//        }else {
//            //测试管理员可以看到全部应用方的活动
//            if (!in_array(ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_TEST_ADMIN, $founderRole['dictAuthFormat'][ActPlatMis_Const_Afx_Const::USER_DATA_ROLE_KEY]) && !empty($appIds)) {
//                $arrConds[] = sprintf("app_id in (%s)", implode(",", $appIds));
//            }
//            $offset = 0;
//            $status= Service_Data_AFX_AFXAct::STATUS_NOT_ALL;
//        }

        // 获取用户权限信息
        $reqParam = [
            "uname" => $arrInput['founder'],
            "systemId"  => ActPlatMis_Const_Auth_Conf::getSysID(),
            "frontRouterUrl" => $arrInput['frontRouterUrl'],
        ];
        $dataAuthInfo = $this->getDataAuthInfo($reqParam);
        // 处理权限信息，获取unameList
        $unameList = [];
        if ($dataAuthInfo && isset($dataAuthInfo['unameList']) && is_array($dataAuthInfo['unameList'])) {
            $unameList = $dataAuthInfo['unameList'];
        }

        if ($promoteBusiness) {
            $arrConds['promoteBusiness'] = $promoteBusiness;
        }

        if ($arrInput['promoteLv1'] != 0) {
            $arrConds['promoteLv1'] = $arrInput['promoteLv1'];
        }

//        if ($arrInput['tag'] != 0) {
//            $arrConds['tag'] = $arrInput['tag'];
//        }

        if ($arrInput['channelLabel'] != 0) {
            $arrConds[] = 'channelLabel like "%,' . $arrInput['channelLabel'] . ',%" ';
        }

        if($actId){
            $arrConds['actId'] = $actId;
        }
        if($actName){
            $arrConds[] = 'name like "%'.$actName.'%" ';
        }

        if($urlKeywords){
            $arrConds[] = 'url like "%'.$urlKeywords.'%" ';
        }
        if($status){
            switch($status){
                case Service_Data_AFX_AFXAct::STATUS_NOT_STARTED:
                    $arrConds[] = 'start_time >'.time();
                    break;
                case Service_Data_AFX_AFXAct::STATUS_STARTED:
                    $arrConds[] = 'start_time <='.time();
                    $arrConds[] = 'end_time >'.time();
                    break;
                case Service_Data_AFX_AFXAct::STATUS_END:
                    $arrConds[] = 'end_time <='.time();
                    break;
            }
        }else{
            if($sTime){
                $arrConds[] = 'startTime <='.$sTime;
            }
            if($eTime){
                $arrConds[] = 'endTime >='.$eTime;
            }
        }

        // 添加操作人条件
        if (!empty($unameList)) {
            $arrConds[] = 'operator in ("' . implode('","', $unameList) . '")';
        }

        // 查询数量
        $total = $this->objAct->getCnt($arrConds);
        if (empty($total)) {
            return $arrOutput;
        }
        if ($type == 2){
            $rn = $total;
        }
        //查询列表
        $order = ' id desc ';
        $list = $this->objAct->getList($arrConds, [], $order, $offset, $rn);
        if (empty($list)) {
            return $arrOutput;
        }

        //app列表
        $appList = $appChannelList = [];
        $arrConds  = null;
        $arrFields = ['id', 'name', 'saleChannelId'];
        $ret       = $this->objDsAFXApp->getListByConds($arrConds, $arrFields);
        if($ret){
            $appList = array_column($ret,'name','id');
            $appChannelList = array_column($ret,'saleChannelId','id');
        }

        //获取actId并获取素材库数量
        $actIds = $mList = $mCountList = [];
        foreach($list as $val){
            $actIds[] = $val['actId'];
        }
        $mConds[] = 'act_id in ('.implode(',',$actIds).')';
        $mFields = [
            'actId',
            'count(id) as count'
        ];
        $mList = $this->objDsAFXMaterial->getList($mConds, $mFields, $order, 0, 100, 'act_id');
        if($mList){
            foreach($mList as $val){
                $mCountList[$val['actId']] = $val['count'];
            }
        }

        $actList = [];
        $newMaterialAuthApp = ActPlatMis_Const_Afx_Const::getMaterialAuthApp();
        foreach($list as $val){
            $tmp = [
                'promoteBusiness' => $val['promoteBusiness'],
                'promoteGradeDepts' => $val['promoteGradeDepts'],
                'promoteLv1New' => $val['promoteLv1New'],
            ];
            if(!empty($val['url'])){
                $url = strpos($val['url'],'?') === false ? $val['url'].'?saleChannelId='.$appChannelList[$val['appId']] : $val['url'].'&saleChannelId='.$appChannelList[$val['appId']];
            }
            if($val['startTime']>time()){
                $tmp['status'] = Service_Data_AFX_AFXAct::$STATUS[Service_Data_AFX_AFXAct::STATUS_NOT_STARTED];
            }elseif($val['startTime']<=time() && $val['endTime']>time()){
                $tmp['status'] = Service_Data_AFX_AFXAct::$STATUS[Service_Data_AFX_AFXAct::STATUS_STARTED];
            }elseif($val['endTime']<=time()){
                $tmp['status'] = Service_Data_AFX_AFXAct::$STATUS[Service_Data_AFX_AFXAct::STATUS_END];
            }
            $tmp['showImportOrder'] = $this->showImportOrder((int)$val['appId'], (int)$val['apiInfo']['apiSource']);
            $tmp['picUrl'] = $val['picId'] ? Hk_Util_Image::getImgUrlBySrc($val['picId']) : '';
            $tmp['picBindStatus'] = $mCountList[$val['actId']]>0 ? '已绑定' : '未绑定';
            $tmp['targetUrl'] = (string)$url;
            $tmp['actId'] = (int)$val['actId'];
            $tmp['appId'] = (int)$val['appId'];
            $tmp['newMaterial'] = in_array($tmp['appId'], $newMaterialAuthApp) ? 1 : 0;
            $tmp['actName'] = (string)$val['name'];
            $tmp['startTime'] = $val['startTime']*1000;
            $tmp['endTime'] = $val['endTime']*1000;
            $tmp['saleNum'] = (int)$val['saleNum'];
            $tmp['refundNum'] = (int)$val['refundNum'];
            $tmp['typeName'] = (string)Oplib_Const_AFX::$actTypeMap[$val['type']];
            $tmp['type'] = (string)$val['type'];
            $tmp['appName'] = (string)$appList[$val['appId']];
            $tmp['url'] = (string)$val['url'];
            $tmp['apiSource'] = (int)$val['apiInfo']['apiSource'];
            $tmp['apiPValue'] = (int)$val['apiInfo']['apiPValue'];
            $tmp['mark'] = (string)$val['mark'];
            $tmp['businessLine'] = (int)$val['businessLine'];
            $tmp['promoteLv1'] = (string)$val['promoteLv1'];
//            $tmp['tag'] = (int)$val['tag'];
            $tmp['channelLabels'] = trim($val['channelLabels'], ','); // 渠道标签
            $actList[] = $tmp;
        }

        $arrOutput['total'] = $total;
        $arrOutput['list'] = $actList;

        return $arrOutput;
    }

    private function showImportOrder($appId, $apiSource)
    {
        $showAppIds = [ActPlatMis_Const_Afx_Const::APP_ID_K12LX, ActPlatMis_Const_Afx_Const::APP_ID_K12DJK, ActPlatMis_Const_Afx_Const::APP_ID_K12DG, ActPlatMis_Const_Afx_Const::APP_ID_K12SQ, ActPlatMis_Const_Afx_Const::APP_ID_TEST, ActPlatMis_Const_Afx_Const::APP_ID_TEST_HUIGUI];
        if (in_array($appId, $showAppIds) && $apiSource == Oplib_Const_AFX::API_SOURCE_TF) {
            return true;
        }
        return false;
    }

    private function getDataAuthInfo($params){
        // ral
        $arrHeader = [
            'pathinfo' => "/qdmis/goauth/authuser/getpermissionunames",
            'querystring' => http_build_query($params),
        ];
        $result = Qdlib_Util_Tool::innerRequest("go-qdmis",'GET',$arrHeader, $params,true);
        if ($result === false) {
            BD_log::notice("获取用户权限失败");
            return false;
        }

        return $result;
    }
}
