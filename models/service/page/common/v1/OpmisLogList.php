<?php
/**
 * @name Service_Page_Sample
 * @desc sample page service, 和action对应，组织页面逻辑，组合调用data service
 * <AUTHOR>
 */
class Service_Page_Common_V1_OpmisLogList {
    private  $objLog = null;

    public function __construct(){
        $this->objLog = new Service_Data_OperateBackLog();
    }

    public function execute($arrInput){
        $userInfo  = $arrInput['userInfo'];
        $mId       = intval($arrInput['mId']);
        $rn        = intval($arrInput['rn']);
        $pn        = intval($arrInput['pn']);
        $operatopList = $arrInput['operatopList'];
        $arrOutput = array('list'=>array(),'total'=>0);
        if (0 >= $mId) {
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'mId不能为空');
        }

        if ($mId == ActPlatMis_Const_BackLogMid::OPERATION_QQGROUP) {
            $logList = $this->objLog->getListBymIdAndType($mId, [Service_Data_OperateBackLog::TYPE_EDIT], [], 'create_time', 'desc', $pn*$rn, $rn);
        } else {
            $logList = $this->objLog->getListBymId($mId,array(),'create_time','desc',$pn*$rn,$rn);
        }

        foreach($logList as $v){
            $one = array(
                'createTime' => date('Y-m-d H:i:s',$v['createTime']),
                'id'         => $v['adId'],
                'operator'   => $operatopList[$v['operatorUid']],
                'operatorType' => Service_Data_OperateBackLog::$TYPE_ARRAY[$v['type']],
            );
            if ($v['opUname']) {
                $one['operator'] = $v['opUname'];
            }
            $arrOutput['list'][] = $one;
        }
        if ($mId == ActPlatMis_Const_BackLogMid::OPERATION_QQGROUP) {
            $total = $this->objLog->getCntBymIdAndType($mId,[Service_Data_OperateBackLog::TYPE_EDIT]);
        } else {
            $total = $this->objLog->getCntBymId($mId);
        }

        $arrOutput['total'] = intval($total);
        return $arrOutput;
    }
}
