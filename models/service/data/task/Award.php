<?php
/**
 * 奖励表数据操作
 * <AUTHOR>
 * @date 2019-12-25
 */
class Service_Data_Task_Award
{
    private $daoAward = null;

    const ALL_FIELDS = 'id,uid,masterTaskId,slaveTaskId,awardType,progress,createTime,ext,prizeStatus,prizeType';


    public function __construct()
    {
        $this->daoAward = new Dao_Task_Award();
    }

    //获取信息
    public function getListByConds($arrConds, $arrParams = [], $order = '', $by = '', $offset = 0, $limit = 0)
    {
        $arrFields = [];
        $allFields = explode(',', self::ALL_FIELDS);
        if (empty($arrParams)) {
            $arrFields = $allFields;
        } else {
            foreach ($arrParams as $val) {
                if ( ! in_array($val, $allFields)) {
                    continue;
                }
                $arrFields[] = $val;
            }
        }
        $orderBy = '';
        if ( ! empty($order)) {
            $orderBy .= 'order by '.$order.' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
        if ($offset >= 0 && $limit > 0) {
            $orderBy .= " limit $offset,$limit";
        } else if ($limit > 0) {
            $orderBy .= " limit $limit";
        }
        $arrAppends = ( ! empty($orderBy)) ? [$orderBy] : null;
        $ret        = $this->daoAward->getListByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }


    //获取总数
    public function getFlowCnt($arrConds = null)
    {
        $res = $this->daoAward->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning(
                'oplib',
                'Service_Data_Lottery_Flow',
                'getActCnt',
                '查询数据库总数失败',
                json_encode(['conds' => $arrConds])
            );
        }
        return $res;
    }
}