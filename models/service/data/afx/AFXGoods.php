<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 **/
class Service_Data_Afx_AFXGoods
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_Afx_AFXGoods::ARR_ALL_FIELDS;

    private $_objDaoGoods;

    public function __construct()
    {
        $this->_objDaoGoods = new Dao_Afx_AFXGoods();
    }

    //新增
    public function add($arrParams) {
        $arrFields = array(
            'actId'     => isset($arrParams['actId']) ? intval($arrParams['actId']) : 0,
            'version'   => isset($arrParams['version']) ? intval($arrParams['version']) : 0,
            'skuId' => isset($arrParams['skuId']) ? intval($arrParams['skuId']) : 0,
            'skuName'      => isset($arrParams['skuName']) ? strval($arrParams['skuName']) : '',
            'createTime'=> time(),
        );

        $result = $this->_objDaoGoods->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoGoods->getInsertId();
        return $id;
    }

    //通过ids批量赐予活动
    public function setActIdByIds($ids, $actId) {
        $arrConds = ['id in ('.implode(',', $ids).')'];
        $arrFields = ['actId'=>$actId];
        $result = $this->_objDaoGoods->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //批量增加
    public function batchInsertRecords($arrParams)
    {
        $time = time();
        $sql = 'insert into tblAFXGoods (`act_id`,`sku_id`,`sku_name`,`create_time`) values ';
        foreach($arrParams as $val){
            $sql .= '("' . $val['actId'] . '","' . $val['skuId'] . '","' . $val['skuName'] . '","' . $time . '"),';
        }
        $sql = rtrim($sql,',');
        $result = $this->_objDaoGoods->query($sql);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode($arrParams));
            return false;
        }
        $id = $this->_objDaoGoods->getInsertId();
        return $id;
	}

    //真删除
    public function deleteById($id)
    {
        $arrConds[] = 'id in('.implode(',', $id).')';
        $result = $this->_objDaoGoods->deleteByConds($arrConds);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->_objDaoGoods->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoGoods->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }
}
