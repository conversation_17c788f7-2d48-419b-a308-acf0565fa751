<?php
/**
 * Created by Growth AutoCode.
 * User: liying<PERSON>@zuoyebang.com
 * Date: 2021/07/14
 * Time: 08:11
 */
class Service_Data_AFX_AFXActSaleDetailAgent
{
    const ALL_FIELDS = 'id,actId,agentId,saleDate,saleNum,saleTotal,createTime,updateTime';
    const DELETE_DEL = 1;

    protected $objDaoAFXActSaleDetailAgent;

    public function __construct()
    {
        $this->objDaoAFXActSaleDetailAgent = new Dao_AFX_AFXActSaleDetailAgent();
    }

    //新增
    public function addAFXActSaleDetailAgent($arrParams) {
        $arrFields = array(
            'id'         => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'actId'      => isset($arrParams['actId']) ? intval($arrParams['actId']) : 0,
            'agentId'    => isset($arrParams['agentId']) ? intval($arrParams['agentId']) : 0,
            'saleDate'   => isset($arrParams['saleDate']) ? intval($arrParams['saleDate']) : 0,
            'saleNum'    => isset($arrParams['saleNum']) ? intval($arrParams['saleNum']) : 0,
            'saleTotal'  => isset($arrParams['saleTotal']) ? intval($arrParams['saleTotal']) : 0,
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,

        );
        $result = $this->objDaoAFXActSaleDetailAgent->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AFXActSaleDetailAgent', 'addAFXActSaleDetailAgent', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoAFXActSaleDetailAgent->getInsertId();
        return $id;
    }

    //编辑
    public function updateAFXActSaleDetailAgent($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoAFXActSaleDetailAgent->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AFXActSaleDetailAgent', 'updateAFXActSaleDetailAgent', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateAFXActSaleDetailAgentById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateAFXActSaleDetailAgent($arrConds, $arrFields);
    }

    //软删除
    public function deleteAFXActSaleDetailAgent($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateAFXActSaleDetailAgent($arrConds, $arrFields);
    }

    //获取信息
    public function getAFXActSaleDetailAgentInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoAFXActSaleDetailAgent->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AFXActSaleDetailAgent', 'getAFXActSaleDetailAgentInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getAFXActSaleDetailAgentList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoAFXActSaleDetailAgent->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AFXActSaleDetailAgent', 'getAFXActSaleDetailAgentList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getAFXActSaleDetailAgentTotal($arrConds = null)
    {
        $res = $this->objDaoAFXActSaleDetailAgent->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AFXActSaleDetailAgent', 'getAFXActSaleDetailAgentTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    //新增或更新数据
    public function addOrUpdateSaleDetail($data){
        if (empty($data)){
            return true;
        }
        $time = strtotime(date('Y-m-d 00:00:00'));
        foreach ($data as $item){
            $conds = ['actId'=> $item['actId'], 'agentId'=> $item['agentId'], 'saleDate' => $time];
            $id = $this->objDaoAFXActSaleDetailAgent->getRecordByConds($conds, ['id']);
            if ($id){
                $up = [
                    'saleNum' => $item['saleNum'],
                    'saleTotal'     => $item['saleTotal'],
                    'updateTime' => time(),
                ];
                $this->updateAFXActSaleDetailAgentById($id['id'], $up);
                continue;
            }
            $in = [
                'actId'         => $item['actId'],
                'agentId'       => $item['agentId'],
                'saleDate'      => $time,
                'saleNum'       => $item['saleNum'],
                'saleTotal'     => $item['saleTotal'],
                'createTime'    => time(),
                'updateTime'    => time(),
            ];
            $this->addAFXActSaleDetailAgent($in);
        }

        return;
    }
}