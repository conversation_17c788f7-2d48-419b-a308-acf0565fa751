<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 **/
class Service_Data_Afx_AFXAct
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_Afx_AFXAct::ARR_ALL_FIELDS;

    //状态
    const STATUS_NOT_ALL = 0;//全部
    const STATUS_NOT_STARTED = 1;//未开始
    const STATUS_STARTED = 2;//已开始
    const STATUS_END = 3;//已结束

    public static $STATUS = array(
        self::STATUS_NOT_ALL => '全部',
        self::STATUS_NOT_STARTED => '未开始',
        self::STATUS_STARTED => '已开始',
        self::STATUS_END => '已结束',
    );
    private $_objDaoAct;

    public function __construct()
    {
        $this->_objDaoAct = new Dao_Afx_AFXAct();
    }

    //新增
    public function add($arrParams) {
        $arrFields = array(
            'appId'     => isset($arrParams['appId']) ? intval($arrParams['appId']) : 0,
            'saleChannelId' => isset($arrParams['saleChannelId']) ? intval($arrParams['saleChannelId']) : 0,
            'name'      => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'businessLine' => isset($arrParams['businessLine']) ? intval($arrParams['businessLine']) : 0,
            'type'      => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'promoteLv1'      => $arrParams['promoteLv1'] ?? '',
//            'tag'      => isset($arrParams['tag']) ? intval($arrParams['tag']) : 0,
            'channelLabels'=> isset($arrParams['channelLabels']) ? strval($arrParams['channelLabels']) : '',
            'url'       => isset($arrParams['url']) ? strval($arrParams['url']) : '',
            'mark'      => isset($arrParams['mark']) ? strval($arrParams['mark']) : '',
            'startTime' => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'endTime'   => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'ruleType'  => isset($arrParams['ruleType']) ? intval($arrParams['ruleType']) : 0,
            'createTime'=> time(),
            'updateTime'=> time(),
            'apiInfo'   => isset($arrParams['apiInfo'])?$arrParams['apiInfo']:'[]',
            'extData'   => isset($arrParams['extData'])?$arrParams['extData']:'[]',
            'promoteGradeDepts' => isset($arrParams['promoteGradeDepts']) ? $arrParams['promoteGradeDepts'] : '',
            'promoteLv1New' => isset($arrParams['promoteLv1New']) ? $arrParams['promoteLv1New'] : '',
            'promoteBusiness' => isset($arrParams['promoteBusiness']) ? $arrParams['promoteBusiness'] : '',
        );

        $result = $this->_objDaoAct->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoAct->getInsertId();
        return $id;
    }

    //通过id编辑
    public function updateById($id, $arrFields) {
        $arrConds = ['id' => $id];
        $result = $this->_objDaoAct->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //软删除(终止)
    public function deleteById($id)
    {
        $arrConds = ['id' => $id];
        $arrFields = ['endTime' => time(), 'updateTime'=>time()];
        $result = $this->_objDaoAct->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->_objDaoAct->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoAct->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }

    /**
     * 将起止时间转换为状态
     * @param $startTime
     * @param $endTime
     * @return int
     */
    public function getStatus($startTime, $endTime)
    {
        $now = time();
        if ($startTime > $now) {
            return self::STATUS_NOT_STARTED;
        } elseif ($startTime <= $now && $endTime > $now) {
            return self::STATUS_STARTED;
        } elseif ($endTime <= $now) {
            return self::STATUS_END;
        }
    }

    /**
     * 根据ID批量获取活动列表
     * @param $ids
     * @return array|false
     */
    public function getListByIds($ids) {
        $acts = $this->getByIds($ids) ;
        foreach ($acts as &$act) {
            $act['status'] = $this->getStatus($act['startTime'], $act['endTime']);
            $act['statusName'] = self::$STATUS[$act['status']];
        }
        return $acts;
    }

    public function getByIds($ids, $arrFields = array()) {
        $arrConds = [ "id in ('" . implode("','", $ids) . "')"] ;
        return $this->getList($arrConds, $arrFields);
    }

    //获取信息
    public function getInfoById($id, $arrFields = array())
    {
        if(is_array($id)){
            $arrConds[] = 'id in ('.implode(',', $id).')';
        }else{
            $arrConds['actId'] = $id;
        }

        if (empty($arrFields)) {
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $result = $this->_objDaoAct->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('actplatmis', __CLASS__, __FUNCTION__, '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        // 增加状态信息
        $result['status'] = $this->getStatus($result['startTime'], $result['endTime']);
        $result['statusName'] = self::$STATUS[$result['status']];
        $result['channelLabels'] = trim($result['channelLabels'], ','); // 渠道标签
        return $result;
    }
}
