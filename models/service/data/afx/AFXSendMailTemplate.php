<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 **/
class Service_Data_Afx_AFXSendMailTemplate
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_Afx_AFXSendMailTemplate::ARR_ALL_FIELDS;

    //状态
    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 2;

    public static $Status = array(
        self::STATUS_ENABLE => '生效',
        self::STATUS_DISABLE => '失效',
    );
    private $_objDaoTemplate;

    public function __construct()
    {
        $this->_objDaoTemplate = new Dao_Afx_AFXSendMailTemplate();
    }

    //通过id编辑
    public function updateById($id, $arrFields) {
        $arrConds = ['id' => $id];
        $result = $this->_objDaoTemplate->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->_objDaoTemplate->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoTemplate->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }

    //获取信息
    public function getInfoById($id, $arrFields = array())
    {
        if(is_array($id)){
            $arrConds = ['id in ('.implode(',', $id).')'];
        }else{
            $arrConds = ['templateId' => $id];
        }

        if (empty($arrFields)) {
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $result = $this->_objDaoTemplate->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('actplatmis', __CLASS__, __FUNCTION__, '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
}
