<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 * @brief       聚合结果表
 **/
class Service_Data_Afx_AFXDorisLeadsDetail
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_Afx_AFXDorisLeadsDetail::ARR_ALL_FIELDS;

    private $_objDaoDorisLeadsDetail;

    public function __construct()
    {
        $this->_objDaoDorisLeadsDetail = new Dao_Afx_AFXDorisLeadsDetail();
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $group = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $group ){
            $strAppend .= 'group by '. $group . ' ';
        }
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoDorisLeadsDetail->getListByConds($arrConds,$arrFields,$arrAppends);
        return $ret;
    }

    //获取总数
    public function getGroupByCnt($arrConds = '', $order = '', $group = '')
    {
        $strAppend = '';
        if( $group ){
            $strAppend .= 'group by '. $group . ' ';
        }
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $res = $this->_objDaoDorisLeadsDetail->getGroupByCntByConds($arrConds, $arrAppends);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->_objDaoDorisLeadsDetail->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}
