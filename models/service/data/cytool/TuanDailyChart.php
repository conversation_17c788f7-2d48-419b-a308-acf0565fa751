<?php
/**
 * 团日报数据DS层
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/11/10 19:14
 */
class Service_Data_Cytool_TuanDailyChart
{
    private $objDaoTuanDailyChart;

    public function __construct()
    {
        $this->objDaoTuanDailyChart = new Dao_Cytool_TuanDailyChart();
    }

    /**
     * 获取日报数据
     * @param $conds
     * @param int $pageNo
     * @param int $pageSize
     * @return array|false
     */
    public function getChartData($conds, $pageNo = 1, $pageSize = 10)
    {
        $arrAppend = [
            sprintf("limit %d, $pageSize", ($pageNo - 1) * $pageSize),
        ];

        $ret = $this->objDaoTuanDailyChart->getListByConds($conds, Dao_Cytool_TuanDailyChart::ARR_ALL_FIELDS, null, $arrAppend);
        if (false === $ret) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, "Error:[DB error]");
            return [];
        }

        return $ret;
    }

    /**
     * 插入日报数据
     * @param $arrFields
     * @return bool|int
     */
    public function addChartData($arrFields)
    {
        if (!$arrFields['createTime']) {
            $arrFields['createTime'] = ActPlatMis_Common_Tool::getCurrentTimestamp();
        }

        $result = $this->objDaoTuanDailyChart->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, '数据库插入失败', json_encode($arrFields));
            return false;
        }

        $id = $this->objDaoTuanDailyChart->getInsertId();
        return $id;
    }

    /**
     * 更新日报数据
     * @param $arrConds
     * @param $arrFields
     * @param null $arrOptions
     * @param null $arrAppends
     * @return bool
     */
    public function updateByConds($arrConds, $arrFields, $arrOptions = null, $arrAppends = null)
    {
        if (!$arrConds || !$arrFields) {
            return false;
        }

        if (!$arrFields['updateTime']) {
            $arrFields['updateTime'] = ActPlatMis_Common_Tool::getCurrentTimestamp();
        }

        $ret = $this->objDaoTuanDailyChart->updateByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
        if (false === $ret) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'DB:[update error],Detail:[' . json_encode([$arrConds, $arrFields, $arrOptions, $arrAppends], JSON_UNESCAPED_UNICODE) . ']');
            return false;
        }
        return $ret;
    }
}
