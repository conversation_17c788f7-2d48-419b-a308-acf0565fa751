<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   CouponActivity.php
 * <AUTHOR>
 * @date   2020/5/20 12:05
 * @brief
 **/
class Service_Data_Actcoupon_CouponActivity
{
    use ActPlatMis_DsTrait;
    const  ARR_ALL_FIELDS = Dao_Actcoupon_CouponActivity::ARR_ALL_FIELDS;
    //状态
    const STATUS_INIT    = 1; // 新建状态
    const STATUS_PUBLISH = 2; // 已发布状态
    const STATUS_CLOSE   = 3; // 已关闭状态

    // 扩展状态，下面的状态在不能设置到status中
    const EXTEND_STATUS_ONGOING = 21; // 进行中：活动状态为已发布，活动时间正在进行中
    const EXTEND_STATUS_END     = 22; // 已结束：活动状态为已发布，活动时间已结束

    // 活动性质
    const NATURE_PUBLIC_RELEASE = 1; // 对外发布
    const NATURE_INNER_TEST     = 2; // 内部测试

    const SUPPORT_CHANNEL_IDS = [9]; //支持的渠道ids，既活动投放途径ids

    const STATUS_MAP = [
        self::STATUS_INIT    => '新建',
        self::STATUS_PUBLISH => '已发布',
        self::STATUS_CLOSE   => '已关闭',
    ];

    const EXTEND_STATUS_MAP = [
        self::EXTEND_STATUS_ONGOING => '进行中',
        self::EXTEND_STATUS_END     => '已结束',
    ];

    const NATURE_MAP = [
        self::NATURE_PUBLIC_RELEASE => '对外发布',
        self::NATURE_INNER_TEST     => '内部测试',
    ];

    private $objDaoActivity;

    public function __construct()
    {
        $this->objDaoActivity = new Dao_Actcoupon_CouponActivity();
        $this->initializeDao($this->objDaoActivity);
    }

    /**
     * @brief  获取状态描述
     *         如果状态是self::STATUS_PUBLISH并且startTime<=stopTime时，
     *         则会进一步根据startTime, stopTime进行再次判断
     * <AUTHOR>
     * @date   2020/5/23 10:42
     * @param int $status    活动状态
     * @param int $startTime 活动开始时间
     * @param int $stopTime  活动结束时间
     * @return mixed
     */
    public static function getStatusDescribe($status, $startTime, $stopTime)
    {
        if (self::STATUS_PUBLISH == $status && $startTime <= $stopTime) {
            $nowTime = time();
            if ($stopTime < $nowTime) {
                return self::EXTEND_STATUS_MAP[self::EXTEND_STATUS_END];
            } elseif ($startTime > $nowTime) {
                return self::STATUS_MAP[self::STATUS_PUBLISH];
            } else {
                return self::EXTEND_STATUS_MAP[self::EXTEND_STATUS_ONGOING];
            }
        }
        return self::STATUS_MAP[$status] ?? self::EXTEND_STATUS_MAP[$status] ?? $status;
    }

    public function getListByConds($arrConds, $arrFields = self::ARR_ALL_FIELDS, $arrOptions = null, $arrAppends = null, $strIndex = null)
    {
        return $this->objDaoActivity->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function getCntByConds($arrConds)
    {
        return $this->objDaoActivity->getCntByConds($arrConds);
    }

    public function getRecordByConds($arrConds, $arrFields = self::ARR_ALL_FIELDS, $arrOptions = null, $arrAppends = null, $strIndex = null)
    {
        return $this->objDaoActivity->getRecordByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function updateByConds($arrConds, $arrFields, $arrOptions = null, $arrAppends = null)
    {
        self::formatFields($arrFields);
        return $this->objDaoActivity->updateByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
    }

    public function insertRecords($arrFields, $options = null, $onDup = null)
    {
        self::formatFields($arrFields);
        if (!isset($arrFields['createTime']) || isset($arrFields['create_time'])) {
            $arrFields['createTime'] = time();
        }
        if (!isset($arrFields['userRange']) || isset($arrFields['user_range'])) {
            $arrFields['userRange'] = '{}';
        }
        if (!isset($arrFields['couponItemIds']) || isset($arrFields['coupon_item_ids'])) {
            $arrFields['couponItemIds'] = '{}';
        }
        if (!isset($arrFields['countRule']) || isset($arrFields['count_rule'])) {
            $arrFields['countRule'] = '{}';
        }
        if (!isset($arrFields['ext']) || isset($arrFields['ext'])) {
            $arrFields['ext'] = '{}';
        }

        $ret = $this->objDaoActivity->insertRecords($arrFields, $options, $onDup);
        if (false === $ret) {
            return $ret;
        }
        return $this->objDaoActivity->getInsertId();
    }

    public function getAffectedRows()
    {
        return $this->objDaoActivity->getAffectedRows();
    }


    private static function formatFields(&$arrFields)
    {
        if (!is_array($arrFields)) {
            return $arrFields;
        }
        foreach ($arrFields as $k => $v) {
            if (!is_array($v)) {
                continue;
            } elseif (empty($v)) {
                $v = '{}';
            } else {
                $v = json_encode($v);
            }
            $arrFields[$k] = $v;
        }
        return $arrFields;
    }
}