<?php
/**
 * 奖品表数据操作
 * <AUTHOR>
 * @date 2019-10-22
 */
class Service_Data_Giftcard_Award
{
    private $daoAward = null;

    // 奖励类型
    const AWARD_TYPE_ENTITY = 1;
    const AWARD_TYPE_COURSE = 2;

    // 奖励领取状态

    private static $awardTypeMap = [
        self::AWARD_TYPE_ENTITY => '实物',
        self::AWARD_TYPE_COURSE => '课程',
    ];

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2019-10-22
     */
    public function __construct()
    {
        $this->daoAward = new Dao_Giftcard_Award();
    }

    /**
     * <AUTHOR>
     * @DateTime 2019-10-23
     * @param    array                 $params 奖品数据
     * @return   bool
     */
    public function createAward($params = [])
    {
        if (empty($params['awardList']) || $params['tacticsId'] <= 0) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode($params));
            return false;
        }

        if (!is_array($params['awardList'])) {
            $params['awardList'] = json_decode($params['awardList'], true);
        }

        $nowTime   = ActPlatMis_Common_Tool::getCurrentTimestamp();
        $tacticsId = (int)$params['tacticsId'];

        $values = [];
        foreach ($params['awardList'] as $award) {
            $type     = (int)$award['type'];
            $skuId    = (int)$award['skuId'];
            $count    = (int)$award['needCount'];
            $ext      = [
                'isChange'      => (int)$award['isChange'],
                'exchangeCount' => (int)$award['exchangeCount'],
                'getType'       => (int)$award['getType'],
            ];
            $ext      = json_encode($ext);
            $values[] = "({$skuId},{$tacticsId},{$type},{$count},{$nowTime},'{$ext}')";
        }

        $string = 'insert into %s (`sku_id`,`tactics_id`,`award_type`,`need_count`,`create_time`,`ext`) values %s';
        $sql = sprintf($string, $this->daoAward->getTable(), implode(',', $values));

        $bool = $this->daoAward->query($sql);
        if ($bool === false) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Data error', json_encode($values));
        }

        return $bool;
    }

    /**
     * 根据策略id获取奖励列表
     * <AUTHOR>
     * @DateTime 2019-10-23
     * @param    int                   $tacticsId 策略id
     * @param    array                 $arrFields 字段信息
     * @return   array|bool
     */
    public function getAwardListByTacticsId($tacticsId, $arrFields = [], $offset = 0, $limit = 0)
    {
        if ($tacticsId <= 0) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode(['tacticsId' => $tacticsId]));
            return false;
        }

        $arrConds = ['tacticsId' => $tacticsId];
        empty($arrFields) && $arrFields = $this->daoAward->getFields();
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $result = $this->daoAward->getListByConds($arrConds, $arrFields,NULL,$arrAppends);

        if ($result === false) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Data error', json_encode(['tacticsId' => $tacticsId]));
        }

        return $result;
    }

    //获取总数
    public function getCntByTacticsId($tacticsId)
    {
        if ($tacticsId <= 0) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode(['tacticsId' => $tacticsId]));
            return 0;
        }
        $arrConds = ['tacticsId' => $tacticsId];
        $res = $this->daoAward->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->daoAward->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 获取奖励类型
     * <AUTHOR>
     * @DateTime 2019-10-28
     * @param    int                $awardType 奖励类型
     * @return   string
     */
    public static function getAwardTypeMap($awardType)
    {
        return self::$awardTypeMap[$awardType] ?? '';
    }

    /**
     * 获取奖励信息
     * <AUTHOR>
     * @DateTime 2019-10-29
     * @param    int                $id        主键
     * @param    array              $arrFields 字段
     * @return   array|bool
     */
    public function getAwardById($id, $arrFields = [])
    {
        $arrConds = ['id' => $id];
        if ($id <= 0) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode($arrConds));
            return false;
        }

        $arrAppends = ['limit 1'];
        empty($arrFields) && $arrFields = $this->daoAward->getFields();
        $award = $this->daoAward->getRecordByConds($arrConds, $arrFields, null, $arrAppends);

        if ($award === false) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Db error', json_encode($arrConds));
        }

        return $award;
    }

    /**
     * 添加单个奖品
     */
    public function addAward($params = [])
    {
        $arrFields = [];
        $arrFields['skuId']       = (int)$params['skuId'];
        $arrFields['tacticsId']   = (int)$params['tacticsId'];
        $arrFields['awardType']  = (int)$params['awardType'];
        $arrFields['needCount'] = (int)$params['needCount'];
        $arrFields['createTime']  = ActPlatMis_Common_Tool::getCurrentTimestamp();
        $ext = [
            'isChange'      => (int)$params['isChange'],
            'exchangeCount' => (int)$params['exchangeCount'],
            'getType'       => (int)$params['getType'],
        ];
        $arrFields['ext'] = json_encode($ext);
        $result = $this->daoAward->insertRecords($arrFields);
        if ($result === true) {
            $result = $this->daoAward->getInsertId();
        } else {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Db error', json_encode($arrFields));
        }

        return $result;
    }

    /**
     * 更改奖品数据
     * @param    [type]                $id     [description]
     * @param    array                 $params [description]
     * @return   [type]                        [description]
     */
    public function updateAwardById($awardId, $awardList = [])
    {
        if ($awardId <= 0) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode(compact('awardId')));
            return false;
        }


        $arrConds = ['id' => $awardId];

        //参数格式化
        if (empty($awardList)) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, 'Param error', json_encode($awardList));
            return false;
        }

        if (!is_array($awardList)) {
            $awardList = json_decode($awardList, true);
        }

        $arrFields = [];
        $arrFields['needCount'] = (int)$awardList['needCount'];
        $ext = [
            'isChange'      => (int)$awardList['isChange'],
            'exchangeCount' => (int)$awardList['exchangeCount'],
            'getType'       => (int)$awardList['getType'],
        ];
        $arrFields['ext'] = json_encode($ext);
        $result = $this->daoAward->updateByConds($arrConds, $arrFields);
        if (false === $result) {
            Qdlib_Util_Log::warning(APP, __METHOD__, __FUNCTION__, 'Db error', json_encode(compact('id')));
        }

        return $result;
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0){
        empty($arrFields) && $arrFields = $this->daoAward->getFields();
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : null;
        $ret = $this->daoAward->getListByConds($arrConds,$arrFields,null,$arrAppends);
        return $ret;

    }
}
