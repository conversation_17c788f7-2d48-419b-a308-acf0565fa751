<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/04/01
 * Time: 03:39
 */
class Service_Data_Oms_ImportOrder
{
    const ALL_FIELDS = 'id,businessLine,shopCode,agentLvone,agentLvtwo,agentLvthree,apiParam,projectLvone,projectLvtwo,status,url,totalLine,currentLine,createTime,updateTime,lastfrom,saleChannel,instId,actId,reImportOrder,operatorName';
    const DELETE_DEL = 1;

    protected $objDaoImportOrder;

    public function __construct()
    {
        $this->objDaoImportOrder = new Dao_Oms_ImportOrder();
    }

    //新增
    public function addImportOrder($arrParams) {
        $arrFields = array(
            'id'           => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'businessLine' => isset($arrParams['businessLine']) ? intval($arrParams['businessLine']) : 0,
            'shopCode'     => isset($arrParams['shopId']) ? strval($arrParams['shopId']) : '',
            'agentLvone'   => isset($arrParams['agentLvOne']) ? intval($arrParams['agentLvOne']) : 0,
            'agentLvtwo'   => isset($arrParams['agentLvTwo']) ? intval($arrParams['agentLvTwo']) : 0,
            'agentLvthree' => isset($arrParams['agentLvThree']) ? intval($arrParams['agentLvThree']) : 0,
            'apiParam'     => isset($arrParams['param']) ? strval($arrParams['param']) : '',
            'projectLvone' => isset($arrParams['projectLvOne']) ? intval($arrParams['projectLvOne']) : 0,
            'projectLvtwo' => isset($arrParams['projectLvTwo']) ? intval($arrParams['projectLvTwo']) : 0,
            'status'       => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'url'          => isset($arrParams['fileUrl']) ? strval($arrParams['fileUrl']) : '',
            'totalLine'    => isset($arrParams['totalLine']) ? intval($arrParams['totalLine']) : 0,
            'currentLine'  => isset($arrParams['currentLine']) ? intval($arrParams['currentLine']) : 0,
            'createTime'   => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'updateTime'   => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : time(),
            'lastfrom'     => isset($arrParams['lastfrom']) ? strval($arrParams['lastfrom']) : '',
            'saleChannel'  => isset($arrParams['saleChannel']) ? strval($arrParams['saleChannel']) : '',
            'instId'       => isset($arrParams['instId']) ? intval($arrParams['instId']) : 0,
            'actId'        => isset($arrParams['actId']) ? intval($arrParams['actId']) : 0,
            'reImportOrder'=> isset($arrParams['reImportOrder']) ? intval($arrParams['reImportOrder']) : 0,
            'operatorName' => isset($arrParams['operatorName']) ? strval($arrParams['operatorName']) : '',

        );

        $result = $this->objDaoImportOrder->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'ImportOrder', 'addImportOrder', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoImportOrder->getInsertId();
        return $id;
    }

    //编辑
    public function updateImportOrder($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }

        $result = $this->objDaoImportOrder->updateByConds($arrConds, $arrFields);

        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'ImportOrder', 'updateImportOrder', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateImportOrderById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateImportOrder($arrConds, $arrFields);
    }

    //软删除
    public function deleteImportOrder($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateImportOrder($arrConds, $arrFields);
    }

    //获取信息
    public function getImportOrderInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoImportOrder->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'ImportOrder', 'getImportOrderInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getImportOrderList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoImportOrder->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'ImportOrder', 'getImportOrderList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->objDaoImportOrder->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }

    //获取总数
    public function getImportOrderTotal($arrConds = null)
    {
        $res = $this->objDaoImportOrder->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'ImportOrder', 'getImportOrderTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}