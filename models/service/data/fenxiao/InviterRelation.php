<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   InviterRelation.php
 * <AUTHOR>
 * @date   2020/1/17 10:48
 * @brief  【分销员和上级分销员关系明细】列表
 **/
class Service_Data_Fenxiao_InviterRelation
{
    use ActPlatMis_DsTrait;

    private $objDaoIR = null;

    public function __construct()
    {
        $this->objDaoIR = new Dao_Fenxiao_InviterRelation();
        $this->initializeDao($this->objDaoIR);
    }

    public function getListByConds($arrConds, $arrFields = [], $arrOptions = null, $arrAppends = null, $strIndex = null)
    {
        if (empty($arrFields)) {
            $arrFields = Dao_Fenxiao_InviterRelation::ARR_ALL_FIELDS;
        }
        return $this->objDaoIR->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function getCntByConds($arrConds)
    {
        return $this->objDaoIR->getCntByConds($arrConds);
    }
}