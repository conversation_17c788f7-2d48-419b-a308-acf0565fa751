<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   CouponActivity.php
 * <AUTHOR>
 * @date   2020/5/20 11:50
 * @brief
 **/
class Dao_Actcoupon_CouponActivity extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'name',
        'sourceId',
        'nature',
        'rule',
        'startTime',
        'stopTime',
        'channelId',
        'userRange',
        'count',
        'limitCount',
        'couponItemIds',
        'countRule',
        'limitCountRule',
        'status',
        'creator',
        'createTime',
        'publishTime',
        'closeTime',
        'ext',
    ];

    public function __construct()
    {
        $this->_dbName      = 'zyb_yk_actplat/zb_actcoupon';
        $this->_db          = null;
        $this->_logFile     = Zb_Const_Db::DBLOG_ZB;
        $this->_table       = 'tblCouponActivity';
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'name'           => 'name',
            'sourceId'       => 'source_id',
            'nature'         => 'nature',
            'rule'           => 'rule',
            'startTime'      => 'start_time',
            'stopTime'       => 'stop_time',
            'channelId'      => 'channel_id',
            'userRange'      => 'user_range',
            'count'          => 'count',
            'limitCount'     => 'limit_count',
            'couponItemIds'  => 'coupon_item_ids',
            'countRule'      => 'count_rule',
            'limitCountRule' => 'limit_count_rule',
            'status'         => 'status',
            'creator'        => 'creator',
            'createTime'     => 'create_time',
            'publishTime'    => 'publish_time',
            'closeTime'      => 'close_time',
            'ext'            => 'ext',

        );

        $this->arrTypesMap = array(
            'id'             => Hk_Service_Db::TYPE_INT,
            'name'           => Hk_Service_Db::TYPE_STR,
            'sourceId'       => Hk_Service_Db::TYPE_INT,
            'nature'         => Hk_Service_Db::TYPE_INT,
            'rule'           => Hk_Service_Db::TYPE_STR,
            'startTime'      => Hk_Service_Db::TYPE_INT,
            'stopTime'       => Hk_Service_Db::TYPE_INT,
            'channelId'      => Hk_Service_Db::TYPE_INT,
            'userRange'      => Hk_Service_Db::TYPE_JSON,
            'count'          => Hk_Service_Db::TYPE_INT,
            'limitCount'     => Hk_Service_Db::TYPE_INT,
            'couponItemIds'  => Hk_Service_Db::TYPE_JSON,
            'countRule'      => Hk_Service_Db::TYPE_JSON,
            'limitCountRule' => Hk_Service_Db::TYPE_INT,
            'status'         => Hk_Service_Db::TYPE_INT,
            'creator'        => Hk_Service_Db::TYPE_STR,
            'createTime'     => Hk_Service_Db::TYPE_INT,
            'publishTime'    => Hk_Service_Db::TYPE_INT,
            'closeTime'      => Hk_Service_Db::TYPE_INT,
            'ext'            => Hk_Service_Db::TYPE_JSON,
        );
    }
}
