<?php
/**
 * User: <EMAIL>
 */

class Dao_Afx_AFXDorisOrderTrans extends ActPlatMis_Afx_Doris
{
    const ARR_ALL_FIELDS = [
        'orderId',
        'brandId',
        'skuId',
        'appId',
        'roleId',
        'instId',
        'uid',
        'tradeTime',
        'buyTime',
        'buyerId',
        'actId',
        'refundTime',
        'amount',
        'transPv',
        'transGmv',
        'leadsId',
        'finishLessonCnt',
        'attendLessonCnt10',
        'needAttendLessonCnt',
    ];

    public function __construct()
    {
        $this->_table         = 'zhibo_laxin.dwd_fenxiao_order_trans_df';
        $this->arrFieldsMap = array(
            'orderId'               => 'order_id',
            'brandId'               => 'brand_id',
            'skuId'                 => 'sku_id',
            'appId'                 => 'app_id',
            'roleId'                => 'role_id',
            'instId'                => 'inst_id',
            'uid'                   => 'uid',
            'tradeTime'             => 'trade_time',
            'buyTime'               => 'buy_time',
            'buyerId'               => 'buyer_id',
            'actId'                 => 'act_id',
            'refundTime'            => 'refund_time',
            'amount'                => 'amount',
            'transPv'               => 'trans_pv',
            'transGmv'              => 'trans_gmv',
            'leadsId'               => 'leads_id',
            'finishLessonCnt'       => 'finish_lesson_cnt',
            'attendLessonCnt10'     => 'attend_lesson_cnt_10',
            'needAttendLessonCnt'   => 'need_attend_lesson_cnt',
        );
    }
}
