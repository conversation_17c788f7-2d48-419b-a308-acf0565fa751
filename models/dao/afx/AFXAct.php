<?php
/**
 * User: <EMAIL>
 */

class Dao_Afx_AFXAct extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'actId',
        'appId',
        'saleChannelId',
        'name',
        'businessLine',
        'type',
        'url',
        'mark',
        'picId',
        'startTime',
        'endTime',
        'saleNum',
        'refundNum',
        'createTime',
        'updateTime',
        'ruleType',
        'apiInfo',
        'extData',
        'tag',
        'promoteLv1',
        'channelLabels',
        'promoteBusiness', // 伏羲机构报备-业务线
        'promoteGradeDepts', // 伏羲机构报备-学部, 多选
        'promoteLv1New', // 伏羲机构报备-新推广产品
        'operator', // 操作人
    ];

    public function __construct()
    {
        $this->_dbName      = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_db          = null;
        $this->_logFile     = Zb_Const_Db::DBLOG_ZB;
        $this->_table       = 'tblAFXAct';
        $this->arrFieldsMap = array(
            'actId'      => 'id',
            'appId'      => 'app_id',
            'saleChannelId' => 'sale_channel_id',
            'name'       => 'name',
            'businessLine' => 'business_line',
            'type'       => 'type',
            'ruleType'   => 'rule_type',
            'url'        => 'url',
            'mark'       => 'mark',
            'picId'      => 'pic_id',
            'startTime'  => 'start_time',
            'endTime'    => 'end_time',
            'saleNum'    => 'sale_num',
            'refundNum'  => 'refund_num',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'apiInfo'    => 'api_info',
            'extData'    => 'ext_data',
            'tag'    => 'tag', // 该字段废弃
            'promoteLv1'    => 'promote_lv1',
            'channelLabels' => 'channel_labels', // 渠道标签，逗号隔开

            'promoteBusiness' => 'promote_business', // 伏羲机构报备-业务线
            'promoteGradeDepts' => 'promote_grade_depts', // 伏羲机构报备-学部, 多选
            'promoteLv1New' => 'promote_lv1_new', // 伏羲机构报备-新推广产品
            'operator' => 'operator', // 操作人
        );

        $this->arrTypesMap = array(
            'actId'      => Hk_Service_Db::TYPE_INT,
            'appId'      => Hk_Service_Db::TYPE_INT,
            'saleChannelId' => Hk_Service_Db::TYPE_INT,
            'name'       => Hk_Service_Db::TYPE_STR,
            'businessLine' => Hk_Service_Db::TYPE_INT,
            'type'       => Hk_Service_Db::TYPE_INT,
            'ruleType'   => Hk_Service_Db::TYPE_INT,
            'url'       => Hk_Service_Db::TYPE_STR,
            'mark'       => Hk_Service_Db::TYPE_STR,
            'picId'      => Hk_Service_Db::TYPE_STR,
            'startTime'  => Hk_Service_Db::TYPE_INT,
            'endTime'    => Hk_Service_Db::TYPE_INT,
            'saleNum'    => Hk_Service_Db::TYPE_INT,
            'refundNum'  => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'apiInfo'    => Hk_Service_Db::TYPE_JSON,
            'extData'    => Hk_Service_Db::TYPE_JSON,
            'tag'           => Hk_Service_Db::TYPE_INT,
            'promoteLv1'    => Hk_Service_Db::TYPE_STR,
            'channelLabels' => Hk_Service_Db::TYPE_STR,
            'promoteBusiness' => Hk_Service_Db::TYPE_STR,
            'promoteGradeDepts' => Hk_Service_Db::TYPE_STR,
            'promoteLv1New' => Hk_Service_Db::TYPE_STR,
            'operator' => Hk_Service_Db::TYPE_STR,
        );
    }
}
