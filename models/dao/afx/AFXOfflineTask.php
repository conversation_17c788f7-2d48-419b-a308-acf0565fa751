<?php
/**
 * User: <EMAIL>
 */

class Dao_Afx_AFXOfflineTask extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'taskId',
        'fileName',
        'projects',
        'apiSign',
        'paramMd5',
        'size',
        'dataRows',
        'fileId',
        'createRealName',
        'createTime',
        'updateTime',
        'filterParam',
        'field',
        'status',
        'deleted',
        'errorCode',
    ];

    public function __construct()
    {
        $this->_dbName      = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_db          = null;
        $this->_logFile     = Zb_Const_Db::DBLOG_ZB;
        $this->_table       = 'tblAFXOfflineTask';
        $this->arrFieldsMap = array(
            'taskId'     => 'id',
            'fileName'   => 'file_name',
            'projects'   => 'projects',
            'apiSign'    => 'api_sign',
            'paramMd5'   => 'param_md5',
            'size'       => 'size',
            'dataRows'   => 'data_rows',
            'createRealName' => 'create_realname',
            'fileId'     => 'file_id',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'filterParam'=> 'filter_param',
            'field'      => 'field',
            'deleted'    => 'deleted',
            'errorCode'  => 'error_code',
        );

        $this->arrTypesMap = array(
            'taskId'     => Hk_Service_Db::TYPE_INT,
            'fileName'   => Hk_Service_Db::TYPE_STR,
            'projects'   => Hk_Service_Db::TYPE_STR,
            'apiSign'    => Hk_Service_Db::TYPE_STR,
            'paramMd5'   => Hk_Service_Db::TYPE_STR,
            'size'       => Hk_Service_Db::TYPE_INT,
            'dataRows'   => Hk_Service_Db::TYPE_INT,
            'createRealName' => Hk_Service_Db::TYPE_STR,
            'fileId'     => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'filterParam'=> Hk_Service_Db::TYPE_JSON,
            'field'      => Hk_Service_Db::TYPE_JSON,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'errorCode'  => Hk_Service_Db::TYPE_INT,
        );
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getFenxiaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}
