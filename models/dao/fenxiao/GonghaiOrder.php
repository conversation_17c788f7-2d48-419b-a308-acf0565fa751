<?php
/**
 * 分销公海订单DAO层
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/11/10 19:08
 */
class Dao_FenXiao_GonghaiOrder extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'uid',
        'tradeId',
        'skuId',
        'tradeTime',
        'skuHitConfigIds',
        'reason',
        'createTime',
        'extData',
    ];

    public function __construct()
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table   = "tblFenXiaoGHOrder";

        $this->arrFieldsMap = [
            'tradeId'         => 'trade_id',
            'skuId'           => 'sku_id',
            'tradeTime'       => 'trade_time',
            'skuHitConfigIds' => 'sku_hit_config_ids',
            'createTime'      => 'create_time',
            'extData'         => 'ext_data',
        ];
        $this->arrTypesMap  = [
            'id'              => Hk_Service_Db::TYPE_INT,
            'uid'             => Hk_Service_Db::TYPE_INT,
            'tradeId'         => Hk_Service_Db::TYPE_INT,
            'skuId'           => Hk_Service_Db::TYPE_INT,
            'tradeTime'       => Hk_Service_Db::TYPE_INT,
            'skuHitConfigIds' => Hk_Service_Db::TYPE_JSON,
            'reason'          => Hk_Service_Db::TYPE_STR,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'extData'         => Hk_Service_Db::TYPE_JSON,
        ];
    }
}
