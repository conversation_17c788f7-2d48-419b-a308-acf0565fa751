<?php
/**
 * sku规则表
 * <AUTHOR>
 * @date 2020-12-10
 */
class Dao_Task_SkuRule extends Hk_Common_BaseDao
{
    /**
     * 构造函数
     * <AUTHOR>
     * @DateTime 2020-12-10
     */
    public function __construct()
    {
        $this->_db = null;
        $this->_table = 'tblSkuRule';
        $this->_dbName = ActPlatMis_Const_DbConfig::DB_YK_TASK;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;

        $this->arrFieldsMap = [
            'id'              => 'id',
            'masterTaskId'    => 'master_task_id',
            'slaveTaskId'     => 'slave_task_id',
            'behaviorId'      => 'behavior_id',
            'source'          => 'source',
            'learnYear'       => 'learn_year',
            'categoryPid'     => 'category_pid',
            'categoryId'      => 'category_id',
            'learnSeason'     => 'learn_season',
            'specialSellType' => 'special_sell_type',
            'grade'           => 'grade',
            'subject'         => 'subject',
            'skuId'           => 'sku_id',
            'sellPrice'       => 'sell_price',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time',
            'extData'         => 'ext_data',
        ];

        $this->arrTypesMap = [
            'id'              => Hk_Service_Db::TYPE_INT,
            'masterTaskId'    => Hk_Service_Db::TYPE_INT,
            'slaveTaskId'     => Hk_Service_Db::TYPE_INT,
            'behaviorId'      => Hk_Service_Db::TYPE_INT,
            'source'          => Hk_Service_Db::TYPE_INT,
            'learnYear'       => Hk_Service_Db::TYPE_INT,
            'categoryPid'     => Hk_Service_Db::TYPE_INT,
            'categoryId'      => Hk_Service_Db::TYPE_INT,
            'learnSeason'     => Hk_Service_Db::TYPE_INT,
            'specialSellType' => Hk_Service_Db::TYPE_INT,
            'grade'           => Hk_Service_Db::TYPE_INT,
            'subject'         => Hk_Service_Db::TYPE_INT,
            'skuId'           => Hk_Service_Db::TYPE_INT,
            'sellPrice'       => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'extData'         => Hk_Service_Db::TYPE_JSON,
        ];
    }

    /**
     * 获取表名
     * <AUTHOR>
     * @DateTime 2020-12-10
     * @return   [type]                [description]
     */
    public function getTable()
    {
        return $this->_table;
    }

    /**
     * 获取字段信息
     * <AUTHOR>
     * @DateTime 2020-12-10
     * @return   [type]                [description]
     */
    public function getFields()
    {
        return array_keys($this->arrFieldsMap);
    }
}