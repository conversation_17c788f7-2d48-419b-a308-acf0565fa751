<?php
/**
 * 短链域名池配置表
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/11/20 14:11
 */
class Dao_DomainPonds_DomainPond extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'businessLine',
        'businessName',
        'activityType',
        'activityName',
        'activeDomain',
        'latestAlertLabel',
        'backupDomain',
        'switchStatus',
        'createTime',
        'updateTime'
    ];

    public function __construct()
    {
        $this->_dbName = 'zyb_yk_actplat/zhibo_tool';
        $this->_db = null;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table = "tblDomainPond";

        $this->arrFieldsMap = [
            'id' => 'id',
            'businessLine' => 'business_line',
            'businessName' => 'business_name',
            'activityType' => 'activity_type',
            'activityName' => 'activity_name',
            'activeDomain' => 'active_domain',
            'latestAlertLabel' => 'latest_alert_label',
            'backupDomain' => 'backup_domain',
            'switchStatus' => 'switch_status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'businessLine' => Hk_Service_Db::TYPE_STR,
            'businessName' => Hk_Service_Db::TYPE_STR,
            'activityType' => Hk_Service_Db::TYPE_INT,
            'activityName' => Hk_Service_Db::TYPE_STR,
            'activeDomain' => Hk_Service_Db::TYPE_STR,
            'latestAlertLabel' => Hk_Service_Db::TYPE_STR,
            'backupDomain' => Hk_Service_Db::TYPE_STR,
            'switchStatus' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}