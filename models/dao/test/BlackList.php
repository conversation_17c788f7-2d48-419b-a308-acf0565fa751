<?php

/**
 * Created by PhpStorm.
 * User: xupengxiang
 * Date: 2020/10/22
 * Time: 2:59 PM
 */
class Dao_Test_BlackList extends Hk_Common_BaseDao
{
    const ALL_FIELD = [
        "id",
        "appId",
        "configId",
        "uid",
        "createTime",
        "extData"
    ];

    public function __construct()
    {
        //设置数据库名称
        $this -> _dbName = ActPlatMis_Const_DbConfig::DB_YK_FENXIAO;

        //设置db
        $this->_db = null;

        //设置日志
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;

        //设置表名
        $this->_table = "tblFenXiaoBlackList";

        //设置字段映射
        $this -> arrFieldsMap = [
            "id" => "id",
            "appId" => "app_id",
            "configId" => "config_id",
            "uid" => "uid",
            "createTime" => "create_time",
            "extData" => "ext_data"
        ];

        //字段验证
        $this->arrTypesMap = [
            "id" => Hk_Service_Db::TYPE_INT,
            "appId" => Hk_Service_Db::TYPE_INT,
            "configId" => Hk_Service_Db::TYPE_INT,
            "uid" => Hk_Service_Db::TYPE_INT,
            "createTime" => Hk_Service_Db::TYPE_INT,
            "extData" => Hk_Service_Db::TYPE_JSON
        ];
    }
}