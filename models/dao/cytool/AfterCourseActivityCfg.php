<?php
/**
 * 到课返现活动配置DAO
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/10/21 15:31
 */
class Dao_Cytool_AfterCourseActivityCfg extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'userMaxFee',
        'userTotalTimes',
        'activityMaxUsers',
        'activityMaxFee',
        'systemSwitch',
        'createTime',
        'updateTime',
    ];

    public function __construct()
    {
        $this->_dbName = 'zyb_yk_actplat/zhibo_tool';
        $this->_db = null;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table = "tblCashReturnAfterCourseCfg";

        $this->arrFieldsMap = [
            'id' => 'id',
            'userMaxFee' => 'user_max_fee',
            'userTotalTimes' => 'user_total_times',
            'activityMaxUsers' => 'activity_max_users',
            'activityMaxFee' => 'activity_max_fee',
            'systemSwitch' => 'system_switch',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'userMaxFee' => Hk_Service_Db::TYPE_INT,
            'userTotalTimes' => Hk_Service_Db::TYPE_INT,
            'activityMaxUsers' => Hk_Service_Db::TYPE_INT,
            'activityMaxFee' => Hk_Service_Db::TYPE_INT,
            'systemSwitch' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}
