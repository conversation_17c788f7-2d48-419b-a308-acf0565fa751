<?php
/**
 * @file: OfcStatus.php
 *        订单履约状态 类文件
 * @author: dds(liuxia<PERSON>@zuoyebang.com)
 * @date: 2023-06-28 18:00:07
 * @brief: 订单存储中的履约状态
 *         使用中
 */


class Sp_Dict_Ofc_OfcStatus
{
   //*Const
   const OFCING = 0;//执行中 - 使用中
   const OFCSUCCESS = 1;//成功 - 使用中
   const OFCIGNORE = 2;//忽略 - 使用中
   const OFCFAI = 3;//失败 - 使用中

   //*Map
   public static $map = [
       self::OFCING => "履约执行中",
       self::OFCSUCCESS => "履约成功",
       self::OFCIGNORE => "履约忽略",
       self::OFCFAI => "履约失败",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::OFCING => "ofcing",
       self::OFCSUCCESS => "ofcsuccess",
       self::OFCIGNORE => "ofcignore",
       self::OFCFAI => "ofcfai",
   ];
}
